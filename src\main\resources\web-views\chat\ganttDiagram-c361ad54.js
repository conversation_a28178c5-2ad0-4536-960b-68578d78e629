import{av as ye,aw as Be,ax as Ze,ay as Xe,az as qe,aA as Yn,aB as Kt,aC as Un,aD as nt,c as Dt,s as Fn,g as Ln,x as An,y as En,b as In,a as Wn,A as On,m as Hn,l as Gt,h as Rt,i as Nn,j as Vn,z as zn}from"./index.js";import{b as Pn,t as Ye,c as Rn,a as Bn,l as Zn}from"./linear.js";import{i as Xn}from"./init.js";var Ge={exports:{}};(function(t,e){(function(n,r){t.exports=r()})(ye,function(){var n={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},r=/(\[[^[]*\])|([-_:/.,()\s]+)|(A|a|Q|YYYY|YY?|ww?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g,i=/\d/,s=/\d\d/,a=/\d\d?/,y=/\d*[^-_:/,()\s\d]+/,S={},p=function(D){return(D=+D)+(D>68?1900:2e3)},g=function(D){return function(L){this[D]=+L}},F=[/[+-]\d\d:?(\d\d)?|Z/,function(D){(this.zone||(this.zone={})).offset=function(L){if(!L||L==="Z")return 0;var H=L.match(/([+-]|\d\d)/g),A=60*H[1]+(+H[2]||0);return A===0?0:H[0]==="+"?-A:A}(D)}],w=function(D){var L=S[D];return L&&(L.indexOf?L:L.s.concat(L.f))},T=function(D,L){var H,A=S.meridiem;if(A){for(var Z=1;Z<=24;Z+=1)if(D.indexOf(A(Z,0,L))>-1){H=Z>12;break}}else H=D===(L?"pm":"PM");return H},q={A:[y,function(D){this.afternoon=T(D,!1)}],a:[y,function(D){this.afternoon=T(D,!0)}],Q:[i,function(D){this.month=3*(D-1)+1}],S:[i,function(D){this.milliseconds=100*+D}],SS:[s,function(D){this.milliseconds=10*+D}],SSS:[/\d{3}/,function(D){this.milliseconds=+D}],s:[a,g("seconds")],ss:[a,g("seconds")],m:[a,g("minutes")],mm:[a,g("minutes")],H:[a,g("hours")],h:[a,g("hours")],HH:[a,g("hours")],hh:[a,g("hours")],D:[a,g("day")],DD:[s,g("day")],Do:[y,function(D){var L=S.ordinal,H=D.match(/\d+/);if(this.day=H[0],L)for(var A=1;A<=31;A+=1)L(A).replace(/\[|\]/g,"")===D&&(this.day=A)}],w:[a,g("week")],ww:[s,g("week")],M:[a,g("month")],MM:[s,g("month")],MMM:[y,function(D){var L=w("months"),H=(w("monthsShort")||L.map(function(A){return A.slice(0,3)})).indexOf(D)+1;if(H<1)throw new Error;this.month=H%12||H}],MMMM:[y,function(D){var L=w("months").indexOf(D)+1;if(L<1)throw new Error;this.month=L%12||L}],Y:[/[+-]?\d+/,g("year")],YY:[s,function(D){this.year=p(D)}],YYYY:[/\d{4}/,g("year")],Z:F,ZZ:F};function I(D){var L,H;L=D,H=S&&S.formats;for(var A=(D=L.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,function(_,x,o){var f=o&&o.toUpperCase();return x||H[o]||n[o]||H[f].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(m,u,M){return u||M.slice(1)})})).match(r),Z=A.length,j=0;j<Z;j+=1){var b=A[j],W=q[b],v=W&&W[0],U=W&&W[1];A[j]=U?{regex:v,parser:U}:b.replace(/^\[|\]$/g,"")}return function(_){for(var x={},o=0,f=0;o<Z;o+=1){var m=A[o];if(typeof m=="string")f+=m.length;else{var u=m.regex,M=m.parser,c=_.slice(f),G=u.exec(c)[0];M.call(x,G),_=_.replace(G,"")}}return function(d){var h=d.afternoon;if(h!==void 0){var C=d.hours;h?C<12&&(d.hours+=12):C===12&&(d.hours=0),delete d.afternoon}}(x),x}}return function(D,L,H){H.p.customParseFormat=!0,D&&D.parseTwoDigitYear&&(p=D.parseTwoDigitYear);var A=L.prototype,Z=A.parse;A.parse=function(j){var b=j.date,W=j.utc,v=j.args;this.$u=W;var U=v[1];if(typeof U=="string"){var _=v[2]===!0,x=v[3]===!0,o=_||x,f=v[2];x&&(f=v[2]),S=this.$locale(),!_&&f&&(S=H.Ls[f]),this.$d=function(c,G,d,h){try{if(["x","X"].indexOf(G)>-1)return new Date((G==="X"?1e3:1)*c);var C=I(G)(c),X=C.year,O=C.month,z=C.day,N=C.hours,P=C.minutes,at=C.seconds,ot=C.milliseconds,k=C.zone,E=C.week,Y=new Date,l=z||(X||O?1:Y.getDate()),B=X||Y.getFullYear(),V=0;X&&!O||(V=O>0?O-1:Y.getMonth());var Q,J=N||0,$=P||0,ct=at||0,yt=ot||0;return k?new Date(Date.UTC(B,V,l,J,$,ct,yt+60*k.offset*1e3)):d?new Date(Date.UTC(B,V,l,J,$,ct,yt)):(Q=new Date(B,V,l,J,$,ct,yt),E&&(Q=h(Q).week(E).toDate()),Q)}catch{return new Date("")}}(b,U,W,H),this.init(),f&&f!==!0&&(this.$L=this.locale(f).$L),o&&b!=this.format(U)&&(this.$d=new Date("")),S={}}else if(U instanceof Array)for(var m=U.length,u=1;u<=m;u+=1){v[1]=U[u-1];var M=H.apply(this,v);if(M.isValid()){this.$d=M.$d,this.$L=M.$L,this.init();break}u===m&&(this.$d=new Date(""))}else Z.call(this,j)}}})})(Ge);var qn=Ge.exports,Qe={exports:{}};(function(t,e){(function(n,r){t.exports=r()})(ye,function(){return function(n,r){var i=r.prototype,s=i.format;i.format=function(a){var y=this,S=this.$locale();if(!this.isValid())return s.bind(this)(a);var p=this.$utils(),g=(a||"YYYY-MM-DDTHH:mm:ssZ").replace(/\[([^\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g,function(F){switch(F){case"Q":return Math.ceil((y.$M+1)/3);case"Do":return S.ordinal(y.$D);case"gggg":return y.weekYear();case"GGGG":return y.isoWeekYear();case"wo":return S.ordinal(y.week(),"W");case"w":case"ww":return p.s(y.week(),F==="w"?1:2,"0");case"W":case"WW":return p.s(y.isoWeek(),F==="W"?1:2,"0");case"k":case"kk":return p.s(String(y.$H===0?24:y.$H),F==="k"?1:2,"0");case"X":return Math.floor(y.$d.getTime()/1e3);case"x":return y.$d.getTime();case"z":return"["+y.offsetName()+"]";case"zzz":return"["+y.offsetName("long")+"]";default:return F}});return s.bind(this)(g)}}})})(Qe);var Gn=Qe.exports;function Qn(t,e){let n;if(e===void 0)for(const r of t)r!=null&&(n<r||n===void 0&&r>=r)&&(n=r);else{let r=-1;for(let i of t)(i=e(i,++r,t))!=null&&(n<i||n===void 0&&i>=i)&&(n=i)}return n}function jn(t,e){let n;if(e===void 0)for(const r of t)r!=null&&(n>r||n===void 0&&r>=r)&&(n=r);else{let r=-1;for(let i of t)(i=e(i,++r,t))!=null&&(n>i||n===void 0&&i>=i)&&(n=i)}return n}function Jn(t){return t}var Zt=1,te=2,ue=3,Bt=4,Ue=1e-6;function $n(t){return"translate("+t+",0)"}function Kn(t){return"translate(0,"+t+")"}function tr(t){return e=>+t(e)}function er(t,e){return e=Math.max(0,t.bandwidth()-e*2)/2,t.round()&&(e=Math.round(e)),n=>+t(n)+e}function nr(){return!this.__axis}function je(t,e){var n=[],r=null,i=null,s=6,a=6,y=3,S=typeof window!="undefined"&&window.devicePixelRatio>1?0:.5,p=t===Zt||t===Bt?-1:1,g=t===Bt||t===te?"x":"y",F=t===Zt||t===ue?$n:Kn;function w(T){var q=r==null?e.ticks?e.ticks.apply(e,n):e.domain():r,I=i==null?e.tickFormat?e.tickFormat.apply(e,n):Jn:i,D=Math.max(s,0)+y,L=e.range(),H=+L[0]+S,A=+L[L.length-1]+S,Z=(e.bandwidth?er:tr)(e.copy(),S),j=T.selection?T.selection():T,b=j.selectAll(".domain").data([null]),W=j.selectAll(".tick").data(q,e).order(),v=W.exit(),U=W.enter().append("g").attr("class","tick"),_=W.select("line"),x=W.select("text");b=b.merge(b.enter().insert("path",".tick").attr("class","domain").attr("stroke","currentColor")),W=W.merge(U),_=_.merge(U.append("line").attr("stroke","currentColor").attr(g+"2",p*s)),x=x.merge(U.append("text").attr("fill","currentColor").attr(g,p*D).attr("dy",t===Zt?"0em":t===ue?"0.71em":"0.32em")),T!==j&&(b=b.transition(T),W=W.transition(T),_=_.transition(T),x=x.transition(T),v=v.transition(T).attr("opacity",Ue).attr("transform",function(o){return isFinite(o=Z(o))?F(o+S):this.getAttribute("transform")}),U.attr("opacity",Ue).attr("transform",function(o){var f=this.parentNode.__axis;return F((f&&isFinite(f=f(o))?f:Z(o))+S)})),v.remove(),b.attr("d",t===Bt||t===te?a?"M"+p*a+","+H+"H"+S+"V"+A+"H"+p*a:"M"+S+","+H+"V"+A:a?"M"+H+","+p*a+"V"+S+"H"+A+"V"+p*a:"M"+H+","+S+"H"+A),W.attr("opacity",1).attr("transform",function(o){return F(Z(o)+S)}),_.attr(g+"2",p*s),x.attr(g,p*D).text(I),j.filter(nr).attr("fill","none").attr("font-size",10).attr("font-family","sans-serif").attr("text-anchor",t===te?"start":t===Bt?"end":"middle"),j.each(function(){this.__axis=Z})}return w.scale=function(T){return arguments.length?(e=T,w):e},w.ticks=function(){return n=Array.from(arguments),w},w.tickArguments=function(T){return arguments.length?(n=T==null?[]:Array.from(T),w):n.slice()},w.tickValues=function(T){return arguments.length?(r=T==null?null:Array.from(T),w):r&&r.slice()},w.tickFormat=function(T){return arguments.length?(i=T,w):i},w.tickSize=function(T){return arguments.length?(s=a=+T,w):s},w.tickSizeInner=function(T){return arguments.length?(s=+T,w):s},w.tickSizeOuter=function(T){return arguments.length?(a=+T,w):a},w.tickPadding=function(T){return arguments.length?(y=+T,w):y},w.offset=function(T){return arguments.length?(S=+T,w):S},w}function rr(t){return je(Zt,t)}function ir(t){return je(ue,t)}const sr=Math.PI/180,ar=180/Math.PI,Qt=18,Je=.96422,$e=1,Ke=.82521,tn=4/29,Mt=6/29,en=3*Mt*Mt,or=Mt*Mt*Mt;function nn(t){if(t instanceof st)return new st(t.l,t.a,t.b,t.opacity);if(t instanceof ft)return rn(t);t instanceof Xe||(t=Yn(t));var e=ie(t.r),n=ie(t.g),r=ie(t.b),i=ee((.2225045*e+.7168786*n+.0606169*r)/$e),s,a;return e===n&&n===r?s=a=i:(s=ee((.4360747*e+.3850649*n+.1430804*r)/Je),a=ee((.0139322*e+.0971045*n+.7141733*r)/Ke)),new st(116*i-16,500*(s-i),200*(i-a),t.opacity)}function cr(t,e,n,r){return arguments.length===1?nn(t):new st(t,e,n,r==null?1:r)}function st(t,e,n,r){this.l=+t,this.a=+e,this.b=+n,this.opacity=+r}Be(st,cr,Ze(qe,{brighter(t){return new st(this.l+Qt*(t==null?1:t),this.a,this.b,this.opacity)},darker(t){return new st(this.l-Qt*(t==null?1:t),this.a,this.b,this.opacity)},rgb(){var t=(this.l+16)/116,e=isNaN(this.a)?t:t+this.a/500,n=isNaN(this.b)?t:t-this.b/200;return e=Je*ne(e),t=$e*ne(t),n=Ke*ne(n),new Xe(re(3.1338561*e-1.6168667*t-.4906146*n),re(-.9787684*e+1.9161415*t+.033454*n),re(.0719453*e-.2289914*t+1.4052427*n),this.opacity)}}));function ee(t){return t>or?Math.pow(t,1/3):t/en+tn}function ne(t){return t>Mt?t*t*t:en*(t-tn)}function re(t){return 255*(t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)}function ie(t){return(t/=255)<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function lr(t){if(t instanceof ft)return new ft(t.h,t.c,t.l,t.opacity);if(t instanceof st||(t=nn(t)),t.a===0&&t.b===0)return new ft(NaN,0<t.l&&t.l<100?0:NaN,t.l,t.opacity);var e=Math.atan2(t.b,t.a)*ar;return new ft(e<0?e+360:e,Math.sqrt(t.a*t.a+t.b*t.b),t.l,t.opacity)}function fe(t,e,n,r){return arguments.length===1?lr(t):new ft(t,e,n,r==null?1:r)}function ft(t,e,n,r){this.h=+t,this.c=+e,this.l=+n,this.opacity=+r}function rn(t){if(isNaN(t.h))return new st(t.l,0,0,t.opacity);var e=t.h*sr;return new st(t.l,Math.cos(e)*t.c,Math.sin(e)*t.c,t.opacity)}Be(ft,fe,Ze(qe,{brighter(t){return new ft(this.h,this.c,this.l+Qt*(t==null?1:t),this.opacity)},darker(t){return new ft(this.h,this.c,this.l-Qt*(t==null?1:t),this.opacity)},rgb(){return rn(this).rgb()}}));function ur(t){return function(e,n){var r=t((e=fe(e)).h,(n=fe(n)).h),i=Kt(e.c,n.c),s=Kt(e.l,n.l),a=Kt(e.opacity,n.opacity);return function(y){return e.h=r(y),e.c=i(y),e.l=s(y),e.opacity=a(y),e+""}}}var fr=ur(Un);function hr(t,e){t=t.slice();var n=0,r=t.length-1,i=t[n],s=t[r],a;return s<i&&(a=n,n=r,r=a,a=i,i=s,s=a),t[n]=e.floor(i),t[r]=e.ceil(s),t}const se=new Date,ae=new Date;function tt(t,e,n,r){function i(s){return t(s=arguments.length===0?new Date:new Date(+s)),s}return i.floor=s=>(t(s=new Date(+s)),s),i.ceil=s=>(t(s=new Date(s-1)),e(s,1),t(s),s),i.round=s=>{const a=i(s),y=i.ceil(s);return s-a<y-s?a:y},i.offset=(s,a)=>(e(s=new Date(+s),a==null?1:Math.floor(a)),s),i.range=(s,a,y)=>{const S=[];if(s=i.ceil(s),y=y==null?1:Math.floor(y),!(s<a)||!(y>0))return S;let p;do S.push(p=new Date(+s)),e(s,y),t(s);while(p<s&&s<a);return S},i.filter=s=>tt(a=>{if(a>=a)for(;t(a),!s(a);)a.setTime(a-1)},(a,y)=>{if(a>=a)if(y<0)for(;++y<=0;)for(;e(a,-1),!s(a););else for(;--y>=0;)for(;e(a,1),!s(a););}),n&&(i.count=(s,a)=>(se.setTime(+s),ae.setTime(+a),t(se),t(ae),Math.floor(n(se,ae))),i.every=s=>(s=Math.floor(s),!isFinite(s)||!(s>0)?null:s>1?i.filter(r?a=>r(a)%s===0:a=>i.count(0,a)%s===0):i)),i}const _t=tt(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);_t.every=t=>(t=Math.floor(t),!isFinite(t)||!(t>0)?null:t>1?tt(e=>{e.setTime(Math.floor(e/t)*t)},(e,n)=>{e.setTime(+e+n*t)},(e,n)=>(n-e)/t):_t);_t.range;const ht=1e3,rt=ht*60,dt=rt*60,mt=dt*24,ke=mt*7,Fe=mt*30,oe=mt*365,kt=tt(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+e*ht)},(t,e)=>(e-t)/ht,t=>t.getUTCSeconds());kt.range;const Et=tt(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*ht)},(t,e)=>{t.setTime(+t+e*rt)},(t,e)=>(e-t)/rt,t=>t.getMinutes());Et.range;const sn=tt(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+e*rt)},(t,e)=>(e-t)/rt,t=>t.getUTCMinutes());sn.range;const It=tt(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*ht-t.getMinutes()*rt)},(t,e)=>{t.setTime(+t+e*dt)},(t,e)=>(e-t)/dt,t=>t.getHours());It.range;const an=tt(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+e*dt)},(t,e)=>(e-t)/dt,t=>t.getUTCHours());an.range;const Tt=tt(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*rt)/mt,t=>t.getDate()-1);Tt.range;const pe=tt(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/mt,t=>t.getUTCDate()-1);pe.range;const on=tt(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/mt,t=>Math.floor(t/mt));on.range;function bt(t){return tt(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(e,n)=>{e.setDate(e.getDate()+n*7)},(e,n)=>(n-e-(n.getTimezoneOffset()-e.getTimezoneOffset())*rt)/ke)}const Ht=bt(0),Wt=bt(1),cn=bt(2),ln=bt(3),vt=bt(4),un=bt(5),fn=bt(6);Ht.range;Wt.range;cn.range;ln.range;vt.range;un.range;fn.range;function xt(t){return tt(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(e,n)=>{e.setUTCDate(e.getUTCDate()+n*7)},(e,n)=>(n-e)/ke)}const Te=xt(0),jt=xt(1),dr=xt(2),mr=xt(3),Yt=xt(4),gr=xt(5),yr=xt(6);Te.range;jt.range;dr.range;mr.range;Yt.range;gr.range;yr.range;const Ot=tt(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());Ot.range;const hn=tt(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());hn.range;const gt=tt(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());gt.every=t=>!isFinite(t=Math.floor(t))||!(t>0)?null:tt(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,n)=>{e.setFullYear(e.getFullYear()+n*t)});gt.range;const pt=tt(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());pt.every=t=>!isFinite(t=Math.floor(t))||!(t>0)?null:tt(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,n)=>{e.setUTCFullYear(e.getUTCFullYear()+n*t)});pt.range;function dn(t,e,n,r,i,s){const a=[[kt,1,ht],[kt,5,5*ht],[kt,15,15*ht],[kt,30,30*ht],[s,1,rt],[s,5,5*rt],[s,15,15*rt],[s,30,30*rt],[i,1,dt],[i,3,3*dt],[i,6,6*dt],[i,12,12*dt],[r,1,mt],[r,2,2*mt],[n,1,ke],[e,1,Fe],[e,3,3*Fe],[t,1,oe]];function y(p,g,F){const w=g<p;w&&([p,g]=[g,p]);const T=F&&typeof F.range=="function"?F:S(p,g,F),q=T?T.range(p,+g+1):[];return w?q.reverse():q}function S(p,g,F){const w=Math.abs(g-p)/F,T=Pn(([,,D])=>D).right(a,w);if(T===a.length)return t.every(Ye(p/oe,g/oe,F));if(T===0)return _t.every(Math.max(Ye(p,g,F),1));const[q,I]=a[w/a[T-1][2]<a[T][2]/w?T-1:T];return q.every(I)}return[y,S]}dn(pt,hn,Te,on,an,sn);const[kr,pr]=dn(gt,Ot,Ht,Tt,It,Et);function ce(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function le(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function Ft(t,e,n){return{y:t,m:e,d:n,H:0,M:0,S:0,L:0}}function Tr(t){var e=t.dateTime,n=t.date,r=t.time,i=t.periods,s=t.days,a=t.shortDays,y=t.months,S=t.shortMonths,p=Lt(i),g=At(i),F=Lt(s),w=At(s),T=Lt(a),q=At(a),I=Lt(y),D=At(y),L=Lt(S),H=At(S),A={a:c,A:G,b:d,B:h,c:null,d:Oe,e:Oe,f:Pr,g:$r,G:ti,H:Nr,I:Vr,j:zr,L:mn,m:Rr,M:Br,p:C,q:X,Q:Ve,s:ze,S:Zr,u:Xr,U:qr,V:Gr,w:Qr,W:jr,x:null,X:null,y:Jr,Y:Kr,Z:ei,"%":Ne},Z={a:O,A:z,b:N,B:P,c:null,d:He,e:He,f:si,g:gi,G:ki,H:ni,I:ri,j:ii,L:yn,m:ai,M:oi,p:at,q:ot,Q:Ve,s:ze,S:ci,u:li,U:ui,V:fi,w:hi,W:di,x:null,X:null,y:mi,Y:yi,Z:pi,"%":Ne},j={a:_,A:x,b:o,B:f,c:m,d:Ie,e:Ie,f:Ir,g:Ee,G:Ae,H:We,I:We,j:Fr,L:Er,m:Ur,M:Lr,p:U,q:Yr,Q:Or,s:Hr,S:Ar,u:Cr,U:Dr,V:Mr,w:wr,W:Sr,x:u,X:M,y:Ee,Y:Ae,Z:_r,"%":Wr};A.x=b(n,A),A.X=b(r,A),A.c=b(e,A),Z.x=b(n,Z),Z.X=b(r,Z),Z.c=b(e,Z);function b(k,E){return function(Y){var l=[],B=-1,V=0,Q=k.length,J,$,ct;for(Y instanceof Date||(Y=new Date(+Y));++B<Q;)k.charCodeAt(B)===37&&(l.push(k.slice(V,B)),($=Le[J=k.charAt(++B)])!=null?J=k.charAt(++B):$=J==="e"?" ":"0",(ct=E[J])&&(J=ct(Y,$)),l.push(J),V=B+1);return l.push(k.slice(V,B)),l.join("")}}function W(k,E){return function(Y){var l=Ft(1900,void 0,1),B=v(l,k,Y+="",0),V,Q;if(B!=Y.length)return null;if("Q"in l)return new Date(l.Q);if("s"in l)return new Date(l.s*1e3+("L"in l?l.L:0));if(E&&!("Z"in l)&&(l.Z=0),"p"in l&&(l.H=l.H%12+l.p*12),l.m===void 0&&(l.m="q"in l?l.q:0),"V"in l){if(l.V<1||l.V>53)return null;"w"in l||(l.w=1),"Z"in l?(V=le(Ft(l.y,0,1)),Q=V.getUTCDay(),V=Q>4||Q===0?jt.ceil(V):jt(V),V=pe.offset(V,(l.V-1)*7),l.y=V.getUTCFullYear(),l.m=V.getUTCMonth(),l.d=V.getUTCDate()+(l.w+6)%7):(V=ce(Ft(l.y,0,1)),Q=V.getDay(),V=Q>4||Q===0?Wt.ceil(V):Wt(V),V=Tt.offset(V,(l.V-1)*7),l.y=V.getFullYear(),l.m=V.getMonth(),l.d=V.getDate()+(l.w+6)%7)}else("W"in l||"U"in l)&&("w"in l||(l.w="u"in l?l.u%7:"W"in l?1:0),Q="Z"in l?le(Ft(l.y,0,1)).getUTCDay():ce(Ft(l.y,0,1)).getDay(),l.m=0,l.d="W"in l?(l.w+6)%7+l.W*7-(Q+5)%7:l.w+l.U*7-(Q+6)%7);return"Z"in l?(l.H+=l.Z/100|0,l.M+=l.Z%100,le(l)):ce(l)}}function v(k,E,Y,l){for(var B=0,V=E.length,Q=Y.length,J,$;B<V;){if(l>=Q)return-1;if(J=E.charCodeAt(B++),J===37){if(J=E.charAt(B++),$=j[J in Le?E.charAt(B++):J],!$||(l=$(k,Y,l))<0)return-1}else if(J!=Y.charCodeAt(l++))return-1}return l}function U(k,E,Y){var l=p.exec(E.slice(Y));return l?(k.p=g.get(l[0].toLowerCase()),Y+l[0].length):-1}function _(k,E,Y){var l=T.exec(E.slice(Y));return l?(k.w=q.get(l[0].toLowerCase()),Y+l[0].length):-1}function x(k,E,Y){var l=F.exec(E.slice(Y));return l?(k.w=w.get(l[0].toLowerCase()),Y+l[0].length):-1}function o(k,E,Y){var l=L.exec(E.slice(Y));return l?(k.m=H.get(l[0].toLowerCase()),Y+l[0].length):-1}function f(k,E,Y){var l=I.exec(E.slice(Y));return l?(k.m=D.get(l[0].toLowerCase()),Y+l[0].length):-1}function m(k,E,Y){return v(k,e,E,Y)}function u(k,E,Y){return v(k,n,E,Y)}function M(k,E,Y){return v(k,r,E,Y)}function c(k){return a[k.getDay()]}function G(k){return s[k.getDay()]}function d(k){return S[k.getMonth()]}function h(k){return y[k.getMonth()]}function C(k){return i[+(k.getHours()>=12)]}function X(k){return 1+~~(k.getMonth()/3)}function O(k){return a[k.getUTCDay()]}function z(k){return s[k.getUTCDay()]}function N(k){return S[k.getUTCMonth()]}function P(k){return y[k.getUTCMonth()]}function at(k){return i[+(k.getUTCHours()>=12)]}function ot(k){return 1+~~(k.getUTCMonth()/3)}return{format:function(k){var E=b(k+="",A);return E.toString=function(){return k},E},parse:function(k){var E=W(k+="",!1);return E.toString=function(){return k},E},utcFormat:function(k){var E=b(k+="",Z);return E.toString=function(){return k},E},utcParse:function(k){var E=W(k+="",!0);return E.toString=function(){return k},E}}}var Le={"-":"",_:" ",0:"0"},et=/^\s*\d+/,vr=/^%/,br=/[\\^$*+?|[\]().{}]/g;function R(t,e,n){var r=t<0?"-":"",i=(r?-t:t)+"",s=i.length;return r+(s<n?new Array(n-s+1).join(e)+i:i)}function xr(t){return t.replace(br,"\\$&")}function Lt(t){return new RegExp("^(?:"+t.map(xr).join("|")+")","i")}function At(t){return new Map(t.map((e,n)=>[e.toLowerCase(),n]))}function wr(t,e,n){var r=et.exec(e.slice(n,n+1));return r?(t.w=+r[0],n+r[0].length):-1}function Cr(t,e,n){var r=et.exec(e.slice(n,n+1));return r?(t.u=+r[0],n+r[0].length):-1}function Dr(t,e,n){var r=et.exec(e.slice(n,n+2));return r?(t.U=+r[0],n+r[0].length):-1}function Mr(t,e,n){var r=et.exec(e.slice(n,n+2));return r?(t.V=+r[0],n+r[0].length):-1}function Sr(t,e,n){var r=et.exec(e.slice(n,n+2));return r?(t.W=+r[0],n+r[0].length):-1}function Ae(t,e,n){var r=et.exec(e.slice(n,n+4));return r?(t.y=+r[0],n+r[0].length):-1}function Ee(t,e,n){var r=et.exec(e.slice(n,n+2));return r?(t.y=+r[0]+(+r[0]>68?1900:2e3),n+r[0].length):-1}function _r(t,e,n){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(n,n+6));return r?(t.Z=r[1]?0:-(r[2]+(r[3]||"00")),n+r[0].length):-1}function Yr(t,e,n){var r=et.exec(e.slice(n,n+1));return r?(t.q=r[0]*3-3,n+r[0].length):-1}function Ur(t,e,n){var r=et.exec(e.slice(n,n+2));return r?(t.m=r[0]-1,n+r[0].length):-1}function Ie(t,e,n){var r=et.exec(e.slice(n,n+2));return r?(t.d=+r[0],n+r[0].length):-1}function Fr(t,e,n){var r=et.exec(e.slice(n,n+3));return r?(t.m=0,t.d=+r[0],n+r[0].length):-1}function We(t,e,n){var r=et.exec(e.slice(n,n+2));return r?(t.H=+r[0],n+r[0].length):-1}function Lr(t,e,n){var r=et.exec(e.slice(n,n+2));return r?(t.M=+r[0],n+r[0].length):-1}function Ar(t,e,n){var r=et.exec(e.slice(n,n+2));return r?(t.S=+r[0],n+r[0].length):-1}function Er(t,e,n){var r=et.exec(e.slice(n,n+3));return r?(t.L=+r[0],n+r[0].length):-1}function Ir(t,e,n){var r=et.exec(e.slice(n,n+6));return r?(t.L=Math.floor(r[0]/1e3),n+r[0].length):-1}function Wr(t,e,n){var r=vr.exec(e.slice(n,n+1));return r?n+r[0].length:-1}function Or(t,e,n){var r=et.exec(e.slice(n));return r?(t.Q=+r[0],n+r[0].length):-1}function Hr(t,e,n){var r=et.exec(e.slice(n));return r?(t.s=+r[0],n+r[0].length):-1}function Oe(t,e){return R(t.getDate(),e,2)}function Nr(t,e){return R(t.getHours(),e,2)}function Vr(t,e){return R(t.getHours()%12||12,e,2)}function zr(t,e){return R(1+Tt.count(gt(t),t),e,3)}function mn(t,e){return R(t.getMilliseconds(),e,3)}function Pr(t,e){return mn(t,e)+"000"}function Rr(t,e){return R(t.getMonth()+1,e,2)}function Br(t,e){return R(t.getMinutes(),e,2)}function Zr(t,e){return R(t.getSeconds(),e,2)}function Xr(t){var e=t.getDay();return e===0?7:e}function qr(t,e){return R(Ht.count(gt(t)-1,t),e,2)}function gn(t){var e=t.getDay();return e>=4||e===0?vt(t):vt.ceil(t)}function Gr(t,e){return t=gn(t),R(vt.count(gt(t),t)+(gt(t).getDay()===4),e,2)}function Qr(t){return t.getDay()}function jr(t,e){return R(Wt.count(gt(t)-1,t),e,2)}function Jr(t,e){return R(t.getFullYear()%100,e,2)}function $r(t,e){return t=gn(t),R(t.getFullYear()%100,e,2)}function Kr(t,e){return R(t.getFullYear()%1e4,e,4)}function ti(t,e){var n=t.getDay();return t=n>=4||n===0?vt(t):vt.ceil(t),R(t.getFullYear()%1e4,e,4)}function ei(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+R(e/60|0,"0",2)+R(e%60,"0",2)}function He(t,e){return R(t.getUTCDate(),e,2)}function ni(t,e){return R(t.getUTCHours(),e,2)}function ri(t,e){return R(t.getUTCHours()%12||12,e,2)}function ii(t,e){return R(1+pe.count(pt(t),t),e,3)}function yn(t,e){return R(t.getUTCMilliseconds(),e,3)}function si(t,e){return yn(t,e)+"000"}function ai(t,e){return R(t.getUTCMonth()+1,e,2)}function oi(t,e){return R(t.getUTCMinutes(),e,2)}function ci(t,e){return R(t.getUTCSeconds(),e,2)}function li(t){var e=t.getUTCDay();return e===0?7:e}function ui(t,e){return R(Te.count(pt(t)-1,t),e,2)}function kn(t){var e=t.getUTCDay();return e>=4||e===0?Yt(t):Yt.ceil(t)}function fi(t,e){return t=kn(t),R(Yt.count(pt(t),t)+(pt(t).getUTCDay()===4),e,2)}function hi(t){return t.getUTCDay()}function di(t,e){return R(jt.count(pt(t)-1,t),e,2)}function mi(t,e){return R(t.getUTCFullYear()%100,e,2)}function gi(t,e){return t=kn(t),R(t.getUTCFullYear()%100,e,2)}function yi(t,e){return R(t.getUTCFullYear()%1e4,e,4)}function ki(t,e){var n=t.getUTCDay();return t=n>=4||n===0?Yt(t):Yt.ceil(t),R(t.getUTCFullYear()%1e4,e,4)}function pi(){return"+0000"}function Ne(){return"%"}function Ve(t){return+t}function ze(t){return Math.floor(+t/1e3)}var Ct,Jt;Ti({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function Ti(t){return Ct=Tr(t),Jt=Ct.format,Ct.parse,Ct.utcFormat,Ct.utcParse,Ct}function vi(t){return new Date(t)}function bi(t){return t instanceof Date?+t:+new Date(+t)}function pn(t,e,n,r,i,s,a,y,S,p){var g=Rn(),F=g.invert,w=g.domain,T=p(".%L"),q=p(":%S"),I=p("%I:%M"),D=p("%I %p"),L=p("%a %d"),H=p("%b %d"),A=p("%B"),Z=p("%Y");function j(b){return(S(b)<b?T:y(b)<b?q:a(b)<b?I:s(b)<b?D:r(b)<b?i(b)<b?L:H:n(b)<b?A:Z)(b)}return g.invert=function(b){return new Date(F(b))},g.domain=function(b){return arguments.length?w(Array.from(b,bi)):w().map(vi)},g.ticks=function(b){var W=w();return t(W[0],W[W.length-1],b==null?10:b)},g.tickFormat=function(b,W){return W==null?j:p(W)},g.nice=function(b){var W=w();return(!b||typeof b.range!="function")&&(b=e(W[0],W[W.length-1],b==null?10:b)),b?w(hr(W,b)):g},g.copy=function(){return Bn(g,pn(t,e,n,r,i,s,a,y,S,p))},g}function xi(){return Xn.apply(pn(kr,pr,gt,Ot,Ht,Tt,It,Et,kt,Jt).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}var Tn={exports:{}};(function(t,e){(function(n,r){t.exports=r()})(ye,function(){var n="day";return function(r,i,s){var a=function(p){return p.add(4-p.isoWeekday(),n)},y=i.prototype;y.isoWeekYear=function(){return a(this).year()},y.isoWeek=function(p){if(!this.$utils().u(p))return this.add(7*(p-this.isoWeek()),n);var g,F,w,T,q=a(this),I=(g=this.isoWeekYear(),F=this.$u,w=(F?s.utc:s)().year(g).startOf("year"),T=4-w.isoWeekday(),w.isoWeekday()>4&&(T+=7),w.add(T,n));return q.diff(I,"week")+1},y.isoWeekday=function(p){return this.$utils().u(p)?this.day()||7:this.day(this.day()%7?p:p-7)};var S=y.startOf;y.startOf=function(p,g){var F=this.$utils(),w=!!F.u(g)||g;return F.p(p)==="isoweek"?w?this.date(this.date()-(this.isoWeekday()-1)).startOf("day"):this.date(this.date()-1-(this.isoWeekday()-1)+7).endOf("day"):S.bind(this)(p,g)}}})})(Tn);var wi=Tn.exports,he=function(){var t=function(x,o,f,m){for(f=f||{},m=x.length;m--;f[x[m]]=o);return f},e=[6,8,10,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,30,32,33,35,37],n=[1,25],r=[1,26],i=[1,27],s=[1,28],a=[1,29],y=[1,30],S=[1,31],p=[1,9],g=[1,10],F=[1,11],w=[1,12],T=[1,13],q=[1,14],I=[1,15],D=[1,16],L=[1,18],H=[1,19],A=[1,20],Z=[1,21],j=[1,22],b=[1,24],W=[1,32],v={trace:function(){},yy:{},symbols_:{error:2,start:3,gantt:4,document:5,EOF:6,line:7,SPACE:8,statement:9,NL:10,weekday:11,weekday_monday:12,weekday_tuesday:13,weekday_wednesday:14,weekday_thursday:15,weekday_friday:16,weekday_saturday:17,weekday_sunday:18,dateFormat:19,inclusiveEndDates:20,topAxis:21,axisFormat:22,tickInterval:23,excludes:24,includes:25,todayMarker:26,title:27,acc_title:28,acc_title_value:29,acc_descr:30,acc_descr_value:31,acc_descr_multiline_value:32,section:33,clickStatement:34,taskTxt:35,taskData:36,click:37,callbackname:38,callbackargs:39,href:40,clickStatementDebug:41,$accept:0,$end:1},terminals_:{2:"error",4:"gantt",6:"EOF",8:"SPACE",10:"NL",12:"weekday_monday",13:"weekday_tuesday",14:"weekday_wednesday",15:"weekday_thursday",16:"weekday_friday",17:"weekday_saturday",18:"weekday_sunday",19:"dateFormat",20:"inclusiveEndDates",21:"topAxis",22:"axisFormat",23:"tickInterval",24:"excludes",25:"includes",26:"todayMarker",27:"title",28:"acc_title",29:"acc_title_value",30:"acc_descr",31:"acc_descr_value",32:"acc_descr_multiline_value",33:"section",35:"taskTxt",36:"taskData",37:"click",38:"callbackname",39:"callbackargs",40:"href"},productions_:[0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[11,1],[11,1],[11,1],[11,1],[11,1],[11,1],[11,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,2],[9,2],[9,1],[9,1],[9,1],[9,2],[34,2],[34,3],[34,3],[34,4],[34,3],[34,4],[34,2],[41,2],[41,3],[41,3],[41,4],[41,3],[41,4],[41,2]],performAction:function(o,f,m,u,M,c,G){var d=c.length-1;switch(M){case 1:return c[d-1];case 2:this.$=[];break;case 3:c[d-1].push(c[d]),this.$=c[d-1];break;case 4:case 5:this.$=c[d];break;case 6:case 7:this.$=[];break;case 8:u.setWeekday("monday");break;case 9:u.setWeekday("tuesday");break;case 10:u.setWeekday("wednesday");break;case 11:u.setWeekday("thursday");break;case 12:u.setWeekday("friday");break;case 13:u.setWeekday("saturday");break;case 14:u.setWeekday("sunday");break;case 15:u.setDateFormat(c[d].substr(11)),this.$=c[d].substr(11);break;case 16:u.enableInclusiveEndDates(),this.$=c[d].substr(18);break;case 17:u.TopAxis(),this.$=c[d].substr(8);break;case 18:u.setAxisFormat(c[d].substr(11)),this.$=c[d].substr(11);break;case 19:u.setTickInterval(c[d].substr(13)),this.$=c[d].substr(13);break;case 20:u.setExcludes(c[d].substr(9)),this.$=c[d].substr(9);break;case 21:u.setIncludes(c[d].substr(9)),this.$=c[d].substr(9);break;case 22:u.setTodayMarker(c[d].substr(12)),this.$=c[d].substr(12);break;case 24:u.setDiagramTitle(c[d].substr(6)),this.$=c[d].substr(6);break;case 25:this.$=c[d].trim(),u.setAccTitle(this.$);break;case 26:case 27:this.$=c[d].trim(),u.setAccDescription(this.$);break;case 28:u.addSection(c[d].substr(8)),this.$=c[d].substr(8);break;case 30:u.addTask(c[d-1],c[d]),this.$="task";break;case 31:this.$=c[d-1],u.setClickEvent(c[d-1],c[d],null);break;case 32:this.$=c[d-2],u.setClickEvent(c[d-2],c[d-1],c[d]);break;case 33:this.$=c[d-2],u.setClickEvent(c[d-2],c[d-1],null),u.setLink(c[d-2],c[d]);break;case 34:this.$=c[d-3],u.setClickEvent(c[d-3],c[d-2],c[d-1]),u.setLink(c[d-3],c[d]);break;case 35:this.$=c[d-2],u.setClickEvent(c[d-2],c[d],null),u.setLink(c[d-2],c[d-1]);break;case 36:this.$=c[d-3],u.setClickEvent(c[d-3],c[d-1],c[d]),u.setLink(c[d-3],c[d-2]);break;case 37:this.$=c[d-1],u.setLink(c[d-1],c[d]);break;case 38:case 44:this.$=c[d-1]+" "+c[d];break;case 39:case 40:case 42:this.$=c[d-2]+" "+c[d-1]+" "+c[d];break;case 41:case 43:this.$=c[d-3]+" "+c[d-2]+" "+c[d-1]+" "+c[d];break}},table:[{3:1,4:[1,2]},{1:[3]},t(e,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:17,12:n,13:r,14:i,15:s,16:a,17:y,18:S,19:p,20:g,21:F,22:w,23:T,24:q,25:I,26:D,27:L,28:H,30:A,32:Z,33:j,34:23,35:b,37:W},t(e,[2,7],{1:[2,1]}),t(e,[2,3]),{9:33,11:17,12:n,13:r,14:i,15:s,16:a,17:y,18:S,19:p,20:g,21:F,22:w,23:T,24:q,25:I,26:D,27:L,28:H,30:A,32:Z,33:j,34:23,35:b,37:W},t(e,[2,5]),t(e,[2,6]),t(e,[2,15]),t(e,[2,16]),t(e,[2,17]),t(e,[2,18]),t(e,[2,19]),t(e,[2,20]),t(e,[2,21]),t(e,[2,22]),t(e,[2,23]),t(e,[2,24]),{29:[1,34]},{31:[1,35]},t(e,[2,27]),t(e,[2,28]),t(e,[2,29]),{36:[1,36]},t(e,[2,8]),t(e,[2,9]),t(e,[2,10]),t(e,[2,11]),t(e,[2,12]),t(e,[2,13]),t(e,[2,14]),{38:[1,37],40:[1,38]},t(e,[2,4]),t(e,[2,25]),t(e,[2,26]),t(e,[2,30]),t(e,[2,31],{39:[1,39],40:[1,40]}),t(e,[2,37],{38:[1,41]}),t(e,[2,32],{40:[1,42]}),t(e,[2,33]),t(e,[2,35],{39:[1,43]}),t(e,[2,34]),t(e,[2,36])],defaultActions:{},parseError:function(o,f){if(f.recoverable)this.trace(o);else{var m=new Error(o);throw m.hash=f,m}},parse:function(o){var f=this,m=[0],u=[],M=[null],c=[],G=this.table,d="",h=0,C=0,X=2,O=1,z=c.slice.call(arguments,1),N=Object.create(this.lexer),P={yy:{}};for(var at in this.yy)Object.prototype.hasOwnProperty.call(this.yy,at)&&(P.yy[at]=this.yy[at]);N.setInput(o,P.yy),P.yy.lexer=N,P.yy.parser=this,typeof N.yylloc=="undefined"&&(N.yylloc={});var ot=N.yylloc;c.push(ot);var k=N.options&&N.options.ranges;typeof P.yy.parseError=="function"?this.parseError=P.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function E(){var lt;return lt=u.pop()||N.lex()||O,typeof lt!="number"&&(lt instanceof Array&&(u=lt,lt=u.pop()),lt=f.symbols_[lt]||lt),lt}for(var Y,l,B,V,Q={},J,$,ct,yt;;){if(l=m[m.length-1],this.defaultActions[l]?B=this.defaultActions[l]:((Y===null||typeof Y=="undefined")&&(Y=E()),B=G[l]&&G[l][Y]),typeof B=="undefined"||!B.length||!B[0]){var Pt="";yt=[];for(J in G[l])this.terminals_[J]&&J>X&&yt.push("'"+this.terminals_[J]+"'");N.showPosition?Pt="Parse error on line "+(h+1)+`:
`+N.showPosition()+`
Expecting `+yt.join(", ")+", got '"+(this.terminals_[Y]||Y)+"'":Pt="Parse error on line "+(h+1)+": Unexpected "+(Y==O?"end of input":"'"+(this.terminals_[Y]||Y)+"'"),this.parseError(Pt,{text:N.match,token:this.terminals_[Y]||Y,line:N.yylineno,loc:ot,expected:yt})}if(B[0]instanceof Array&&B.length>1)throw new Error("Parse Error: multiple actions possible at state: "+l+", token: "+Y);switch(B[0]){case 1:m.push(Y),M.push(N.yytext),c.push(N.yylloc),m.push(B[1]),Y=null,C=N.yyleng,d=N.yytext,h=N.yylineno,ot=N.yylloc;break;case 2:if($=this.productions_[B[1]][1],Q.$=M[M.length-$],Q._$={first_line:c[c.length-($||1)].first_line,last_line:c[c.length-1].last_line,first_column:c[c.length-($||1)].first_column,last_column:c[c.length-1].last_column},k&&(Q._$.range=[c[c.length-($||1)].range[0],c[c.length-1].range[1]]),V=this.performAction.apply(Q,[d,C,h,P.yy,B[1],M,c].concat(z)),typeof V!="undefined")return V;$&&(m=m.slice(0,-1*$*2),M=M.slice(0,-1*$),c=c.slice(0,-1*$)),m.push(this.productions_[B[1]][0]),M.push(Q.$),c.push(Q._$),ct=G[m[m.length-2]][m[m.length-1]],m.push(ct);break;case 3:return!0}}return!0}},U=function(){var x={EOF:1,parseError:function(f,m){if(this.yy.parser)this.yy.parser.parseError(f,m);else throw new Error(f)},setInput:function(o,f){return this.yy=f||this.yy||{},this._input=o,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var o=this._input[0];this.yytext+=o,this.yyleng++,this.offset++,this.match+=o,this.matched+=o;var f=o.match(/(?:\r\n?|\n).*/g);return f?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),o},unput:function(o){var f=o.length,m=o.split(/(?:\r\n?|\n)/g);this._input=o+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-f),this.offset-=f;var u=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),m.length-1&&(this.yylineno-=m.length-1);var M=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:m?(m.length===u.length?this.yylloc.first_column:0)+u[u.length-m.length].length-m[0].length:this.yylloc.first_column-f},this.options.ranges&&(this.yylloc.range=[M[0],M[0]+this.yyleng-f]),this.yyleng=this.yytext.length,this},more:function(){return this._more=!0,this},reject:function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},less:function(o){this.unput(this.match.slice(o))},pastInput:function(){var o=this.matched.substr(0,this.matched.length-this.match.length);return(o.length>20?"...":"")+o.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var o=this.match;return o.length<20&&(o+=this._input.substr(0,20-o.length)),(o.substr(0,20)+(o.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var o=this.pastInput(),f=new Array(o.length+1).join("-");return o+this.upcomingInput()+`
`+f+"^"},test_match:function(o,f){var m,u,M;if(this.options.backtrack_lexer&&(M={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(M.yylloc.range=this.yylloc.range.slice(0))),u=o[0].match(/(?:\r\n?|\n).*/g),u&&(this.yylineno+=u.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:u?u[u.length-1].length-u[u.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+o[0].length},this.yytext+=o[0],this.match+=o[0],this.matches=o,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(o[0].length),this.matched+=o[0],m=this.performAction.call(this,this.yy,this,f,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),m)return m;if(this._backtrack){for(var c in M)this[c]=M[c];return!1}return!1},next:function(){if(this.done)return this.EOF;this._input||(this.done=!0);var o,f,m,u;this._more||(this.yytext="",this.match="");for(var M=this._currentRules(),c=0;c<M.length;c++)if(m=this._input.match(this.rules[M[c]]),m&&(!f||m[0].length>f[0].length)){if(f=m,u=c,this.options.backtrack_lexer){if(o=this.test_match(m,M[c]),o!==!1)return o;if(this._backtrack){f=!1;continue}else return!1}else if(!this.options.flex)break}return f?(o=this.test_match(f,M[u]),o!==!1?o:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){var f=this.next();return f||this.lex()},begin:function(f){this.conditionStack.push(f)},popState:function(){var f=this.conditionStack.length-1;return f>0?this.conditionStack.pop():this.conditionStack[0]},_currentRules:function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},topState:function(f){return f=this.conditionStack.length-1-Math.abs(f||0),f>=0?this.conditionStack[f]:"INITIAL"},pushState:function(f){this.begin(f)},stateStackSize:function(){return this.conditionStack.length},options:{"case-insensitive":!0},performAction:function(f,m,u,M){switch(u){case 0:return this.begin("open_directive"),"open_directive";case 1:return this.begin("acc_title"),28;case 2:return this.popState(),"acc_title_value";case 3:return this.begin("acc_descr"),30;case 4:return this.popState(),"acc_descr_value";case 5:this.begin("acc_descr_multiline");break;case 6:this.popState();break;case 7:return"acc_descr_multiline_value";case 8:break;case 9:break;case 10:break;case 11:return 10;case 12:break;case 13:break;case 14:this.begin("href");break;case 15:this.popState();break;case 16:return 40;case 17:this.begin("callbackname");break;case 18:this.popState();break;case 19:this.popState(),this.begin("callbackargs");break;case 20:return 38;case 21:this.popState();break;case 22:return 39;case 23:this.begin("click");break;case 24:this.popState();break;case 25:return 37;case 26:return 4;case 27:return 19;case 28:return 20;case 29:return 21;case 30:return 22;case 31:return 23;case 32:return 25;case 33:return 24;case 34:return 26;case 35:return 12;case 36:return 13;case 37:return 14;case 38:return 15;case 39:return 16;case 40:return 17;case 41:return 18;case 42:return"date";case 43:return 27;case 44:return"accDescription";case 45:return 33;case 46:return 35;case 47:return 36;case 48:return":";case 49:return 6;case 50:return"INVALID"}},rules:[/^(?:%%\{)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:%%(?!\{)*[^\n]*)/i,/^(?:[^\}]%%*[^\n]*)/i,/^(?:%%*[^\n]*[\n]*)/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:%[^\n]*)/i,/^(?:href[\s]+["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:call[\s]+)/i,/^(?:\([\s]*\))/i,/^(?:\()/i,/^(?:[^(]*)/i,/^(?:\))/i,/^(?:[^)]*)/i,/^(?:click[\s]+)/i,/^(?:[\s\n])/i,/^(?:[^\s\n]*)/i,/^(?:gantt\b)/i,/^(?:dateFormat\s[^#\n;]+)/i,/^(?:inclusiveEndDates\b)/i,/^(?:topAxis\b)/i,/^(?:axisFormat\s[^#\n;]+)/i,/^(?:tickInterval\s[^#\n;]+)/i,/^(?:includes\s[^#\n;]+)/i,/^(?:excludes\s[^#\n;]+)/i,/^(?:todayMarker\s[^\n;]+)/i,/^(?:weekday\s+monday\b)/i,/^(?:weekday\s+tuesday\b)/i,/^(?:weekday\s+wednesday\b)/i,/^(?:weekday\s+thursday\b)/i,/^(?:weekday\s+friday\b)/i,/^(?:weekday\s+saturday\b)/i,/^(?:weekday\s+sunday\b)/i,/^(?:\d\d\d\d-\d\d-\d\d\b)/i,/^(?:title\s[^\n]+)/i,/^(?:accDescription\s[^#\n;]+)/i,/^(?:section\s[^\n]+)/i,/^(?:[^:\n]+)/i,/^(?::[^#\n;]+)/i,/^(?::)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[6,7],inclusive:!1},acc_descr:{rules:[4],inclusive:!1},acc_title:{rules:[2],inclusive:!1},callbackargs:{rules:[21,22],inclusive:!1},callbackname:{rules:[18,19,20],inclusive:!1},href:{rules:[15,16],inclusive:!1},click:{rules:[24,25],inclusive:!1},INITIAL:{rules:[0,1,3,5,8,9,10,11,12,13,14,17,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50],inclusive:!0}}};return x}();v.lexer=U;function _(){this.yy={}}return _.prototype=v,v.Parser=_,new _}();he.parser=he;const Ci=he;nt.extend(wi);nt.extend(qn);nt.extend(Gn);let it="",ve="",be,xe="",Nt=[],Vt=[],we={},Ce=[],$t=[],Ut="",De="";const vn=["active","done","crit","milestone"];let Me=[],zt=!1,Se=!1,_e="sunday",de=0;const Di=function(){Ce=[],$t=[],Ut="",Me=[],Xt=0,ge=void 0,qt=void 0,K=[],it="",ve="",De="",be=void 0,xe="",Nt=[],Vt=[],zt=!1,Se=!1,de=0,we={},On(),_e="sunday"},Mi=function(t){ve=t},Si=function(){return ve},_i=function(t){be=t},Yi=function(){return be},Ui=function(t){xe=t},Fi=function(){return xe},Li=function(t){it=t},Ai=function(){zt=!0},Ei=function(){return zt},Ii=function(){Se=!0},Wi=function(){return Se},Oi=function(t){De=t},Hi=function(){return De},Ni=function(){return it},Vi=function(t){Nt=t.toLowerCase().split(/[\s,]+/)},zi=function(){return Nt},Pi=function(t){Vt=t.toLowerCase().split(/[\s,]+/)},Ri=function(){return Vt},Bi=function(){return we},Zi=function(t){Ut=t,Ce.push(t)},Xi=function(){return Ce},qi=function(){let t=Pe();const e=10;let n=0;for(;!t&&n<e;)t=Pe(),n++;return $t=K,$t},bn=function(t,e,n,r){return r.includes(t.format(e.trim()))?!1:t.isoWeekday()>=6&&n.includes("weekends")||n.includes(t.format("dddd").toLowerCase())?!0:n.includes(t.format(e.trim()))},Gi=function(t){_e=t},Qi=function(){return _e},xn=function(t,e,n,r){if(!n.length||t.manualEndTime)return;let i;t.startTime instanceof Date?i=nt(t.startTime):i=nt(t.startTime,e,!0),i=i.add(1,"d");let s;t.endTime instanceof Date?s=nt(t.endTime):s=nt(t.endTime,e,!0);const[a,y]=ji(i,s,e,n,r);t.endTime=a.toDate(),t.renderEndTime=y},ji=function(t,e,n,r,i){let s=!1,a=null;for(;t<=e;)s||(a=e.toDate()),s=bn(t,n,r,i),s&&(e=e.add(1,"d")),t=t.add(1,"d");return[e,a]},me=function(t,e,n){n=n.trim();const i=/^after\s+(?<ids>[\d\w- ]+)/.exec(n);if(i!==null){let a=null;for(const S of i.groups.ids.split(" ")){let p=wt(S);p!==void 0&&(!a||p.endTime>a.endTime)&&(a=p)}if(a)return a.endTime;const y=new Date;return y.setHours(0,0,0,0),y}let s=nt(n,e.trim(),!0);if(s.isValid())return s.toDate();{Gt.debug("Invalid date:"+n),Gt.debug("With date format:"+e.trim());const a=new Date(n);if(a===void 0||isNaN(a.getTime())||a.getFullYear()<-1e4||a.getFullYear()>1e4)throw new Error("Invalid date:"+n);return a}},wn=function(t){const e=/^(\d+(?:\.\d+)?)([Mdhmswy]|ms)$/.exec(t.trim());return e!==null?[Number.parseFloat(e[1]),e[2]]:[NaN,"ms"]},Cn=function(t,e,n,r=!1){n=n.trim();const s=/^until\s+(?<ids>[\d\w- ]+)/.exec(n);if(s!==null){let g=null;for(const w of s.groups.ids.split(" ")){let T=wt(w);T!==void 0&&(!g||T.startTime<g.startTime)&&(g=T)}if(g)return g.startTime;const F=new Date;return F.setHours(0,0,0,0),F}let a=nt(n,e.trim(),!0);if(a.isValid())return r&&(a=a.add(1,"d")),a.toDate();let y=nt(t);const[S,p]=wn(n);if(!Number.isNaN(S)){const g=y.add(S,p);g.isValid()&&(y=g)}return y.toDate()};let Xt=0;const St=function(t){return t===void 0?(Xt=Xt+1,"task"+Xt):t},Ji=function(t,e){let n;e.substr(0,1)===":"?n=e.substr(1,e.length):n=e;const r=n.split(","),i={};_n(r,i,vn);for(let a=0;a<r.length;a++)r[a]=r[a].trim();let s="";switch(r.length){case 1:i.id=St(),i.startTime=t.endTime,s=r[0];break;case 2:i.id=St(),i.startTime=me(void 0,it,r[0]),s=r[1];break;case 3:i.id=St(r[0]),i.startTime=me(void 0,it,r[1]),s=r[2];break}return s&&(i.endTime=Cn(i.startTime,it,s,zt),i.manualEndTime=nt(s,"YYYY-MM-DD",!0).isValid(),xn(i,it,Vt,Nt)),i},$i=function(t,e){let n;e.substr(0,1)===":"?n=e.substr(1,e.length):n=e;const r=n.split(","),i={};_n(r,i,vn);for(let s=0;s<r.length;s++)r[s]=r[s].trim();switch(r.length){case 1:i.id=St(),i.startTime={type:"prevTaskEnd",id:t},i.endTime={data:r[0]};break;case 2:i.id=St(),i.startTime={type:"getStartDate",startData:r[0]},i.endTime={data:r[1]};break;case 3:i.id=St(r[0]),i.startTime={type:"getStartDate",startData:r[1]},i.endTime={data:r[2]};break}return i};let ge,qt,K=[];const Dn={},Ki=function(t,e){const n={section:Ut,type:Ut,processed:!1,manualEndTime:!1,renderEndTime:null,raw:{data:e},task:t,classes:[]},r=$i(qt,e);n.raw.startTime=r.startTime,n.raw.endTime=r.endTime,n.id=r.id,n.prevTaskId=qt,n.active=r.active,n.done=r.done,n.crit=r.crit,n.milestone=r.milestone,n.order=de,de++;const i=K.push(n);qt=n.id,Dn[n.id]=i-1},wt=function(t){const e=Dn[t];return K[e]},ts=function(t,e){const n={section:Ut,type:Ut,description:t,task:t,classes:[]},r=Ji(ge,e);n.startTime=r.startTime,n.endTime=r.endTime,n.id=r.id,n.active=r.active,n.done=r.done,n.crit=r.crit,n.milestone=r.milestone,ge=n,$t.push(n)},Pe=function(){const t=function(n){const r=K[n];let i="";switch(K[n].raw.startTime.type){case"prevTaskEnd":{const s=wt(r.prevTaskId);r.startTime=s.endTime;break}case"getStartDate":i=me(void 0,it,K[n].raw.startTime.startData),i&&(K[n].startTime=i);break}return K[n].startTime&&(K[n].endTime=Cn(K[n].startTime,it,K[n].raw.endTime.data,zt),K[n].endTime&&(K[n].processed=!0,K[n].manualEndTime=nt(K[n].raw.endTime.data,"YYYY-MM-DD",!0).isValid(),xn(K[n],it,Vt,Nt))),K[n].processed};let e=!0;for(const[n,r]of K.entries())t(n),e=e&&r.processed;return e},es=function(t,e){let n=e;Dt().securityLevel!=="loose"&&(n=Hn.sanitizeUrl(e)),t.split(",").forEach(function(r){wt(r)!==void 0&&(Sn(r,()=>{window.open(n,"_self")}),we[r]=n)}),Mn(t,"clickable")},Mn=function(t,e){t.split(",").forEach(function(n){let r=wt(n);r!==void 0&&r.classes.push(e)})},ns=function(t,e,n){if(Dt().securityLevel!=="loose"||e===void 0)return;let r=[];if(typeof n=="string"){r=n.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);for(let s=0;s<r.length;s++){let a=r[s].trim();a.charAt(0)==='"'&&a.charAt(a.length-1)==='"'&&(a=a.substr(1,a.length-2)),r[s]=a}}r.length===0&&r.push(t),wt(t)!==void 0&&Sn(t,()=>{zn.runFunc(e,...r)})},Sn=function(t,e){Me.push(function(){const n=document.querySelector(`[id="${t}"]`);n!==null&&n.addEventListener("click",function(){e()})},function(){const n=document.querySelector(`[id="${t}-text"]`);n!==null&&n.addEventListener("click",function(){e()})})},rs=function(t,e,n){t.split(",").forEach(function(r){ns(r,e,n)}),Mn(t,"clickable")},is=function(t){Me.forEach(function(e){e(t)})},ss={getConfig:()=>Dt().gantt,clear:Di,setDateFormat:Li,getDateFormat:Ni,enableInclusiveEndDates:Ai,endDatesAreInclusive:Ei,enableTopAxis:Ii,topAxisEnabled:Wi,setAxisFormat:Mi,getAxisFormat:Si,setTickInterval:_i,getTickInterval:Yi,setTodayMarker:Ui,getTodayMarker:Fi,setAccTitle:Fn,getAccTitle:Ln,setDiagramTitle:An,getDiagramTitle:En,setDisplayMode:Oi,getDisplayMode:Hi,setAccDescription:In,getAccDescription:Wn,addSection:Zi,getSections:Xi,getTasks:qi,addTask:Ki,findTaskById:wt,addTaskOrg:ts,setIncludes:Vi,getIncludes:zi,setExcludes:Pi,getExcludes:Ri,setClickEvent:rs,setLink:es,getLinks:Bi,bindFunctions:is,parseDuration:wn,isInvalidDate:bn,setWeekday:Gi,getWeekday:Qi};function _n(t,e,n){let r=!0;for(;r;)r=!1,n.forEach(function(i){const s="^\\s*"+i+"\\s*$",a=new RegExp(s);t[0].match(a)&&(e[i]=!0,t.shift(1),r=!0)})}const as=function(){Gt.debug("Something is calling, setConf, remove the call")},Re={monday:Wt,tuesday:cn,wednesday:ln,thursday:vt,friday:un,saturday:fn,sunday:Ht},os=(t,e)=>{let n=[...t].map(()=>-1/0),r=[...t].sort((s,a)=>s.startTime-a.startTime||s.order-a.order),i=0;for(const s of r)for(let a=0;a<n.length;a++)if(s.startTime>=n[a]){n[a]=s.endTime,s.order=a+e,a>i&&(i=a);break}return i};let ut;const cs=function(t,e,n,r){const i=Dt().gantt,s=Dt().securityLevel;let a;s==="sandbox"&&(a=Rt("#i"+e));const y=s==="sandbox"?Rt(a.nodes()[0].contentDocument.body):Rt("body"),S=s==="sandbox"?a.nodes()[0].contentDocument:document,p=S.getElementById(e);ut=p.parentElement.offsetWidth,ut===void 0&&(ut=1200),i.useWidth!==void 0&&(ut=i.useWidth);const g=r.db.getTasks();let F=[];for(const v of g)F.push(v.type);F=W(F);const w={};let T=2*i.topPadding;if(r.db.getDisplayMode()==="compact"||i.displayMode==="compact"){const v={};for(const _ of g)v[_.section]===void 0?v[_.section]=[_]:v[_.section].push(_);let U=0;for(const _ of Object.keys(v)){const x=os(v[_],U)+1;U+=x,T+=x*(i.barHeight+i.barGap),w[_]=x}}else{T+=g.length*(i.barHeight+i.barGap);for(const v of F)w[v]=g.filter(U=>U.type===v).length}p.setAttribute("viewBox","0 0 "+ut+" "+T);const q=y.select(`[id="${e}"]`),I=xi().domain([jn(g,function(v){return v.startTime}),Qn(g,function(v){return v.endTime})]).rangeRound([0,ut-i.leftPadding-i.rightPadding]);function D(v,U){const _=v.startTime,x=U.startTime;let o=0;return _>x?o=1:_<x&&(o=-1),o}g.sort(D),L(g,ut,T),Nn(q,T,ut,i.useMaxWidth),q.append("text").text(r.db.getDiagramTitle()).attr("x",ut/2).attr("y",i.titleTopMargin).attr("class","titleText");function L(v,U,_){const x=i.barHeight,o=x+i.barGap,f=i.topPadding,m=i.leftPadding,u=Zn().domain([0,F.length]).range(["#00B9FA","#F95002"]).interpolate(fr);A(o,f,m,U,_,v,r.db.getExcludes(),r.db.getIncludes()),Z(m,f,U,_),H(v,o,f,m,x,u,U),j(o,f),b(m,f,U,_)}function H(v,U,_,x,o,f,m){const M=[...new Set(v.map(h=>h.order))].map(h=>v.find(C=>C.order===h));q.append("g").selectAll("rect").data(M).enter().append("rect").attr("x",0).attr("y",function(h,C){return C=h.order,C*U+_-2}).attr("width",function(){return m-i.rightPadding/2}).attr("height",U).attr("class",function(h){for(const[C,X]of F.entries())if(h.type===X)return"section section"+C%i.numberSectionStyles;return"section section0"});const c=q.append("g").selectAll("rect").data(v).enter(),G=r.db.getLinks();if(c.append("rect").attr("id",function(h){return h.id}).attr("rx",3).attr("ry",3).attr("x",function(h){return h.milestone?I(h.startTime)+x+.5*(I(h.endTime)-I(h.startTime))-.5*o:I(h.startTime)+x}).attr("y",function(h,C){return C=h.order,C*U+_}).attr("width",function(h){return h.milestone?o:I(h.renderEndTime||h.endTime)-I(h.startTime)}).attr("height",o).attr("transform-origin",function(h,C){return C=h.order,(I(h.startTime)+x+.5*(I(h.endTime)-I(h.startTime))).toString()+"px "+(C*U+_+.5*o).toString()+"px"}).attr("class",function(h){const C="task";let X="";h.classes.length>0&&(X=h.classes.join(" "));let O=0;for(const[N,P]of F.entries())h.type===P&&(O=N%i.numberSectionStyles);let z="";return h.active?h.crit?z+=" activeCrit":z=" active":h.done?h.crit?z=" doneCrit":z=" done":h.crit&&(z+=" crit"),z.length===0&&(z=" task"),h.milestone&&(z=" milestone "+z),z+=O,z+=" "+X,C+z}),c.append("text").attr("id",function(h){return h.id+"-text"}).text(function(h){return h.task}).attr("font-size",i.fontSize).attr("x",function(h){let C=I(h.startTime),X=I(h.renderEndTime||h.endTime);h.milestone&&(C+=.5*(I(h.endTime)-I(h.startTime))-.5*o),h.milestone&&(X=C+o);const O=this.getBBox().width;return O>X-C?X+O+1.5*i.leftPadding>m?C+x-5:X+x+5:(X-C)/2+C+x}).attr("y",function(h,C){return C=h.order,C*U+i.barHeight/2+(i.fontSize/2-2)+_}).attr("text-height",o).attr("class",function(h){const C=I(h.startTime);let X=I(h.endTime);h.milestone&&(X=C+o);const O=this.getBBox().width;let z="";h.classes.length>0&&(z=h.classes.join(" "));let N=0;for(const[at,ot]of F.entries())h.type===ot&&(N=at%i.numberSectionStyles);let P="";return h.active&&(h.crit?P="activeCritText"+N:P="activeText"+N),h.done?h.crit?P=P+" doneCritText"+N:P=P+" doneText"+N:h.crit&&(P=P+" critText"+N),h.milestone&&(P+=" milestoneText"),O>X-C?X+O+1.5*i.leftPadding>m?z+" taskTextOutsideLeft taskTextOutside"+N+" "+P:z+" taskTextOutsideRight taskTextOutside"+N+" "+P+" width-"+O:z+" taskText taskText"+N+" "+P+" width-"+O}),Dt().securityLevel==="sandbox"){let h;h=Rt("#i"+e);const C=h.nodes()[0].contentDocument;c.filter(function(X){return G[X.id]!==void 0}).each(function(X){var O=C.querySelector("#"+X.id),z=C.querySelector("#"+X.id+"-text");const N=O.parentNode;var P=C.createElement("a");P.setAttribute("xlink:href",G[X.id]),P.setAttribute("target","_top"),N.appendChild(P),P.appendChild(O),P.appendChild(z)})}}function A(v,U,_,x,o,f,m,u){if(m.length===0&&u.length===0)return;let M,c;for(const{startTime:O,endTime:z}of f)(M===void 0||O<M)&&(M=O),(c===void 0||z>c)&&(c=z);if(!M||!c)return;if(nt(c).diff(nt(M),"year")>5){Gt.warn("The difference between the min and max time is more than 5 years. This will cause performance issues. Skipping drawing exclude days.");return}const G=r.db.getDateFormat(),d=[];let h=null,C=nt(M);for(;C.valueOf()<=c;)r.db.isInvalidDate(C,G,m,u)?h?h.end=C:h={start:C,end:C}:h&&(d.push(h),h=null),C=C.add(1,"d");q.append("g").selectAll("rect").data(d).enter().append("rect").attr("id",function(O){return"exclude-"+O.start.format("YYYY-MM-DD")}).attr("x",function(O){return I(O.start)+_}).attr("y",i.gridLineStartPadding).attr("width",function(O){const z=O.end.add(1,"day");return I(z)-I(O.start)}).attr("height",o-U-i.gridLineStartPadding).attr("transform-origin",function(O,z){return(I(O.start)+_+.5*(I(O.end)-I(O.start))).toString()+"px "+(z*v+.5*o).toString()+"px"}).attr("class","exclude-range")}function Z(v,U,_,x){let o=ir(I).tickSize(-x+U+i.gridLineStartPadding).tickFormat(Jt(r.db.getAxisFormat()||i.axisFormat||"%Y-%m-%d"));const m=/^([1-9]\d*)(millisecond|second|minute|hour|day|week|month)$/.exec(r.db.getTickInterval()||i.tickInterval);if(m!==null){const u=m[1],M=m[2],c=r.db.getWeekday()||i.weekday;switch(M){case"millisecond":o.ticks(_t.every(u));break;case"second":o.ticks(kt.every(u));break;case"minute":o.ticks(Et.every(u));break;case"hour":o.ticks(It.every(u));break;case"day":o.ticks(Tt.every(u));break;case"week":o.ticks(Re[c].every(u));break;case"month":o.ticks(Ot.every(u));break}}if(q.append("g").attr("class","grid").attr("transform","translate("+v+", "+(x-50)+")").call(o).selectAll("text").style("text-anchor","middle").attr("fill","#000").attr("stroke","none").attr("font-size",10).attr("dy","1em"),r.db.topAxisEnabled()||i.topAxis){let u=rr(I).tickSize(-x+U+i.gridLineStartPadding).tickFormat(Jt(r.db.getAxisFormat()||i.axisFormat||"%Y-%m-%d"));if(m!==null){const M=m[1],c=m[2],G=r.db.getWeekday()||i.weekday;switch(c){case"millisecond":u.ticks(_t.every(M));break;case"second":u.ticks(kt.every(M));break;case"minute":u.ticks(Et.every(M));break;case"hour":u.ticks(It.every(M));break;case"day":u.ticks(Tt.every(M));break;case"week":u.ticks(Re[G].every(M));break;case"month":u.ticks(Ot.every(M));break}}q.append("g").attr("class","grid").attr("transform","translate("+v+", "+U+")").call(u).selectAll("text").style("text-anchor","middle").attr("fill","#000").attr("stroke","none").attr("font-size",10)}}function j(v,U){let _=0;const x=Object.keys(w).map(o=>[o,w[o]]);q.append("g").selectAll("text").data(x).enter().append(function(o){const f=o[0].split(Vn.lineBreakRegex),m=-(f.length-1)/2,u=S.createElementNS("http://www.w3.org/2000/svg","text");u.setAttribute("dy",m+"em");for(const[M,c]of f.entries()){const G=S.createElementNS("http://www.w3.org/2000/svg","tspan");G.setAttribute("alignment-baseline","central"),G.setAttribute("x","10"),M>0&&G.setAttribute("dy","1em"),G.textContent=c,u.appendChild(G)}return u}).attr("x",10).attr("y",function(o,f){if(f>0)for(let m=0;m<f;m++)return _+=x[f-1][1],o[1]*v/2+_*v+U;else return o[1]*v/2+U}).attr("font-size",i.sectionFontSize).attr("class",function(o){for(const[f,m]of F.entries())if(o[0]===m)return"sectionTitle sectionTitle"+f%i.numberSectionStyles;return"sectionTitle"})}function b(v,U,_,x){const o=r.db.getTodayMarker();if(o==="off")return;const f=q.append("g").attr("class","today"),m=new Date,u=f.append("line");u.attr("x1",I(m)+v).attr("x2",I(m)+v).attr("y1",i.titleTopMargin).attr("y2",x-i.titleTopMargin).attr("class","today"),o!==""&&u.attr("style",o.replace(/,/g,";"))}function W(v){const U={},_=[];for(let x=0,o=v.length;x<o;++x)Object.prototype.hasOwnProperty.call(U,v[x])||(U[v[x]]=!0,_.push(v[x]));return _}},ls={setConf:as,draw:cs},us=t=>`
  .mermaid-main-font {
    font-family: var(--mermaid-font-family, "trebuchet ms", verdana, arial, sans-serif);
  }

  .exclude-range {
    fill: ${t.excludeBkgColor};
  }

  .section {
    stroke: none;
    opacity: 0.2;
  }

  .section0 {
    fill: ${t.sectionBkgColor};
  }

  .section2 {
    fill: ${t.sectionBkgColor2};
  }

  .section1,
  .section3 {
    fill: ${t.altSectionBkgColor};
    opacity: 0.2;
  }

  .sectionTitle0 {
    fill: ${t.titleColor};
  }

  .sectionTitle1 {
    fill: ${t.titleColor};
  }

  .sectionTitle2 {
    fill: ${t.titleColor};
  }

  .sectionTitle3 {
    fill: ${t.titleColor};
  }

  .sectionTitle {
    text-anchor: start;
    font-family: var(--mermaid-font-family, "trebuchet ms", verdana, arial, sans-serif);
  }


  /* Grid and axis */

  .grid .tick {
    stroke: ${t.gridColor};
    opacity: 0.8;
    shape-rendering: crispEdges;
  }

  .grid .tick text {
    font-family: ${t.fontFamily};
    fill: ${t.textColor};
  }

  .grid path {
    stroke-width: 0;
  }


  /* Today line */

  .today {
    fill: none;
    stroke: ${t.todayLineColor};
    stroke-width: 2px;
  }


  /* Task styling */

  /* Default task */

  .task {
    stroke-width: 2;
  }

  .taskText {
    text-anchor: middle;
    font-family: var(--mermaid-font-family, "trebuchet ms", verdana, arial, sans-serif);
  }

  .taskTextOutsideRight {
    fill: ${t.taskTextDarkColor};
    text-anchor: start;
    font-family: var(--mermaid-font-family, "trebuchet ms", verdana, arial, sans-serif);
  }

  .taskTextOutsideLeft {
    fill: ${t.taskTextDarkColor};
    text-anchor: end;
  }


  /* Special case clickable */

  .task.clickable {
    cursor: pointer;
  }

  .taskText.clickable {
    cursor: pointer;
    fill: ${t.taskTextClickableColor} !important;
    font-weight: bold;
  }

  .taskTextOutsideLeft.clickable {
    cursor: pointer;
    fill: ${t.taskTextClickableColor} !important;
    font-weight: bold;
  }

  .taskTextOutsideRight.clickable {
    cursor: pointer;
    fill: ${t.taskTextClickableColor} !important;
    font-weight: bold;
  }


  /* Specific task settings for the sections*/

  .taskText0,
  .taskText1,
  .taskText2,
  .taskText3 {
    fill: ${t.taskTextColor};
  }

  .task0,
  .task1,
  .task2,
  .task3 {
    fill: ${t.taskBkgColor};
    stroke: ${t.taskBorderColor};
  }

  .taskTextOutside0,
  .taskTextOutside2
  {
    fill: ${t.taskTextOutsideColor};
  }

  .taskTextOutside1,
  .taskTextOutside3 {
    fill: ${t.taskTextOutsideColor};
  }


  /* Active task */

  .active0,
  .active1,
  .active2,
  .active3 {
    fill: ${t.activeTaskBkgColor};
    stroke: ${t.activeTaskBorderColor};
  }

  .activeText0,
  .activeText1,
  .activeText2,
  .activeText3 {
    fill: ${t.taskTextDarkColor} !important;
  }


  /* Completed task */

  .done0,
  .done1,
  .done2,
  .done3 {
    stroke: ${t.doneTaskBorderColor};
    fill: ${t.doneTaskBkgColor};
    stroke-width: 2;
  }

  .doneText0,
  .doneText1,
  .doneText2,
  .doneText3 {
    fill: ${t.taskTextDarkColor} !important;
  }


  /* Tasks on the critical line */

  .crit0,
  .crit1,
  .crit2,
  .crit3 {
    stroke: ${t.critBorderColor};
    fill: ${t.critBkgColor};
    stroke-width: 2;
  }

  .activeCrit0,
  .activeCrit1,
  .activeCrit2,
  .activeCrit3 {
    stroke: ${t.critBorderColor};
    fill: ${t.activeTaskBkgColor};
    stroke-width: 2;
  }

  .doneCrit0,
  .doneCrit1,
  .doneCrit2,
  .doneCrit3 {
    stroke: ${t.critBorderColor};
    fill: ${t.doneTaskBkgColor};
    stroke-width: 2;
    cursor: pointer;
    shape-rendering: crispEdges;
  }

  .milestone {
    transform: rotate(45deg) scale(0.8,0.8);
  }

  .milestoneText {
    font-style: italic;
  }
  .doneCritText0,
  .doneCritText1,
  .doneCritText2,
  .doneCritText3 {
    fill: ${t.taskTextDarkColor} !important;
  }

  .activeCritText0,
  .activeCritText1,
  .activeCritText2,
  .activeCritText3 {
    fill: ${t.taskTextDarkColor} !important;
  }

  .titleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${t.titleColor||t.textColor};
    font-family: var(--mermaid-font-family, "trebuchet ms", verdana, arial, sans-serif);
  }
`,fs=us,gs={parser:Ci,db:ss,renderer:ls,styles:fs};export{gs as diagram};
