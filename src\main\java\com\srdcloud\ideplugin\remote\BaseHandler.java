package com.srdcloud.ideplugin.remote;

import com.google.common.collect.Maps;
import com.srdcloud.ideplugin.general.utils.LocalStorageUtil;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2024/12/25
 */
public class BaseHandler {

    /**
     * 生成网关层鉴权头域字段
     */
    protected static HashMap<String, String> generateAuthHeaders() {
        HashMap<String, String> headers = Maps.newHashMapWithExpectedSize(4);
        headers.put("apiKey", LocalStorageUtil.getApikey());
        headers.put("invokerId", LocalStorageUtil.getUserId());
        headers.put("userId", LocalStorageUtil.getUserId());
        return headers;
    }
}
