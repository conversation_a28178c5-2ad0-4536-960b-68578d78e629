package com.srdcloud.ideplugin.codechat.domain;

import com.srdcloud.ideplugin.general.utils.TimeUtil;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.DialogBaseInfo;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.DialogCondition;
import com.srdcloud.ideplugin.webview.codechat.common.ChatTips;
import com.srdcloud.ideplugin.webview.codechat.relatedfile.RelatedFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/26
 * 本地会话管理载体
 */
public class Conversation {
    private static final Logger logger = LoggerFactory.getLogger(Conversation.class);

    private String id;

    /**
     * 对话标题
     */
    private String title;

    /**
     * 产生本对话的时间，格式为yyyy-MM-dd HH:mm:ss
     */
    private String createTime;

    /**
     * 会话最近更新时间，格式为yyyy-MM-dd HH:mm:ss
     */
    private String updateTime;

    /**
     * 原子能力标识
     */
    private String subService;

    /**
     * 是否本地新会话
     */
    private boolean isNewConversation = false;

    /**
     * 本对话限定条件
     */
    private DialogCondition modelRouteCondition;

    /**
     * 本对话的system prompt对象，其中role=systen
     */
    private ChatMessageSimple systemPrompt = new ChatMessageSimple();

    /**
     * 问答轮：一棵树
     * 注：默认值必须要有，否则新会话在Webview解析会有问题
     */
    private ChatMessageSimple questions = new ChatMessageSimple();


    private List<ChatMessage> messages = new ArrayList<>();


    public Conversation() {
    }

    public Conversation(DialogBaseInfo remoteConversation) {
        this.id = remoteConversation.getDialogId() != null ? remoteConversation.getDialogId() : generateUUID();
        this.title = remoteConversation.getTitle() != null ? remoteConversation.getTitle() : ChatTips.CONVERSATION_TITLE;
        this.subService = remoteConversation.getSubService() != null ? remoteConversation.getSubService() : "assistant";
        this.createTime = remoteConversation.getCreateTime() != null ? remoteConversation.getCreateTime() : formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
        this.updateTime = remoteConversation.getUpdateTime() != null ? remoteConversation.getUpdateTime() : formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
        this.isNewConversation = remoteConversation.isNewConversation();
    }

    public Conversation(DialogBaseInfo remoteConversation, List<ChatMessage> messages, ChatMessageSimple questions) {
        this(remoteConversation);

        if (messages != null) {
            this.messages = messages;
        }
        if (questions != null) {
            this.questions = questions;
        }
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getSubService() {
        return subService;
    }

    public void setSubService(String subService) {
        this.subService = subService;
    }

    public boolean isNewConversation() {
        return isNewConversation;
    }

    public void setNewConversation(boolean newConversation) {
        isNewConversation = newConversation;
    }

    public ChatMessageSimple getSystemPrompt() {
        return systemPrompt;
    }

    public void setSystemPrompt(ChatMessageSimple systemPrompt) {
        this.systemPrompt = systemPrompt;
    }

    public List<ChatMessage> getMessages() {
        return messages;
    }

    public void setMessages(List<ChatMessage> messages) {
        this.messages = messages;
    }

    public ChatMessageSimple getQuestions() {
        return questions;
    }

    public void setQuestions(ChatMessageSimple questions) {
        this.questions = questions;
    }

    public DialogCondition getModelRouteCondition() {
        return modelRouteCondition;
    }

    public void setModelRouteCondition(DialogCondition modelRouteCondition) {
        this.modelRouteCondition = modelRouteCondition;
    }


    /**
     * 通过ID获取会话中的消息内容
     */
    @Deprecated
    public ChatMessage getMessage(String msgId) {
        for (ChatMessage message : this.messages) {
            if (message.getId().equals(msgId)) {
                return message;
            }
        }
        return null;
    }

    // 辅助方法：生成UUID
    private static String generateUUID() {
        return java.util.UUID.randomUUID().toString();
    }

    // 辅助方法：格式化日期
    private static String formatDate(Date date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }

    /**
     * 获取用于排序的时间
     */
    public Date getSortDateTime() {
        Date date = new Date();
        try {
            date = TimeUtil.parseTimeStrToDate(createTime);
        } catch (Exception e) {
            logger.error("[cf] getSortDateTime error,DialogBaseInfo:{},e:{}", this.toString(), e.getMessage());
        }
        return date;
    }

    /**
     * 对话上下文问答树，提取当前问答分支消息列表
     */
    public static List<ChatMessage> getChatMessages(ChatMessageSimple questions) {
        List<ChatMessage> messages = new ArrayList<>();
        if (Objects.isNull(questions)) {
            return messages;
        }
        traverse(questions, messages);
        return messages;
    }

    private static void traverse(ChatMessageSimple node, List<ChatMessage> messages) {

        if (node.getContent() == null) {
            return;
        }

        // 根据type去提取msg内容
        String content = extractContentByType(node);

        // 提取关联文件内容
        List<RelatedFile> files = new ArrayList<>();

        if (node.getContent() != null) {
            for (MultiTypeContent content1 : node.getContent()) {
                if (content1.getType().equals("local_file")) {
                    files.add(new RelatedFile(content1.getLocalFile().getPath(), content1.getText(), content1.getLocalFile().getStartLine(), content1.getLocalFile().getEndLine()));
                }
            }
        }
        if (files.isEmpty()) {
            files = null;
        }


        // 创建并添加消息
        ChatMessage message = new ChatMessage();
        message.setRole(node.getRole());
        message.setContent(content);
        message.setFiles(files);
        message.setErrMsg(node.getErrMsg() != null && !node.getErrMsg().isEmpty());

        messages.add(message);

        // 递归遍历子节点
        if (node.getChildren() != null) {
            for (ChatMessageSimple child : node.getChildren()) {
                traverse(child, messages);
            }
        }
    }

    private static String extractContentByType(ChatMessageSimple node) {
        // todo：file_chat分支根据type来提取内容

        // 获取内容，优先取 text，如果没有则取 image_url
        String content = (node.getContent() != null && !node.getContent().isEmpty())
                ? (node.getContent().get(0).getText() != null ? node.getContent().get(0).getText()
                : node.getContent().get(0).getImageUrl())
                : null;

        return content;
    }
}
