package com.srdcloud.ideplugin.assistant.codechatNative.ui.message;

import com.google.common.collect.Lists;
import com.google.gson.JsonArray;
import com.intellij.openapi.Disposable;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.NullableComponent;
import com.intellij.ui.JBColor;
import com.intellij.ui.components.JBLabel;
import com.intellij.ui.components.JBLoadingPanel;
import com.intellij.ui.components.panels.VerticalLayout;
import com.intellij.util.ui.JBUI;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.CodeChatCompleteEngin;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.ConversationManagerByAeBackend;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.Conversation;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.ConversationMessage;
import com.srdcloud.ideplugin.assistant.codechatNative.ui.CodeChatMainPanel;
import com.srdcloud.ideplugin.assistant.codechatNative.uicomponent.MyScrollPane;
import com.srdcloud.ideplugin.general.enums.ConversationMessageType;
import com.srdcloud.ideplugin.general.enums.PromptRoleType;
import com.srdcloud.ideplugin.general.utils.UIUtil;
import com.srdcloud.ideplugin.service.CodeChatRequestSender;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.CodeChatPrompt;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;
import java.awt.event.AdjustmentEvent;
import java.awt.event.AdjustmentListener;
import java.util.ArrayList;
import java.util.Queue;

/**
 * <AUTHOR> yangy
 * @create 2023/9/6 17:18
 * 会话功能UI组件
 */
public class CodeChatMessageGroupComponent extends JBLoadingPanel implements NullableComponent {
    private static final Logger logger = LoggerFactory.getLogger(CodeChatMessageGroupComponent.class);

    private final CodeChatMainPanel codeChatMainPanelParent;

    private final Project project;

    // 组件中央Panel
    private final JPanel centerContainer;

    // mainPanel-Center：聊天消息窗口，垂直布局、可滚动
    private final JPanel currentMessageComponentListPanel = new JPanel(new VerticalLayout(JBUI.scale(0)));
    // -- 将消息列表包装成可滚动
    private final MyScrollPane currentMessageComponentScrollPane = new MyScrollPane(currentMessageComponentListPanel, ScrollPaneConstants.VERTICAL_SCROLLBAR_AS_NEEDED,
            ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
    // -- 滚动条位置值
    public int currentMessageComponentScrollValue = 0;

    // -- 滚动条拖动事件响应器
    private final MyAdjustmentListener scrollListener = new MyAdjustmentListener();

    @Deprecated
    /**
     * 消息数组
     * 已废弃，改用 JList来管理
     */
    private JsonArray messages = new JsonArray();

    /**
     * 会话标题
     * 已废弃，消息列表不显示会话标题
     */
    @Deprecated
    private JBLabel myTitle = new JBLabel();


    /**
     * 聊天区域面板构造
     */
    public CodeChatMessageGroupComponent(@NotNull Project project, CodeChatMainPanel codeChatMainPanelParent, @Nullable LayoutManager manager, @NotNull Disposable parent, int startDelayMs) {
        super(manager, parent, startDelayMs);
        
        this.project = project;
        this.codeChatMainPanelParent = codeChatMainPanelParent;

        // 一、设置布局、样式
        setBorder(JBUI.Borders.empty());
        setLayout(new BorderLayout(JBUI.scale(7), 0));
        setBackground(com.intellij.util.ui.UIUtil.getListBackground());

        // 二、创建主panel
        centerContainer = new JPanel(new BorderLayout(0, JBUI.scale(0)));
        centerContainer.setOpaque(false);
        centerContainer.setBorder(JBUI.Borders.empty());
        // - 设置到窗体中央
        add(centerContainer, BorderLayout.CENTER);

        // 四、渲染消息聊天窗
        // 主窗体center区域添加聊天窗口面板
        centerContainer.add(currentMessageComponentScrollPane);
        currentMessageComponentListPanel.setOpaque(false);
        UIUtil.setBackground(currentMessageComponentListPanel);
        currentMessageComponentListPanel.setBorder(JBUI.Borders.emptyRight(0));
        currentMessageComponentScrollPane.setBorder(JBUI.Borders.empty());
        currentMessageComponentScrollPane.getVerticalScrollBar().setAutoscrolls(true);
        // 设置滚动条拖动事件
        currentMessageComponentScrollPane.getVerticalScrollBar().addAdjustmentListener(e -> {
            SwingUtilities.invokeLater(() -> {
                int value = e.getValue();// 获取滚动条值
                if (currentMessageComponentScrollValue == 0 && value > 0 || currentMessageComponentScrollValue > 0 && value == 0) {
                    currentMessageComponentScrollValue = value;
                    repaint();//重新渲染聊天窗口
                } else {
                    currentMessageComponentScrollValue = value;
                }
            });

        });
    }

    // =============业务逻辑=============

    /**
     * 新增一条对话消息到窗口面板
     *
     * @param codeChatMessageComponent
     */
    public void addNewMessageComponentToUI(CodeChatMessageComponent codeChatMessageComponent) {
        SwingUtilities.invokeLater(() -> {
            currentMessageComponentListPanel.add(codeChatMessageComponent);
            this.updateLayoutAndAutoScroll();
        });
    }

    /**
     * 删除一条对话消息到窗口面板
     *
     * @param codeChatMessageComponent
     */
    public void deleteMessageComponentToUI(CodeChatMessageComponent codeChatMessageComponent) {
        SwingUtilities.invokeLater(() -> {
            currentMessageComponentListPanel.remove(codeChatMessageComponent);
            this.updateLayoutAndAutoScroll();
        });
    }

    /**
     * 新增多条对话消息到窗口面板
     */
    public void batchAddNewMessageComponentToUI(ArrayList<CodeChatMessageComponent> codeChatMessageComponentList, boolean clearBeforeBatch) {
        SwingUtilities.invokeLater(() -> {
            if (clearBeforeBatch) {
                currentMessageComponentListPanel.removeAll();
            }
            if (CollectionUtils.isNotEmpty(codeChatMessageComponentList)) {
                codeChatMessageComponentList.forEach(currentMessageComponentListPanel::add);
            }else {
                JPanel jPanel = new JPanel();
                jPanel.setOpaque(false);
                currentMessageComponentListPanel.add(jPanel);
            }
            this.updateLayoutAndAutoScroll();
        });
    }

    /**
     * 加载会话并应用到当前：聊天窗、对话引擎
     */
    public void applyConversationToCurrent(Project project, Conversation conversation) {
        try {
            // 设置当前对话到会话引擎
            CodeChatCompleteEngin.setCurrentConversation(conversation);

            // 重设对话引擎会话模型路由条件
            CodeChatCompleteEngin.currModelRouteCondition = conversation.getModelRouteCondition();
            CodeChatCompleteEngin.currConversationConditionTemplateId = null;

            // 重设对话引擎问答轮
            CodeChatCompleteEngin.payloadPromptsChats.clear();
            //-- important：设置system prompt，限定对话背景
            CodeChatPrompt codeChatPromptFirst = new CodeChatPrompt();
            codeChatPromptFirst.setContent(CodeChatRequestSender.systemRoleText);
            codeChatPromptFirst.setRole(PromptRoleType.SYSTEM.getType());
            CodeChatCompleteEngin.payloadPromptsChats.add(codeChatPromptFirst);

            // 重设当前会话id
            ConversationManagerByAeBackend.getInstance(project).setConversationId(conversation.getId());

            // 设置会话标题
            if (conversation.getTitle() != null && conversation.getTitle().length() > 100) {
                myTitle.setText(conversation.getTitle().substring(0, 100));
            } else {
                myTitle.setText(conversation.getTitle());
            }

            // -- 逐条处理会话消息：渲染到对话面板、加入到问答上下文
            Queue<ConversationMessage> conversationMessages = conversation.getConversationMessages();
            ArrayList<CodeChatMessageComponent> newCodeChatMessageComponentList = Lists.newArrayListWithExpectedSize(conversationMessages.size());
            while (!conversationMessages.isEmpty()) {
                //历史提问和回答：已从后端获取到并构建消息model，已加入会话
                ConversationMessage message = (ConversationMessage) conversationMessages.poll();
                boolean me = true;
                if (!message.getMessageType().equals(ConversationMessageType.USER)) {
                    me = false;
                }

                //历史提问和回答：构建消息view，消息内容非空且有效问答返回的答案，才允许点赞点踩
                CodeChatMessageComponent component = new CodeChatMessageComponent(codeChatMainPanelParent, message);
                component.setDialogId(conversation.getId());
                component.setReqId(message.getReqId());
                //历史提问和回答：消息view加入消息列表
                newCodeChatMessageComponentList.add(component);

                // 将会话消息添加到问答轮上下文
                CodeChatPrompt codeChatPrompt = new
                        CodeChatPrompt();
                codeChatPrompt.setRole(message.getMessageType().getName());
                codeChatPrompt.setContent(message.getContent());
                CodeChatCompleteEngin.payloadPromptsChats.add(codeChatPrompt);

                // 动态更新上一条请求id
                ConversationManagerByAeBackend.getInstance(project).setParentReqId(message.getReqId());

            }

            //-- 最后一步：批量加载历史信息到UI，并覆盖刷新页面
            batchAddNewMessageComponentToUI(newCodeChatMessageComponentList, true);
        } catch (Exception e) {
            logger.error("[cf] applyConversationToCurrent error:{}", e.getMessage());
        }
    }

    /**
     * 更新聊天窗口布局内容，自动拉到最底下
     */
    public void updateLayoutAndAutoScroll() {
        // 右侧UI重绘
        currentMessageComponentListPanel.revalidate();
        currentMessageComponentListPanel.repaint();
        currentMessageComponentScrollPane.revalidate();
        currentMessageComponentScrollPane.repaint();
        codeChatMainPanelParent.getMainPanelContentRightCenter().revalidate();
        codeChatMainPanelParent.getMainPanelContentRightCenter().repaint();

        // 手动滑动到最底部
        manualScrollToBottom();
    }

    public void manualScrollToBottom() {
        SwingUtilities.invokeLater(() -> {
            currentMessageComponentScrollPane.getVerticalScrollBar().setValue(currentMessageComponentScrollPane.getVerticalScrollBar().getMaximum());
        });
    }

    /**
     * 获取上一个回答的组件
     */
    public Component getPreviousAnswerComponent() {
        int count = currentMessageComponentListPanel.getComponentCount();
        //问候语不是有效消息内容
        if (count > 1) {
            return currentMessageComponentListPanel.getComponent(count - 1);
        } else {
            return null;
        }
    }

    // =============非业务逻辑=============
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        if (currentMessageComponentScrollValue > 0) {
            g.setColor(JBColor.border());
            int y = currentMessageComponentScrollPane.getY() - 1;
            g.drawLine(0, y, getWidth(), y);
        }
    }


    @Override
    public boolean isVisible() {
        if (super.isVisible()) {
            int count = currentMessageComponentListPanel.getComponentCount();
            for (int i = 0; i < count; i++) {
                if (currentMessageComponentListPanel.getComponent(i).isVisible()) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public boolean isNull() {
        return !isVisible();
    }

    class MyAdjustmentListener implements AdjustmentListener {

        @Override
        public void adjustmentValueChanged(AdjustmentEvent e) {
            JScrollBar source = (JScrollBar) e.getSource();
            if (!source.getValueIsAdjusting()) {
                currentMessageComponentScrollValue = source.getValue();
                currentMessageComponentScrollPane.getVerticalScrollBar().setValue(source.getValue());
            }
        }
    }

    public void addScrollListener() {
        currentMessageComponentScrollPane.getVerticalScrollBar().
                addAdjustmentListener(scrollListener);
    }

    public void removeScrollListener() {
        currentMessageComponentScrollPane.getVerticalScrollBar().
                removeAdjustmentListener(scrollListener);
    }

    public void setMessages(JsonArray messages) {
        this.messages = messages;
    }

    public int getCurrentMessageComponentScrollValue() {
        return currentMessageComponentScrollPane.getVerticalScrollBar().getValue();
    }

    public boolean isNewConversation() {
        return getComponents().length <= 1;
    }
}
