package com.srdcloud.ideplugin.codecomplete.domain

import com.intellij.openapi.editor.Editor
import com.intellij.openapi.util.Key
import com.srdcloud.ideplugin.codecomplete.actions.Actions

/**
 * 代码补全状态类，包含当前的提示列表、当前展示的提示下标等信息
 */
class CompletionState private constructor(
    var suggestions: List<CompletionElement> = emptyList(),
    var suggestionIndex: Int = 0,
    var lastStartOffset: Int = 0,
    var lastModificationStamp: Long = 0
) {
    fun init() {
        suggestionIndex = 0
    }

    /**
     * 判断是否需要获取新的补全提示
     */
    fun needGetSuggestion(): Bo<PERSON>an {
        return this.suggestions.size - suggestionIndex <= 1
    }

    /**
     * 检查用户持续输入的内容，是否与提示的一致
     */
    fun checkUserInput(userInput: CharSequence): Boolean {
        val suggestion = suggestions.getOrNull(suggestionIndex) ?: return false
        if (suggestion.text.startsWith(userInput)) {
            val len = userInput.length
            suggestions = listOf(CompletionElement(suggestion.text.substring(len), suggestion.isAuto))
            suggestionIndex = 0
            lastStartOffset += len
            return true
        }
        return false
    }

    /**
     * 切换展示不同下标的补全建议
     */
    fun flip(action: Actions) {
        when (action) {
            Actions.NEXT -> suggestionIndex =
                if (suggestionIndex < suggestions.size) suggestionIndex + 1 else suggestions.size

            Actions.PREVIOUS -> {
                if (suggestions.size in 1..suggestionIndex) {
                    suggestionIndex = suggestions.size - 1
                }
                suggestionIndex = if (suggestionIndex > 0) suggestionIndex - 1 else 0
            }

            else -> Unit
        }
    }

    companion object {
        private val INLINE_COMPLETION_STATE: Key<CompletionState> = Key.create("com.srdcloud.ideplugin.completion.state")

        fun Editor.getInlineCompletionState(): CompletionState? = getUserData(INLINE_COMPLETION_STATE)
        fun Editor.initOrGetInlineCompletionState(): CompletionState =
            getInlineCompletionState() ?: CompletionState().also { putUserData(INLINE_COMPLETION_STATE, it) }

        fun Editor.resetInlineCompletionState(): Unit = putUserData(INLINE_COMPLETION_STATE, null)
    }
}