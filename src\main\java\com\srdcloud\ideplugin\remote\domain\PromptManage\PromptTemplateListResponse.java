package com.srdcloud.ideplugin.remote.domain.PromptManage;

import com.srdcloud.ideplugin.service.domain.template.PromptTemplate;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2024/5/13
 */
public class PromptTemplateListResponse {

    /**
     * 返回码
     */
    private int optResult;

    /**
     * 异常描述
     */
    private String msg;


    /**
     * 模板列表数据
     */
    private ArrayList<PromptTemplate> data;

    /**
     * 列表接口所含总条数
     */
    private int totalCount;

    public PromptTemplateListResponse() {
    }

    public PromptTemplateListResponse(int optResult, String msg, ArrayList<PromptTemplate> data, int totalCount) {
        this.optResult = optResult;
        this.msg = msg;
        this.data = data;
        this.totalCount = totalCount;
    }

    public int getOptResult() {
        return optResult;
    }

    public void setOptResult(int optResult) {
        this.optResult = optResult;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public ArrayList<PromptTemplate> getData() {
        return data;
    }

    public void setData(ArrayList<PromptTemplate> data) {
        this.data = data;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }
}
