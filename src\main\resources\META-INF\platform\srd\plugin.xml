<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin require-restart="true">
    <!-- Unique identifier of the plugin. It should be FQN. It cannot be changed between the plugin versions. -->
    <id>com.srdcloud.IDEPlugin</id>

    <!-- Public plugin name should be written in Title Case.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-name -->
    <name>SRD CodeFree</name>

    <!-- A displayed Vendor name or Organization ID displayed on the Plugins Page. -->
    <vendor email="<EMAIL>" url="https://www.srdcloud.cn">中国电信研究院</vendor>

    <!-- Description of the plugin displayed on the Plugin Page and IDE Plugin Manager.
         Simple HTML elements (text formatting, paragraphs, and lists) can be added inside of <![CDATA[ ]]> tag.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-description -->
    <description><![CDATA[
        <p><span>CodeFree is an AI tool provided by China Telecom&#39;s SRDCloud.It is a comprehensive solution that leverages AI capabilities to assist in code development. This tool is integrated on the IDE side to implement features such as code completion, code generation, code optimization, and programming assistance. Moreover, it seamlessly integrates with various existing capabilities within the SRDCloud, extending the development workflow into the IDE. The ultimate goal is to enhance the efficiency of code writing and development by bringing the entire development process into the IDE environment.  </span></p>
        <p><span>The features we currently support include:</span></p>
        <ul>
            <li><p><span>Code Completion: Use the Tab key to select when the code auto-completion appears. </span></p>
            </li>
            <li><p><span>Code Q&amp;A: Open the programming assistant page using thes shortcut Alt+Shift+K. </span></p>
            </li>
            <li><p><span>Code Explanation: Right-click on the selected code and choose “SRD Codefree” &gt; “Code Explanation”. </span>
            </p></li>
            <li><p><span>Code Annotation: Right-click on the selected code and choose “SRD  Codefree” &gt; “Code Annotation”. </span>
            </p></li>
            <li><p><span>Generate Unit Tests: Right-click on the selected code and choose “SRD  Codefree” &gt; “Generate Unit Tests.”</span>
            </p></li>
            <li><p><span>Code Optimization：Right-click on the selected code and choose “SRD  Codefree” &gt; “Code Optimization.”</span>
            </p></li>
            <li><p>
                <span>Error message explanation: Click &quot;CodeFree&quot; at the error location in the console.</span>
            </p></li>
            <li><p><span>Knowledge Base: Select a srdcloud knowledge base to ask questions, and code can be generated directly based on the API documentation within the knowledge base.</span>
            </p></li>
            <li><p><span>AI Developer：View modifications of multiple project files after sending requests in AI Developer mode</span>
            </p></li>
        </ul>
        <h2 id='研发云codefree-ai编程助手'><span>研发云CodeFree AI编程助手</span></h2>
        <p><span>研发云CodeFree是中国电信研发云的AI工具。CodeFree AI编程工具是使用AI能力协助用户进行代码开发的综合解决方案。这个工具在IDE侧实现了代码补全、编程助手等功能，更进一步和研发云各种已有的能力拉通，实现将研发流程下沉到IDE，以提高代码编写开发效率的目标。</span>
        </p>
        <p><span>我们目前支持的功能有：</span></p>
        <p><span>代码补全：当补全代码出现后，用tab键选择</span></p>
        <p><span>开发问答：使用快捷键Alt+Shift+K打开编程助手页面</span></p>
        <p><span>代码解释：选中代码后右键，选择“研发云codefree”&gt;“代码解释”</span></p>
        <p><span>代码注释：选中代码后右键，选择“研发云codefree”&gt;“代码注释”</span></p>
        <p><span>生成单元测试：选中代码后右键，选择“研发云codefree”&gt;“生成单元测试”</span></p>
        <p><span>代码优化：选中代码后右键，选择“研发云codefree”&gt;“生成优化建议”</span></p>
        <p><span>异常报错解释：在console的报错处点击“CodeFree“</span></p>
        <p><span>知识库：选择研发云的知识库进行提问，可根据知识库中的API文档直接生成代码</span></p>
        <p><span>智能编程：在智能编程模式下发送需求后查看项目多文件的修改</span></p>
        <h2 id='主要功能features'><span>主要功能Features</span></h2>
        <h3 id='代码补全-code-completion'><span>代码补全 Code Completion</span></h3>
        <p>
            <span>代码补全是本插件主要功能之一，通过本功能用户可以在编写代码过程中得到代码提示从而加快代码编写速度。代码补全功能包括触发、选择、确认和取消。具体功能如下：</span>
        </p>
        <ul>
            <li><p><span>自动补全：在IDE输入代码时自动触发的补全</span></p></li>
            <li><p><span>手动补全：在代码中合适的位置，通过按组合键发起的补全，默认快捷键为ctrl+enter</span></p></li>
            <li><p><span>确认选择：当补全代码出现后，用tab键选择</span></p></li>
            <li><p><span>禁用补全：默认使用ctrl+shift+alt+o禁用代码补全</span></p></li>
        </ul>
        <h3 id='开发问答code-qa'><span>开发问答Code Q&amp;A</span></h3>
        <p>
            <span>开发问答，是以技术问答的形式，向用户提供开发和技术方面的建议。用户通过编程助手问答，可以在不离开IDE的情况下，获得解决当前代码工程的编程上的关键帮助，从而提升开发的质量和效率。具体操作如下：</span>
        </p>
        <ul>
            <li><p><span>点击页面侧边栏的图标或使用快捷键Alt+Shift+K打开编程助手页面</span></p></li>
            <li><p><span>输入框输入问题，默认使用enter发送问题</span></p></li>
            <li><p><span>AI生成问题答案，其中代码部分单独列出，并可单独复制</span></p></li>
            <li><p><span>查看历史问题，问题和答案均可复制</span></p></li>
            <li><p><span>新建会话，自动保存当前会话</span></p></li>
            <li><p><span>可以使用指令模板辅助提问</span></p></li>
            <li><p><span>支持重新回答</span></p></li>
        </ul>
        <h3 id='代码解释code-explanation'><span>代码解释Code Explanation</span></h3>
        <p>
            <span>支持代码解释功能，选中代码后执行“代码解释”的命令，AI将为您详细解析代码，让您轻松跨越语言障碍，快速了解代码的功能逻辑，提高开发效率。具体操作如下：</span>
        </p>
        <ul>
            <li><p><span>在编辑器中选中代码后点击右键，选择“代码解释”功能</span></p></li>
            <li><p><span>在对话框中查看生成结果</span></p></li>
        </ul>
        <h3 id='生成代码注释code-annotation'><span>生成代码注释Code Annotation</span></h3>
        <p>
            <span>支持生成代码注释功能，为选中的代码一键生成函数注释及行间注释，节省编写注释的时间，使代码变得清晰易读。具体操作如下：</span>
        </p>
        <ul>
            <li><p><span>在编辑器中选中代码后点击右键，选择“生成代码注释”功能</span></p></li>
            <li><p><span>在对话框中查看生成结果</span></p></li>
        </ul>
        <h3 id='生成单元测试generate-unit-tests'><span>生成单元测试Generate Unit Tests</span></h3>
        <p>
            <span>支持生成单元测试功能，为选中的代码生成单元测试，同时支持一键生成新文件，节省宝贵的开发时间，使开发过程更加高效。具体操作如下：</span>
        </p>
        <ul>
            <li><p><span>在编辑器中选中代码后点击右键，选择“生成单元测试”功能</span></p></li>
            <li><p><span>在对话框中查看生成结果</span></p></li>
        </ul>
        <h3 id='代码优化code-optimization'><span>代码优化Code Optimization</span></h3>
        <p><span>支持代码优化功能，为选中的代码生成优化建议，提升代码质量。具体操作如下：</span></p>
        <ul>
            <li><p><span>在编辑器中选中代码后点击右键，选择“生成优化建议”功能</span></p></li>
            <li><p><span>在对话框中查看生成结果</span></p></li>
        </ul>
        <h3 id='异常报错解释explanation-of-exception-error'><span>异常报错解释Explanation of Exception Error</span></h3>
        <p><span>在IDEA中，支持对代码运行后的报错信息进行分析和提供修复建议，具体操作如下：</span></p>
        <ul>
            <li><p><span>在console中的报错处点击“CodeFree&quot;按钮</span></p></li>
            <li><p><span>在对话框中查看生成结果</span></p></li>
        </ul>
        <h3 id='知识库提问knowledge-base-inquiry'><span>知识库提问Knowledge Base Inquiry</span></h3>
        <p><span>支持对研发云上您有权限的知识库进行提问，具体操作如下：</span></p>
        <ul>
            <li><p><span>在输入框上方选择知识库，输入问题进行提问</span></p></li>
            <li><p><span>当提问到知识库中的接口时，知识库提供相关的备选API可以进行选择</span></p></li>
            <li><p><span>选择后可在对话框中直接生成调用代码</span></p></li>
        </ul>
        <h3 id='智能编程ai-developer'><span>智能编程AI Developer</span></h3>
        <p><span>CodeFree智能编程（CodeFree AI Developer）作为新一代AI开发助手，具备跨文件代码协作功能和多文件代码修改的能力，通过人机协作模式帮助开发者完成开发工作，涵盖需求开发、缺陷修复、测试用例生成及规模化代码重构等场景。</span>
        </p>
        <ul>
            <li><p>
                <span>多文件修改：当把需求发送给CodeFree智能编程之后，CodeFree会返回回答。回答中可能会包含对项目中多个文件的修改。具体的修改信息体现在输入框上方出现的小工作区内</span>
            </p></li>
            <li><p>
                <span>审查和采纳：可以点击工作区的每一个文件查看CodeFree修改的详细内容，点击工作区中的“接受变更”或“撤销变更”的按钮进行整个文件的变更操作</span>
            </p></li>
        </ul>
        <h2 id='版本更新update'><span>版本更新Update</span></h2>
        <p><span>版本更新以及历史更新内容见</span><a href='https://www.srdcloud.cn/composq/AI' target='_blank'
                                                     class='url'>https://www.srdcloud.cn/composq/AI</a></p>
        <h2 id='帮助文档help'><span>帮助文档Help</span></h2>
        <p><a href='https://www.srdcloud.cn/helpcenter/content?id=1189244999559761920'><span>帮助文档</span></a></p>
        <h2 id='使用反馈feedback'><span>使用反馈Feedback</span></h2>
        <p><span>使用中有任何问题，欢迎进入反馈QQ群862534630向我们提问。</span></p>
    ]]></description>
    <change-notes><![CDATA[
        <p><span>1.5.3</span></p>
        <ul>
            <li><p><span>优化代码补全效果，支持python跨文件关联的代码生成</span></p></li>
            <li><p><span>优化智能编程diff窗口的显示逻辑，支持撤回对变更的操作</span></p></li>
            <li><p><span>问答面板上增加代码提交入口，提交代码关联工作项处支持关联多个工作项</span></p></li>
            <li><p><span>加快项目索引速度</span></p></li>
            <li><p><span>修复其他已知问题</span></p></li>
        </ul>
    ]]></change-notes>


    <!-- Product and plugin compatibility requirements.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-compatibility.html -->
    <depends>com.intellij.modules.platform</depends>
    <depends>Git4Idea</depends>
    <depends optional="true" config-file="javaPlugin.xml">com.intellij.modules.java</depends>
    <depends>org.jetbrains.plugins.terminal</depends>

    <actions>
        <!-- 主菜单栏(已下线，改为statusBar弹出菜单) -->
        <!--        <group id="IDEPlugin.MainMenu" text="研发云CodeFree" description="研发云CodeFree" popup="true">-->
        <!--            <add-to-group group-id="MainMenu" anchor="last"/>-->
        <!--        </group>-->

        <!-- statusBar弹出菜单项Action注册 -->
        <action id="IDEPlugin.SignInOutAction"
                class="com.srdcloud.ideplugin.actions.SignInOutAction"
                text="登录/登出研发云"
                description="Startup the SRD CodeFree plugin"/>
        <action class="com.srdcloud.ideplugin.codecomplete.actions.ToggleCompletionEnabledAction"
                id="IDEPlugin.ToggleCompletionEnabled"
                text="启用/禁用代码自动补全">
            <keyboard-shortcut keymap="$default" first-keystroke="ctrl shift alt O"/>
        </action>
        <action id="com.srdcloud.ideplugin.codecomplete.actions.ManualCompleteAction"
                class="com.srdcloud.ideplugin.codecomplete.actions.ManualCompleteAction"
                text="手动发起补全请求">
            <keyboard-shortcut keymap="$default" first-keystroke="ctrl ENTER"/>
        </action>
        <action id="com.srdcloud.ideplugin.HelpAction"
                class="com.srdcloud.ideplugin.actions.HelpAction"
                text="帮助"
                description="To docs"/>
        <!--        已下线，改为 右键开始聊天 Action-->
        <!--        <action id="com.srdcloud.ideplugin.actions.ToggleToolWindowAction"-->
        <!--                class="com.srdcloud.ideplugin.actions.ToggleToolWindowAction"-->
        <!--                text="打开编程助手"-->
        <!--                description="Open code chat">-->
        <!--            <keyboard-shortcut keymap="$default" first-keystroke="shift alt K"/>-->
        <!--        </action>-->

        <!-- 右键菜单 -->
        <group id="com.srdcloud.ideplugin.codechat.actions.RightMenuMainGroup" text="研发云CodeFree" popup="true"
               icon="/icons/srd_toolWindow.svg">
            <add-to-group group-id="EditorPopupMenu" anchor="first"/>
            <action id="RightMenuExplainAction"
                    class="com.srdcloud.ideplugin.webview.codechat.actions.rightmenu.RightMenuExplainAction"
                    popup="true" text="解释代码"/>
            <action id="RightMenuCommentAction"
                    class="com.srdcloud.ideplugin.webview.codechat.actions.rightmenu.RightMenuCommentAction"
                    popup="true" text="生成代码注释"/>
            <action id="RightMenuTestAction"
                    class="com.srdcloud.ideplugin.webview.codechat.actions.rightmenu.RightMenuTestAction"
                    popup="true" text="生成单元测试"/>
            <action id="RightMenuOptimizeAction"
                    class="com.srdcloud.ideplugin.webview.codechat.actions.rightmenu.RightMenuOptimizeAction"
                    popup="true" text="生成优化建议"/>
            <action id="RightMenuStartChatAction"
                    class="com.srdcloud.ideplugin.webview.codechat.actions.rightmenu.RightMenuStartChatAction"
                    popup="true" text="开始聊天">
                <keyboard-shortcut keymap="$default" first-keystroke="shift alt K"/>
            </action>
            <!--            <action id="RightMenuQuestionDialogAction"-->
            <!--                    class="com.srdcloud.ideplugin.codenatural.QuestionDialogAction"-->
            <!--                    popup="true" text="自然语言编程"/>-->
        </group>

        <!-- 自然语言编程相关 -->
        <!--        <action id="com.srdcloud.ideplugin.codenatural.QuestionDialogAction"-->
        <!--                class="com.srdcloud.ideplugin.codenatural.QuestionDialogAction" text="自然语言编程">-->
        <!--            <keyboard-shortcut keymap="$default" first-keystroke="shift alt A"/>-->
        <!--        </action>-->
        <!--        <group id="IDEPlugin.NLC" description="Srd CodeFree nlc">-->
        <!--            <action class="com.srdcloud.ideplugin.codenatural.NLCSelectAction"-->
        <!--                    id="com.srdcloud.ideplugin.codenatural.select"-->
        <!--                    text="确认生成代码">-->
        <!--                <keyboard-shortcut keymap="$default" first-keystroke="alt Y"/>-->
        <!--            </action>-->
        <!--            <action class="com.srdcloud.ideplugin.codenatural.NLCDeleteAction"-->
        <!--                    id="com.srdcloud.ideplugin.codenatural.delete"-->
        <!--                    text="删除高亮区域">-->
        <!--                <keyboard-shortcut keymap="$default" first-keystroke="alt K"/>-->
        <!--            </action>-->
        <!--        </group>-->

        <!-- 代码补全相关 -->
        <action class="com.srdcloud.ideplugin.codecomplete.actions.AcceptAction"
                id="IDEPlugin.InsertInlineCompletionAction"
                text="选择当前补全建议">
            <keyboard-shortcut keymap="$default" first-keystroke="TAB"/>
        </action>
        <action id="com.srdcloud.ideplugin.codecomplete.actions.PreviousAction"
                class="com.srdcloud.ideplugin.codecomplete.actions.PreviousAction" text="查看上一条补全建议">
            <keyboard-shortcut keymap="$default" first-keystroke="alt OPEN_BRACKET"/>
        </action>
        <action id="com.srdcloud.ideplugin.codecomplete.actions.NextAction"
                class="com.srdcloud.ideplugin.codecomplete.actions.NextAction" text="查看下一条补全建议">
            <keyboard-shortcut keymap="$default" first-keystroke="alt CLOSE_BRACKET"/>
        </action>

        <!-- 代码提交助手 -->
        <action id="com.srdcloud.ideplugin.aicommit.CommitPluginAction"
                class="com.srdcloud.ideplugin.aicommit.CommitPluginAction"
                text="关联工作项并生成提交信息"
                description="关联工作项并生成提交信息">
            <add-to-group group-id="Vcs.MessageActionGroup" anchor="first"/>
        </action>

        <!-- Agent相关 -->
        <action id="com.srdcloud.ideplugin.actions.IndexProgressAction"
                class="com.srdcloud.ideplugin.actions.IndexProgressAction" text="IndexProgressAction"
        />

    </actions>

    <!-- Extension points defined by the plugin.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-extension-points.html -->
    <extensions defaultExtensionNs="com.intellij">
        <postStartupActivity implementation="com.srdcloud.ideplugin.service.StartupActivityService"/>

        <!-- 右边侧边栏菜单注册 -->
        <toolWindow id="研发云CodeFree" anchor="right" icon="/icons/srd_toolWindow.svg" canCloseContents="true"
                    factoryClass="com.srdcloud.ideplugin.assistant.AssistantToolWindow"/>

        <!-- 状态条注册-->
        <statusBarWidgetFactory implementation="com.srdcloud.ideplugin.statusbar.WidgetFactory"
                                id="com.srdcloud.ideplugin.statusbar.Widget" order="first"/>

        <!-- 右下角通知组注册 -->
        <notificationGroup id="com.srdcloud.IDEPlugin" displayType="BALLOON"/>

        <!-- project层级service托管 -->
        <projectService
                serviceImplementation="com.srdcloud.ideplugin.webview.workitem.WorkItemWebview"/>
        <projectService
                serviceImplementation="com.srdcloud.ideplugin.webview.codechat.login.LoginCheckTask"/>

<!--        <projectService-->
<!--                serviceImplementation="com.srdcloud.ideplugin.webview.treesitter.WebviewTreeSitter"/>-->
<!--        <projectService serviceImplementation="com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc.ProjectCodeIndexer"/>-->
<!--        <projectService-->
<!--                serviceImplementation="com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc.CodeObjectIndexSystemUpdateTask"/>-->
        <!--        <projectService-->
        <!--                serviceImplementation="com.srdcloud.ideplugin.webview.treesitter.WebviewTreeSitterLoadTask"/>-->
        <!--        <projectService-->
        <!--                serviceImplementation="com.srdcloud.ideplugin.webview.codechat.CodeChatWebviewRefreshTask"/>-->

        <projectService
                serviceImplementation="com.srdcloud.ideplugin.agent.AgentManager"/>
        <projectService
                serviceImplementation="com.srdcloud.ideplugin.codeindex.CodeIndexService"/>
        <projectService
                serviceImplementation="com.srdcloud.ideplugin.composer.ComposerService"/>

        <projectService
                serviceImplementation="com.srdcloud.ideplugin.webview.codechat.CodeChatWebview"/>
        <projectService
                serviceImplementation="com.srdcloud.ideplugin.webview.codechat.actions.FixExceptionAction"/>
        <projectService
                serviceImplementation="com.srdcloud.ideplugin.webview.codechat.actions.rightmenu.RightMenuOptimizeAction"/>
        <projectService
                serviceImplementation="com.srdcloud.ideplugin.webview.codechat.actions.rightmenu.RightMenuTestAction"/>
        <projectService
                serviceImplementation="com.srdcloud.ideplugin.webview.codechat.actions.rightmenu.RightMenuCommentAction"/>
        <projectService
                serviceImplementation="com.srdcloud.ideplugin.webview.codechat.actions.rightmenu.RightMenuExplainAction"/>

        <!-- 编辑器工厂事件监听器 -->
        <editorFactoryListener
                implementation="com.srdcloud.ideplugin.codecomplete.listener.CompletionEditorFactoryListener"/>

        <!-- 编辑器行为处理器 -->
        <editorActionHandler
                id="com.srdcloud.completion.escape" action="EditorEscape"
                implementationClass="com.srdcloud.ideplugin.codecomplete.listener.EscapeHandler"
                order="before hide-hints"/>

        <!-- action快捷键冲突促进器 -->
        <actionPromoter implementation="com.srdcloud.ideplugin.codecomplete.actions.ActionsPromoter"/>

        <!-- 浏览器请求处理监听器 -->
        <httpRequestHandler implementation="com.srdcloud.ideplugin.service.BrowserRedirectListener"/>

        <!-- DIFF文件创建监听器 -->
        <diff.DiffExtension implementation="com.srdcloud.ideplugin.diff.diffreport.DiffEditorCreateListener"/>

    </extensions>

    <!-- 应用层监听器注册 -->
    <applicationListeners>
        <listener class="com.srdcloud.ideplugin.service.IDELifecycleService"
                  topic="com.intellij.ide.AppLifecycleListener"/>
    </applicationListeners>

    <!-- 项目层级监听器注册 -->
    <projectListeners>
        <listener class="com.srdcloud.ideplugin.service.ProjectLifecycleService"
                  topic="com.intellij.openapi.project.ProjectManagerListener"/>
    </projectListeners>
</idea-plugin>