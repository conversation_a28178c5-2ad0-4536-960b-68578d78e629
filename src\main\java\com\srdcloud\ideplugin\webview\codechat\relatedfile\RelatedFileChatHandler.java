package com.srdcloud.ideplugin.webview.codechat.relatedfile;

import com.intellij.diff.editor.ChainDiffVirtualFile;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.fileEditor.FileEditorManagerListener;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.LocalFileSystem;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.openapi.vfs.VirtualFileManager;
import com.intellij.openapi.vfs.newvfs.BulkFileListener;
import com.intellij.openapi.vfs.newvfs.events.VFileDeleteEvent;
import com.intellij.openapi.vfs.newvfs.events.VFileEvent;
import com.intellij.psi.impl.file.impl.FileManager;
import com.srdcloud.ideplugin.general.utils.*;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCode;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCommand;
import com.srdcloud.ideplugin.webview.codechat.relatedfile.request.GetDirectoryStructureRequest;
import com.srdcloud.ideplugin.webview.codechat.relatedfile.request.GetRelatedFilesRequest;
import com.srdcloud.ideplugin.webview.codechat.relatedfile.request.OpenTextDocumentRequest;
import com.srdcloud.ideplugin.webview.codechat.relatedfile.response.*;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Stack;

@Deprecated
public class RelatedFileChatHandler {
    private static final Logger logger = LoggerFactory.getLogger(RelatedFileChatHandler.class);

    private final CodeChatWebview parent;
    private final Project project;

    // 关联文件缓存管理
    private static final int MAX_RELATED_FILE_SIZE = 20;
    private final List<VirtualFile> recentlyVirtualFiles = new Stack<>();

    public RelatedFileChatHandler(Project project, CodeChatWebview parent) {
        this.parent = parent;
        this.project = project;

        addFileChangeListener();
    }

    private void addFileLRUCacheItemWithExcludeCheck(VirtualFile file) {
        DebugLogUtil.println("add file" + file.getName());
        if (ExclusionRules.isExcludeFile(file.getName())) {
            logger.warn("[cf] addFileLRUCacheItemWithExcludeCheck skip:" + file.getName());
            return;
        }
        recentlyVirtualFiles.add(0, file);
    }

    // 抽成全局类
    public void addFileChangeListener() {
        ApplicationManager.getApplication().getMessageBus().connect(parent).subscribe(FileEditorManagerListener.FILE_EDITOR_MANAGER, new FileEditorManagerListener() {
            @Override
            public void fileOpened(@NotNull FileEditorManager source, @NotNull VirtualFile file) {
                if (recentlyVirtualFiles.contains(file)) {
                    recentlyVirtualFiles.remove(file);
                    addFileLRUCacheItemWithExcludeCheck(file);
                } else {
                    if (recentlyVirtualFiles.size() >= MAX_RELATED_FILE_SIZE) {
                        recentlyVirtualFiles.remove(recentlyVirtualFiles.size() - 1);
                    }
                    addFileLRUCacheItemWithExcludeCheck(file);
                }
            }

            @Override
            public void fileClosed(@NotNull FileEditorManager source, @NotNull VirtualFile file) {
                // diff文件不支持打开
                if (file instanceof ChainDiffVirtualFile) {
                    recentlyVirtualFiles.remove(file);
                }
            }
        });

        // 监听文件夹变化
        ApplicationManager.getApplication().getMessageBus().connect(parent).subscribe(VirtualFileManager.VFS_CHANGES, new BulkFileListener() {
            @Override
            public void after(@NotNull List<? extends @NotNull VFileEvent> events) {
                for (VFileEvent event : events) {
                    if (event instanceof VFileDeleteEvent) {
                        recentlyVirtualFiles.remove(event.getFile());
                    }
                }
            }
        });
    }

    public void openTextDocument(String request) {
        OpenTextDocumentRequest openTextDocumentRequest = JsonUtil.getInstance().fromJson(request, OpenTextDocumentRequest.class);
        String filePath = openTextDocumentRequest.getData().getFilePath();

        if (filePath != null && !filePath.isEmpty()) {
            VirtualFile file = LocalFileSystem.getInstance().findFileByPath(filePath);

            if (file != null && file.exists()) {
                // 必须在EDT中进行
                ApplicationManager.getApplication().invokeLater(() -> {
                    FileEditorManager.getInstance(project).openFile(file, true);
                });
            } else {
                MessageBalloonNotificationUtil.showCommonNotificationWithConfirm(project, "文件不存在");
            }
        }

        // 回复文件打开行为
        OpenTextDocumentResponseData responseData = new OpenTextDocumentResponseData(WebViewRspCode.SUCCESS);
        OpenTextDocumentResponse getTextDocumentResponse = new OpenTextDocumentResponse(WebViewRspCommand.OPEN_TEXT_DOCUMENT_RESPONSE, responseData);
        parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(getTextDocumentResponse));
    }

    public void getDirectoryStructure(String request) {
        // 转换请求
        GetDirectoryStructureRequest getDirectoryStructureRequest = JsonUtil.getInstance().fromJson(request, GetDirectoryStructureRequest.class);

        // 获取需要的文件树根目录
        String filePath = getDirectoryStructureRequest.getData().getFilePath();
        FileNode files = convertFileTree(filePath);

        // 创建对应response
        GetDirectoryStructureResponseData getDirectoryStructureResponseData = null;
        if (files != null) {
            getDirectoryStructureResponseData = new GetDirectoryStructureResponseData(WebViewRspCode.SUCCESS, files);
        }
        GetDirectoryStructureResponse getDirectoryStructureResponse = new GetDirectoryStructureResponse(WebViewRspCommand.GET_DIRECTORY_STRUCTURE_RESPONSE, getDirectoryStructureResponseData);

        // 发送回复
        parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(getDirectoryStructureResponse));
    }

    public FileNode convertFileTree(String filePath) {
        // 构建文件树
        StringBuilder fileTree;
        if (filePath == null || filePath.isEmpty()) {
            // 默认项目顶级目录
            fileTree = FileUtil.getFullPathFileTree(project.getBasePath());
        } else {
            // 获取置顶文件或者项目结构
            fileTree = FileUtil.getFullPathFileTree(filePath);
        }

        // 消除头尾[]符号
        fileTree.deleteCharAt(0);
        fileTree.deleteCharAt(fileTree.length() - 1);

        // 将文件树放入列表中
        if (fileTree.length() != 0) {
            FileNode fileNode = JsonUtil.getInstance().fromJson(fileTree.toString(), FileNode.class);
            return fileNode;
        }

        return null;
    }

    public void getRelatedFiles(String request) {
        try {
            GetRelatedFilesRequest relatedFilesRequest = JsonUtil.getInstance().fromJson(request, GetRelatedFilesRequest.class);
            String reqType = relatedFilesRequest.getData().getReqType();

            int currentIndex = -1;

            // 先处理当前打开文件：如果有则移至栈顶，且标记下标；
            if (Objects.nonNull(FileEditorManager.getInstance(project).getSelectedEditor())) {
                VirtualFile selectedFile = Objects.requireNonNull(FileEditorManager.getInstance(project).getSelectedEditor()).getFile();
                if (selectedFile != null && !ExclusionRules.isExcludeFile(selectedFile.getName())) {
                    recentlyVirtualFiles.remove(selectedFile);
                    recentlyVirtualFiles.add(0, selectedFile);
                    currentIndex = 0;
                }
            }

            // 再获取最近使用过的文件列表
            List<RecentlyUsedFile> recentlyUsedFiles = getRecentlyUsedFiles(recentlyVirtualFiles);

            // 回传响应
            GetRelatedFilesResponseData data = new GetRelatedFilesResponseData(reqType, WebViewRspCode.SUCCESS, currentIndex, recentlyUsedFiles, convertFileTree(""));
            GetRelatedFilesResponse getRelatedFilesResponse = new GetRelatedFilesResponse(WebViewRspCommand.QA_FOR_RELATED_FILES_RESPONSE, data);
            parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(getRelatedFilesResponse));
        } catch (Exception e) {
            logger.warn("[cf] getRelatedFiles error:\n", e);
            e.printStackTrace();
        }
    }

    /**
     * 获取LRU 最近使用过的文件列表
     *
     * @return
     */
    public List<RecentlyUsedFile> getRecentlyUsedFiles(final List<VirtualFile> recentlyVirtualFiles) {
        // 将缓存的文件加入列表中
        List<RecentlyUsedFile> recentFiles = new ArrayList<>();
        for (VirtualFile file : recentlyVirtualFiles) {
            RecentlyUsedFile recentFile = new RecentlyUsedFile(file.getName(), file.getPath(), file.getLength());
            recentFiles.add(recentFile);
        }

        return recentFiles;
    }

    public void fillFileContent(List<RelatedFile> files, int relatedFilesLengthLimit) throws IOException {
        if (files == null || files.isEmpty()) {
            return;
        }

        int countLength = 0;
        for (RelatedFile file : files) {
            String filePath = file.getPath();

            if (filePath != null && !filePath.isEmpty()) {
                String text = FileUtil.getFileContent(filePath);
                if (StringUtils.isBlank(text)) {
                    continue;
                }

                // 长度校验，超长则截断
//                if (countLength + text.length() > relatedFilesLengthLimit) {
//                    text = text.substring(0, relatedFilesLengthLimit - countLength);
//                    file.setText(text);
//                    MessageBalloonNotificationUtil.showCommonNotificationWithConfirm(project, "所选代码超长，已截断");
//                    return;
//                } else {
//                    file.setText(text);
//                    countLength += text.length();
//                }

                // 20250430版本：截断逻辑会导致@codebase、@folder转换后的提示语文件有截断风险。插件侧不再截断处理，由模型层截断
                file.setText(text);
            }

        }
    }
}
