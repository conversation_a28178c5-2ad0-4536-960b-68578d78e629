package com.srdcloud.ideplugin.settings

import com.intellij.openapi.components.service
import com.intellij.openapi.options.SearchableConfigurable
import com.intellij.openapi.project.Project
import com.srdcloud.ideplugin.agent.AgentManager
import com.srdcloud.ideplugin.general.constants.AgentNameConstant
import com.srdcloud.ideplugin.general.utils.CheckUpdateForSecUtil
import com.srdcloud.ideplugin.general.utils.pluginSettings
import com.srdcloud.ideplugin.service.SecIdeaApplicationSettingsStateService
import com.srdcloud.ideplugin.ui.SecIdeaSettingsDialog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.slf4j.LoggerFactory
import javax.swing.JComponent

class SecIdeaProjectSettingsConfigurable(val project: Project) : SearchableConfigurable {

    private val logger = LoggerFactory.getLogger(SecIdeaProjectSettingsConfigurable::class.java)
    private val settingsStateService
        get() = pluginSettings()

    private var secIdeaSettingsDialog: SecIdeaSettingsDialog = SecIdeaSettingsDialog(project, settingsStateService)

    override fun getId(): String = "com.srdcloud.ideplugin.settings.SecIdeaProjectSettingsConfigurable"

    override fun getDisplayName(): String = "海云智码"

    override fun createComponent(): JComponent = secIdeaSettingsDialog.getRootPanel()

    override fun isModified(): Boolean {
        return isCoreParamsModified() ||
                secIdeaSettingsDialog.getSecretKey() != settingsStateService.secretKey ||
                secIdeaSettingsDialog.getShortcutMode() != settingsStateService.shortcutMode ||
                secIdeaSettingsDialog.getDisabledLanguages() != settingsStateService.getDisabledLanguages() ||
                secIdeaSettingsDialog.getNewPackageSaveDir() != settingsStateService.newPackageSaveDir ||
                secIdeaSettingsDialog.getIsAutoDownloadNewPackage() != settingsStateService.isAutoDownloadNewPackage ||
                secIdeaSettingsDialog.getHasUpdateTip() != settingsStateService.hasUpdateTip ||
                secIdeaSettingsDialog.getUpdateTipTimeInterval() != settingsStateService.updateTipTimeInterval
    }

    private fun isCoreParamsModified() = isAddressModified() || isCplModeModified() || secIdeaSettingsDialog.preStoreAddress

    override fun apply() {

        // 通知 tabby-agent 配置变更
        if (isCoreParamsModified()) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val agentManager = AgentManager.getInstance(project)
                    val tabbyAgentClient = agentManager.getAgentCommClient(AgentNameConstant.TABBY_AGENT)

                    if (tabbyAgentClient != null && tabbyAgentClient.isEnabled) {
                        // 更新服务器端点配置
                        if (isAddressModified()) {
                            val newAddress = secIdeaSettingsDialog.getAddress()
                            if (!newAddress.isNullOrBlank()) {
                                tabbyAgentClient.request("updateConfig", listOf("server.endpoint", newAddress), null) { response ->
                                    logger.info("[cf] tabby agent server.endpoint updated: {}", response)
                                }
                            }
                        }

                        // 更新完成模式配置
                        if (isCplModeModified()) {
                            val newMode = secIdeaSettingsDialog.getCompletionMode()
                            if (!newMode.isNullOrBlank()) {
                                tabbyAgentClient.request("updateConfig", listOf("server.cplMode", newMode), null) { response ->
                                    logger.info("[cf] tabby agent server.cplMode updated: {}", response)
                                }
                            }
                        }
                    }
                } catch (e: Exception) {
                    logger.warn("[cf] Failed to notify tabby agent of config changes: {}", e.message)
                }
            }
        }

        settingsStateService.address = secIdeaSettingsDialog.getAddress()
        settingsStateService.secretKey = secIdeaSettingsDialog.getSecretKey().trim()
        settingsStateService.completionMode = secIdeaSettingsDialog.getCompletionMode()
        settingsStateService.shortcutMode = secIdeaSettingsDialog.getShortcutMode()

        settingsStateService.newPackageSaveDir = secIdeaSettingsDialog.getNewPackageSaveDir()
        settingsStateService.hasUpdateTip = secIdeaSettingsDialog.getHasUpdateTip()
        settingsStateService.isAutoDownloadNewPackage = secIdeaSettingsDialog.getIsAutoDownloadNewPackage()
        settingsStateService.updateTipTimeInterval = secIdeaSettingsDialog.getUpdateTipTimeInterval()
        settingsStateService.setDisabledLanguages(secIdeaSettingsDialog.getDisabledLanguages())

        // 设置定时器，定时检测更新包的下载
        if (settingsStateService.hasUpdateTip) {
            CheckUpdateForSecUtil.setTimer(project)
        } else {
            CheckUpdateForSecUtil.clearTimer()
        }

        val keymapSettings = service<KeymapSettings>()
        settingsStateService.shortcutMode = secIdeaSettingsDialog.getShortcutMode()
        keymapSettings.applyKeymapStyle(secIdeaSettingsDialog.getKeymapStyle())
    }

    private fun isAddressModified(): Boolean =
        secIdeaSettingsDialog.getAddress() != settingsStateService.address

    private fun isCplModeModified(): Boolean =
        secIdeaSettingsDialog.getCompletionMode() != settingsStateService.completionMode

    override fun reset() {
        val settings = service<SecIdeaApplicationSettingsStateService>()
        settings.completionMode?.let { secIdeaSettingsDialog.setCompletionMode(it) }
        secIdeaSettingsDialog.setDisabledLanguages(settings.getDisabledLanguages())
        settings.shortcutMode?.let { secIdeaSettingsDialog.setShortcutMode(it) }
        secIdeaSettingsDialog.setCustomInstruction(settings.customInstruction)
    }
}
