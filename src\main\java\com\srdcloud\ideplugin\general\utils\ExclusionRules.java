package com.srdcloud.ideplugin.general.utils;

import com.srdcloud.ideplugin.general.constants.IgnoreRules;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class ExclusionRules {

    // 排除目录与文件规则列表，统一参照IdeProtocolClient中对应列表

    // 按目录名称排除（全字匹配）
    @Deprecated
    public static final Set<String> DIRECTORIES = new HashSet<>(Arrays.asList(
            // 编译产物目录
            "target", "build", "dist", "out", "bin",
            // 依赖管理目录
            "node_modules", "venv", ".venv", ".gradle", ".mvn", "packages", "bower_components", "vendor",
            // 版本控制
            ".git", ".svn", ".hg",
            // IDE配置
            ".idea", ".vscode", ".settings",
            // 语言特定
            "__pycache__"
    ));

    // 按文件后缀名排除
    @Deprecated
    public static final String[] FILE_PATTERNS = {
            // 编译产物文件
            ".class", ".o", ".so", ".a", ".exe", ".pyc",
            ".jar", ".war", ".zip",
            // 构建产物
            ".iml", ".ipr", ".iws",
            // 特殊系统文件
            ".DS_Store", ".db", ".gitignore", ".git"
    };

    public static boolean needExcludeFile(File file) {
        // 隐藏文件不处理
        if (file.isHidden()) {
            DebugLogUtil.println("needExcludeFile,file isHidden:" + file.getAbsolutePath() + "\n");
            return true;
        }
        // 规则排除的目录不处理
        if (file.isDirectory() && isExcludeDirectory((file.getName()))) {
            DebugLogUtil.println("needExcludeFile directory:" + file.getAbsolutePath() + "\n");
            return true;
        }
        // 规则排除的文件不处理
        if (file.isFile() && isExcludeFile(file.getName())) {
            DebugLogUtil.println("needExcludeFile file:" + file.getAbsolutePath() + "\n");
            return true;
        }
        return false;
    }

    public static boolean isExcludeDirectory(String directoryName) {
        if (IgnoreRules.DEFAULT_IGNORE_DIRS.contains(directoryName)) {
            return true;
        }
        return false;
    }

    // todo：优化代码
    public static boolean isExcludeFile(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return false;
        }
        for (String suffix : IgnoreRules.DEFAULT_IGNORE_FILETYPES) {
            if (fileName.endsWith(suffix)) {
                return true;
            }
        }
        return false;
    }

}
