package com.srdcloud.ideplugin.service.domain.codechat;

import com.srdcloud.ideplugin.general.enums.QuestionType;
import com.srdcloud.ideplugin.remote.domain.WorkItem.WorkItemInfo;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.CodeAIRequestPromptChat;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.QuoteItem;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.CodeMessage;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.DialogCondition;
import com.srdcloud.ideplugin.webview.codechat.relatedfile.RelatedFile;

import java.util.List;

/**
 * <AUTHOR> yangy
 * @create 2023/11/23 10:36
 * @description : 提问参数类
 */
public class AskChannelParam {
    private final String question;
    private final Integer kbId;
    private final String answerMode;
    private final String questionTaskType;
    private final String fileName;
    private final String prefix;
    private final String suffix;
    private final Integer manualType;
    private final List<String> stopWords;

    private List<CodeMessage.ImportSnippets> importSnippets;

    // 提问次数
    private Integer askIndex = 1;

    private QuestionType questionType;

    private String dialogId;

    private String parentReqId;

    private String promptTemplateId;

    private DialogCondition modelRouteCondition;

    private List<CodeAIRequestPromptChat> prompts;

    private List<RelatedFile> relatedFiles;

    private List<String> gitUrls;
    private List<String> diffList;
    private List<WorkItemInfo> workItemList;

    /**
     * 用户选择的知识库引用内容
     */
    private QuoteItem quote;


    public AskChannelParam(String question, Integer kbId, String answerMode, String questionTaskType, String fileName, String prefix, String suffix, Integer manualType,
                           List<String> stopWords, QuestionType questionType, String dialogId, String parentReqId, String promptTemplateId,
                           DialogCondition modelRouteCondition, List<CodeAIRequestPromptChat> prompts,
                           QuoteItem quote, List<CodeMessage.ImportSnippets> importSnippets, List<RelatedFile> relatedFiles, List<String> gitUrls,
                           List<String> diffList, List<WorkItemInfo> workItemList) {
        this.question = question;
        this.kbId = kbId;
        this.answerMode = answerMode;
        this.questionTaskType = questionTaskType;
        this.fileName = fileName;
        this.prefix = prefix;
        this.suffix = suffix;
        this.manualType = manualType;
        this.stopWords = stopWords;
        this.questionType = questionType;
        this.dialogId = dialogId;
        this.parentReqId = parentReqId;
        this.promptTemplateId = promptTemplateId;
        this.modelRouteCondition = modelRouteCondition;
        this.prompts = prompts;
        this.quote = quote;
        this.importSnippets = importSnippets;
        this.relatedFiles = relatedFiles;
        this.gitUrls = gitUrls;
        this.diffList = diffList;
        this.workItemList = workItemList;
    }

    public String getQuestion() {
        return question;
    }

    public Integer getKbId() {
        return kbId;
    }

    public String getAnswerMode() {
        return answerMode;
    }

    public String getQuestionTaskType() {
        return questionTaskType;
    }

    public String getFileName() {
        return fileName;
    }

    public String getPrefix() {
        return prefix;
    }

    public String getSuffix() {
        return suffix;
    }

    public Integer getManualType() {
        return manualType;
    }

    public Integer getAskIndex() {
        return askIndex;
    }

    public void setAskIndex(Integer askIndex) {
        this.askIndex = askIndex;
    }

    public List<String> getStopWords() {
        return stopWords;
    }

    public QuestionType getQuestionType() {
        return questionType;
    }

    public void setQuestionType(QuestionType questionType) {
        this.questionType = questionType;
    }

    public String getDialogId() {
        return dialogId;
    }

    public void setDialogId(String dialogId) {
        this.dialogId = dialogId;
    }

    public String getParentReqId() {
        return parentReqId;
    }

    public void setParentReqId(String parentReqId) {
        this.parentReqId = parentReqId;
    }

    public String getPromptTemplateId() {
        return promptTemplateId;
    }

    public void setPromptTemplateId(String promptTemplateId) {
        this.promptTemplateId = promptTemplateId;
    }

    public DialogCondition getModelRouteCondition() {
        return modelRouteCondition;
    }

    public void setModelRouteCondition(DialogCondition modelRouteCondition) {
        this.modelRouteCondition = modelRouteCondition;
    }

    public List<CodeAIRequestPromptChat> getPrompts() {
        return prompts;
    }

    public void setPrompts(List<CodeAIRequestPromptChat> prompts) {
        this.prompts = prompts;
    }

    public QuoteItem getQuote() {
        return quote;
    }

    public void setQuote(QuoteItem quote) {
        this.quote = quote;
    }

    public List<RelatedFile> getRelatedFiles() {
        return relatedFiles;
    }

    public void setRelatedFiles(List<RelatedFile> relatedFiles) {
        this.relatedFiles = relatedFiles;
    }

    public List<CodeMessage.ImportSnippets> getImportSnippets() {
        return importSnippets;
    }

    public void setImportSnippets(List<CodeMessage.ImportSnippets> importSnippets) {
        this.importSnippets = importSnippets;
    }

    public List<String> getGitUrls() {
        return gitUrls;
    }

    public void setGitUrls(List<String> gitUrls) {
        this.gitUrls = gitUrls;
    }

    public List<String> getDiffList() {
        return diffList;
    }

    public void setDiffList(List<String> diffList) {
        this.diffList = diffList;
    }

    public List<WorkItemInfo> getWorkItemList() {
        return workItemList;
    }

    public void setWorkItemList(List<WorkItemInfo> workItemList) {
        this.workItemList = workItemList;
    }
}
