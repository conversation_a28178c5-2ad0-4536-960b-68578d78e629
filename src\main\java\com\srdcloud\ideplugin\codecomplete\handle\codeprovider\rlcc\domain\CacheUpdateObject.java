package com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc.domain;

/**
 * <AUTHOR>
 * @date 2024/12/30
 * @desc 缓存更新类
 */
public class CacheUpdateObject {
    /**
     * Java内部类需要额外拼接的包路径：一般是某个外部类名
     */
    String packageSuffix;

    /**
     *  关联的数据类型
     * @see CacheObjectType
     */
    String objectType;

    /**
     * 要更新的缓存数据：类名、结构体名、Structs、Functions等
     */
    String objectName;

    /**
     * 缓存内容
     */
    String simpleText;

    public CacheUpdateObject(String packageSuffix, String objectType,String objectName, String simpleText) {
        this.packageSuffix = packageSuffix;
        this.objectType = objectType;
        this.objectName = objectName;
        this.simpleText = simpleText;
    }

    public String getPackageSuffix() {
        return packageSuffix;
    }

    public String getObjectType() {
        return objectType;
    }

    public String getObjectName() {
        return objectName;
    }

    public String getSimpleText() {
        return simpleText;
    }
}
