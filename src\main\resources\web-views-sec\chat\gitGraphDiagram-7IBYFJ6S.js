import{p as Z}from"./chunk-4BMEZGHF.js";import{I as F}from"./chunk-XZIHB7SX.js";import{F as U,_ as l,d as rr,G as er,H as tr,I as ar,l as v,s as nr,g as sr,b as or,c as cr,o as ir,p as dr,e as L,v as hr,j as lr,u as $r,K as fr}from"./index.js";import{p as gr}from"./mermaid-parser.core.js";import"./_baseUniq.js";import"./_basePickBy.js";import"./clone.js";var u={NORMAL:0,REVERSE:1,HIGHLIGHT:2,MERGE:3,CHERRY_PICK:4},yr=U.gitGraph,z=l(()=>er({...yr,...tr().gitGraph}),"getConfig"),i=new F(()=>{const a=z(),e=a.mainBranchName,t=a.mainBranchOrder;return{mainBranchName:e,commits:new Map,head:null,branchConfig:new Map([[e,{name:e,order:t}]]),branches:new Map([[e,null]]),currBranch:e,direction:"LR",seq:0,options:{}}});function S(){return ar({length:7})}l(S,"getID");function N(a,e){const t=Object.create(null);return a.reduce((s,r)=>{const n=e(r);return t[n]||(t[n]=!0,s.push(r)),s},[])}l(N,"uniqBy");var xr=l(function(a){i.records.direction=a},"setDirection"),ur=l(function(a){v.debug("options str",a),a=a==null?void 0:a.trim(),a=a||"{}";try{i.records.options=JSON.parse(a)}catch(e){v.error("error while parsing gitGraph options",e.message)}},"setOptions"),pr=l(function(){return i.records.options},"getOptions"),br=l(function(a){let e=a.msg,t=a.id;const s=a.type;let r=a.tags;v.info("commit",e,t,s,r),v.debug("Entering commit:",e,t,s,r);const n=z();t=L.sanitizeText(t,n),e=L.sanitizeText(e,n),r=r==null?void 0:r.map(o=>L.sanitizeText(o,n));const c={id:t||i.records.seq+"-"+S(),message:e,seq:i.records.seq++,type:s!=null?s:u.NORMAL,tags:r!=null?r:[],parents:i.records.head==null?[]:[i.records.head.id],branch:i.records.currBranch};i.records.head=c,v.info("main branch",n.mainBranchName),i.records.commits.set(c.id,c),i.records.branches.set(i.records.currBranch,c.id),v.debug("in pushCommit "+c.id)},"commit"),mr=l(function(a){let e=a.name;const t=a.order;if(e=L.sanitizeText(e,z()),i.records.branches.has(e))throw new Error(`Trying to create an existing branch. (Help: Either use a new name if you want create a new branch or try using "checkout ${e}")`);i.records.branches.set(e,i.records.head!=null?i.records.head.id:null),i.records.branchConfig.set(e,{name:e,order:t}),_(e),v.debug("in createBranch")},"branch"),wr=l(a=>{let e=a.branch,t=a.id;const s=a.type,r=a.tags,n=z();e=L.sanitizeText(e,n),t&&(t=L.sanitizeText(t,n));const c=i.records.branches.get(i.records.currBranch),o=i.records.branches.get(e),$=c?i.records.commits.get(c):void 0,h=o?i.records.commits.get(o):void 0;if($&&h&&$.branch===e)throw new Error(`Cannot merge branch '${e}' into itself.`);if(i.records.currBranch===e){const d=new Error('Incorrect usage of "merge". Cannot merge a branch to itself');throw d.hash={text:`merge ${e}`,token:`merge ${e}`,expected:["branch abc"]},d}if($===void 0||!$){const d=new Error(`Incorrect usage of "merge". Current branch (${i.records.currBranch})has no commits`);throw d.hash={text:`merge ${e}`,token:`merge ${e}`,expected:["commit"]},d}if(!i.records.branches.has(e)){const d=new Error('Incorrect usage of "merge". Branch to be merged ('+e+") does not exist");throw d.hash={text:`merge ${e}`,token:`merge ${e}`,expected:[`branch ${e}`]},d}if(h===void 0||!h){const d=new Error('Incorrect usage of "merge". Branch to be merged ('+e+") has no commits");throw d.hash={text:`merge ${e}`,token:`merge ${e}`,expected:['"commit"']},d}if($===h){const d=new Error('Incorrect usage of "merge". Both branches have same head');throw d.hash={text:`merge ${e}`,token:`merge ${e}`,expected:["branch abc"]},d}if(t&&i.records.commits.has(t)){const d=new Error('Incorrect usage of "merge". Commit with id:'+t+" already exists, use different custom Id");throw d.hash={text:`merge ${e} ${t} ${s} ${r==null?void 0:r.join(" ")}`,token:`merge ${e} ${t} ${s} ${r==null?void 0:r.join(" ")}`,expected:[`merge ${e} ${t}_UNIQUE ${s} ${r==null?void 0:r.join(" ")}`]},d}const f=o||"",x={id:t||`${i.records.seq}-${S()}`,message:`merged branch ${e} into ${i.records.currBranch}`,seq:i.records.seq++,parents:i.records.head==null?[]:[i.records.head.id,f],branch:i.records.currBranch,type:u.MERGE,customType:s,customId:!!t,tags:r!=null?r:[]};i.records.head=x,i.records.commits.set(x.id,x),i.records.branches.set(i.records.currBranch,x.id),v.debug(i.records.branches),v.debug("in mergeBranch")},"merge"),vr=l(function(a){let e=a.id,t=a.targetId,s=a.tags,r=a.parent;v.debug("Entering cherryPick:",e,t,s);const n=z();if(e=L.sanitizeText(e,n),t=L.sanitizeText(t,n),s=s==null?void 0:s.map($=>L.sanitizeText($,n)),r=L.sanitizeText(r,n),!e||!i.records.commits.has(e)){const $=new Error('Incorrect usage of "cherryPick". Source commit id should exist and provided');throw $.hash={text:`cherryPick ${e} ${t}`,token:`cherryPick ${e} ${t}`,expected:["cherry-pick abc"]},$}const c=i.records.commits.get(e);if(c===void 0||!c)throw new Error('Incorrect usage of "cherryPick". Source commit id should exist and provided');if(r&&!(Array.isArray(c.parents)&&c.parents.includes(r)))throw new Error("Invalid operation: The specified parent commit is not an immediate parent of the cherry-picked commit.");const o=c.branch;if(c.type===u.MERGE&&!r)throw new Error("Incorrect usage of cherry-pick: If the source commit is a merge commit, an immediate parent commit must be specified.");if(!t||!i.records.commits.has(t)){if(o===i.records.currBranch){const x=new Error('Incorrect usage of "cherryPick". Source commit is already on current branch');throw x.hash={text:`cherryPick ${e} ${t}`,token:`cherryPick ${e} ${t}`,expected:["cherry-pick abc"]},x}const $=i.records.branches.get(i.records.currBranch);if($===void 0||!$){const x=new Error(`Incorrect usage of "cherry-pick". Current branch (${i.records.currBranch})has no commits`);throw x.hash={text:`cherryPick ${e} ${t}`,token:`cherryPick ${e} ${t}`,expected:["cherry-pick abc"]},x}const h=i.records.commits.get($);if(h===void 0||!h){const x=new Error(`Incorrect usage of "cherry-pick". Current branch (${i.records.currBranch})has no commits`);throw x.hash={text:`cherryPick ${e} ${t}`,token:`cherryPick ${e} ${t}`,expected:["cherry-pick abc"]},x}const f={id:i.records.seq+"-"+S(),message:`cherry-picked ${c==null?void 0:c.message} into ${i.records.currBranch}`,seq:i.records.seq++,parents:i.records.head==null?[]:[i.records.head.id,c.id],branch:i.records.currBranch,type:u.CHERRY_PICK,tags:s?s.filter(Boolean):[`cherry-pick:${c.id}${c.type===u.MERGE?`|parent:${r}`:""}`]};i.records.head=f,i.records.commits.set(f.id,f),i.records.branches.set(i.records.currBranch,f.id),v.debug(i.records.branches),v.debug("in cherryPick")}},"cherryPick"),_=l(function(a){var e;if(a=L.sanitizeText(a,z()),i.records.branches.has(a)){i.records.currBranch=a;const t=i.records.branches.get(i.records.currBranch);t===void 0||!t?i.records.head=null:i.records.head=(e=i.records.commits.get(t))!=null?e:null}else{const t=new Error(`Trying to checkout branch which is not yet created. (Help try using "branch ${a}")`);throw t.hash={text:`checkout ${a}`,token:`checkout ${a}`,expected:[`branch ${a}`]},t}},"checkout");function K(a,e,t){const s=a.indexOf(e);s===-1?a.push(t):a.splice(s,1,t)}l(K,"upsert");function Y(a){const e=a.reduce((r,n)=>r.seq>n.seq?r:n,a[0]);let t="";a.forEach(function(r){r===e?t+="	*":t+="	|"});const s=[t,e.id,e.seq];for(const r in i.records.branches)i.records.branches.get(r)===e.id&&s.push(r);if(v.debug(s.join(" ")),e.parents&&e.parents.length==2&&e.parents[0]&&e.parents[1]){const r=i.records.commits.get(e.parents[0]);K(a,e,r),e.parents[1]&&a.push(i.records.commits.get(e.parents[1]))}else{if(e.parents.length==0)return;if(e.parents[0]){const r=i.records.commits.get(e.parents[0]);K(a,e,r)}}a=N(a,r=>r.id),Y(a)}l(Y,"prettyPrintCommitHistory");var Cr=l(function(){v.debug(i.records.commits);const a=V()[0];Y([a])},"prettyPrint"),Er=l(function(){i.reset(),hr()},"clear"),Br=l(function(){return[...i.records.branchConfig.values()].map((e,t)=>e.order!==null&&e.order!==void 0?e:{...e,order:parseFloat(`0.${t}`)}).sort((e,t)=>{var s,r;return((s=e.order)!=null?s:0)-((r=t.order)!=null?r:0)}).map(({name:e})=>({name:e}))},"getBranchesAsObjArray"),kr=l(function(){return i.records.branches},"getBranches"),Lr=l(function(){return i.records.commits},"getCommits"),V=l(function(){const a=[...i.records.commits.values()];return a.forEach(function(e){v.debug(e.id)}),a.sort((e,t)=>e.seq-t.seq),a},"getCommitsArray"),Tr=l(function(){return i.records.currBranch},"getCurrentBranch"),Mr=l(function(){return i.records.direction},"getDirection"),Rr=l(function(){return i.records.head},"getHead"),X={commitType:u,getConfig:z,setDirection:xr,setOptions:ur,getOptions:pr,commit:br,branch:mr,merge:wr,cherryPick:vr,checkout:_,prettyPrint:Cr,clear:Er,getBranchesAsObjArray:Br,getBranches:kr,getCommits:Lr,getCommitsArray:V,getCurrentBranch:Tr,getDirection:Mr,getHead:Rr,setAccTitle:nr,getAccTitle:sr,getAccDescription:or,setAccDescription:cr,setDiagramTitle:ir,getDiagramTitle:dr},Ir=l((a,e)=>{Z(a,e),a.dir&&e.setDirection(a.dir);for(const t of a.statements)qr(t,e)},"populate"),qr=l((a,e)=>{const s={Commit:l(r=>e.commit(Or(r)),"Commit"),Branch:l(r=>e.branch(zr(r)),"Branch"),Merge:l(r=>e.merge(Gr(r)),"Merge"),Checkout:l(r=>e.checkout(Hr(r)),"Checkout"),CherryPicking:l(r=>e.cherryPick(Pr(r)),"CherryPicking")}[a.$type];s?s(a):v.error(`Unknown statement type: ${a.$type}`)},"parseStatement"),Or=l(a=>{var t,s;return{id:a.id,msg:(t=a.message)!=null?t:"",type:a.type!==void 0?u[a.type]:u.NORMAL,tags:(s=a.tags)!=null?s:void 0}},"parseCommit"),zr=l(a=>{var t;return{name:a.name,order:(t=a.order)!=null?t:0}},"parseBranch"),Gr=l(a=>{var t,s;return{branch:a.branch,id:(t=a.id)!=null?t:"",type:a.type!==void 0?u[a.type]:void 0,tags:(s=a.tags)!=null?s:void 0}},"parseMerge"),Hr=l(a=>a.branch,"parseCheckout"),Pr=l(a=>{var t;return{id:a.id,targetId:"",tags:((t=a.tags)==null?void 0:t.length)===0?void 0:a.tags,parent:a.parent}},"parseCherryPicking"),Wr={parse:l(async a=>{const e=await gr("gitGraph",a);v.debug(e),Ir(e,X)},"parse")},A=rr(),m=A==null?void 0:A.gitGraph,I=10,q=40,T=4,M=2,O=8,C=new Map,E=new Map,W=30,H=new Map,j=[],R=0,y="LR",jr=l(()=>{C.clear(),E.clear(),H.clear(),R=0,j=[],y="LR"},"clear"),J=l(a=>{const e=document.createElementNS("http://www.w3.org/2000/svg","text");return(typeof a=="string"?a.split(/\\n|\n|<br\s*\/?>/gi):a).forEach(s=>{const r=document.createElementNS("http://www.w3.org/2000/svg","tspan");r.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),r.setAttribute("dy","1em"),r.setAttribute("x","0"),r.setAttribute("class","row"),r.textContent=s.trim(),e.appendChild(r)}),e},"drawText"),Q=l(a=>{let e,t,s;return y==="BT"?(t=l((r,n)=>r<=n,"comparisonFunc"),s=1/0):(t=l((r,n)=>r>=n,"comparisonFunc"),s=0),a.forEach(r=>{var c,o;const n=y==="TB"||y=="BT"?(c=E.get(r))==null?void 0:c.y:(o=E.get(r))==null?void 0:o.x;n!==void 0&&t(n,s)&&(e=r,s=n)}),e},"findClosestParent"),Sr=l(a=>{let e="",t=1/0;return a.forEach(s=>{const r=E.get(s).y;r<=t&&(e=s,t=r)}),e||void 0},"findClosestParentBT"),Ar=l((a,e,t)=>{let s=t,r=t;const n=[];a.forEach(c=>{const o=e.get(c);if(!o)throw new Error(`Commit not found for key ${c}`);o.parents.length?(s=Yr(o),r=Math.max(s,r)):n.push(o),Dr(o,s)}),s=r,n.forEach(c=>{Nr(c,s,t)}),a.forEach(c=>{const o=e.get(c);if(o!=null&&o.parents.length){const $=Sr(o.parents);s=E.get($).y-q,s<=r&&(r=s);const h=C.get(o.branch).pos,f=s-I;E.set(o.id,{x:h,y:f})}})},"setParallelBTPos"),Kr=l(a=>{var s;const e=Q(a.parents.filter(r=>r!==null));if(!e)throw new Error(`Closest parent not found for commit ${a.id}`);const t=(s=E.get(e))==null?void 0:s.y;if(t===void 0)throw new Error(`Closest parent position not found for commit ${a.id}`);return t},"findClosestParentPos"),Yr=l(a=>Kr(a)+q,"calculateCommitPosition"),Dr=l((a,e)=>{const t=C.get(a.branch);if(!t)throw new Error(`Branch not found for commit ${a.id}`);const s=t.pos,r=e+I;return E.set(a.id,{x:s,y:r}),{x:s,y:r}},"setCommitPosition"),Nr=l((a,e,t)=>{const s=C.get(a.branch);if(!s)throw new Error(`Branch not found for commit ${a.id}`);const r=e+t,n=s.pos;E.set(a.id,{x:n,y:r})},"setRootPosition"),_r=l((a,e,t,s,r,n)=>{if(n===u.HIGHLIGHT)a.append("rect").attr("x",t.x-10).attr("y",t.y-10).attr("width",20).attr("height",20).attr("class",`commit ${e.id} commit-highlight${r%O} ${s}-outer`),a.append("rect").attr("x",t.x-6).attr("y",t.y-6).attr("width",12).attr("height",12).attr("class",`commit ${e.id} commit${r%O} ${s}-inner`);else if(n===u.CHERRY_PICK)a.append("circle").attr("cx",t.x).attr("cy",t.y).attr("r",10).attr("class",`commit ${e.id} ${s}`),a.append("circle").attr("cx",t.x-3).attr("cy",t.y+2).attr("r",2.75).attr("fill","#fff").attr("class",`commit ${e.id} ${s}`),a.append("circle").attr("cx",t.x+3).attr("cy",t.y+2).attr("r",2.75).attr("fill","#fff").attr("class",`commit ${e.id} ${s}`),a.append("line").attr("x1",t.x+3).attr("y1",t.y+1).attr("x2",t.x).attr("y2",t.y-5).attr("stroke","#fff").attr("class",`commit ${e.id} ${s}`),a.append("line").attr("x1",t.x-3).attr("y1",t.y+1).attr("x2",t.x).attr("y2",t.y-5).attr("stroke","#fff").attr("class",`commit ${e.id} ${s}`);else{const c=a.append("circle");if(c.attr("cx",t.x),c.attr("cy",t.y),c.attr("r",e.type===u.MERGE?9:10),c.attr("class",`commit ${e.id} commit${r%O}`),n===u.MERGE){const o=a.append("circle");o.attr("cx",t.x),o.attr("cy",t.y),o.attr("r",6),o.attr("class",`commit ${s} ${e.id} commit${r%O}`)}n===u.REVERSE&&a.append("path").attr("d",`M ${t.x-5},${t.y-5}L${t.x+5},${t.y+5}M${t.x-5},${t.y+5}L${t.x+5},${t.y-5}`).attr("class",`commit ${s} ${e.id} commit${r%O}`)}},"drawCommitBullet"),Vr=l((a,e,t,s)=>{var r;if(e.type!==u.CHERRY_PICK&&(e.customId&&e.type===u.MERGE||e.type!==u.MERGE)&&(m==null?void 0:m.showCommitLabel)){const n=a.append("g"),c=n.insert("rect").attr("class","commit-label-bkg"),o=n.append("text").attr("x",s).attr("y",t.y+25).attr("class","commit-label").text(e.id),$=(r=o.node())==null?void 0:r.getBBox();if($&&(c.attr("x",t.posWithOffset-$.width/2-M).attr("y",t.y+13.5).attr("width",$.width+2*M).attr("height",$.height+2*M),y==="TB"||y==="BT"?(c.attr("x",t.x-($.width+4*T+5)).attr("y",t.y-12),o.attr("x",t.x-($.width+4*T)).attr("y",t.y+$.height-12)):o.attr("x",t.posWithOffset-$.width/2),m.rotateCommitLabel))if(y==="TB"||y==="BT")o.attr("transform","rotate(-45, "+t.x+", "+t.y+")"),c.attr("transform","rotate(-45, "+t.x+", "+t.y+")");else{const h=-7.5-($.width+10)/25*9.5,f=10+$.width/25*8.5;n.attr("transform","translate("+h+", "+f+") rotate(-45, "+s+", "+t.y+")")}}},"drawCommitLabel"),Xr=l((a,e,t,s)=>{var r;if(e.tags.length>0){let n=0,c=0,o=0;const $=[];for(const h of e.tags.reverse()){const f=a.insert("polygon"),x=a.append("circle"),d=a.append("text").attr("y",t.y-16-n).attr("class","tag-label").text(h),g=(r=d.node())==null?void 0:r.getBBox();if(!g)throw new Error("Tag bbox not found");c=Math.max(c,g.width),o=Math.max(o,g.height),d.attr("x",t.posWithOffset-g.width/2),$.push({tag:d,hole:x,rect:f,yOffset:n}),n+=20}for(const{tag:h,hole:f,rect:x,yOffset:d}of $){const g=o/2,p=t.y-19.2-d;if(x.attr("class","tag-label-bkg").attr("points",`
      ${s-c/2-T/2},${p+M}  
      ${s-c/2-T/2},${p-M}
      ${t.posWithOffset-c/2-T},${p-g-M}
      ${t.posWithOffset+c/2+T},${p-g-M}
      ${t.posWithOffset+c/2+T},${p+g+M}
      ${t.posWithOffset-c/2-T},${p+g+M}`),f.attr("cy",p).attr("cx",s-c/2+T/2).attr("r",1.5).attr("class","tag-hole"),y==="TB"||y==="BT"){const b=s+d;x.attr("class","tag-label-bkg").attr("points",`
        ${t.x},${b+2}
        ${t.x},${b-2}
        ${t.x+I},${b-g-2}
        ${t.x+I+c+4},${b-g-2}
        ${t.x+I+c+4},${b+g+2}
        ${t.x+I},${b+g+2}`).attr("transform","translate(12,12) rotate(45, "+t.x+","+s+")"),f.attr("cx",t.x+T/2).attr("cy",b).attr("transform","translate(12,12) rotate(45, "+t.x+","+s+")"),h.attr("x",t.x+5).attr("y",b+3).attr("transform","translate(14,14) rotate(45, "+t.x+","+s+")")}}}},"drawCommitTags"),Jr=l(a=>{var t;switch((t=a.customType)!=null?t:a.type){case u.NORMAL:return"commit-normal";case u.REVERSE:return"commit-reverse";case u.HIGHLIGHT:return"commit-highlight";case u.MERGE:return"commit-merge";case u.CHERRY_PICK:return"commit-cherry-pick";default:return"commit-normal"}},"getCommitClassType"),Qr=l((a,e,t,s)=>{var n,c,o;const r={x:0,y:0};if(a.parents.length>0){const $=Q(a.parents);if($){const h=(n=s.get($))!=null?n:r;return e==="TB"?h.y+q:e==="BT"?((c=s.get(a.id))!=null?c:r).y-q:h.x+q}}else return e==="TB"?W:e==="BT"?((o=s.get(a.id))!=null?o:r).y-q:0;return 0},"calculatePosition"),Zr=l((a,e,t)=>{var c,o;const s=y==="BT"&&t?e:e+I,r=y==="TB"||y==="BT"?s:(c=C.get(a.branch))==null?void 0:c.pos,n=y==="TB"||y==="BT"?(o=C.get(a.branch))==null?void 0:o.pos:s;if(n===void 0||r===void 0)throw new Error(`Position were undefined for commit ${a.id}`);return{x:n,y:r,posWithOffset:s}},"getCommitPosition"),D=l((a,e,t)=>{var f;if(!m)throw new Error("GitGraph config not found");const s=a.append("g").attr("class","commit-bullets"),r=a.append("g").attr("class","commit-labels");let n=y==="TB"||y==="BT"?W:0;const c=[...e.keys()],o=(f=m==null?void 0:m.parallelCommits)!=null?f:!1,$=l((x,d)=>{var b,k;const g=(b=e.get(x))==null?void 0:b.seq,p=(k=e.get(d))==null?void 0:k.seq;return g!==void 0&&p!==void 0?g-p:0},"sortKeys");let h=c.sort($);y==="BT"&&(o&&Ar(h,e,n),h=h.reverse()),h.forEach(x=>{var p,b,k;const d=e.get(x);if(!d)throw new Error(`Commit not found for key ${x}`);o&&(n=Qr(d,y,n,E));const g=Zr(d,n,o);if(t){const G=Jr(d),B=(p=d.customType)!=null?p:d.type,w=(k=(b=C.get(d.branch))==null?void 0:b.index)!=null?k:0;_r(s,d,g,G,w,B),Vr(r,d,g,n),Xr(r,d,g,n)}y==="TB"||y==="BT"?E.set(d.id,{x:g.x,y:g.posWithOffset}):E.set(d.id,{x:g.posWithOffset,y:g.y}),n=y==="BT"&&o?n+q:n+q+I,n>R&&(R=n)})},"drawCommits"),Fr=l((a,e,t,s,r)=>{const c=(y==="TB"||y==="BT"?t.x<s.x:t.y<s.y)?e.branch:a.branch,o=l(h=>h.branch===c,"isOnBranchToGetCurve"),$=l(h=>h.seq>a.seq&&h.seq<e.seq,"isBetweenCommits");return[...r.values()].some(h=>$(h)&&o(h))},"shouldRerouteArrow"),P=l((a,e,t=0)=>{const s=a+Math.abs(a-e)/2;if(t>5)return s;if(j.every(c=>Math.abs(c-s)>=10))return j.push(s),s;const n=Math.abs(a-e);return P(a,e-n/5,t+1)},"findLane"),Ur=l((a,e,t,s)=>{var g,p,b,k,G;const r=E.get(e.id),n=E.get(t.id);if(r===void 0||n===void 0)throw new Error(`Commit positions not found for commits ${e.id} and ${t.id}`);const c=Fr(e,t,r,n,s);let o="",$="",h=0,f=0,x=(g=C.get(t.branch))==null?void 0:g.index;t.type===u.MERGE&&e.id!==t.parents[0]&&(x=(p=C.get(e.branch))==null?void 0:p.index);let d;if(c){o="A 10 10, 0, 0, 0,",$="A 10 10, 0, 0, 1,",h=10,f=10;const B=r.y<n.y?P(r.y,n.y):P(n.y,r.y),w=r.x<n.x?P(r.x,n.x):P(n.x,r.x);y==="TB"?r.x<n.x?d=`M ${r.x} ${r.y} L ${w-h} ${r.y} ${$} ${w} ${r.y+f} L ${w} ${n.y-h} ${o} ${w+f} ${n.y} L ${n.x} ${n.y}`:(x=(b=C.get(e.branch))==null?void 0:b.index,d=`M ${r.x} ${r.y} L ${w+h} ${r.y} ${o} ${w} ${r.y+f} L ${w} ${n.y-h} ${$} ${w-f} ${n.y} L ${n.x} ${n.y}`):y==="BT"?r.x<n.x?d=`M ${r.x} ${r.y} L ${w-h} ${r.y} ${o} ${w} ${r.y-f} L ${w} ${n.y+h} ${$} ${w+f} ${n.y} L ${n.x} ${n.y}`:(x=(k=C.get(e.branch))==null?void 0:k.index,d=`M ${r.x} ${r.y} L ${w+h} ${r.y} ${$} ${w} ${r.y-f} L ${w} ${n.y+h} ${o} ${w-f} ${n.y} L ${n.x} ${n.y}`):r.y<n.y?d=`M ${r.x} ${r.y} L ${r.x} ${B-h} ${o} ${r.x+f} ${B} L ${n.x-h} ${B} ${$} ${n.x} ${B+f} L ${n.x} ${n.y}`:(x=(G=C.get(e.branch))==null?void 0:G.index,d=`M ${r.x} ${r.y} L ${r.x} ${B+h} ${$} ${r.x+f} ${B} L ${n.x-h} ${B} ${o} ${n.x} ${B-f} L ${n.x} ${n.y}`)}else o="A 20 20, 0, 0, 0,",$="A 20 20, 0, 0, 1,",h=20,f=20,y==="TB"?(r.x<n.x&&(t.type===u.MERGE&&e.id!==t.parents[0]?d=`M ${r.x} ${r.y} L ${r.x} ${n.y-h} ${o} ${r.x+f} ${n.y} L ${n.x} ${n.y}`:d=`M ${r.x} ${r.y} L ${n.x-h} ${r.y} ${$} ${n.x} ${r.y+f} L ${n.x} ${n.y}`),r.x>n.x&&(o="A 20 20, 0, 0, 0,",$="A 20 20, 0, 0, 1,",h=20,f=20,t.type===u.MERGE&&e.id!==t.parents[0]?d=`M ${r.x} ${r.y} L ${r.x} ${n.y-h} ${$} ${r.x-f} ${n.y} L ${n.x} ${n.y}`:d=`M ${r.x} ${r.y} L ${n.x+h} ${r.y} ${o} ${n.x} ${r.y+f} L ${n.x} ${n.y}`),r.x===n.x&&(d=`M ${r.x} ${r.y} L ${n.x} ${n.y}`)):y==="BT"?(r.x<n.x&&(t.type===u.MERGE&&e.id!==t.parents[0]?d=`M ${r.x} ${r.y} L ${r.x} ${n.y+h} ${$} ${r.x+f} ${n.y} L ${n.x} ${n.y}`:d=`M ${r.x} ${r.y} L ${n.x-h} ${r.y} ${o} ${n.x} ${r.y-f} L ${n.x} ${n.y}`),r.x>n.x&&(o="A 20 20, 0, 0, 0,",$="A 20 20, 0, 0, 1,",h=20,f=20,t.type===u.MERGE&&e.id!==t.parents[0]?d=`M ${r.x} ${r.y} L ${r.x} ${n.y+h} ${o} ${r.x-f} ${n.y} L ${n.x} ${n.y}`:d=`M ${r.x} ${r.y} L ${n.x-h} ${r.y} ${o} ${n.x} ${r.y-f} L ${n.x} ${n.y}`),r.x===n.x&&(d=`M ${r.x} ${r.y} L ${n.x} ${n.y}`)):(r.y<n.y&&(t.type===u.MERGE&&e.id!==t.parents[0]?d=`M ${r.x} ${r.y} L ${n.x-h} ${r.y} ${$} ${n.x} ${r.y+f} L ${n.x} ${n.y}`:d=`M ${r.x} ${r.y} L ${r.x} ${n.y-h} ${o} ${r.x+f} ${n.y} L ${n.x} ${n.y}`),r.y>n.y&&(t.type===u.MERGE&&e.id!==t.parents[0]?d=`M ${r.x} ${r.y} L ${n.x-h} ${r.y} ${o} ${n.x} ${r.y-f} L ${n.x} ${n.y}`:d=`M ${r.x} ${r.y} L ${r.x} ${n.y+h} ${$} ${r.x+f} ${n.y} L ${n.x} ${n.y}`),r.y===n.y&&(d=`M ${r.x} ${r.y} L ${n.x} ${n.y}`));if(d===void 0)throw new Error("Line definition not found");a.append("path").attr("d",d).attr("class","arrow arrow"+x%O)},"drawArrow"),re=l((a,e)=>{const t=a.append("g").attr("class","commit-arrows");[...e.keys()].forEach(s=>{const r=e.get(s);r.parents&&r.parents.length>0&&r.parents.forEach(n=>{Ur(t,e.get(n),r,e)})})},"drawArrows"),ee=l((a,e)=>{const t=a.append("g");e.forEach((s,r)=>{var p;const n=r%O,c=(p=C.get(s.name))==null?void 0:p.pos;if(c===void 0)throw new Error(`Position not found for branch ${s.name}`);const o=t.append("line");o.attr("x1",0),o.attr("y1",c),o.attr("x2",R),o.attr("y2",c),o.attr("class","branch branch"+n),y==="TB"?(o.attr("y1",W),o.attr("x1",c),o.attr("y2",R),o.attr("x2",c)):y==="BT"&&(o.attr("y1",R),o.attr("x1",c),o.attr("y2",W),o.attr("x2",c)),j.push(c);const $=s.name,h=J($),f=t.insert("rect"),d=t.insert("g").attr("class","branchLabel").insert("g").attr("class","label branch-label"+n);d.node().appendChild(h);const g=h.getBBox();f.attr("class","branchLabelBkg label"+n).attr("rx",4).attr("ry",4).attr("x",-g.width-4-((m==null?void 0:m.rotateCommitLabel)===!0?30:0)).attr("y",-g.height/2+8).attr("width",g.width+18).attr("height",g.height+4),d.attr("transform","translate("+(-g.width-14-((m==null?void 0:m.rotateCommitLabel)===!0?30:0))+", "+(c-g.height/2-1)+")"),y==="TB"?(f.attr("x",c-g.width/2-10).attr("y",0),d.attr("transform","translate("+(c-g.width/2-5)+", 0)")):y==="BT"?(f.attr("x",c-g.width/2-10).attr("y",R),d.attr("transform","translate("+(c-g.width/2-5)+", "+R+")")):f.attr("transform","translate(-19, "+(c-g.height/2)+")")})},"drawBranches"),te=l(function(a,e,t,s,r){return C.set(a,{pos:e,index:t}),e+=50+(r?40:0)+(y==="TB"||y==="BT"?s.width/2:0),e},"setBranchPosition"),ae=l(function(a,e,t,s){var h,f;if(jr(),v.debug("in gitgraph renderer",a+`
`,"id:",e,t),!m)throw new Error("GitGraph config not found");const r=(h=m.rotateCommitLabel)!=null?h:!1,n=s.db;H=n.getCommits();const c=n.getBranchesAsObjArray();y=n.getDirection();const o=lr(`[id="${e}"]`);let $=0;c.forEach((x,d)=>{var B;const g=J(x.name),p=o.append("g"),b=p.insert("g").attr("class","branchLabel"),k=b.insert("g").attr("class","label branch-label");(B=k.node())==null||B.appendChild(g);const G=g.getBBox();$=te(x.name,$,d,G,r),k.remove(),b.remove(),p.remove()}),D(o,H,!1),m.showBranches&&ee(o,c),re(o,H),D(o,H,!0),$r.insertTitle(o,"gitTitleText",(f=m.titleTopMargin)!=null?f:0,n.getDiagramTitle()),fr(void 0,o,m.diagramPadding,m.useMaxWidth)},"draw"),ne={draw:ae},se=l(a=>`
  .commit-id,
  .commit-msg,
  .branch-label {
    fill: lightgrey;
    color: lightgrey;
    font-family: 'trebuchet ms', verdana, arial, sans-serif;
    font-family: var(--mermaid-font-family);
  }
  ${[0,1,2,3,4,5,6,7].map(e=>`
        .branch-label${e} { fill: ${a["gitBranchLabel"+e]}; }
        .commit${e} { stroke: ${a["git"+e]}; fill: ${a["git"+e]}; }
        .commit-highlight${e} { stroke: ${a["gitInv"+e]}; fill: ${a["gitInv"+e]}; }
        .label${e}  { fill: ${a["git"+e]}; }
        .arrow${e} { stroke: ${a["git"+e]}; }
        `).join(`
`)}

  .branch {
    stroke-width: 1;
    stroke: ${a.lineColor};
    stroke-dasharray: 2;
  }
  .commit-label { font-size: ${a.commitLabelFontSize}; fill: ${a.commitLabelColor};}
  .commit-label-bkg { font-size: ${a.commitLabelFontSize}; fill: ${a.commitLabelBackground}; opacity: 0.5; }
  .tag-label { font-size: ${a.tagLabelFontSize}; fill: ${a.tagLabelColor};}
  .tag-label-bkg { fill: ${a.tagLabelBackground}; stroke: ${a.tagLabelBorder}; }
  .tag-hole { fill: ${a.textColor}; }

  .commit-merge {
    stroke: ${a.primaryColor};
    fill: ${a.primaryColor};
  }
  .commit-reverse {
    stroke: ${a.primaryColor};
    fill: ${a.primaryColor};
    stroke-width: 3;
  }
  .commit-highlight-outer {
  }
  .commit-highlight-inner {
    stroke: ${a.primaryColor};
    fill: ${a.primaryColor};
  }

  .arrow { stroke-width: 8; stroke-linecap: round; fill: none}
  .gitTitleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${a.textColor};
  }
`,"getStyles"),oe=se,ge={parser:Wr,db:X,renderer:ne,styles:oe};export{ge as diagram};
