package com.srdcloud.ideplugin.webview.codechat.conversation.request;

import com.srdcloud.ideplugin.webview.base.domain.WebViewCommand;

public class ConversationRefreshRequest extends WebViewCommand {

    private ChatHistoryRequest data;

    public ConversationRefreshRequest(String command, ChatHistoryRequest data) {
        this.command = command;
        this.data = data;
    }

    public ChatHistoryRequest getData() {
        return data;
    }

    public void setData(ChatHistoryRequest data) {
        this.data = data;
    }
}
