package com.srdcloud.ideplugin.general.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: yangy
 * @date: 2023/8/15 19:21
 * @Desc
 */
public class LanguageValidator {

    private static final Map<String, String> languages = new HashMap<>();

    static {
        languages.put("java", "Java");
        languages.put("python", "Python");
        languages.put("py", "Python");
        languages.put("javascript", "JavaScript");
        languages.put("js", "JavaScript");
        languages.put("kotlin", "Kotlin");
        languages.put("kt", "Kotlin");
        languages.put("cs", "C#");
        languages.put("c#", "C#");
        languages.put("cpp", "C++");
        languages.put("c++", "C++");
        languages.put("go", "Go");
        languages.put("rust", "Rust");
        languages.put("rs", "Rust");
        languages.put("typeScript", "TypeScript");
        languages.put("ts", "TypeScript");
        languages.put("ruby", "Ruby");
        languages.put("rb", "Ruby");
        languages.put("php", "PHP");
        languages.put("swift", "Swift");
        languages.put("scala", "Scala");
        languages.put("lua", "Lua");

        // 新增语言
        languages.put("c", "C");
        languages.put("html", "HTML");
        languages.put("css", "CSS");
        languages.put("json", "JSON");
        languages.put("xml", "XML");
        languages.put("sh", "Shell");
        languages.put("bash", "Bash");
        languages.put("sql", "SQL");
        languages.put("r", "R");
        languages.put("vb", "Visual Basic");
        languages.put("ocaml", "OCaml");
        languages.put("perl", "Perl");
        languages.put("pascal", "Pascal");
        languages.put("haskell", "Haskell");
        languages.put("yaml", "YAML");
        languages.put("shell", "SHELL");
        languages.put("text", "TEXT");
    }

    public static String getFullLanguage(String language) {
        if (language == null || language.isEmpty()) {
            return "";
        }

        String full = languages.get(language);
        if (full != null) {
            return language;
        }

        for (Map.Entry<String, String> entry : languages.entrySet()) {
            if (entry.getKey().startsWith(language)) {
                return entry.getKey();
            }
        }

        return "";
    }

}
