package com.srdcloud.ideplugin.general.utils;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024/4/26
 * @desc 代码处理
 */
public class CodeUtil {
    /**
     * 提取第一个代码块
     */
    public static String extractFirstCodeBlock(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }
        StringBuilder textBuilder = new StringBuilder(text);
        long count = countCodeBlocks(text);
        if (count == 0) {
            return "";
        } else if (count == 1) {
            textBuilder.append("```");
        }
        Pattern codeBlockPattern = Pattern.compile("```(.*?)```", Pattern.DOTALL);
        Matcher match = codeBlockPattern.matcher(textBuilder.toString());
        String codeBlockContent = match.group(1);

        // 使用换行符分割代码块，并移除第一行（语言标识）
        String[] lines = codeBlockContent.split("\n");
        if (lines.length > 1) {
            StringBuilder result = new StringBuilder();
            for (int i = 1; i < lines.length; i++) {
                result.append(lines[i]).append("\n");
            }
            return result.toString();
        } else {
            return "";
        }
    }

    /**
     * 计算文本中包含多少个代码块
     */
    public static long countCodeBlocks(String text) {
        Pattern codeBlockPattern = Pattern.compile("```");
        Matcher matches = codeBlockPattern.matcher(text);
        return matches.results().count();
    }

    /**
     * 提取多个代码块
     */
    public static ArrayList<String> extractCodeBlocks(String text) {
        if (text == null || text.isEmpty()) {
            return null;
        }
        long count = countCodeBlocks(text);
        if (count == 0) {
            return null;
        }
        ArrayList<String> codeBlocks = new ArrayList<>();
        Pattern codeBlockPattern = Pattern.compile("```(.*?)```", Pattern.DOTALL);
        Matcher matcher = codeBlockPattern.matcher(text);
        while (matcher.find()) {
            String codeBlockContent = matcher.group(1);
            // 使用换行符分割代码内容，并移除第一行（语言标识）
            String[] lines = codeBlockContent.split("\n");
            if (lines.length > 1) {
                StringBuilder pureCode = new StringBuilder();
                for (int i = 1; i < lines.length; i++) {
                    pureCode.append(lines[i]).append('\n');
                }
                codeBlocks.add(pureCode.toString().trim());
            }
        }
        return codeBlocks;
    }

    /**
     * 计算生成的代码行数
     */
    public static Double calGenCodeLines(String code, final String firstLineExclude) {
        Double lines = null;

        // 如果存在前缀内容，则拼接在开头第一行
        if (StringUtils.isNotBlank(firstLineExclude)) {
            code = firstLineExclude + code;
        }

        // 计算总行数
        String[] codeStrLines = code.split("\r\n|\r|\n");
        if (codeStrLines.length == 0) {
            return lines;
        }
        BigDecimal total = new BigDecimal(codeStrLines.length);
        BigDecimal firstLinePercentage = new BigDecimal("0.0");
        // 第一行存在排除内容，则单独计算补全内容占比
        if (StringUtils.isNotBlank(firstLineExclude) && StringUtils.isNotBlank(codeStrLines[0])) {
            //if (firstLineExclude!=null && codeStrLines[0]!=null) {
            BigDecimal firstLineLen = new BigDecimal(codeStrLines[0].length());
            BigDecimal firstLineNeedCountLen = new BigDecimal(codeStrLines[0].length() - firstLineExclude.length());
            firstLinePercentage = firstLineNeedCountLen.divide(firstLineLen, 1, RoundingMode.CEILING);
            total = total.subtract(new BigDecimal("1")).add(firstLinePercentage);
        }
        return total.doubleValue();
    }

}