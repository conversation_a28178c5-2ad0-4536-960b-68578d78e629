package com.srdcloud.ideplugin.general.utils

import com.intellij.ide.plugins.PluginManagerCore
import com.intellij.notification.Notification
import com.intellij.notification.NotificationAction
import com.intellij.openapi.Disposable
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.extensions.PluginId
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.wm.ToolWindowManager
import com.intellij.util.Alarm
import com.srdcloud.ideplugin.general.constants.Constants
import com.srdcloud.ideplugin.service.UpdateService
import com.srdcloud.ideplugin.service.VersionCheckResponse

class CheckUpdateForSecUtil: Disposable {
    companion object {
        private lateinit var alarm: Alarm
        private var isTimerSet = false
        private val settings = pluginSettings()

        // 初始化 CheckUpdate 实例
        private val instance = CheckUpdateForSecUtil()

        private fun getProductType(project: Project): String {
            val toolWindowManager = ToolWindowManager.getInstance(project)
            val myToolWindow = toolWindowManager.getToolWindow("海云智码")

            return if (myToolWindow != null) {
                "d10"
            } else {
                "c10"
            }
        }

        @JvmStatic
        fun setTimer(project: Project) {

            if (settings.hasUpdateTip && settings.updateTipTimeInterval != null) {

                if (isTimerSet) {
                    // 取消之前的定时任务
                    alarm.cancelAllRequests()
                }

                val intervalMillis = parseIntervalToMillis(settings.updateTipTimeInterval)
                setRecursiveTimer(intervalMillis, project)
                isTimerSet = true
            }
        }

        @JvmStatic
        fun clearTimer() {
            alarm.cancelAllRequests()
        }

        private fun setRecursiveTimer(intervalMillis: Long, project: Project) {
            // 添加定时任务
            alarm.addRequest({
                if (settings.hasUpdateTip && settings.updateTipTimeInterval != null) {
                    checkUpdate(project)
                    // 递归调用，实现重复任务
                    setRecursiveTimer(intervalMillis, project)
                }
            }, intervalMillis)
        }

        private fun parseIntervalToMillis(interval: String?): Long {
            // 去掉字符串中的非数字部分，提取数字
            val hours = interval!!.replace("h", "").toIntOrNull() ?: 0
            return hours * 60 * 60 * 1000L
            // return hours * 10 * 1000L // todo: 测试用，用完后注释
        }

        private fun checkUpdate(project: Project) {

            val currentVersion = PluginManagerCore.getPlugin(PluginId.getId(Constants.PLUGIN_ID))?.version ?: "0.0.0"
            val baseUrl = pluginSettings().address
            val response = UpdateService.checkNewVersion(currentVersion, getProductType(project), baseUrl!!)

            when {
                response.hasUpdate -> {
                    val saveDir = settings.newPackageSaveDir!!
                    notifyNewVersionDownload(response, saveDir, project)
                }
            }

        }


        /**
         * 如果勾选了自动下载，则直接下载。如果没有勾选自动下载但是勾选了更新提醒，则提醒下载
         * 仅在启动插件时调用
         */
        @JvmStatic
        fun autoCheckUpdateAtStart(project: Project) {
            if(!settings.isAutoDownloadNewPackage && !settings.hasUpdateTip) {
                return
            }

            val currentVersion = PluginManagerCore.getPlugin(PluginId.getId(Constants.PLUGIN_ID))?.version ?: "0.0.0"
            val baseUrl = pluginSettings().address
            val response = UpdateService.checkNewVersion(currentVersion, getProductType(project), baseUrl!!)

            when {
                response.hasUpdate -> {
                    val saveDir = settings.newPackageSaveDir!!
                    if (settings.isAutoDownloadNewPackage) {
                        if (UpdateService.downloadNewVersion(saveDir, getProductType(project))) {
                            MessageBalloonNotificationUtil.showCommonNotification(
                                project,
                                "新版本海云智码已下载到: $saveDir, 可手动进行安装"
                            )
                        }
                    } else if (settings.hasUpdateTip) {
                        notifyNewVersionDownload(response, saveDir, project)
                    }
                }
            }
        }

        private fun notifyNewVersionDownload(
            response: VersionCheckResponse,
            saveDir: String,
            project: Project
        ) {
            val content = "发现新版本 ${response.latestVersion}，是否现在下载?"
            val notification = MessageBalloonNotificationUtil.createSrdNotification()
            notification.setContent(HtmlUtil.wrapHtmlNotification(content))
            notification.addAction(object : NotificationAction(Constants.NOTIFY_CONFIRM_TEXT) {
                override fun actionPerformed(e: AnActionEvent, notification: Notification) {
                    if (UpdateService.downloadNewVersion(saveDir, getProductType(project))) {
                        MessageBalloonNotificationUtil.showCommonNotification(
                            project,
                            "新版本海云智码已下载到: $saveDir, 可手动进行安装"
                        )
                    }
                }
            })
            notification.notify(project)
        }
    }

    init {
        // 初始化 Alarm，并传入 this 作为 Disposable 对象
        alarm = Alarm(Alarm.ThreadToUse.POOLED_THREAD, this)
    }
    override fun dispose() {
        // 释放资源
        Disposer.dispose(this)
    }
}
