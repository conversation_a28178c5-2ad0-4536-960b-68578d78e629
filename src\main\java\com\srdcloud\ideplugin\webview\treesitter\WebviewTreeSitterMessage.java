package com.srdcloud.ideplugin.webview.treesitter;

import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc.domain.CacheUpdateObject;

import java.util.List;

/**
 * WebMessage类用于封装网页消息
 * 它包含了消息的类型、数据内容和唯一标识符
 */
public class WebviewTreeSitterMessage {

    // 消息类型
    private String type;

    // 数据内容
    private String data;

    // 当前发送消息和回传消息标识符
    private String id;

    // 文件路径
    private String path;

    // 包路径
    private String packagePath;

    // 代码文件语言
    private String language;

    // Go模块映射JSON
    private String goModMapJson;

    // 文件名
    private String fileName;

    // 对象所在行
    private int row;

    // 对象所在列
    private int col;

    // 旧文件名
    private String oldFileName;

    // 新文件名
    private String newFileName;

    // 对象名
    private String objectName;

    // 错误信息
    private String errorMessage;

    // 模块名
    private String moduleName;

    // 模块目录路径
    private String moduleDirPath;

    // 需要更新的对象列表
    private List<CacheUpdateObject> objectsToUpdate;

    /**
     * 构造函数，初始化消息类型和标识符
     *
     * @param type 消息类型
     * @param id   消息标识符
     */
    public WebviewTreeSitterMessage(String type, String id) {
        this.type = type;
        this.id = id;
    }

    /**
     * 构造函数，初始化消息类型、文件路径、数据内容、标识符和语言
     *
     * @param type       消息类型
     * @param path       文件路径
     * @param data       数据内容
     * @param id         消息标识符
     * @param language   语言
     */
    public WebviewTreeSitterMessage(String type, String path, String data, String id, String language) {
        this.type = type;
        this.path = path;
        this.data = data;
        this.id = id;
        this.language = language;
    }

    /**
     * 构造函数，初始化消息类型、行号、列号、文件路径、数据内容、标识符、语言和Go模块映射JSON
     *
     * @param type          消息类型
     * @param row           所在行
     * @param col           所在列
     * @param path          文件路径
     * @param data          数据内容
     * @param id            消息标识符
     * @param language      语言
     * @param goModMapJson  Go模块映射JSON
     */
    public WebviewTreeSitterMessage(String type, int row, int col, String path, String data, String id, String language, String goModMapJson) {
        this.type = type;
        this.path = path;
        this.data = data;
        this.id = id;
        this.row = row;
        this.col = col;
        this.language = language;
        this.goModMapJson = goModMapJson;
    }

    /**
     * 获取错误信息
     *
     * @return 错误信息
     */
    public String getErrorMessage() {
        return errorMessage;
    }

    /**
     * 设置错误信息
     *
     * @param errorMessage 错误信息
     */
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    /**
     * 设置数据内容
     *
     * @param data 数据内容
     */
    public void setData(String data) {
        this.data = data;
    }

    /**
     * 设置消息标识符
     *
     * @param id 消息标识符
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 设置消息类型
     *
     * @param type 消息类型
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * 设置文件路径
     *
     * @param path 文件路径
     */
    public void setPath(String path) {
        this.path = path;
    }

    /**
     * 获取数据内容
     *
     * @return 数据内容
     */
    public String getData() {
        return data;
    }

    /**
     * 获取消息类型
     *
     * @return 消息类型
     */
    public String getType() {
        return type;
    }

    /**
     * 获取包路径
     *
     * @return 包路径
     */
    public String getPackagePath() {
        return packagePath;
    }

    /**
     * 设置包路径
     *
     * @param packagePath 包路径
     */
    public void setPackagePath(String packagePath) {
        this.packagePath = packagePath;
    }

    /**
     * 获取旧文件名
     *
     * @return 旧文件名
     */
    public String getOldFileName() {
        return oldFileName;
    }

    /**
     * 设置旧文件名
     *
     * @param oldFileName 旧文件名
     */
    public void setOldFileName(String oldFileName) {
        this.oldFileName = oldFileName;
    }

    /**
     * 获取新文件名
     *
     * @return 新文件名
     */
    public String getNewFileName() {
        return newFileName;
    }

    /**
     * 设置新文件名
     *
     * @param newFileName 新文件名
     */
    public void setNewFileName(String newFileName) {
        this.newFileName = newFileName;
    }

    /**
     * 获取对象名
     *
     * @return 对象名
     */
    public String getObjectName() {
        return objectName;
    }

    /**
     * 设置对象名
     *
     * @param objectName 对象名
     */
    public void setObjectName(String objectName) {
        this.objectName = objectName;
    }

    /**
     * 获取消息标识符
     *
     * @return 消息标识符
     */
    public String getId() {
        return id;
    }

    /**
     * 获取文件路径
     *
     * @return 文件路径
     */
    public String getPath() {
        return path;
    }

    /**
     * 获取文件名
     *
     * @return 文件名
     */
    public String getFileName() {
        return fileName;
    }

    /**
     * 设置文件名
     *
     * @param fileName 文件名
     */
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    /**
     * 获取列号
     *
     * @return 列号
     */
    public int getCol() {
        return col;
    }

    /**
     * 设置行号
     *
     * @param row 行号
     */
    public void setRow(int row) {
        this.row = row;
    }

    /**
     * 获取行号
     *
     * @return 行号
     */
    public int getRow() {
        return row;
    }

    /**
     * 设置列号
     *
     * @param col 列号
     */
    public void setCol(int col) {
        this.col = col;
    }

    /**
     * 设置语言
     *
     * @param language 语言
     */
    public void setLanguage(String language) {
        this.language = language;
    }

    /**
     * 获取语言
     *
     * @return 语言
     */
    public String getLanguage() {
        return language;
    }

    /**
     * 设置需要更新的对象列表
     *
     * @param objectsToUpdate 需要更新的对象列表
     */
    public void setObjectsToUpdate(List<CacheUpdateObject> objectsToUpdate) {
        this.objectsToUpdate = objectsToUpdate;
    }

    /**
     * 获取需要更新的对象列表
     *
     * @return 需要更新的对象列表
     */
    public List<CacheUpdateObject> getObjectsToUpdate() {
        return objectsToUpdate;
    }

    /**
     * 获取模块名
     *
     * @return 模块名
     */
    public String getModuleName() {
        return moduleName;
    }

    /**
     * 设置模块名
     *
     * @param moduleName 模块名
     */
    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    /**
     * 获取模块目录路径
     *
     * @return 模块目录路径
     */
    public String getModuleDirPath() {
        return moduleDirPath;
    }

    /**
     * 设置模块目录路径
     *
     * @param moduleDirPath 模块目录路径
     */
    public void setModuleDirPath(String moduleDirPath) {
        this.moduleDirPath = moduleDirPath;
    }

    /**
     * 获取Go模块映射JSON
     *
     * @return Go模块映射JSON
     */
    public String getGoModMapJson() {
        return goModMapJson;
    }

    /**
     * 设置Go模块映射JSON
     *
     * @param goModMapJson Go模块映射JSON
     */
    public void setGoModMapJson(String goModMapJson) {
        this.goModMapJson = goModMapJson;
    }
}
