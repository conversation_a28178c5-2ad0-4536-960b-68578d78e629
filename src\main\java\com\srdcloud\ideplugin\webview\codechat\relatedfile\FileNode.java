package com.srdcloud.ideplugin.webview.codechat.relatedfile;

import java.util.List;

/**
 * 项目目录树节点
 */
public class FileNode {
    private String name;
    private String path;
    private long size;
    private List<FileNode> children;

    public FileNode(String name, String path, long size, List<FileNode> children) {
        this.name = name;
        this.path = path;
        this.size = size;
        this.children = children;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public long getSize() {
        return size;
    }

    public void setSize(long size) {
        this.size = size;
    }

    public List<FileNode> getChildren() {
        return children;
    }

    public void setChildren(List<FileNode> children) {
        this.children = children;
    }
}
