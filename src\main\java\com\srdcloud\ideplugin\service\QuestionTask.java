package com.srdcloud.ideplugin.service;

import com.google.gson.Gson;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.general.constants.RtnMessage;
import com.srdcloud.ideplugin.general.enums.AnswerMode;
import com.srdcloud.ideplugin.general.utils.*;
import com.srdcloud.ideplugin.remote.domain.ApiResponse;
import com.srdcloud.ideplugin.service.domain.apigw.ApigwWebsocketRespPayload;
import com.srdcloud.ideplugin.service.domain.codechat.AskChannelParam;
import com.srdcloud.ideplugin.service.domain.codechat.AskQuestionParams;
import com.srdcloud.ideplugin.service.interfaces.IAnswerHandler;
import com.srdcloud.ideplugin.service.interfaces.IQuestionTaskEventHandler;
import com.srdcloud.ideplugin.statusbar.Notify;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 提问原子任务：一次提问请求创建一个提问任务，异步等待结果回传并进一步处理
 */
public class QuestionTask implements IAnswerHandler {

    private static Logger logger = LoggerFactory.getLogger(QuestionTask.class);

    /**
     * 提问请求reqId，uuid生成，唯一标识一次提问
     */
    private String reqId;

    /**
     * 缓存本次提问reqId与通道传入的原始提问参数
     */
    private Map<String, AskChannelParam> questionMap = new HashMap<>();

    /**
     * 本次提问期望的作答模式：同步、异步
     */
    private String answerModel;

    /**
     * 作答等待 超时计时器
     */
    private Timer msgResvTimeoutTimer;

    /**
     * 提问结果事件处理器：收到回答、发送异常
     */
    private final IQuestionTaskEventHandler iQstTskEvent;

    /**
     * 累计回答：流式回传token拼接
     */
    private String accumulateAnswerContent;


    private final Gson gson = new Gson();


    public QuestionTask(IQuestionTaskEventHandler iQstTskEvent, String am) {
        this.iQstTskEvent = iQstTskEvent;
        CodeChatResponseReceiver.SetChatQuestionTaskEvent(iQstTskEvent);
        accumulateAnswerContent = "";
        this.answerModel = am;
    }

    /**
     * 提问入口
     */
    public Integer AskQuestion(AskQuestionParams questionParam) {

        //生成提问参数
        this.reqId = questionParam.getReqId();
        AskChannelParam askChannelParam = new AskChannelParam(questionParam.getQuestion(), questionParam.getKbId(), answerModel, questionParam.getQuestionTaskType(), questionParam.getFileName(), questionParam.getPrefix(),
                questionParam.getSuffix(), questionParam.getManualType(), questionParam.getStopWords(), questionParam.getQuestionType(), questionParam.getDialogId(), questionParam.getParentReqId(), questionParam.getPromptTemplateId(),
                questionParam.getCurrModelRouteCondition(), questionParam.getPrompts(),
                questionParam.getQuote(), questionParam.getImportSnippets(), questionParam.getRelatedFiles(), questionParam.getGitUrls(), questionParam.getDiffList(), questionParam.getWorkItemList());
        questionMap.put(reqId, askChannelParam);

        // 校验登录状态
        if (LoginService.getLoginStatus() == Constants.LoginStatus_NOK) {
            logger.warn("[cf] AskQuestion check status fail,loginStatus:{}", Constants.LoginStatus_NOK);
            if(LoginService.getSecideaLoginStatus() == Constants.LoginStatus_OK) {
                MessageBalloonNotificationUtil.showSettingsNotification(
                        Objects.requireNonNull(IdeUtil.findCurrentProject()), "账号未授权，请更换登录账号或联系客服");
            }
            // 更新statusbar
            Notify.Companion.updateStatusNotify();
            return RtnCode.NOT_LOGIN;
        }

        // 判断WebSocket通道状态
        if (!LocalStorageUtil.checkChannelConnected()) {
            logger.warn("[cf] AskQuestion check status fail,channelStatus:{}", Constants.Channel_Disconnected);
            // 更新statusbar
            Notify.Companion.updateStatusNotify();
            return RtnCode.NO_CHANNEL;
        }


        // 通过后台线程，向通道发起提问
        new Thread(() -> {
            AskByWSChannel(askChannelParam);
        }, "AskQuestion-" + reqId).start();

        DebugLogUtil.info(String.format("InlineCompletion askQuestion,reqId:%s,fileName:%s,\n importSnippets:%s,\n prefix:%s", reqId, questionParam.getFileName(), JsonUtil.getInstance().toJson(questionParam.getImportSnippets()), questionParam.getPrefix()));
        return RtnCode.SUCCESS;
    }

    /**
     * 通过客户端与服务端维持的websocket通道发起提问
     */
    public void AskByWSChannel(AskChannelParam askChannelParam) {
        // 将本task加入到消息接收器池中，等待接受异步结果：在CodeAIRequestReceiver收到下行应答后，通过reqId找到本task实例进行后续处理
        CodeChatResponseReceiver.AddMessageHandler(this);

        // 超时计时器
        TimerTask timerTask = new TimerTask() {
            @Override
            public void run() {
                CodeChatResponseReceiver.DeleteMessageHandler(reqId);
                iQstTskEvent.onTaskError(reqId, RtnCode.RECV_TIMEOUT, null);
            }
        };
        long timerIntervel = 0;
        if (ConfigWrapper.ClientAnswerMode.equals(AnswerMode.ASYNC.getValue())) {
            timerIntervel = ConfigWrapper.AsyncMessageRecvTimeout * 1000L;
        } else if (ConfigWrapper.ClientAnswerMode.equals(AnswerMode.SYNC.getValue())) {
            timerIntervel = ConfigWrapper.SyncMessageRecvTimeout * 1000L;
        }
        msgResvTimeoutTimer = new Timer();
        msgResvTimeoutTimer.schedule(timerTask, timerIntervel);

        // 根据提问场景，调用CodeAIRequestSender具体业务消息发送方法
        ApiResponse sndMsgRtn = new ApiResponse(RtnCode.SEND_ERROR, RtnMessage.getMessageByCode(RtnCode.SEND_ERROR));
        if (Constants.QUESTION_TASK_TYPE_CODEGEN.equals(askChannelParam.getQuestionTaskType())) {
            sndMsgRtn = CodeChatRequestSender.SendCodeGenRequest(reqId, askChannelParam.getFileName(), askChannelParam.getPrefix(), askChannelParam.getSuffix(), Constants.MAX_NEW_TOKENS_AI_AUTO, askChannelParam.getStopWords(), askChannelParam.getImportSnippets(), askChannelParam.getGitUrls());
        } else if (Constants.QUESTION_TASK_TYPE_CHAT.equals(askChannelParam.getQuestionTaskType())) {
            sndMsgRtn = CodeChatRequestSender.SendChatGenRequest(reqId, askChannelParam.getQuestion(), askChannelParam.getKbId(), Constants.MAX_NEW_TOKENS_CHAT, askChannelParam.getManualType(), askChannelParam.getQuestionType(), askChannelParam.getDialogId(), askChannelParam.getParentReqId(), askChannelParam.getPromptTemplateId(), askChannelParam.getModelRouteCondition(), askChannelParam.getPrompts(), askChannelParam.getQuote(), askChannelParam.getRelatedFiles(), askChannelParam.getGitUrls(), askChannelParam.getWorkItemList());
        } else if (Constants.QUESTION_TASK_TYPE_CODEGEN_MANUAL.equals(askChannelParam.getQuestionTaskType())) {
            sndMsgRtn = CodeChatRequestSender.SendCodeGenRequest(reqId, askChannelParam.getFileName(), askChannelParam.getPrefix(), askChannelParam.getSuffix(), Constants.MAX_NEW_TOKENS_AI_MANUAL, askChannelParam.getStopWords(), null, askChannelParam.getGitUrls());
        } else if (Constants.QUESTION_TASK_TYPE_NATURAL_LANGUAGE.equals(askChannelParam.getQuestionTaskType())) {
            sndMsgRtn = CodeChatRequestSender.SendCodeAIByChatRequest(reqId, askChannelParam.getFileName(), askChannelParam.getPrefix(), askChannelParam.getSuffix(), askChannelParam.getQuestion(), askChannelParam.getManualType(), Constants.MAX_NEW_TOKENS_CHAT);
        } else if (Constants.QUESTION_TASK_TYPE_COMMIT.equals(askChannelParam.getQuestionTaskType())) {
            sndMsgRtn = CodeChatRequestSender.SendCommitChatRequest(reqId, askChannelParam.getGitUrls(), askChannelParam.getDiffList(), askChannelParam.getWorkItemList());
        }

        // 提问发送失败，取消定时器，执行通信异常结果处理器
        if (sndMsgRtn.getRtnCode() != RtnCode.SUCCESS) {
            logger.warn("[cf] AskByWSChannel fail,rtnCode:{},message:{}", sndMsgRtn.getRtnCode(), sndMsgRtn.getMessage());
            cancelReqIdTimeoutTask(reqId, true);
            iQstTskEvent.onTaskError(reqId, sndMsgRtn.getRtnCode(), null);
        }

    }

    /**
     * 手动停止本次对话
     */
    public void Cancel(String messageType) {
        // 取消定时器
        cancelReqIdTimeoutTask(reqId, ConfigWrapper.ChannelType == Constants.ChannelStyle_Websocket);

        // 通知相关UI组件执行对话停止行为
        CodeChatResponseReceiver.onTaskErrorOrClose(reqId, true);
    }


    /**
     * 需要转换错误信息为应答内容的case：敏感词提问、敏感词回答、知识库删除或被禁用
     */
    public static boolean needConvertErrMsgToAnswer(int rtnCode) {
        return rtnCode == RtnCode.INVALID_QUESTION || rtnCode == RtnCode.INVALID_ANSWER || rtnCode == RtnCode.KNOWLEDGE_BASE_DELETED;
    }

    /**
     * 收到下行应答，回调处理
     */
    @Override
    public void OnAnswer(String reqId, final ApigwWebsocketRespPayload payload) {
        int rtnCode = RtnCode.SEND_ERROR;
        int isEnd = 0;
        String answer = null;
        int seqNo = 0;
        if (payload != null) {
            answer = payload.getAnswer();
            rtnCode = payload.getRetCode();
            isEnd = payload.getIsEnd();
            seqNo = payload.getSeqId();
        }
        // 1、正常应答\需要携带异常应答到下一轮应答
        if (rtnCode == RtnCode.SUCCESS || needConvertErrMsgToAnswer(rtnCode)) {
            // 流式结果回传：未到结束位
            if (isEnd == Constants.IS_ANSWER_END_FALSE) {
                // 拼接累计回答内容
                accumulateAnswerContent += answer;
                if (answerModel.equals(AnswerMode.ASYNC.getValue()) &&
                        ConfigWrapper.ChannelType == Constants.ChannelStyle_Websocket) {
                    // 停止本次提问定时
                    cancelReqIdTimeoutTask(reqId, false);

                    // 将收到的消息上报给本task处理器，进行对应场景的结果处理：代码续写、聊天窗口渲染......
                    iQstTskEvent.onAnswer(reqId, isEnd, accumulateAnswerContent, answer, seqNo, payload);
                    // 开启新一轮定时等待
                    TimerTask timerTask = new TimerTask() {
                        @Override
                        public void run() {
                            CodeChatResponseReceiver.DeleteMessageHandler(reqId);
                            iQstTskEvent.onTaskError(reqId, RtnCode.RECV_TIMEOUT, null);
                        }
                    };
                    msgResvTimeoutTimer = new Timer();
                    msgResvTimeoutTimer.schedule(timerTask, ConfigWrapper.AsyncMessageRecvTimeout * 1000L);
                }
            }
            // 流式结果回传：到达结束位
            else if (isEnd == Constants.IS_ANSWER_END_TRUE) {
                if (ConfigWrapper.ChannelType == Constants.ChannelStyle_Websocket) {
                    // 停止定时器，并移除响应池task实例，不再处理后续下行消息
                    cancelReqIdTimeoutTask(reqId, true);
                    // 拼接累计回答内容
                    accumulateAnswerContent += answer;

                    // 将收到的消息上报给本task处理器，进行对应场景的结果处理：代码续写、聊天窗口渲染、commit信息渲染......
                    iQstTskEvent.onAnswer(reqId, isEnd, accumulateAnswerContent, answer, seqNo, payload);
                    // 恢复用户状态
                    LoginService.setUserStatus(Constants.UserStatus_OK);
                    Notify.Companion.updateStatusNotify();
                } else if (ConfigWrapper.ChannelType == Constants.ChannelStyle_Http) {
                    // 不存在这种case
                }

            }
        } else {
            // 2、异常应答
            if (ConfigWrapper.ChannelType == Constants.ChannelStyle_Websocket) {
                // 取消定时器
                cancelReqIdTimeoutTask(reqId, true);
            }
            // 处理异常case
            iQstTskEvent.onTaskError(reqId, rtnCode, payload);
        }
    }

    private void cancelReqIdTimeoutTask(String reqId, boolean isDeleteMessageHandler) {
        // 移除任务注册，后续该reqId下行的应答直接丢弃，不交给本task实例处理
        if (isDeleteMessageHandler) {
            CodeChatResponseReceiver.DeleteMessageHandler(reqId);
        }

        // 停止计时器
        if (msgResvTimeoutTimer != null) {
            try {
                msgResvTimeoutTimer.cancel();
            } catch (Exception e) {
                e.printStackTrace();
                logger.warn("[cf] msgResvTimeoutTimer Cancel", e);
            }
            msgResvTimeoutTimer = null;
        }
    }

    @Override
    public String GetReqId() {
        return reqId;
    }

    public Map<String, AskChannelParam> getQuestionMap() {
        return questionMap;
    }

    public void setQuestionMapByReqId(String reqId, AskChannelParam askChannelParam) {
        questionMap.put(reqId, askChannelParam);
    }

    public void removeQuestionMapByReqId(String reqId) {
        try {
            questionMap.remove(reqId);
        } catch (Exception e) {
            logger.warn("[cf] removeQuestionMapByReqId reqId:{}", reqId);
        }
    }
}
