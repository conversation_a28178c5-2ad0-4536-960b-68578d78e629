package com.srdcloud.ideplugin.assistant.codechatNative.logics.domain;

import java.util.ArrayList;
import java.util.List;

/**
 * 树节点连接器抽象类，用于管理和连接树结构中的节点
 * 该类是一个泛型类，可以用于多种类型的树节点
 */
public abstract class TreeNodeConnector<T> {

    // 子节点列表，用于存储当前节点的所有子节点
    private List<T> children;
    // 当前选中节点的索引，表示在子节点列表中的位置
    private int selectedIndex;

    /**
     * 默认构造方法
     * 初始化子节点列表为空列表，选中节点索引为-1，表示没有节点被选中
     */
    public TreeNodeConnector() {
        this.children = new ArrayList<>();
        this.selectedIndex = -1;
    }

    /**
     * 获取子节点列表
     *
     * @return 子节点的列表
     */
    public List<T> getChildren() {
        return children;
    }

    /**
     * 获取当前选中的子节点
     *
     * @return 当前选中的子节点
     * 可能抛出IndexOutOfBoundsException，如果没有任何节点被选中
     */
    public T getSelectedChildren() {
        return children.get(selectedIndex);
    }

    /**
     * 添加一个子节点到子节点列表中
     *
     * @param child 要添加的子节点
     */
    public void addChild(T child) {
        children.add(child);
    }

    /**
     * 设置当前选中节点的索引
     *
     * @param selectedIndex 要设置的选中节点索引
     * 如果索引超出子节点列表范围，将不会进行任何操作
     */
    public void setSelectedIndex(int selectedIndex) {
        if (selectedIndex >= 0 && selectedIndex < children.size()) {
            this.selectedIndex = selectedIndex;
        }
    }

    /**
     * 获取当前选中节点的索引
     *
     * @return 当前选中节点的索引
     */
    public int getSelectedIndex() {
        return selectedIndex;
    }
}

