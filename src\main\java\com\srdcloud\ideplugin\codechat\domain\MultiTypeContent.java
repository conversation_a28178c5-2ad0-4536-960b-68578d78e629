package com.srdcloud.ideplugin.codechat.domain;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * @author: yangy
 * @date: 2024/5/13 16:50
 * @Desc
 */
public class MultiTypeContent implements Serializable {

    /**
     * 本对象的内容类型，目前支持的取值为：
     * text - text内容为question或者answer内容文本
     * image_url - text内容为上传的图片地址
     * knowledge_base - 则knowledgeBaseId记录所用知识库id
     * quote - text内容为知识库提问的候选答案，可以用CodeChatQuote反序列化解出
     * @see com.srdcloud.ideplugin.general.enums.MultiTypeContentType
     */
    private String type;

    /**
     * 当type=text时，此项必选，为文本内容
     */
    private String text;

    /**
     * 当type=image_url时，此项必选，为上传的图片链接地址
     */
    @SerializedName("image_url")
    private String imageUrl;

    /**
     * 当该问答对使用了知识库提问时，记录知识库id
     */
    @SerializedName("knowledge_base_id")
    private String knowledgeBaseId;

    @SerializedName("local_file")
    private ChatLocalFile localFile;

    @SerializedName("work_item")
    private ChatWorkItem workItem;


    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getKnowledgeBaseId() {
        return knowledgeBaseId;
    }

    public void setKnowledgeBaseId(String knowledgeBaseId) {
        this.knowledgeBaseId = knowledgeBaseId;
    }

    public ChatLocalFile getLocalFile() {
        return localFile;
    }

    public void setLocalFile(ChatLocalFile localFile) {
        this.localFile = localFile;
    }

    public ChatWorkItem getWorkItem() {
        return workItem;
    }

    public void setWorkItem(ChatWorkItem workItem) {
        this.workItem = workItem;
    }
}
