package com.srdcloud.ideplugin.service.domain.apigw;

import java.io.Serializable;

public class ApigwWebsocketRespConfig implements Serializable {
    private String inputCharacterLimit;

    private String snippetsCharacterLimit;

    private String chatCharacterLimit; // 问答字符数限制

    private String clientUrlSubPath;

    private String sqsc; // 安全助手相关配置，json字符串值的格式

    private String printCacheConfig; // 打字机效果缓存配置

    private String indexVersion;

    // 忽略规则列表，json字符串数组格式 ["",""],业务方反序列化为list后使用
    private String ignoreList;

    // 代码补全策略列表，json字符串数组格式 ["rlcc","bm25"]
    private String codeCompleteStrategy;

    public String getInputCharacterLimit() {
        return inputCharacterLimit;
    }

    public void setInputCharacterLimit(String inputCharacterLimit) {
        this.inputCharacterLimit = inputCharacterLimit;
    }

    public String getSnippetsCharacterLimit() {
        return snippetsCharacterLimit;
    }

    public void setSnippetsCharacterLimit(String snippetsCharacterLimit) {
        this.snippetsCharacterLimit = snippetsCharacterLimit;
    }

    public String getClientUrlSubPath() {
        return clientUrlSubPath;
    }

    public void setClientUrlSubPath(String clientUrlSubPath) {
        this.clientUrlSubPath = clientUrlSubPath;
    }

    public String getSqsc() {
        return sqsc;
    }

    public void setSqsc(String sqsc) {
        this.sqsc = sqsc;
    }

    public String getPrintCacheConfig() {
        return printCacheConfig;
    }

    public void setPrintCacheConfig(String printCacheConfig) {
        this.printCacheConfig = printCacheConfig;
    }

    public String getChatCharacterLimit() {
        return chatCharacterLimit;
    }

    public void setChatCharacterLimit(String chatCharacterLimit) {
        this.chatCharacterLimit = chatCharacterLimit;
    }

    public String getIndexVersion() {
        return indexVersion;
    }

    public void setIndexVersion(String indexVersion) {
        this.indexVersion = indexVersion;
    }

    public String getIgnoreList() {
        return ignoreList;
    }

    public void setIgnoreList(String ignoreList) {
        this.ignoreList = ignoreList;
    }

    public String getCodeCompleteStrategy() {
        return codeCompleteStrategy;
    }

    public void setCodeCompleteStrategy(String codeCompleteStrategy) {
        this.codeCompleteStrategy = codeCompleteStrategy;
    }
}
