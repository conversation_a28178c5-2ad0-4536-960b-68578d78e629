package com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc.domain;

/**
 * 关联对象类
 * 通过各语言的CodeFinder类的FindRelativeObject方法查找出来的当前光标处代码所关联的代码对象
 */
public class RelativeCodeObject {

    // 关联对象名，可以是：类名、结构体名、包名、文件路径名......
    private final String relativeObject;

    // 关联代码的内容，不同语言、不同触发场景，所携带的关联内容按需自定义，转成代码text之后传入
    private final String relativeText;

    public RelativeCodeObject(String relativeObject, String relativeText) {
        this.relativeObject = relativeObject;
        this.relativeText = relativeText;
    }

    public String getRelativeObject() {
        return this.relativeObject;
    }

    public String getRelativeText() {
        return  this.relativeText;
    }

}
