package com.srdcloud.ideplugin.service.domain.apigw;

import com.srdcloud.ideplugin.agent.model.AgentVersion;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.CodeChatQuote;

import java.io.Serializable;
import java.util.List;

/**
 * @author: yangy
 * @date: 2023/7/23 20:23
 * @Desc
 */
public class ApigwWebsocketRespPayload implements Serializable {

    private String messageName;

    private String invokerId;

    private String reqId;

    private Integer seqId;

    private Integer retCode;

    private Integer isEnd;

    private String answer;

    private String errMsg;

    private boolean inValid;

    private String apiKey;

    private ApigwWebsocketRespConfig config;

    /**
     * 知识库提问引用内容
     */
    private CodeChatQuote quote;

    // agent版本信息
    private List<AgentVersion> agentVersion;

    // 插件当前版本更新内容
    private String clientVersionContent;

    // 远端最新版本
    private String clientLatestVersion;

    // 远端最新版本变更内容
    private String clientLatestVersionContent;

    // 插件最新版本下载Id
    private String clientLatestVersionId;

    // 插件最新版本直接下载地址
    private String clientLatestVersionDownloadUrl;


    @Override
    public String toString() {
        return "ApigwWebsocketRespPayload{" +
                "seqId=" + seqId +
                ", retCode=" + retCode +
                ", isEnd=" + isEnd +
                ", apiKey=" + apiKey +
                ", clientLatestVersion=" + clientLatestVersion +
                ", answer='" + answer + '\'' +
                '}';
    }

    public ApigwWebsocketRespPayload() {
    }

    public ApigwWebsocketRespPayload(String messageName, String invokerId, String reqId, Integer seqId, Integer retCode, Integer isEnd, String answer, String errMsg, boolean inValid, String apiKey, ApigwWebsocketRespConfig config, CodeChatQuote quote, List<AgentVersion> agentVersion, String clientVersionContent, String clientLatestVersion, String clientLatestVersionContent, String clientLatestVersionId, String clientLatestVersionDownloadUrl) {
        this.messageName = messageName;
        this.invokerId = invokerId;
        this.reqId = reqId;
        this.seqId = seqId;
        this.retCode = retCode;
        this.isEnd = isEnd;
        this.answer = answer;
        this.errMsg = errMsg;
        this.inValid = inValid;
        this.apiKey = apiKey;
        this.config = config;
        this.quote = quote;
        this.agentVersion = agentVersion;
        this.clientVersionContent = clientVersionContent;
        this.clientLatestVersion = clientLatestVersion;
        this.clientLatestVersionContent = clientLatestVersionContent;
        this.clientLatestVersionId = clientLatestVersionId;
        this.clientLatestVersionDownloadUrl = clientLatestVersionDownloadUrl;
    }

    public String getMessageName() {
        return messageName;
    }

    public void setMessageName(String messageName) {
        this.messageName = messageName;
    }

    public String getInvokerId() {
        return invokerId;
    }

    public void setInvokerId(String invokerId) {
        this.invokerId = invokerId;
    }

    public String getReqId() {
        return reqId;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    public Integer getSeqId() {
        return seqId;
    }

    public void setSeqId(Integer seqId) {
        this.seqId = seqId;
    }

    public Integer getRetCode() {
        return retCode;
    }

    public void setRetCode(Integer retCode) {
        this.retCode = retCode;
    }

    public Integer getIsEnd() {
        return isEnd;
    }

    public void setIsEnd(Integer isEnd) {
        this.isEnd = isEnd;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public boolean isInValid() {
        return inValid;
    }

    public void setInValid(boolean inValid) {
        this.inValid = inValid;
    }

    public String getClientLatestVersion() {
        return clientLatestVersion;
    }

    public void setClientLatestVersion(String clientLatestVersion) {
        this.clientLatestVersion = clientLatestVersion;
    }

    public String getClientLatestVersionId() {
        return clientLatestVersionId;
    }

    public void setClientLatestVersionId(String clientLatestVersionId) {
        this.clientLatestVersionId = clientLatestVersionId;
    }

    public String getClientLatestVersionDownloadUrl() {
        return clientLatestVersionDownloadUrl;
    }

    public void setClientLatestVersionDownloadUrl(String clientLatestVersionDownloadUrl) {
        this.clientLatestVersionDownloadUrl = clientLatestVersionDownloadUrl;
    }

    public String getClientVersionContent() {
        return clientVersionContent;
    }

    public void setClientVersionContent(String clientVersionContent) {
        this.clientVersionContent = clientVersionContent;
    }

    public String getClientLatestVersionContent() {
        return clientLatestVersionContent;
    }

    public void setClientLatestVersionContent(String clientLatestVersionContent) {
        this.clientLatestVersionContent = clientLatestVersionContent;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public ApigwWebsocketRespConfig getConfig() {
        return config;
    }

    public void setConfig(ApigwWebsocketRespConfig config) {
        this.config = config;
    }

    public CodeChatQuote getQuote() {
        return quote;
    }

    public void setQuote(CodeChatQuote quote) {
        this.quote = quote;
    }

    public List<AgentVersion> getAgentVersion() {
        return agentVersion;
    }

    public void setAgentVersion(List<AgentVersion> agentVersion) {
        this.agentVersion = agentVersion;
    }
}
