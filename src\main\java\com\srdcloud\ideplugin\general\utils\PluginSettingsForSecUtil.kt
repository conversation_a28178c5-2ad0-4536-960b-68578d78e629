@file:JvmName("PluginSettingsForSecUtilKt")

package com.srdcloud.ideplugin.general.utils

import com.intellij.openapi.application.ApplicationManager
import com.srdcloud.ideplugin.service.SecIdeaApplicationSettingsStateService


// 获取SecIdeaApplicationSettingsStateService类实例
fun pluginSettings(): SecIdeaApplicationSettingsStateService = getApplicationService()


/**
 * Copy of [com.intellij.openapi.components.service] to make code compilable with jvm 11 bytecode (Idea 2022.1)
 */
private inline fun <reified T : Any> getApplicationService(): T {
    val serviceClass = T::class.java
    return ApplicationManager.getApplication()?.getService(serviceClass)
            ?: throw RuntimeException("Cannot find service ${serviceClass.name} (classloader=${serviceClass.classLoader})")
}