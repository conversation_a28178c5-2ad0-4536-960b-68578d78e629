package com.srdcloud.ideplugin.webview.codechat.workitem;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vcs.ProjectLevelVcsManager;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowManager;
import com.intellij.vcs.commit.CommitMode;
import com.intellij.vcs.commit.CommitModeManager;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.general.utils.JsonUtil;
import com.srdcloud.ideplugin.general.utils.MessageBalloonNotificationUtil;
import com.srdcloud.ideplugin.remote.WorkItemCommHandler;
import com.srdcloud.ideplugin.remote.domain.WorkItem.WorkItemListResponse;
import com.srdcloud.ideplugin.service.LoginService;
import com.srdcloud.ideplugin.webview.base.domain.ErrorResponse;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCommand;
import com.srdcloud.ideplugin.webview.codechat.common.WorkItemEventType;
import com.srdcloud.ideplugin.webview.codechat.workitem.request.WorkItemRequest;
import com.srdcloud.ideplugin.webview.codechat.workitem.response.WorkItemResponse;
import com.srdcloud.ideplugin.webview.codechat.workitem.response.WorkItemResponseData;
import git4idea.GitVcs;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.util.Objects;

import static com.srdcloud.ideplugin.general.utils.GitUtil.getOriginGitProjectCode;

/**
 * 工作项处理器类，用于处理来自WebView的工作项请求。
 */
public class WorkItemHandler {

    private static final Logger logger = LoggerFactory.getLogger(WorkItemHandler.class);

    /**
     * 当前项目实例。
     */
    private final Project project;

    /**
     * 父级CodeChatWebView实例。
     */
    private final CodeChatWebview parent;

    /**
     * 构造函数，初始化工作项处理器。
     *
     * @param project 当前项目实例
     * @param parent  父级CodeChatWebView实例
     */
    public WorkItemHandler(Project project, CodeChatWebview parent) {
        this.project = project;
        this.parent = parent;
    }

    /**
     * 处理工作项请求。
     *
     * @param request 请求字符串
     */
    public void processWorkItemRequest(String request) {
        // 检查用户是否已登录
        if (LoginService.getLoginStatus() != Constants.LoginStatus_OK) {
            parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(ErrorResponse.getNoLoginResponse(WebViewRspCommand.WORKITEM_RESPONSE)));
            return;
        }

        // 解析webView请求
        WorkItemRequest workItemRequest = JsonUtil.getInstance().fromJson(request, WorkItemRequest.class);
        String reqType = workItemRequest.getData().getReqType();

        switch (reqType) {
            // 查询工作项
            case WorkItemEventType.SEARCH_WORK_ITEMS:
                ApplicationManager.getApplication().executeOnPooledThread(() -> {
                    // webView响应，工作项列表
                    WorkItemResponseData workItemResponseData;

                    String projectCode = getOriginGitProjectCode(project);
                    if (StringUtils.isBlank(projectCode)) {
                        workItemResponseData = new WorkItemResponseData(WorkItemEventType.SEARCH_WORK_ITEMS, null, "当前工程未关联研发云项目，无法获取工作项");
                    } else {
                        // 网络请求，获取工作项列表
                        WorkItemListResponse workItemListResponse = WorkItemCommHandler.getWorkItemList(projectCode, workItemRequest.getData().getSearchParam());

                        if (workItemListResponse.getCode() == RtnCode.SUCCESS) {
                            if (StringUtils.isBlank(workItemRequest.getData().getSearchParam()) && CollectionUtils.isEmpty(workItemListResponse.getData())) {
                                workItemResponseData = new WorkItemResponseData(WorkItemEventType.SEARCH_WORK_ITEMS, null, "当前项目不存在指派给你的工作项");
                            } else {
                                workItemResponseData = new WorkItemResponseData(WorkItemEventType.SEARCH_WORK_ITEMS, workItemListResponse.getData(), null);
                            }
                        } else {
                            logger.error("[cf]getWorkItemList error:{}", workItemListResponse.getMsg());
                            workItemResponseData = new WorkItemResponseData(WorkItemEventType.SEARCH_WORK_ITEMS, null, "获取工作项失败，请重试");
                        }
                    }
                    WorkItemResponse workItemResponse = new WorkItemResponse(WebViewRspCommand.WORKITEM_RESPONSE, workItemResponseData);
                    parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(workItemResponse));
                });
                break;
            case WorkItemEventType.OPEN_GIT_PANEL:
                ProjectLevelVcsManager vcsManager = ProjectLevelVcsManager.getInstance(project);
                // 1、先判断是否git项目
                if (vcsManager.checkVcsIsActive(GitVcs.NAME)) {
                    // 2、再判断git面板设置模式
                    CommitMode mode = CommitModeManager.getInstance(project).getCurrentCommitMode();
                    if (mode instanceof CommitMode.NonModalCommitMode) {
                        ToolWindow gitWindow = ToolWindowManager.getInstance(project).getToolWindow("Commit");
                        if (Objects.isNull(gitWindow)) {
                            MessageBalloonNotificationUtil.showCommonNotification(project, "当前IDE版本较低，请手动打开Git面板");
                        } else {
                            SwingUtilities.invokeLater(() -> {
                                gitWindow.show(null);
                            });
                        }
                    } else {
                        // ModalCommitModel 的情况
                        // fixme:调研拉起Dialog形式的提交框，一期先弹通知
                        MessageBalloonNotificationUtil.showCommonNotification(project, "请在“设置-版本控制-提交”处，勾选“使用非模式提交界面”选项，才可以打开提交页面");
                    }
                } else {
                    MessageBalloonNotificationUtil.showCommonNotification(project, "该项目未托管至Git代码库，无法提交");
                }
                break;
            default:
                break;
        }
    }
}
