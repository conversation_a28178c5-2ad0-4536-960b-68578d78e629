package com.srdcloud.ideplugin.general.enums;

/**
 * <AUTHOR> yangy
 * @create 2024/5/15 11:55
 */
public enum QuestionType {
    NEW_ASK("newAsk","对话下新的提问"),

    RE_ASK("reAsk","对话下原有问题重新提问");

    private String name;
    private String description;

    private QuestionType(String name, String description) {
        this.name = name;
        this.description = description;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
