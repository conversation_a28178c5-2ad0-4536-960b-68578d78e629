package com.srdcloud.ideplugin.webview.codechat.composer.request;

import com.srdcloud.ideplugin.webview.base.domain.WebViewCommand;

public class ComposerRequest extends WebViewCommand {

    public static final String REQ_TYPE_GET_CHAT_CONTEXTS = "getchatcontexts";
    public static final String REQ_TYPE_COMPOSER_CHAT = "composerchat";
    public static final String REQ_TYPE_STOP_COMPOSER_CHAT = "stopcomposerchat";
    public static final String REQ_TYPE_REPORT_DIFF_FILES = "reportdifffiles";
    public static final String REQ_TYPE_OPEN_DIFF_VIEW_VERTICAL = "opendiffviewvertical";
    public static final String REQ_TYPE_ACCEPT_FILE_DIFF = "acceptfilediff";
    public static final String REQ_TYPE_REJECT_FILE_DIFF = "rejectfilediff";
    public static final String REQ_TYPE_LOAD_HISTORY = "loadhistory";
    public static final String REQ_TYPE_DELETE_HISTORY = "deletehistory";
    public static final String REQ_TYPE_UNDO_FILE_DIFF = "undofilediff";


    private ComposerRequestData data;

    public ComposerRequest(String command, ComposerRequestData data) {
        this.command = command;
        this.data = data;
    }

    public void setData(ComposerRequestData data) {
        this.data = data;
    }

    public ComposerRequestData getData() {
        return data;
    }
}
