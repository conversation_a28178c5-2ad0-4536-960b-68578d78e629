{"name": "codefree-ide-tree-sitter", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force"}, "dependencies": {"vite-plugin-singlefile": "^2.0.3", "vue": "^3.5.12", "web-tree-sitter": "^0.24.4"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/node": "^22.9.0", "@vitejs/plugin-vue": "^5.1.4", "@vitejs/plugin-vue-jsx": "^4.0.1", "@vue/tsconfig": "^0.5.1", "npm-run-all2": "^7.0.1", "tree-sitter-java": "^0.23.3", "typescript": "~5.6.3", "vite": "^5.4.10", "vite-plugin-vue-devtools": "^7.5.4", "vue-tsc": "^2.1.10"}}