package com.srdcloud.ideplugin.webview.codechat.conversation.request;

/**
 * <AUTHOR>
 * @date 2025/1/21
 */
public class ChatHistoryRequest {
    private Integer pageNum;
    private Integer pageDataCount;
    private String title;
    private String subService;
    private String subServices;
    private String dialogId;
    private Boolean isFromSearch;

    // 无参构造函数
    public ChatHistoryRequest() {
    }

    // 带参构造函数
    public ChatHistoryRequest(Integer pageNum, Integer pageDataCount, String title, String subService, String subServices, String dialogId, Boolean isFromSearch) {
        this.pageNum = pageNum;
        this.pageDataCount = pageDataCount;
        this.title = title;
        this.subService = subService;
        this.subServices = subServices;
        this.dialogId = dialogId;
        this.isFromSearch = isFromSearch;
    }

    // Getter 和 Setter 方法
    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageDataCount() {
        return pageDataCount;
    }

    public void setPageDataCount(Integer pageDataCount) {
        this.pageDataCount = pageDataCount;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubService() {
        return subService;
    }

    public void setSubService(String subService) {
        this.subService = subService;
    }

    public String getSubServices() {
        return subServices;
    }

    public void setSubServices(String subServices) {
        this.subServices = subServices;
    }

    public String getDialogId() {
        return dialogId;
    }

    public void setDialogId(String dialogId) {
        this.dialogId = dialogId;
    }

    public Boolean getIsFromSearch() {
        return isFromSearch;
    }

    public void setIsFromSearch(Boolean isFromSearch) {
        this.isFromSearch = isFromSearch;
    }
}
