package com.srdcloud.ideplugin.service;

import com.intellij.openapi.progress.ProgressIndicator;
import com.intellij.openapi.progress.Task.Backgroundable;
import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.general.utils.CheckUpdateForSecUtil;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;



/**
 * 更新检查后台线程
 *
 * <AUTHOR>
 */
public class VersionCheckerForSecTask extends Backgroundable {
    private static final Logger logger = LoggerFactory.getLogger(VersionCheckerForSecTask.class);
    private Project project;

    public VersionCheckerForSecTask(Project project) {
        super(project, "Checking for updates", true);
        this.project = project;
    }

    @Override
    public void run(@NotNull ProgressIndicator indicator) {

        try {
            CheckUpdateForSecUtil.setTimer(project);
            CheckUpdateForSecUtil.autoCheckUpdateAtStart(project);
        } catch (Exception e) {
            logger.error("VersionCheckerForSecTask occur error: ");
            e.printStackTrace();
        }

    }

}

