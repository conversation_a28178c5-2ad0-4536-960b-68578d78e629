package com.srdcloud.ideplugin.assistant.codechatNative.uicomponent;

import com.intellij.openapi.Disposable;
import com.intellij.ui.ColorUtil;
import com.intellij.ui.HyperlinkAdapter;
import com.intellij.ui.JBColor;
import com.srdcloud.ideplugin.general.utils.HtmlUtil;
import com.srdcloud.ideplugin.general.utils.IdeUtil;
import com.srdcloud.ideplugin.general.utils.StringUtil;
import com.srdcloud.ideplugin.general.utils.UIUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import javax.swing.event.HyperlinkEvent;
import javax.swing.text.html.HTMLEditorKit;
import javax.swing.text.html.StyleSheet;
import java.awt.*;

/**
 * @author: yangy
 * @date: 2023/7/3 10:18
 * @Desc 文本内容查看器
 */
public class TextViewer extends MessageViewer implements Disposable {
    private static final Logger logger = LoggerFactory.getLogger(TextViewer.class);

    /**
     * 富文本编辑器面板：支持html标签等解析渲染
     */
    private JEditorPane editorPane = new JEditorPane();

    public TextViewer() {
        this.editorPane.setContentType("text/html; charset=UTF-8");
        this.editorPane.setOpaque(false);
        this.editorPane.setEditable(false);
        this.editorPane.putClientProperty("JEditorPane.honorDisplayProperties", Boolean.TRUE);
        this.editorPane.setFont(IdeUtil.getIDELabelFont());
        this.editorPane.setDoubleBuffered(true);
        String hexString = UIUtil.judgeBackgroudDarkTheme() ? "373a3d" : "eaeef7";
        StyleSheet styleSheet = new StyleSheet();
        String var10000 = ColorUtil.toHex(JBColor.namedColor("Link.activeForeground", JBColor.namedColor("link.foreground", 0x589DF6)));
        String bgColor = "#" + var10000;
        String codeColor = UIUtil.judgeBackgroudDarkTheme() ? "ea951f" : "000080";
        styleSheet.addRule("a { color: " + bgColor + "; text-decoration: none;}");
        styleSheet.addRule("code {font-weight:bold;border-left: 2px solid #" + hexString + ";border-right: 2px solid #" + hexString + "; display: inline-block; color: #" + codeColor + "; margin: 0 0px;background: #" + hexString + ";}");
        HTMLEditorKit htmlKit = new HTMLEditorKit();
        htmlKit.setStyleSheet(styleSheet);
        this.editorPane.setEditorKit(htmlKit);

//      页面点击效果
        this.editorPane.addHyperlinkListener(new HyperlinkAdapter() {
            @Override
            protected void hyperlinkActivated(HyperlinkEvent e) {
                try {
                    Desktop.getDesktop().browse(e.getURL().toURI());
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        });
    }

    public void setText(String text, boolean lastLine) {
        if (StringUtil.isEmpty(text)) {
            text = ".";
        }

        this.editorPane.setText(HtmlUtil.addHtmlBodyTag(text));
    }

    //==========getter/setter=============
    public JEditorPane getEditorPane() {
        return this.editorPane;
    }

    @Override
    public void dispose() {
        this.editorPane = null;
    }
}
