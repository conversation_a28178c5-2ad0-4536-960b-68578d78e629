package com.srdcloud.ideplugin.webview.codechat.login;

import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.service.LoginService;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;

/**
 * 登录请求处理类，用于处理登录相关的请求。
 */
public class LoginRequestHandler {

    /**
     * 当前项目实例。
     */
    private final Project project;

    /**
     * 所属 CodeChat。
     */
    private final CodeChatWebview parent;

    /**
     * 构造函数，初始化登录请求处理类。
     *
     * @param project 当前项目实例
     * @param parent  父级 CodeChatWebview 实例
     */
    public LoginRequestHandler(Project project, CodeChatWebview parent) {
        this.project = project;
        this.parent = parent;
    }

    /**
     * 处理登录请求。如果用户未登录，则调用登录服务进行登录。
     */
    public void login() {
        if (LoginService.getLoginStatus() != Constants.LoginStatus_OK) {
            LoginService.Login();
        }
    }

    /**
     * 检查用户是否已登录，并返回相应的响应。
     */
    public void checkIfLogin() {
        // 改为io多路复用模式，避免消息轮询占满IDE线程
        LoginCheckTask.getInstance(project).addTaskItem(parent);


        //// 初始化错误响应码，默认为成功
        //ErrorResponseCode errorResponseCode = new ErrorResponseCode(WebViewRspCode.SUCCESS, "");
        //
        //// 获取登录服务实例并检查登录状态
        //if (LoginService.GetInstance().GetLoginStatus() != Constants.LoginStatus_OK) {
        //    // 如果未登录，设置错误响应码
        //    errorResponseCode = new ErrorResponseCode(WebViewRspCode.NOT_LOGIN, ChatTips.NOT_LOGIN);
        //}
        //
        //// 创建错误响应对象
        //ErrorResponse errorResponse = new ErrorResponse(WebViewRspCommand.CHECK_IF_LOGIN_RESPONSE, errorResponseCode);
        //
        //// 将响应消息发送到 WebView
        //parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(errorResponse));
    }

}
