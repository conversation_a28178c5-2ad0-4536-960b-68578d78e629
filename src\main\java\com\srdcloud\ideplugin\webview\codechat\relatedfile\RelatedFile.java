package com.srdcloud.ideplugin.webview.codechat.relatedfile;

/**
 * 文件类增强上下文
 * - 关联当前编辑文件
 * - 关联具体文件
 * - 关联目录（下的文件）
 * - 关联当前代码工程（下的文件）
 */
public class RelatedFile {
    private String path;
    private String text = "";
    private Integer startLine;
    private Integer endLine;
    private Boolean codebase; // 是否为@codebase关联文件，默认false
    private String folderPath; // 如果本对象是@folder，则为folder值，否则为空值

    public RelatedFile(String path, String text, Integer startLine, Integer endLine) {
        this.path = path;
        this.text = text;
        this.startLine = startLine;
        this.endLine = endLine;
        codebase = false;
        folderPath = null;
    }

    public RelatedFile(String path, String text, Integer startLine, Integer endLine, Boolean codebase, String folderPath) {
        this.path = path;
        this.text = text;
        this.startLine = startLine;
        this.endLine = endLine;
        this.codebase = codebase;
        this.folderPath = folderPath;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String filePath) {
        this.path = filePath;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public Integer getStartLine() {
        return startLine;
    }

    public void setStartLine(Integer startLine) {
        this.startLine = startLine;
    }

    public Integer getEndLine() {
        return endLine;
    }

    public void setEndLine(Integer endLine) {
        this.endLine = endLine;
    }

    public Boolean getCodebase() {
        return codebase;
    }

    public void setCodebase(Boolean codebase) {
        this.codebase = codebase;
    }

    public String getFolderPath() {
        return folderPath;
    }

    public void setFolderPath(String folderPath) {
        this.folderPath = folderPath;
    }
}
