package com.srdcloud.ideplugin.general.utils;

/**
 * 系统相关工具
 */
public class OsUtil {
    /**
     * 获取当前操作系统
     * @param isConfigName true-用于本地配置文件 false-发给后端用于查询agent版本
     * @return String
     */
    public static String getOs(boolean isConfigName) {
        String osName = System.getProperty("os.name").toLowerCase();
        String os = "";
        if (osName.contains("windows")) {
            os = isConfigName ? "win32" : "windows";
        }else if (osName.contains("mac") || osName.contains("darwin")) {
            os = isConfigName ? "darwin" : "macos";
        }else if (osName.contains("nix") || osName.contains("nux") ||  osName.contains("aix")) {
            os = "linux";
        } else {
            os = "linux";
        }
        return os;
    }

    // 默认返回short name
    public static String getOs() {
        return getOs(true);
    }

    /**
     * 获取当前系统架构
     * @return String
     */
    public static String getArch() {
        String osArch = System.getProperty("os.arch");
        String arch = "";
        if ((osArch.contains("arm") &&  osArch.contains("64")) || osArch.contains("aarch64")) {
            arch = "arm64";
        }else if (osArch.contains("amd64") || osArch.contains("x86_64") ) {
            arch = "x64";
        }else {
            arch = "x64";
        }
        return arch;
    }
}
