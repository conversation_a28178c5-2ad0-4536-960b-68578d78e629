package com.srdcloud.ideplugin.assistant.codechatNative.ui;

import com.intellij.openapi.project.Project;
import org.jetbrains.annotations.NotNull;

import javax.swing.*;


/**
 * <AUTHOR> yangy
 * @create 2023/6/15 10:21
 * 开发问答窗口
 */
public class CodeChatWindow {

  /**
   * 窗体主面板
   */
  private final CodeChatMainPanel panel;

  public CodeChatWindow(@NotNull Project project) {
    panel = new CodeChatMainPanel(project);
  }

  public JPanel getContent() {
    return panel.getMainPanelContent();
  }

  public CodeChatMainPanel getPanel() {
    return panel;
  }

}
