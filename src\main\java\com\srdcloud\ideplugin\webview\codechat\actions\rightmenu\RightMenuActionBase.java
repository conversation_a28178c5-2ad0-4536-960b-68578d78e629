package com.srdcloud.ideplugin.webview.codechat.actions.rightmenu;

import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.assistant.AssistantToolWindow;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.general.enums.ChatMessageType;
import com.srdcloud.ideplugin.general.utils.EditorUtil;
import com.srdcloud.ideplugin.general.utils.MessageBalloonNotificationUtil;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;

/**
 * 右键菜单Action基类
 */
public abstract class RightMenuActionBase extends AnAction {
    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {
        Project project = e.getProject();
        if (project == null) return;

        // 展开编程助手对话窗
        // 无论有无选取代码，都展开一次窗口，触发界面渲染
        AssistantToolWindow.toolWindowVisible(project);

        // 获取当前编辑器选中代码、行号
        String selectCode = EditorUtil.getCurrentSelectedCode(project);
        Integer[] lineNumbers = EditorUtil.getCurrentSelectedFileLineNumber(project);
        Integer startLine = null;
        Integer endLine = null;
        if (lineNumbers != null && lineNumbers.length == 2) {
            startLine = lineNumbers[0];
            endLine = lineNumbers[1];
        }

        if (StringUtils.isBlank(selectCode)) {
            MessageBalloonNotificationUtil.showBalloonNotificationByReason(project, "请选择代码", RtnCode.RtnCode_Not_Select_Text);
            return;
        }

        // 获取当前编辑器选中代码的所在文件的路径
        String filePath = EditorUtil.getCurrentSelectedFilePath(project);
        if (StringUtils.isBlank(filePath)) {
            MessageBalloonNotificationUtil.showBalloonNotificationByReason(project, "请先选择代码", RtnCode.RtnCode_Not_Select_Editor);
            return;
        }

        // 发送右键菜单选中代码到CodeChatWebview进行处理
        CodeChatWebview.getInstance(project).getActionHandler().rightSelectionAction(selectCode, getChatMessageType(), filePath, startLine, endLine);
    }

    protected int getChatMessageType() {
        return ChatMessageType.CHAT_GENERATE.getType();
    }
}
