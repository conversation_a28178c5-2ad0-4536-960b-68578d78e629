package com.srdcloud.ideplugin.remote.client;

import com.alibaba.fastjson.JSON;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import com.srdcloud.ideplugin.general.utils.EnvUtil;
import com.srdcloud.ideplugin.remote.domain.ApiResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 弱网条件快速失败的http通信客户端
 * 用于无法异步请求切阻塞UI渲染的请求使用
 */
public class FastFailHttpClient {

    private static final Logger logger = LoggerFactory.getLogger(FastFailHttpClient.class);

    private static int connectTimeout = 2; //客户端和服务器建立连接的最长时间

    private static int connectionRequestTimeout = 2; //从连接池获取连接的超时时间设为1秒

    private static int socketTimeout = 3;//读取响应数据超时时间

    private static int responseTimeout = 10;//等待服务端返回响应超时时间

    public static ApiResponse doGet(String url, HashMap<String, String> headers) {
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        ApiResponse result = new ApiResponse(RtnCode.OFFLINE, "通信超时");
        try {
            // 通过址默认配置创建一个httpClient实例
            httpClient = HttpClients.createDefault();
            if (EnvUtil.isSec()) {
                try {
                    httpClient = HttpClients.custom()
                            .setSSLContext(SSLContexts.custom()
                                    .loadTrustMaterial(null,(x509Certificates, s) -> true)
                                    .build())
                            .setSSLHostnameVerifier(new NoopHostnameVerifier())
                            .build();
                } catch (Exception ignored) {}
            }
            // 创建httpGet远程连接实例
            HttpGet httpGet = new HttpGet(url);
            // 设置请求头信息，鉴权
            if (null != headers && !headers.isEmpty()) {
                for (String key : headers.keySet()) {
                    httpGet.setHeader(key, headers.get(key));
                }
            }
            // 设置配置请求参数
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(connectTimeout * 1000)// 连接主机服务超时时间
                    .setConnectionRequestTimeout(connectionRequestTimeout * 1000)// 请求超时时间
                    .setSocketTimeout(socketTimeout * 1000)// 数据读取超时时间
                    .build();
            // 为httpGet实例设置配置
            httpGet.setConfig(requestConfig);
            // 执行get请求得到返回对象
//            DebugLogUtil.info(String.format("doGet begin:%s", url));

            // 使用CompletableFuture包装同步请求
            CloseableHttpClient finalHttpClient = httpClient;
            CompletableFuture<CloseableHttpResponse> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return finalHttpClient.execute(httpGet);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            // 设置响应返回超时时间
            response = future.orTimeout(responseTimeout, TimeUnit.SECONDS)
                    .exceptionally(ex -> {
                        logger.warn("FastFailHttpClient doGet error:\n");
                        ex.printStackTrace();
                        return null;
                    }).join();

            if (response == null) {
                logger.warn("FastFailHttpClient doGet url:{} response timeout.", url);
                return result;
            }

            // 处理响应...
            int status = response.getStatusLine().getStatusCode();
            HttpEntity entity = response.getEntity();
            String respStr = EntityUtils.toString(entity);


            DebugLogUtil.info(String.format("doGet end url:%s , respStr:%s", url, respStr));


            if (status == 200 || status == 201) {
                // 通过EntityUtils中的toString方法将结果转换为字符串
                result = new ApiResponse(RtnCode.SUCCESS, respStr);
            } else if (status == 401) {
                // 未登录
                logger.error("[cf] HttpClient doGet fail by oauth,url:{},status:{},respStr:{}", url, status, respStr);
                result = new ApiResponse(RtnCode.NOT_LOGIN, respStr);
            } else if (status == 403) {
                // 用户被禁用
                logger.error("[cf] HttpClient doGet fail by oauth,url:{},status:{},respStr:{}", url, status, respStr);
                result = new ApiResponse(RtnCode.USER_FORBIDDEN, respStr);
            } else {
                logger.error("[cf] HttpClient doGet fail,url:{},status:{},respStr:{}", url, status, respStr);
                result = new ApiResponse(RtnCode.SERVER_DOWN, respStr);
            }

        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("[cf] HttpClient doGet send http request to server error, exception=" + e.getMessage());
            result = new ApiResponse(RtnCode.SEND_ERROR, e.getMessage());
        } finally {
            // 关闭资源
            if (null != response) {
                try {
                    response.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (null != httpClient) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return result;
    }


    public static ApiResponse doPost(String url, String body, HashMap<String, String> headers) {
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        ApiResponse result = new ApiResponse(RtnCode.OFFLINE, "通信超时");
        // 创建httpClient实例
        httpClient = HttpClients.createDefault();
        // 创建httpPost远程连接实例
        HttpPost httpPost = new HttpPost(url);
        // 配置请求参数实例
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(connectTimeout * 1000)// 连接主机服务超时时间
                .setConnectionRequestTimeout(connectionRequestTimeout * 1000)// 请求超时时间
                .setSocketTimeout(socketTimeout * 1000)// 数据读取超时时间
                .build();
        // 为httpPost实例设置配置
        httpPost.setConfig(requestConfig);

        // 设置请求头
        httpPost.addHeader("Content-Type", "application/json;charset=utf8");
        if (null != headers && !headers.isEmpty()) {
            for (String key : headers.keySet()) {
                httpPost.setHeader(key, headers.get(key));
            }
        }

        // 为httpPost设置请求体
        if (StringUtils.isNotBlank(body)) {
            httpPost.setEntity(new StringEntity(body, StandardCharsets.UTF_8));
        }

        try {
            DebugLogUtil.info(String.format("doPost begin url:%s", url));


            // 使用CompletableFuture包装同步请求
            CloseableHttpClient finalHttpClient = httpClient;
            CompletableFuture<CloseableHttpResponse> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return finalHttpClient.execute(httpPost);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            // 设置响应返回超时时间
            httpResponse = future.orTimeout(responseTimeout, TimeUnit.SECONDS)
                    .exceptionally(ex -> {
                        logger.warn("FastFailHttpClient doPost url:{},error:{}", url, ex.getMessage());
                        return null;
                    }).join();

            if (httpResponse == null) {
                logger.warn("FastFailHttpClient doPost url:{} response timeout.", url);
                return result;
            }

            // 处理响应...
            int status = httpResponse.getStatusLine().getStatusCode();
            HttpEntity entity = httpResponse.getEntity();
            String responseStr = EntityUtils.toString(entity);

            DebugLogUtil.info(String.format("doPost end url:%s , respStr:%s", url, responseStr));

            if (status == 200 || status == 201) {
                // 从响应对象中获取响应内容
                result = new ApiResponse(RtnCode.SUCCESS, responseStr);
            } else if (status == 401) {
                // 未登录
                logger.error("[cf] HttpClient doPost fail by oauth,url:{},status:{},respStr:{}", url, status, responseStr);
                result = new ApiResponse(RtnCode.NOT_LOGIN, responseStr);
            } else if (status == 403) {
                // 用户被禁用
                logger.error("[cf] HttpClient doPost fail by oauth,url:{},status:{},respStr:{}", url, status, responseStr);
                result = new ApiResponse(RtnCode.USER_FORBIDDEN, responseStr);
            } else {
                logger.error("[cf] HttpClient doPost fail,url:{},status:{},respStr:{}", url, status, responseStr);
                result = new ApiResponse(RtnCode.SERVER_DOWN, responseStr);
            }
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("[cf] HttpClient doPost send http request to server error, exception=" + e.getMessage());
            result = new ApiResponse(RtnCode.SEND_ERROR, e.getMessage());
        } finally {
            // 关闭资源
            if (null != httpResponse) {
                try {
                    httpResponse.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (null != httpClient) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return result;
    }

    public static ApiResponse doDelete(String url, HashMap<String, String> headers) {
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        ApiResponse result = new ApiResponse(RtnCode.OFFLINE, "通信超时");
        // 创建httpClient实例
        httpClient = HttpClients.createDefault();
        // 创建HttpDelete远程连接实例
        HttpDelete httpDelete = new HttpDelete(url);
        // 配置请求参数实例
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(connectTimeout * 1000)// 连接主机服务超时时间
                .setConnectionRequestTimeout(connectionRequestTimeout * 1000)// 请求超时时间
                .setSocketTimeout(socketTimeout * 1000)// 数据读取超时时间
                .build();
        // 为httpPost实例设置配置
        httpDelete.setConfig(requestConfig);

        // 设置请求头
        httpDelete.addHeader("Content-Type", "application/json;charset=utf8");
        if (null != headers && !headers.isEmpty()) {
            for (String key : headers.keySet()) {
                httpDelete.setHeader(key, headers.get(key));
            }
        }

        try {
            DebugLogUtil.info(String.format("doDelete begin url:%s", url));

            // 使用CompletableFuture包装同步请求
            CloseableHttpClient finalHttpClient = httpClient;
            CompletableFuture<CloseableHttpResponse> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return finalHttpClient.execute(httpDelete);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            // 设置响应返回超时时间
            httpResponse = future.orTimeout(responseTimeout, TimeUnit.SECONDS)
                    .exceptionally(ex -> {
                        logger.warn("FastFailHttpClient doDelete error:{}", ex.getMessage());
                        return null;
                    }).join();

            if (httpResponse == null) {
                logger.warn("FastFailHttpClient doDelete url:{} response timeout.", url);
                return result;
            }

            // 处理响应...
            int status = httpResponse.getStatusLine().getStatusCode();
            HttpEntity entity = httpResponse.getEntity();
            String responseStr = EntityUtils.toString(entity);
            DebugLogUtil.info(String.format("doDelete end url:%s , respStr:%s", url, responseStr));

            if (status == 200 || status == 201) {
                // 从响应对象中获取响应内容
                result = new ApiResponse(RtnCode.SUCCESS, responseStr);
            } else if (status == 401) {
                // 未登录
                logger.warn("[CodeFree] HttpClient doDelete fail by oauth,url:{},header:{},status:{},respStr:{}", url, JSON.toJSONString(headers), status, responseStr);
                result = new ApiResponse(RtnCode.NOT_LOGIN, responseStr);
            } else if (status == 403) {
                // 未登录
                logger.warn("[CodeFree] HttpClient doDelete fail by oauth,url:{},header:{},status:{},respStr:{}", url, JSON.toJSONString(headers), status, responseStr);
                result = new ApiResponse(RtnCode.USER_FORBIDDEN, responseStr);
            } else {
                logger.error("[CodeFree] HttpClient doDelete fail,url:{},header:{},status:{},respStr:{}", url, JSON.toJSONString(headers), status, responseStr);
                result = new ApiResponse(RtnCode.SERVER_DOWN, responseStr);
            }
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("[CodeFree] HttpClient doDelete send http request to server error, exception=" + e.getMessage());
            result = new ApiResponse(RtnCode.SEND_ERROR, e.getMessage());
        } finally {
            // 关闭资源
            if (null != httpResponse) {
                try {
                    httpResponse.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (null != httpClient) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return result;
    }
}