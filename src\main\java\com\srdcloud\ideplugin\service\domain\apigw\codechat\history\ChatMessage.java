package com.srdcloud.ideplugin.service.domain.apigw.codechat.history;

import com.srdcloud.ideplugin.codechat.domain.MultiTypeContent;

import java.util.List;

/**
 * @author: yangy
 * @date: 2024/5/13 16:40
 * @Desc
 */
public class ChatMessage {

    /**
     * 写入接口中无需携带，除了首个提问为空外，由服务存入mongo时自动赋值，
     * 若该消息role=assistant，则该值为上表questions字段某个元素的reqId；
     * 若该消息role=user，则该值为上表answers字段某个元素的reqId
     */
    private String parentReqId;

    /**
     * 本对象的角色，可取值为：
     * system - 本对象为system prompt
     * user - 本对象为用户提问prompt
     * assistant - 本对象为模型回答prompt
     */
    private String role;

    /**
     * 本对象的内容，可携带多媒体内容的信息，当内容为纯文本时，数组只有一个元素，元素type取值为text
     */
    private List<MultiTypeContent> content;

    /**
     * 本轮问答的id号，用于关联提问和回答，以及后续关联统计等目的
     */
    private String reqId;

    /**
     * 产生本对话的时间，当role=user时为必选，格式为yyyy-MM-dd HH:mm:ss
     */
    private String reqTime;;

    /**
     * 承担本轮回答的模型名称，当role=assistant时为必选
     */
    private String modelName;

    /**
     * 用户对本轮回答的反馈评价，当role=assistant时为必选，可选值为：
     * none - 用户无反馈
     * like - 用户点赞
     * unlike - 用户点踩
     */
    private String feedback;

    /**
     * 本对话处理时长，当role=assistant时为必选，单位毫秒
     */
    private int duration;

    /**
     * 对话内容安全检查结果，取值为：
     * 0 - 安全检查没通过
     * 1 - 安全检查通过
     */
    private int safeCheckResult;

    /**
     * 当safeCheckResult=fail时必选，传递触发内容检查不通过的内容
     */
    private String failContent;

    public String getParentReqId() {
        return parentReqId;
    }

    public void setParentReqId(String parentReqId) {
        this.parentReqId = parentReqId;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public List<MultiTypeContent> getContent() {
        return content;
    }

    public void setContent(List<MultiTypeContent> content) {
        this.content = content;
    }

    public String getReqId() {
        return reqId;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    public String getReqTime() {
        return reqTime;
    }

    public void setReqTime(String reqTime) {
        this.reqTime = reqTime;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getFeedback() {
        return feedback;
    }

    public void setFeedback(String feedback) {
        this.feedback = feedback;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public int getSafeCheckResult() {
        return safeCheckResult;
    }

    public void setSafeCheckResult(int safeCheckResult) {
        this.safeCheckResult = safeCheckResult;
    }

    public String getFailContent() {
        return failContent;
    }

    public void setFailContent(String failContent) {
        this.failContent = failContent;
    }
}
