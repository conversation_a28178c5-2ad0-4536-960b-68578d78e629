package com.srdcloud.ideplugin.webview.codechat.getideutils;

import com.intellij.diff.editor.ChainDiffVirtualFile;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.fileEditor.FileEditorManagerListener;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.LocalFileSystem;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.openapi.vfs.VirtualFileManager;
import com.intellij.openapi.vfs.newvfs.BulkFileListener;
import com.intellij.openapi.vfs.newvfs.events.*;
import com.srdcloud.ideplugin.general.utils.*;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;
import com.srdcloud.ideplugin.webview.codechat.common.GetIdeUtilsReqType;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCode;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCommand;
import com.srdcloud.ideplugin.webview.codechat.getideutils.domain.DirItem;
import com.srdcloud.ideplugin.webview.codechat.getideutils.domain.FileNode;
import com.srdcloud.ideplugin.webview.codechat.getideutils.domain.RecentlyUsedFile;
import com.srdcloud.ideplugin.webview.codechat.getideutils.request.*;
import com.srdcloud.ideplugin.webview.codechat.getideutils.response.*;
import com.srdcloud.ideplugin.webview.codechat.relatedfile.RelatedFile;
import com.srdcloud.ideplugin.webview.codechat.relatedfile.request.OpenTextDocumentRequest;
import com.srdcloud.ideplugin.webview.codechat.relatedfile.response.OpenTextDocumentResponse;
import com.srdcloud.ideplugin.webview.codechat.relatedfile.response.OpenTextDocumentResponseData;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Stack;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.locks.ReentrantLock;

public class GetIdeUtilsHandler {
    private static final Logger logger = LoggerFactory.getLogger(GetIdeUtilsHandler.class);

    private final CodeChatWebview parent;
    private final Project project;

    // 关联文件缓存管理
    private static final int MAX_RELATED_FILE_SIZE = 20;
    private final List<VirtualFile> recentlyVirtualFiles = new Stack<>();
    private FileNode cachedFiles;
    private List<DirItem> cachedDirItems;
    private final ReentrantLock cachedFilesUpdateLock = new ReentrantLock();
    private final ReentrantLock dirItemsUpdateLock = new ReentrantLock();

    public GetIdeUtilsHandler(Project project, CodeChatWebview parent) {
        this.parent = parent;
        this.project = project;

        addFileChangeListener();

        // 初始化缓存文件树和目录列表
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            try {
                cachedFiles = convertFileTree();
                cachedDirItems = new ArrayList<>();
                recursiveDirItems(cachedFiles, cachedDirItems);
            } catch (Exception e) {
                logger.error("[cf] Init cachedFiles and cachedDirItems error", e);
                throw new RuntimeException("[cf] Fail to init cachedFiles and cachedDirItems: ", e);
            }
        });
    }

    /**
     * 递归过滤FileNode中children列表里的null元素
     */
    private void filterNullChildren(FileNode node) {
        if (node == null || node.getChildren() == null) {
            return;
        }

        // 移除null元素
        node.getChildren().removeIf(Objects::isNull);

        // 递归处理每个子节点
        for (FileNode child : node.getChildren()) {
            filterNullChildren(child);
        }
    }

    private void addFileLRUCacheItemWithExcludeCheck(VirtualFile file) {
        DebugLogUtil.println("add file" + file.getName());
        if (ExclusionRules.isExcludeFile(file.getName())) {
            logger.warn("[cf] addFileLRUCacheItemWithExcludeCheck skip:" + file.getName());
            return;
        }
        recentlyVirtualFiles.add(0, file);
    }

    // 抽成全局类
    public void addFileChangeListener() {
        ApplicationManager.getApplication().getMessageBus().connect(parent).subscribe(FileEditorManagerListener.FILE_EDITOR_MANAGER, new FileEditorManagerListener() {
            @Override
            public void fileOpened(@NotNull FileEditorManager source, @NotNull VirtualFile file) {

                // 非同一个项目不共享最近打开列表
                if (source.getProject() != project) {
                    return;
                }

                // 同一个项目更新打开列表
                if (recentlyVirtualFiles.contains(file)) {
                    recentlyVirtualFiles.remove(file);
                    addFileLRUCacheItemWithExcludeCheck(file);
                } else {
                    if (recentlyVirtualFiles.size() >= MAX_RELATED_FILE_SIZE) {
                        recentlyVirtualFiles.remove(recentlyVirtualFiles.size() - 1);
                    }
                    addFileLRUCacheItemWithExcludeCheck(file);
                }
            }

            @Override
            public void fileClosed(@NotNull FileEditorManager source, @NotNull VirtualFile file) {
                // 非同一个项目不共享最近打开列表
                if (source.getProject() != project) {
                    return;
                }

                // diff文件不支持打开
                if (file instanceof ChainDiffVirtualFile) {
                    recentlyVirtualFiles.remove(file);
                }
            }
        });

        // 监听文件夹变化
        ApplicationManager.getApplication().getMessageBus().connect(parent).subscribe(VirtualFileManager.VFS_CHANGES, new BulkFileListener() {
            @Override
            public void after(@NotNull List<? extends @NotNull VFileEvent> events) {
                try {
                    for (VFileEvent event : events) {
                        if (event instanceof VFileDeleteEvent) {
                            recentlyVirtualFiles.remove(event.getFile());
                        }
                        updateCachedFiles(event);
                    }
                } catch (Exception e) {
                    // 捕获异常，防止影响EDT线程
                    logger.error("[cf] Uncaught exception in VFS change listener", e);
                }
            }
        });
    }

    public void processGetIdeUtilsRequest(String request) {
        GetIdeUtilsRequest getIdeUtilsRequest = JsonUtil.getInstance().fromJson(request, GetIdeUtilsRequest.class);
        GetIdeUtilsRequestData getIdeUtilsRequestData = getIdeUtilsRequest.getData();

        if (getIdeUtilsRequestData.getReqType().equals(GetIdeUtilsReqType.GET_CHAT_CONTEXTS)) {
            // 获取需要的文件树根目录
            FileNode files = cachedFiles;

            int currentIndex = -1;
            // 先处理当前打开文件：如果有则移至栈顶，且标记下标；
            if (Objects.nonNull(FileEditorManager.getInstance(project).getSelectedEditor())) {
                VirtualFile selectedFile = Objects.requireNonNull(FileEditorManager.getInstance(project).getSelectedEditor()).getFile();
                if (selectedFile != null && !ExclusionRules.isExcludeFile(selectedFile.getName())) {
                    recentlyVirtualFiles.remove(selectedFile);
                    recentlyVirtualFiles.add(0, selectedFile);
                    currentIndex = 0;
                }
            }

            // 将缓存的文件加入列表中
            List<RecentlyUsedFile> recentFiles = new ArrayList<>();
            for (VirtualFile file : recentlyVirtualFiles) {
                RecentlyUsedFile recentFile = new RecentlyUsedFile(file.getName(), file.getPath(), FileUtil.getRelativePath(file, project), file.getLength());
                recentFiles.add(recentFile);
            }
            // 获取当前工程文件目录列表，从前一步files中获取，已经按照ExclusionRules进行了排除
            List<DirItem> dirItems = cachedDirItems;

            // 构造response, 并发送回复
            GetIdeUtilsResponseData getIdeUtilsResponseData = new GetIdeUtilsResponseData(GetIdeUtilsReqType.GET_CHAT_CONTEXTS, recentFiles, files, dirItems, "", currentIndex);
            GetIdeUtilsResponse getIdeUtilsResponse = new GetIdeUtilsResponse(WebViewRspCommand.GET_IDE_UTILS_RESPONSE, getIdeUtilsResponseData);
            DebugLogUtil.println("related util file get" + JsonUtil.getInstance().toJson(getIdeUtilsResponse));
            parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(getIdeUtilsResponse));
        } else {
            logger.error("[cf] processGetIdeUtilsRequest error: unsupported reqType:{}", getIdeUtilsRequestData.getReqType());
        }
    }

    /**
     * 获取文件树
     */
    public FileNode convertFileTree() {
        if (project.getBasePath() == null) {
            logger.error("[cf] In convertFileTree, project.getBasePath() is null");
            return null;
        }

        File projectDir = new File(project.getBasePath());
        String absolutePrefix = project.getBasePath().substring(0, project.getBasePath().lastIndexOf("/"));
        return recursiveFileToNode(projectDir, "", absolutePrefix);
    }

    /**
     * 递归地将File对象直接转换为FileNode对象
     */
    private FileNode recursiveFileToNode(File file, String relativePrefix, String absolutePrefix) {
        String currRelativeDirName = file.getName();
        String currDirName = file.getName();
        
        // 处理相对路径
        String relativePath;
        if (StringUtils.isBlank(relativePrefix)) {
            relativePrefix = ".";
            relativePath = relativePrefix + "/";
        } else {
            relativePath = StringUtils.removeEnd(relativePrefix, "/") + "/" + currRelativeDirName;
        }

        // 构建当前节点
        FileNode node = new FileNode(
            currDirName,
            absolutePrefix + "/" + currDirName,
            relativePath,
            file.length(),
            null
        );

        // 如果是目录且有子文件，递归处理子节点
        if (file.isDirectory()) {
            List<FileNode> children = new ArrayList<>();
            if (file.listFiles() != null) {
                for (File childFile : Objects.requireNonNull(file.listFiles())) {
                    if (ExclusionRules.needExcludeFile(childFile)) {
                        continue;
                    }
                    FileNode childNode = recursiveFileToNode(
                            childFile,
                            relativePath,
                            absolutePrefix + "/" + currDirName
                    );
                    children.add(childNode);
                }
            }
            node.setChildren(children);
        }
        return node;
    }

    /**
     * 递归获取当前工程文件目录列表
     * 对于没有子节点的目录，getChildren为[]。对于文件节点，getChildren为null。
     */
    public void recursiveDirItems(FileNode curr, List<DirItem> dirItems) {
        // 如果根节点为 null 或根节点没有子节点，则直接返回 null，表示没有一级目录
        if (curr == null || curr.getChildren() == null) {
            return;
        }

        // 处理当前节点
        dirItems.add(new DirItem(curr.getName(), curr.getPath(), curr.getRelativePath()));

        // 递归子节点
        for (FileNode child : curr.getChildren()) {
            recursiveDirItems(child, dirItems);
        }
    }

    public void openTextDocument(String request) {
        OpenTextDocumentRequest openTextDocumentRequest = JsonUtil.getInstance().fromJson(request, OpenTextDocumentRequest.class);
        String filePath = openTextDocumentRequest.getData().getFilePath();

        if (filePath != null && !filePath.isEmpty()) {
            VirtualFile file = LocalFileSystem.getInstance().findFileByPath(filePath);

            if (file != null && file.exists()) {
                // 必须在EDT中进行
                ApplicationManager.getApplication().invokeLater(() -> {
                    FileEditorManager.getInstance(project).openFile(file, true);
                });
            } else {
                MessageBalloonNotificationUtil.showCommonNotificationWithConfirm(project, "文件不存在");
            }
        }

        // 回复文件打开行为
        OpenTextDocumentResponseData responseData = new OpenTextDocumentResponseData(WebViewRspCode.SUCCESS);
        OpenTextDocumentResponse getTextDocumentResponse = new OpenTextDocumentResponse(WebViewRspCommand.OPEN_TEXT_DOCUMENT_RESPONSE, responseData);
        parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(getTextDocumentResponse));
    }

    public void fillFileContent(List<RelatedFile> files, int relatedFilesLengthLimit) throws IOException {
        if (files == null || files.isEmpty()) {
            return;
        }

        int countLength = 0;
        for (RelatedFile file : files) {
            String filePath = file.getPath();

            if (filePath != null && !filePath.isEmpty()) {
                String text = FileUtil.getFileContent(filePath);
                if (StringUtils.isBlank(text)) {
                    continue;
                }

                // 长度校验，超长则截断
                if (countLength + text.length() > relatedFilesLengthLimit) {
                    text = text.substring(0, relatedFilesLengthLimit - countLength);
                    file.setText(text);
                    FileExceedLimitResponse response = new FileExceedLimitResponse(WebViewRspCommand.FILE_EXCEED_LIMIT);
                    parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(response));
                    return;
                } else {
                    file.setText(text);
                    countLength += text.length();
                }

                // 20250430版本：截断逻辑会导致@codebase、@folder转换后的提示语文件有截断风险。插件侧不再截断处理，由模型层截断
                file.setText(text);
            }

        }
    }

    /**
     * 更新缓存文件树
     */
    public void updateCachedFiles(VFileEvent event) {
        if (event.getFile() == null){
            return;
        }

        // 按规则排除文件
        if (!(event instanceof VFilePropertyChangeEvent) && ExclusionRules.needExcludeFile(new File(event.getFile().getPath()))) {
            return;
        }

        // 粘贴目录逻辑：按照目录结构，递归调用事件。对于文件夹-VFileCreateEvent，对于文件-VFileCopyEvent
        if (event instanceof VFileDeleteEvent) {
            handleDeleteFile(event.getFile());
        } else if (event instanceof VFileCreateEvent) {
            if(!isCurrentProjectPath(Objects.requireNonNull(event.getFile()).getPath())){
                return;
            }
            handleCreateFile(event.getFile());
        } else if (event instanceof VFileCopyEvent vFileCopyEvent) {
            if(!isCurrentProjectPath(vFileCopyEvent.getNewParent().getPath())){
                return;
            }
            handleCopyFile(event.getFile(), vFileCopyEvent.getNewParent(), vFileCopyEvent.getNewChildName());
        } else if(event instanceof VFileMoveEvent vFileMoveEvent) {
            if(!isCurrentProjectPath(vFileMoveEvent.getNewParent().getPath())){
                return;
            }
            handleMoveFile(event.getFile(), vFileMoveEvent.getOldParent(), vFileMoveEvent.getNewParent());
        } else if(event instanceof VFilePropertyChangeEvent propertyChangeEvent) {
            // 属性变更事件，用于处理改名的情况
            handlePropertyChange(event.getFile(), propertyChangeEvent.getOldPath(), propertyChangeEvent.getNewPath());
        }

        if (event.getFile().isDirectory()) {
            // 目录变化时，异步更新cachedDirItems
            asyncUpdateDirItems();
        }
    }

    /**
     * 判断文件路径是否属于当前项目
     */
    public boolean isCurrentProjectPath(String path) {
        if (project.getBasePath() == null){
            return false;
        }
        return path.startsWith(project.getBasePath());
    }

    /**
     * 异步更新cachedDirItems
     */
    public void asyncUpdateDirItems() {
        CompletableFuture.runAsync(() -> {
            // 加锁，用于应对批量目录变化的情况
            boolean locked = dirItemsUpdateLock.tryLock();
            try {
                if(!locked) {
                    return;
                }
                // sleep 2s，等待本轮目录变化事件都处理完成后再更新cachedDirItems
                Thread.sleep(2000);
                cachedDirItems = new ArrayList<>();
                recursiveDirItems(cachedFiles, cachedDirItems);
            } catch (Exception e) {
                logger.error("[cf] Error updating cachedDirItems after directory change", e);
            } finally {
                if (locked) {
                    dirItemsUpdateLock.unlock();
                }
            }
        });
    }

    /**
     * 处理文件创建事件
     */
    public void handleCreateFile(VirtualFile file) {
        // 系统层不能创建同名文件，暂不做文件同名检查

        FileNode parentNode = findNodeByPath(cachedFiles, file.getParent().getPath());
        if (parentNode == null) {
            // 父节点不存在，直接返回
            DebugLogUtil.warn("Parent node " + file.getParent().getPath() + " is null, skip create file node.");
            return;
        }

        if (parentNode.getChildren() == null) {
            parentNode.setChildren(new ArrayList<>());
        }

        // 目录节点-children为空list，文件节点-children为null
        ArrayList<FileNode> children = null;
        if (file.isDirectory()) {
            children = new ArrayList<>();
        }
        parentNode.getChildren().add(
                new FileNode(
                        file.getName(),
                        file.getPath(),
                        StringUtils.removeEnd(parentNode.getRelativePath(),"/") + "/" + file.getName(),
                        file.getLength(),
                        children
                )
        );
    }

    /**
     * 处理文件拷贝事件
     * @param newChildName 新文件名（复制时可以改名）
     */
    public void handleCopyFile(VirtualFile file, VirtualFile newParentFile, String newChildName){
        // 系统层不能创建同名文件，暂不做文件同名检查

        FileNode parentNode = findNodeByPath(cachedFiles, newParentFile.getPath());
        if(parentNode == null) {
            // 父节点不存在，直接返回
            DebugLogUtil.warn("Parent node" + newParentFile.getPath() + "is null, skip copy file node.");
            return;
        }

        if (parentNode.getChildren() == null) {
            parentNode.setChildren(new ArrayList<>());
        }

        // 目录节点-children为空list，文件节点-children为null
        ArrayList<FileNode> children = null;
        if (file.isDirectory()) {
            children = new ArrayList<>();
        }
        parentNode.getChildren().add(
                new FileNode(
                        newChildName,
                        newParentFile.getPath() + "/" + newChildName,
                        StringUtils.removeEnd(parentNode.getRelativePath(),"/") + "/" + newChildName,
                        file.getLength(),
                        children
                )
        );
    }

    /**
     * 处理文件删除事件
     */
    public void handleDeleteFile(VirtualFile file) {
        FileNode parentNode = findNodeByPath(cachedFiles, file.getParent().getPath());
        if (parentNode != null && parentNode.getChildren() != null) {
            parentNode.getChildren().removeIf(child ->
                    child != null && child.getPath().equals(file.getPath())
            );
        } else {
            DebugLogUtil.info("Parent node null or parent children null, skip delete from cachedFiles. File path: " + file.getPath());
        }
    }

    /**
     * 处理文件移动事件
     */
    public void handleMoveFile(VirtualFile file, VirtualFile oldParentFile, VirtualFile newParentFile) {
        if (file.isDirectory()) {
            // 文件夹移动时，异步重建缓存
            // TODO: 考虑直接递归处理文件夹移动事件
            CompletableFuture.runAsync(() -> {
                DebugLogUtil.info("Directory moved, rebuilding file cache...");
                // 加锁，用于应对批量移动平级文件夹的情况
                boolean locked = cachedFilesUpdateLock.tryLock();
                try {
                    if(!locked) {
                        return;
                    }
                    // sleep 1.5s，等待本轮文件移动事件都处理完成后再重建缓存
                    Thread.sleep(1500);
                    cachedFiles = convertFileTree();
                } catch (Exception e) {
                    logger.error("[cf] Error rebuilding file cache after directory move", e);
                } finally {
                    if (locked) {
                        cachedFilesUpdateLock.unlock();
                    }
                }
            });
        } else {
            FileNode oldParentNode = findNodeByPath(cachedFiles, oldParentFile.getPath());
            FileNode newParentNode = findNodeByPath(cachedFiles, newParentFile.getPath());
            if(oldParentNode != null && oldParentNode.getChildren() != null) {
                oldParentNode.getChildren().removeIf(child -> child.getPath().equals(oldParentFile.getPath() + "/" + file.getName()));
            } else {
                DebugLogUtil.warn("Old parent node or children node null, skip remove from cachedFiles. Old parent path: " + oldParentFile.getPath());
            }
            if(newParentNode != null) {
                if(newParentNode.getChildren() == null) {
                    newParentNode.setChildren(new ArrayList<>());
                }
                newParentNode.getChildren().add(
                        new FileNode(
                                file.getName(),
                                newParentFile.getPath() + "/" + file.getName(),
                                StringUtils.removeEnd(newParentNode.getRelativePath(),"/") + "/" + file.getName(),
                                file.getLength(),
                                null
                        )
                );
            } else {
                DebugLogUtil.warn("New parent node null, skip move from cachedFiles. New parent path: " + newParentFile.getPath());
            }
        }
    }

    /**
     * 处理改名事件
     */
    public void handlePropertyChange(VirtualFile file, String oldPath, String newPath) {
        if (file.isDirectory()) {
            // 重命名文件夹时，异步重建缓存
            // TODO: 考虑直接递归处理文件夹重命名事件
            CompletableFuture.runAsync(() -> {
                DebugLogUtil.info("Directory renamed, rebuilding file cache...");
                try {
                    cachedFiles = convertFileTree();
                } catch (Exception e) {
                    logger.error("[cf] Error rebuilding file cache after directory rename.", e);
                }
            });
        } else {
            FileNode parentNode = findNodeByPath(cachedFiles, file.getParent().getPath());
            if (parentNode == null) {
                DebugLogUtil.warn("Parent node null, skip rename from cachedFiles. Parent path: " + oldPath);
                return;
            }
            // 如果新命名的文件需要排除，则直接从缓存中移除原有节点
            if (ExclusionRules.needExcludeFile(new File(newPath))) {
                DebugLogUtil.info("File need exclude, skip rename from cachedFiles: " + file.getName());
                parentNode.getChildren().removeIf(child -> child.getPath().equals(oldPath));
                return;
            }

            FileNode oldNode = findNodeByPath(cachedFiles, oldPath);
            if (oldNode == null) {
                DebugLogUtil.warn("Old node null, create new node. Old path: " + oldPath);
                parentNode.getChildren().add(
                        new FileNode(
                                file.getName(),
                                parentNode.getPath() + "/" + file.getName(),
                                StringUtils.removeEnd(parentNode.getRelativePath(),"/") + "/" + file.getName(),
                                file.getLength(),
                                null
                        )
                );
                return;
            }
            oldNode.setName(file.getName());
            String oldRelativePath = oldNode.getRelativePath();
            oldNode.setRelativePath(oldRelativePath.substring(0, oldRelativePath.lastIndexOf("/")) + "/" + file.getName());
            oldNode.setPath(newPath);
        }
    }

    /**
     * 根据fullPath找到文件节点
     */
    private FileNode findNodeByPath(FileNode root, String path) {
        if (root == null) {
            return null;
        }

        if (path.equals(root.getPath())) {
            return root;
        }

        if (root.getChildren() != null) {
            for (FileNode child : root.getChildren()) {
                FileNode result = findNodeByPath(child, path);
                if (result != null) {
                    return result;
                }
            }
        }

        return null;
    }
}
