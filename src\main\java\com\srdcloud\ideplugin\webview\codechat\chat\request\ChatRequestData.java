package com.srdcloud.ideplugin.webview.codechat.chat.request;

import com.srdcloud.ideplugin.remote.domain.WorkItem.WorkItemInfo;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.QuoteItem;
import com.srdcloud.ideplugin.codechat.domain.ChatMessageSimple;
import com.srdcloud.ideplugin.webview.codechat.composer.domain.ContextInputItem;
import com.srdcloud.ideplugin.webview.codechat.relatedfile.RelatedFile;

import java.util.List;

public class ChatRequestData {
    private String question;
    private String createTime;
    private String type;
    private String text;
    private String dialogId;
    private String parentReqId;
    private String subService;
    private String templateId;
    private ChatMessageSimple chatContext;
    private String questionAskType;
    private String kbId;
    private QuoteItem[] quote;
    private List<RelatedFile> relatedFiles;
    private List<ContextInputItem> contextInputItems;
    private List<WorkItemInfo> selectedWorkItems;

    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getDialogId() {
        return dialogId;
    }

    public void setDialogId(String dialogId) {
        this.dialogId = dialogId;
    }

    public String getParentReqId() {
        return parentReqId;
    }

    public void setParentReqId(String parentReqId) {
        this.parentReqId = parentReqId;
    }

    public String getSubService() {
        return subService;
    }

    public void setSubService(String subService) {
        this.subService = subService;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public ChatMessageSimple getChatContext() {
        return chatContext;
    }

    public void setChatContext(ChatMessageSimple chatContext) {
        this.chatContext = chatContext;
    }

    public String getQuestionAskType() {
        return questionAskType;
    }

    public void setQuestionAskType(String questionAskType) {
        this.questionAskType = questionAskType;
    }

    public String getKbId() {
        return kbId;
    }

    public void setKbId(String kbId) {
        this.kbId = kbId;
    }

    public QuoteItem[] getQuote() {
        return quote;
    }

    public void setQuote(QuoteItem[] quote) {
        this.quote = quote;
    }

    public List<RelatedFile> getRelatedFiles() {
        return relatedFiles;
    }

    public void setRelatedFiles(List<RelatedFile> relatedFiles) {
        this.relatedFiles = relatedFiles;
    }

    public List<ContextInputItem> getContextInputItems() {
        return contextInputItems;
    }

    public List<WorkItemInfo> getSelectedWorkItems() {
        return selectedWorkItems;
    }

    public void setSelectedWorkItems(List<WorkItemInfo> selectedWorkItems) {
        this.selectedWorkItems = selectedWorkItems;
    }
}
