package com.srdcloud.ideplugin.statusbar

import com.intellij.openapi.util.SystemInfo
import com.intellij.openapi.wm.impl.status.TextPanel
import com.intellij.util.ui.JBFont
import com.intellij.util.ui.JBUI
import org.jetbrains.annotations.Nls
import java.awt.BorderLayout
import javax.swing.BoxLayout
import javax.swing.Icon
import javax.swing.JLabel
import javax.swing.JPanel

class StatusBarPanel : JPanel(BorderLayout()) {
    private var myLabel: TextPanel
    private var myIconLabel: JLabel

    init {
        isOpaque = false
        layout = BoxLayout(this, BoxLayout.LINE_AXIS)
        alignmentY = CENTER_ALIGNMENT

        myIconLabel = JLabel("")
        myIconLabel.border = JBUI.Borders.emptyRight(4)
        add(myIconLabel)

        myLabel = object : TextPanel() {}
        myLabel.font = if (SystemInfo.isMac) JBUI.Fonts.label(11f) else JBFont.label()
        myLabel.recomputeSize()
        add(myLabel)
    }

    fun setText(text: @Nls String) {
        myLabel.text = text
    }

    fun getText(): String? {
        return myLabel.text
    }

    fun setIcon(icon: Icon?) {
        myIconLabel.icon = icon
        myIconLabel.isVisible = icon != null
    }
}