package com.srdcloud.ideplugin.service.domain.component;

/**
 * 制品库组件详情数据结构
 * <AUTHOR>
 */
public class ComponentDetailData {
    private boolean canDel;

    private boolean canPublish;

    private ComponentDTO componentDTO;

    private int contributors;

    private int dependentReposCount;

    private int dependentsCount;

    private String description;

    private String downloadUrl;

    private int forks;

    private String gitHub;

    private String homepage;

    private int quality;

    private int resourceType;

    private int stars;

    public boolean isCanDel() {
        return canDel;
    }

    public void setCanDel(boolean canDel) {
        this.canDel = canDel;
    }

    public boolean isCanPublish() {
        return canPublish;
    }

    public void setCanPublish(boolean canPublish) {
        this.canPublish = canPublish;
    }

    public ComponentDTO getComponentDTO() {
        return componentDTO;
    }

    public void setComponentDTO(ComponentDTO componentDTO) {
        this.componentDTO = componentDTO;
    }

    public int getContributors() {
        return contributors;
    }

    public void setContributors(int contributors) {
        this.contributors = contributors;
    }

    public int getDependentReposCount() {
        return dependentReposCount;
    }

    public void setDependentReposCount(int dependentReposCount) {
        this.dependentReposCount = dependentReposCount;
    }

    public int getDependentsCount() {
        return dependentsCount;
    }

    public void setDependentsCount(int dependentsCount) {
        this.dependentsCount = dependentsCount;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    public int getForks() {
        return forks;
    }

    public void setForks(int forks) {
        this.forks = forks;
    }

    public String getGitHub() {
        return gitHub;
    }

    public void setGitHub(String gitHub) {
        this.gitHub = gitHub;
    }

    public String getHomepage() {
        return homepage;
    }

    public void setHomepage(String homepage) {
        this.homepage = homepage;
    }

    public int getQuality() {
        return quality;
    }

    public void setQuality(int quality) {
        this.quality = quality;
    }

    public int getResourceType() {
        return resourceType;
    }

    public void setResourceType(int resourceType) {
        this.resourceType = resourceType;
    }

    public int getStars() {
        return stars;
    }

    public void setStars(int stars) {
        this.stars = stars;
    }
}
