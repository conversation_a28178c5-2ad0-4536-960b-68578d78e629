import{t as m,g as O,a as v,k as P,u as p,b as w,c as N,w as $,x as o,y as A,z as E}from"./_baseUniq.js";import{a1 as g,S as F,U as M,a8 as _,aj as c,N as l,Q as B,a0 as S,an as T,P as y}from"./index.js";var G=/\s/;function H(n){for(var r=n.length;r--&&G.test(n.charAt(r)););return r}var L=/^\s+/;function R(n){return n&&n.slice(0,H(n)+1).replace(L,"")}var x=0/0,q=/^[-+]0x[0-9a-f]+$/i,C=/^0b[01]+$/i,K=/^0o[0-7]+$/i,Q=parseInt;function U(n){if(typeof n=="number")return n;if(m(n))return x;if(g(n)){var r=typeof n.valueOf=="function"?n.valueOf():n;n=g(r)?r+"":r}if(typeof n!="string")return n===0?n:+n;n=R(n);var t=C.test(n);return t||K.test(n)?Q(n.slice(2),t?2:8):q.test(n)?x:+n}var I=1/0,W=17976931348623157e292;function X(n){if(!n)return n===0?n:0;if(n=U(n),n===I||n===-I){var r=n<0?-1:1;return r*W}return n===n?n:0}function Y(n){var r=X(n),t=r%1;return r===r?t?r-t:r:0}function un(n){var r=n==null?0:n.length;return r?O(n,1):[]}var b=Object.prototype,z=b.hasOwnProperty,D=F(function(n,r){n=Object(n);var t=-1,e=r.length,a=e>2?r[2]:void 0;for(a&&M(r[0],r[1],a)&&(e=1);++t<e;)for(var f=r[t],i=_(f),s=-1,d=i.length;++s<d;){var u=i[s],h=n[u];(h===void 0||c(h,b[u])&&!z.call(n,u))&&(n[u]=f[u])}return n}),hn=D;function gn(n){var r=n==null?0:n.length;return r?n[r-1]:void 0}function J(n){return function(r,t,e){var a=Object(r);if(!l(r)){var f=v(t);r=P(r),t=function(s){return f(a[s],s,a)}}var i=n(r,t,e);return i>-1?a[f?r[i]:i]:void 0}}var Z=Math.max;function V(n,r,t){var e=n==null?0:n.length;if(!e)return-1;var a=t==null?0:Y(t);return a<0&&(a=Z(e+a,0)),p(n,v(r),a)}var j=J(V),vn=j;function k(n,r){var t=-1,e=l(n)?Array(n.length):[];return w(n,function(a,f,i){e[++t]=r(a,f,i)}),e}function xn(n,r){var t=B(n)?N:k;return t(n,v(r))}var nn=Object.prototype,rn=nn.hasOwnProperty;function tn(n,r){return n!=null&&rn.call(n,r)}function In(n,r){return n!=null&&$(n,r,tn)}function an(n,r){return n<r}function en(n,r,t){for(var e=-1,a=n.length;++e<a;){var f=n[e],i=r(f);if(i!=null&&(s===void 0?i===i&&!m(i):t(i,s)))var s=i,d=f}return d}function mn(n){return n&&n.length?en(n,S,an):void 0}function sn(n,r,t,e){if(!g(n))return n;r=o(r,n);for(var a=-1,f=r.length,i=f-1,s=n;s!=null&&++a<f;){var d=A(r[a]),u=t;if(d==="__proto__"||d==="constructor"||d==="prototype")return n;if(a!=i){var h=s[d];u=e?e(h,d,s):void 0,u===void 0&&(u=g(h)?h:T(r[a+1])?[]:{})}y(s,d,u),s=s[d]}return n}function on(n,r,t){for(var e=-1,a=r.length,f={};++e<a;){var i=r[e],s=E(n,i);t(s,i)&&sn(f,o(i,n),s)}return f}export{vn as a,on as b,mn as c,hn as d,en as e,un as f,an as g,In as h,k as i,X as j,gn as l,xn as m,Y as t};
