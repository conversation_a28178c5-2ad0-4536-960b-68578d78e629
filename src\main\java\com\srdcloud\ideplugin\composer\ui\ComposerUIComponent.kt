package com.srdcloud.ideplugin.composer.ui

import com.srdcloud.ideplugin.webview.codechat.composer.response.*

/**
 * Composer UI组件接口
 * 定义了Composer所需的UI组件基本能力
 */
interface ComposerUIComponent {

    /**
     * Composer Chat回答消息回调
     */
    fun onChatResponseChunk(chunk: ComposerResponse)

    /**
     * 停止Composer Chat响应消息回调
     */
    fun sendStopChatResponse(response: ComposerResponse)

    /**
     * 向UI发送diff文件列表消息
     */
    fun sendDiffFiles(diffFiles: ComposerResponse)

    /**
     * 拉起Diff窗口响应消息
     */
    fun sendShowDiffViewsResponse(response: ShowDiffViewResponseData)

    /**
     * 接受diff文件响应消息
     */
    fun sendAcceptFileDiffResponse(response: AccpetFileDiffResponseData)

    /**
     * 拒绝diff文件响应消息
     */
    fun sendRejectFileDiffResponse(response: RejectFileDiffResponseData)

    /**
     * 推送DiffFile变更状态
     */
    fun sendDiffStatusChangedResponse(response: DiffStatusChangedResponse)

    /**
     * 加载历史对话记录消息响应
     */
    fun sendLoadHistoryChatResponse(response: ComposerResponse)

    /**
     * 删除历史对话记录消息响应 
     */
    fun sendDeleteHistoryChatResponse(response: ComposerResponse)
}