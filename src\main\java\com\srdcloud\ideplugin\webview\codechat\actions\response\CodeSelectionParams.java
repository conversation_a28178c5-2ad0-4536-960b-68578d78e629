package com.srdcloud.ideplugin.webview.codechat.actions.response;

/**
 * <AUTHOR>
 * @date 2025/1/8
 * @desc 选中代码提问
 */
public class CodeSelectionParams {
    private int type;
    private String code;
    private String text;
    private String errDesc;
    private boolean isNewConversation;
    private boolean cancelLastQuestion;

    private Integer startLine;
    private Integer endLine;

    private String filePath;

    // 构造函数
    public CodeSelectionParams() {

    }

    public CodeSelectionParams(int type, String code, String text) {
        this.type = type;
        this.code = code;
        this.text = text;
    }

    // 全参数构造函数
    public CodeSelectionParams(int type, String code, String errDesc, boolean isNewConversation, boolean cancelLastQuestion, String filePath) {
        this.type = type;
        this.code = code;
        this.errDesc = errDesc;
        this.isNewConversation = isNewConversation;
        this.cancelLastQuestion = cancelLastQuestion;
        this.filePath = filePath;
    }

    // 全参数构造函数
    public CodeSelectionParams(int type, String code, String errDesc, boolean isNewConversation, boolean cancelLastQuestion, String filePath, Integer startLine, Integer endLine) {
        this.type = type;
        this.code = code;
        this.errDesc = errDesc;
        this.isNewConversation = isNewConversation;
        this.cancelLastQuestion = cancelLastQuestion;
        this.filePath = filePath;
        this.startLine = startLine;
        this.endLine = endLine;
    }

    // Getter 和 Setter 方法
    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getErrDesc() {
        return errDesc;
    }

    public void setErrDesc(String errDesc) {
        this.errDesc = errDesc;
    }

    public boolean isNewConversation() {
        return isNewConversation;
    }

    public void setNewConversation(boolean isNewConversation) {
        this.isNewConversation = isNewConversation;
    }

    public boolean isCancelLastQuestion() {
        return cancelLastQuestion;
    }

    public void setCancelLastQuestion(boolean cancelLastQuestion) {
        this.cancelLastQuestion = cancelLastQuestion;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public Integer getEndLine() {
        return endLine;
    }

    public Integer getStartLine() {
        return startLine;
    }

    public void setEndLine(Integer endLine) {
        this.endLine = endLine;
    }

    public void setStartLine(Integer startLine) {
        this.startLine = startLine;
    }
}

