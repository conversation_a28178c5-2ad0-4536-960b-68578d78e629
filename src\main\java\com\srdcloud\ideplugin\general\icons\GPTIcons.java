package com.srdcloud.ideplugin.general.icons;

import com.intellij.openapi.util.IconLoader;
import com.srdcloud.ideplugin.general.utils.UIUtil;

import javax.swing.*;

/**
 * <AUTHOR> yangy
 * @create 2023/6/21 14:56
 */
public class GPTIcons {
    public static final Icon TOOL_WINDOW_DARK = UIUtil.loadIcon("/icons/sec/secidea_dark_16.svg","/icons/srd_toolWindow_dark.svg");

    public static final Icon EXCEPTION = IconLoader.getIcon("/icons/srd_exception.svg", GPTIcons.class);
    public static final Icon EXCEPTION_DARK = IconLoader.getIcon("/icons/srd_exception_dark.svg", GPTIcons.class);

    public static final Icon LOGO = IconLoader.getIcon("/icons/srd_logo.svg", GPTIcons.class);
    public static final Icon LOGO_DARK = IconLoader.getIcon("/icons/srd_logo_dark.svg", GPTIcons.class);

    public static final Icon CODEFREE = IconLoader.getIcon("/icons/srd_codefree.svg", GPTIcons.class);
    public static final Icon CODEFREE_DARK = IconLoader.getIcon("/icons/srd_codefree_dark.svg", GPTIcons.class);

}
