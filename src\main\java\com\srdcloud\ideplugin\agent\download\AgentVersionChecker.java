package com.srdcloud.ideplugin.agent.download;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.Collections;
import java.util.List;
import java.io.File;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.srdcloud.ideplugin.agent.config.AgentConfig;
import com.srdcloud.ideplugin.agent.model.AgentVersion;
import com.srdcloud.ideplugin.agent.model.VersionCheckResponse;
import com.srdcloud.ideplugin.agent.exception.AgentException;
import com.srdcloud.ideplugin.agent.config.AgentPath;
import com.srdcloud.ideplugin.general.utils.OsUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AgentVersionChecker {

    private static final Logger logger = LoggerFactory.getLogger(AgentVersionChecker.class);

    private final String serverUrl;
    private final String invokerId;
    private final String apiKey;
    private final AgentConfig agentConfig;

    public AgentVersionChecker(String serverUrl, String invokerId, String apiKey, AgentConfig agentConfig) {
        this.serverUrl = serverUrl;
        this.invokerId = invokerId;
        this.apiKey = apiKey;
        this.agentConfig = agentConfig;
    }

    public List<AgentVersion> checkVersions(String pluginType, String pluginVersion, String clientType) {
        HttpClient client = HttpClient.newHttpClient();
        String os = System.getProperty("os.name").toLowerCase();
        String cpu = System.getProperty("os.arch").toLowerCase();

        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create(String.format("%s/plugin-file-server/v1/agent-version-check" +
                "?pluginType=%s&pluginVersion=%s&clientType=%s&os=%s&cpu=%s",
                serverUrl, pluginType, pluginVersion, clientType, os, cpu)))
            .header("invokerId", invokerId)
            .header("apiKey", apiKey)
            .header("Content-Type", "application/json")
            .GET()
            .build();

        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            if (response.statusCode() == 200) {
                // Parse response and return agent versions
                return parseVersionResponse(response.body());
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to check agent versions", e);
        }
        return Collections.emptyList();
    }

    private List<AgentVersion> parseVersionResponse(String responseBody) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            VersionCheckResponse response = mapper.readValue(responseBody, VersionCheckResponse.class);
            
            if (response.getOptResult() != 0) {
                throw new AgentException("Version check failed: " + response.getMsg());
            }
            
            return response.getAdaptedAgentVersions();
        } catch (Exception e) {
            throw new AgentException("Failed to parse version response", e);
        }
    }

    /**
     * 检查是否需要升级agent
     * @param agentVersions 远端请求的版本列表
     * @param agentName agent名称
     * @return 是否需要升级，true表示需要升级，false表示不需要升级
     */
    @Deprecated
    public boolean needsUpgrade(List<AgentVersion> agentVersions, String agentName) {
        if (agentVersions == null || agentVersions.isEmpty() || agentName == null) {
            return false;
        }

        try {
            // 获取config.json文件路径
            AgentPath agentPath = new AgentPath(agentConfig.getPluginType(), agentName);
            String configPath = agentPath.getConfigPath();
            File configFile = new File(configPath);
            
            if (!configFile.exists()) {
                return true; // 如果配置文件不存在，认为需要升级
            }

            // 读取配置文件
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(configFile);
            
            // 获取当前操作系统
            String os = System.getProperty("os.name").toLowerCase();
            String currentOs = os.contains("win") ? "windows" : 
                             os.contains("mac") ? "macos" : "linux";

            // 从配置文件中获取当前agent版本
            String currentVersion = rootNode.path("agent")
                                         .path(agentName)
                                         .path(OsUtil.getOs() + "-" + OsUtil.getArch())
                                         .path("version")
                                         .asText();
            currentVersion = StringUtils.removeStart(currentVersion, "v");

            // 在agentVersions中查找对应的版本信息
            for (AgentVersion version : agentVersions) {
                if (version.getAgentName().equals(agentName)) {
                    String remoteVersion = StringUtils.removeStart(version.getVersion(),"v");
                    // 比较版本号，如果远程版本大于本地版本，则需要升级
                    return compareVersions(remoteVersion, currentVersion) > 0;
                }
            }
            
            return false;
        } catch (Exception e) {
            throw new AgentException("Failed to check agent upgrade status", e);
        }
    }

    private int compareVersions(String version1, String version2) {
        String[] v1Parts = version1.split("\\.");
        String[] v2Parts = version2.split("\\.");
        
        int length = Math.max(v1Parts.length, v2Parts.length);
        
        for (int i = 0; i < length; i++) {
            int v1 = i < v1Parts.length ? Integer.parseInt(v1Parts[i]) : 0;
            int v2 = i < v2Parts.length ? Integer.parseInt(v2Parts[i]) : 0;
            
            if (v1 != v2) {
                return v1 - v2;
            }
        }
        
        return 0;
    }

    /**
     * 检查指定agent的版本是否需要下载
     * 只要在本地配置中不存在该版本，就需要下载
     * 
     * @param agentVersions 远端请求的版本列表（假设只有一个匹配的版本）
     * @param agentName agent名称
     * @return 是否需要下载，true表示需要下载，false表示不需要下载
     */
    public boolean needsDownload(List<AgentVersion> agentVersions, String agentName) {
        if (agentVersions == null || agentVersions.isEmpty() || agentName == null) {
            return false;
        }

        // 查找匹配agentName的版本
        AgentVersion targetVersion = agentVersions.stream()
                .filter(v -> v.getAgentName().equals(agentName))
                .findFirst()
                .orElse(null);

        // 没有找到agentName的匹配版本，说明这个agent不需要更新
        if (targetVersion == null) {
            return false;
        }

        logger.info("[cf] checkDownload: {} for {}", targetVersion.getVersion(), agentName);
        
        try {
            // 获取config.json文件路径
            AgentPath agentPath = new AgentPath(agentConfig.getPluginType(), agentName);
            String configPath = agentPath.getConfigPath();
            File configFile = new File(configPath);
            
            if (!configFile.exists()) {
                return true; // 如果配置文件不存在，需要下载
            }

            // 读取配置文件
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(configFile);
            
            // 获取当前平台标识
            String platform = OsUtil.getOs() + "-" + OsUtil.getArch();
            // 从配置文件中获取已安装的版本列表
            JsonNode agentVersionsNode = rootNode.path("agent").path(agentName).path(platform);
            // 远程版本
            String remoteVersion = targetVersion.getVersion();
            
            // 如果配置节点不是数组，或者数组为空，需要下载
            if (!agentVersionsNode.isArray() || agentVersionsNode.isEmpty()) {
                return true;
            }
            
            // 检查配置中是否存在匹配的版本
            for (JsonNode versionNode : agentVersionsNode) {
                String installedVersion = versionNode.path("version").asText();
                if (remoteVersion.equals(installedVersion)) {
                    return false; // 找到匹配的版本，不需要下载
                }
            }
            
            // 没有找到匹配的版本，需要下载
            return true;
            
        } catch (Exception e) {
            throw new AgentException("Failed to check if agent needs download", e);
        }
    }
} 