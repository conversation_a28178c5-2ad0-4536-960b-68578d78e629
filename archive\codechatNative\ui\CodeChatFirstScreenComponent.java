package com.srdcloud.ideplugin.assistant.codechatNative.ui;

import com.intellij.ide.DataManager;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.DataContext;
import com.intellij.openapi.actionSystem.Presentation;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.NullableComponent;
import com.intellij.openapi.util.SystemInfo;
import com.intellij.ui.components.JBLabel;
import com.intellij.ui.components.JBPanel;
import com.intellij.util.ui.JBUI;
import com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions.QuickHelpAction;
import com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions.SendAction;
import com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions.rightmenu.RightMenuCommentAction;
import com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions.rightmenu.RightMenuExplainAction;
import com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions.rightmenu.RightMenuOptimizeAction;
import com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions.rightmenu.RightMenuTestAction;
import com.srdcloud.ideplugin.assistant.codechatNative.uicomponent.RoundedPanel;
import com.srdcloud.ideplugin.general.enums.QuestionType;
import com.srdcloud.ideplugin.general.icons.GPTIcons;
import com.srdcloud.ideplugin.general.utils.LocalStorageUtil;
import com.srdcloud.ideplugin.general.utils.UIUtil;
import com.srdcloud.ideplugin.service.LoginService;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

/**
 * <AUTHOR> zhangwei
 * @create 2024/7/19
 * 首屏功能UI组件
 */
public class CodeChatFirstScreenComponent extends JBPanel<CodeChatFirstScreenComponent> implements NullableComponent {
    private static final Logger logger = LoggerFactory.getLogger(CodeChatFirstScreenComponent.class);

    private final CodeChatMainPanel codeChatMainPanelParent;

    private final Project project;

    // 组件顶部Panel
    private final JPanel topContainer;

    // 组件底部Panel
    private final JPanel bottomContainer;

    // 组件底部各行Panel
    private final JPanel bottomLine1;
    private final JPanel bottomLine2;
    private final JPanel bottomLine3;
    private final JPanel bottomLine4;
    private final JPanel bottomLine5;

    /**
     * 首屏面板构造
     */
    public CodeChatFirstScreenComponent(@NotNull Project project, CodeChatMainPanel codeChatMainPanelParent) {
        this.project = project;
        this.codeChatMainPanelParent = codeChatMainPanelParent;

        // 1.1 界面风格颜色
        Color componentColor;
        Color bottomColor;
        Color bottontextHighlightColor;
        Color textHighlightColor;
        Color buttonBackgroundColor;
        Icon logoIcon;
        Icon codefreeIcon;
        Icon optionIcon;
        Icon shiftIcon;
        Icon keyKIcon;

        if (UIUtil.judgeBackgroudDarkTheme()) {
            componentColor = Color.decode("#24282C");
            bottomColor = Color.decode("#2E3338");
            buttonBackgroundColor = Color.decode("#307CFB");
            bottontextHighlightColor = Color.decode("#FFFFFF");
            textHighlightColor = Color.decode("#468CFF");
            logoIcon = GPTIcons.LOGO_DARK;
            codefreeIcon = GPTIcons.CODEFREE_DARK;
            // 根据当前操作系统，修改Mac和Window的快捷键图标（默认为Win环境下Alt）
            optionIcon = SystemInfo.isMac ? GPTIcons.MAC_OPTION_DARK : GPTIcons.WIN_ALT_DARK;
            shiftIcon = SystemInfo.isMac ? GPTIcons.MAC_SHIFT_DARK : GPTIcons.WIN_SHIFT_DARK;
            keyKIcon = GPTIcons.KEY_K_DARK;
        } else {
            componentColor = Color.decode("#FFFFFF");
            bottomColor = Color.decode("#F5F7F9");
            buttonBackgroundColor = Color.decode("#E6EFFE");
            bottontextHighlightColor = Color.decode("#307CFB");
            textHighlightColor = Color.decode("#307CFB");
            logoIcon = GPTIcons.LOGO;
            codefreeIcon = GPTIcons.CODEFREE;
            optionIcon = SystemInfo.isMac ? GPTIcons.MAC_OPTION : GPTIcons.WIN_ALT;
            shiftIcon = SystemInfo.isMac ? GPTIcons.MAC_SHIFT : GPTIcons.WIN_SHIFT;
            keyKIcon = GPTIcons.KEY_K;
        }

        // 1.2 顶部内容
        JBLabel logo = new JBLabel(logoIcon);
        JBLabel codefree = new JBLabel(codefreeIcon);
        JBLabel option = new JBLabel(optionIcon);
        JBLabel shift = new JBLabel(shiftIcon);
        JBLabel keyK = new JBLabel(keyKIcon);
        JBLabel quickText = new JBLabel("快速开启问答");

        // 1.3 底部内容
        JLabel bottomLine1Text = new JLabel("<html>我是研发云CodeFree，您的智能开发助手。我可以在您编程时进行代码补全或编程问答，您也可以选中代码后右键或使用/触发快捷指令</html>");

        JLabel explain = new JLabel("代码解释");
        explain.setCursor(new Cursor(Cursor.HAND_CURSOR));
        explain.setHorizontalAlignment(JLabel.CENTER);
        explain.setForeground(bottontextHighlightColor);
        explain.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                RightMenuExplainAction rightMenuExplainAction = project.getService(RightMenuExplainAction.class);
                DataContext context = DataManager.getInstance().getDataContext(e.getComponent());
                AnActionEvent event = AnActionEvent.createFromInputEvent(e, "", new Presentation(), context);
                rightMenuExplainAction.actionPerformed(event);
            }
        });

        JLabel comment = new JLabel("生成代码注释");
        comment.setCursor(new Cursor(Cursor.HAND_CURSOR));
        comment.setHorizontalAlignment(JLabel.CENTER);
        comment.setForeground(bottontextHighlightColor);
        comment.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                RightMenuCommentAction rightMenuCommentAction = project.getService(RightMenuCommentAction.class);
                DataContext context = DataManager.getInstance().getDataContext(e.getComponent());
                AnActionEvent event = AnActionEvent.createFromInputEvent(e, "", new Presentation(), context);
                rightMenuCommentAction.actionPerformed(event);
            }
        });

        JLabel test = new JLabel("生成单元测试");
        test.setCursor(new Cursor(Cursor.HAND_CURSOR));
        test.setHorizontalAlignment(JLabel.CENTER);
        test.setForeground(bottontextHighlightColor);
        test.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                RightMenuTestAction rightMenuTestAction = project.getService(RightMenuTestAction.class);
                DataContext context = DataManager.getInstance().getDataContext(e.getComponent());
                AnActionEvent event = AnActionEvent.createFromInputEvent(e, "", new Presentation(), context);
                rightMenuTestAction.actionPerformed(event);
            }
        });

        JLabel optimize = new JLabel("生成优化建议");
        optimize.setCursor(new Cursor(Cursor.HAND_CURSOR));
        optimize.setHorizontalAlignment(JLabel.CENTER);
        optimize.setForeground(bottontextHighlightColor);
        optimize.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                RightMenuOptimizeAction rightMenuOptimizeAction = project.getService(RightMenuOptimizeAction.class);
                DataContext context = DataManager.getInstance().getDataContext(e.getComponent());
                AnActionEvent event = AnActionEvent.createFromInputEvent(e, "", new Presentation(), context);
                rightMenuOptimizeAction.actionPerformed(event);
            }
        });

        JLabel bottomLine3Text = new JLabel("您可以尝试问我：");

        JLabel codeFreeDo = new JLabel("CodeFree可以做什么？");
        codeFreeDo.setForeground(textHighlightColor);
        codeFreeDo.setOpaque(false);
        codeFreeDo.setCursor(new Cursor(Cursor.HAND_CURSOR));
        codeFreeDo.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                QuickHelpAction quickHelpAction = project.getService(QuickHelpAction.class);
                quickHelpAction.doActionPerformed(codeChatMainPanelParent, "/help", null, QuestionType.NEW_ASK, null);
            }
        });

        JLabel pythonDo = new JLabel("python如何遍历一个字典？");
        pythonDo.setForeground(textHighlightColor);
        pythonDo.setOpaque(false);
        pythonDo.setCursor(new Cursor(Cursor.HAND_CURSOR));
        pythonDo.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                SendAction sendAction = project.getService(SendAction.class);
                sendAction.doActionPerformed(codeChatMainPanelParent, "python如何遍历一个字典", null, QuestionType.NEW_ASK, null);
            }
        });

        JLabel login = new JLabel("使用前请先登录");
        login.setCursor(new Cursor(Cursor.HAND_CURSOR));
        login.setHorizontalAlignment(JLabel.CENTER);
        login.setForeground(bottontextHighlightColor);
        login.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                LoginService loginService = LoginService.GetInstance();
                loginService.Login();
            }
        });

        // 2.1 顶部第一行布局
        JPanel topLine1 = new JPanel();
        topLine1.setOpaque(false);
        topLine1.setLayout(new BoxLayout(topLine1, BoxLayout.X_AXIS));
        topLine1.setAlignmentX(Component.CENTER_ALIGNMENT);
        topLine1.add(logo);

        // 2.2 顶部第二行布局
        JPanel topLine2 = new JPanel();
        topLine2.setOpaque(false);
        topLine2.setLayout(new BoxLayout(topLine2, BoxLayout.X_AXIS));
        topLine2.setAlignmentX(Component.CENTER_ALIGNMENT);
        topLine2.add(codefree);

        // 2.3 顶部第三行布局
        JPanel topLine3 = new JPanel();
        topLine3.setOpaque(false);
        topLine3.setLayout(new BoxLayout(topLine3, BoxLayout.X_AXIS));
        topLine3.setAlignmentX(Component.CENTER_ALIGNMENT);
        topLine3.add(option);
        topLine3.add(Box.createHorizontalStrut(5));
        topLine3.add(shift);
        topLine3.add(Box.createHorizontalStrut(5));
        topLine3.add(keyK);
        topLine3.add(Box.createHorizontalStrut(5));
        topLine3.add(quickText);

        // 3.1 底部第一行布局
        bottomLine1 = new JPanel();
        bottomLine1.setOpaque(false);
        bottomLine1.setLayout(new BoxLayout(bottomLine1, BoxLayout.X_AXIS));
        bottomLine1.setAlignmentX(Component.LEFT_ALIGNMENT);
        bottomLine1.add(bottomLine1Text);

        // 3.2 底部第二行布局
        bottomLine2 = new JPanel();
        bottomLine2.setOpaque(false);
        bottomLine2.setLayout(new BoxLayout(bottomLine2, BoxLayout.X_AXIS));
        bottomLine2.setMinimumSize(new Dimension(Integer.MIN_VALUE, 60));
        bottomLine2.setPreferredSize(new Dimension(Integer.MAX_VALUE, 60));
        bottomLine2.setMaximumSize(new Dimension(Integer.MAX_VALUE, 60));
        bottomLine2.setAlignmentX(Component.LEFT_ALIGNMENT);

        RoundedPanel explainRoundedPanel = new RoundedPanel(20, 1, 1, buttonBackgroundColor);
        explainRoundedPanel.setLayout(new BorderLayout());
        explainRoundedPanel.setMinimumSize(new Dimension(70, 24));
        explainRoundedPanel.setPreferredSize(new Dimension(100, 24));
        explainRoundedPanel.setMaximumSize(new Dimension(100, 24));
        explainRoundedPanel.add(explain, BorderLayout.CENTER);
        bottomLine2.add(explainRoundedPanel);

        bottomLine2.add(Box.createHorizontalStrut(8));

        RoundedPanel commentRoundedPanel = new RoundedPanel(20, 1, 1, buttonBackgroundColor);
        commentRoundedPanel.setLayout(new BorderLayout());
        commentRoundedPanel.setMinimumSize(new Dimension(100, 24));
        commentRoundedPanel.setPreferredSize(new Dimension(120, 24));
        commentRoundedPanel.setMaximumSize(new Dimension(120, 24));
        commentRoundedPanel.add(comment, BorderLayout.CENTER);
        bottomLine2.add(commentRoundedPanel);

        bottomLine2.add(Box.createHorizontalStrut(8));

        RoundedPanel testRoundedPanel = new RoundedPanel(20, 1, 1, buttonBackgroundColor);
        testRoundedPanel.setLayout(new BorderLayout());
        testRoundedPanel.setMinimumSize(new Dimension(100, 24));
        testRoundedPanel.setPreferredSize(new Dimension(120, 24));
        testRoundedPanel.setMaximumSize(new Dimension(120, 24));
        testRoundedPanel.add(test, BorderLayout.CENTER);
        bottomLine2.add(testRoundedPanel);

        bottomLine2.add(Box.createHorizontalStrut(8));

        RoundedPanel optimizeRoundedPanel = new RoundedPanel(20, 1, 1, buttonBackgroundColor);
        optimizeRoundedPanel.setLayout(new BorderLayout());
        optimizeRoundedPanel.setMinimumSize(new Dimension(100, 24));
        optimizeRoundedPanel.setPreferredSize(new Dimension(120, 24));
        optimizeRoundedPanel.setMaximumSize(new Dimension(120, 24));
        optimizeRoundedPanel.add(optimize, BorderLayout.CENTER);
        bottomLine2.add(optimizeRoundedPanel);

        // 3.3 底部第三行布局
        bottomLine3 = new JPanel();
        bottomLine3.setOpaque(false);
        bottomLine3.setLayout(new BoxLayout(bottomLine3, BoxLayout.X_AXIS));
        bottomLine3.setAlignmentX(Component.LEFT_ALIGNMENT);
        bottomLine3.add(bottomLine3Text);

        // 3.4 底部第四行布局
        bottomLine4 = new JPanel();
        bottomLine4.setOpaque(false);
        bottomLine4.setLayout(new BoxLayout(bottomLine4, BoxLayout.X_AXIS));
        bottomLine4.setAlignmentX(Component.LEFT_ALIGNMENT);
        bottomLine4.add(codeFreeDo);
        bottomLine4.add(Box.createHorizontalStrut(16));
        bottomLine4.add(pythonDo);

        // 3.5 底部第五行布局
        bottomLine5 = new JPanel();
        bottomLine5.setOpaque(false);
        bottomLine5.setLayout(new BoxLayout(bottomLine5, BoxLayout.X_AXIS));
        bottomLine5.setAlignmentX(Component.LEFT_ALIGNMENT);
        RoundedPanel loginRoundedPanel = new RoundedPanel(20, 1, 1, buttonBackgroundColor);
        loginRoundedPanel.setLayout(new BorderLayout());
        loginRoundedPanel.setMinimumSize(new Dimension(100, 32));
        loginRoundedPanel.setPreferredSize(new Dimension(120, 32));
        loginRoundedPanel.setMaximumSize(new Dimension(120, 32));
        loginRoundedPanel.add(login, BorderLayout.CENTER);
        bottomLine5.add(loginRoundedPanel);

        // 4.1 主界面设置
        setBorder(JBUI.Borders.empty(0, 16, 4, 16));
        setLayout(new BorderLayout(JBUI.scale(7), 0));
        setOpaque(true);
        setBackground(componentColor);

        // 4.2 顶部设置
        JPanel top = new JPanel();
        top.setBackground(componentColor);
        top.setLayout(new BoxLayout(top, BoxLayout.Y_AXIS));
        top.add(Box.createVerticalStrut(40));
        top.add(topLine1);
        top.add(Box.createVerticalStrut(8));
        top.add(topLine2);
        top.add(Box.createVerticalStrut(12));
        top.add(topLine3);
        topContainer = new JPanel(new BorderLayout());
        topContainer.setBorder(JBUI.Borders.empty());
        topContainer.setOpaque(true);
        topContainer.setBackground(componentColor);
        topContainer.add(top, BorderLayout.NORTH);

        // 4.3 底部设置
        bottomContainer = new RoundedPanel(20, 1, 1, bottomColor);
        bottomContainer.setLayout(new BoxLayout(bottomContainer, BoxLayout.Y_AXIS));
        bottomContainer.setBorder(JBUI.Borders.empty(16, 16, 16, 16));
        bottomContainer.setOpaque(true);

        // 4.4 显示首屏提示或登录提示
        updateAction();

        // 4.5 主界面整体布局
        add(topContainer, BorderLayout.CENTER);
        add(bottomContainer, BorderLayout.SOUTH);
    }

    /**
     * 刷新首屏，显示登录提示或正常首屏
     */
    public void updateAction() {

        bottomContainer.removeAll();

        // 显示首屏提示或登录提示
        if (LocalStorageUtil.checkIsLogin()) {
            bottomContainer.setMinimumSize(new Dimension(Integer.MIN_VALUE, 100));
            bottomContainer.setPreferredSize(new Dimension(Integer.MAX_VALUE, 186));
            bottomContainer.setMaximumSize(new Dimension(Integer.MAX_VALUE, 186));
            bottomContainer.add(bottomLine1);
            bottomContainer.add(bottomLine2);
            bottomContainer.add(bottomLine3);
            bottomContainer.add(bottomLine4);
        } else {
            bottomContainer.setMinimumSize(new Dimension(Integer.MIN_VALUE, 100));
            bottomContainer.setPreferredSize(new Dimension(Integer.MAX_VALUE, 129));
            bottomContainer.setMaximumSize(new Dimension(Integer.MAX_VALUE, 129));
            bottomContainer.add(bottomLine1);
            bottomContainer.add(Box.createVerticalStrut(9));
            bottomContainer.add(bottomLine5);
        }
    }

    @Override
    public boolean isNull() {
        return false;
    }

    public JPanel getTopContainer() {
        return topContainer;
    }
}
