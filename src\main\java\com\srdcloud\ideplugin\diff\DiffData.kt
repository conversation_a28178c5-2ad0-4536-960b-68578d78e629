package com.srdcloud.ideplugin.diff

data class DiffMsgData(
    val path: String = "",
    val beforeContent: String = "",
    val afterContent: String = ""
)

data class DiffMessage(
    val messageType: String,
    val data: List<DiffMsgData>,
    val messageId: String
)

data class DiffFile(
    val path: String = "",
    val name: String= "",
    val content: String= "",
    val originalContent: String= "",
    var status: String = ""
)
