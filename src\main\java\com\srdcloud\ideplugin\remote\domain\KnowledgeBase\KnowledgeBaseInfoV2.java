package com.srdcloud.ideplugin.remote.domain.KnowledgeBase;

/**
 * 知识库V2列表接口，列表单元信息实体类
 */
public class KnowledgeBaseInfoV2 {
    /**
     * 知识库id
     */
    private Integer kbId;
    /**
     * 知识库级别 1-个人 2-项目 3-组织
     */
    private Integer kbLevel;
    /**
     * 知识库名称
     */
    private String kbName;
    /**
     * 是否公开，true为已公开
     */
    private Boolean openFlag;
    /**
     * 组织名称
     */
    private String organizationName;
    /**
     * 知识库类型 通用文档/API
     */
    private String type;

    // 以下为接口多余返回，没有作用
    private Boolean isDeleted;
    private Boolean isEnabled;
    private Boolean isUsed;

    public KnowledgeBaseInfoV2(Integer kbId, Integer kbLevel, String kbName, Boolean openFlag, String organizationName, String type, Boolean isDeleted, Boolean isEnabled, Boolean isUsed) {
        this.kbId = kbId;
        this.kbLevel = kbLevel;
        this.kbName = kbName;
        this.openFlag = openFlag;
        this.organizationName = organizationName;
        this.type = type;
        this.isDeleted = isDeleted;
        this.isEnabled = isEnabled;
        this.isUsed = isUsed;
    }

    public Integer getKbId() {
        return kbId;
    }

    public void setKbId(Integer kbId) {
        this.kbId = kbId;
    }

    public Integer getKbLevel() {
        return kbLevel;
    }

    public void setKbLevel(Integer kbLevel) {
        this.kbLevel = kbLevel;
    }

    public String getKbName() {
        return kbName;
    }

    public void setKbName(String kbName) {
        this.kbName = kbName;
    }

    public Boolean getOpenFlag() {
        return openFlag;
    }

    public void setOpenFlag(Boolean openFlag) {
        this.openFlag = openFlag;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public Boolean getDeleted() {
        return isDeleted;
    }

    public void setDeleted(Boolean deleted) {
        isDeleted = deleted;
    }

    public Boolean getEnabled() {
        return isEnabled;
    }

    public void setEnabled(Boolean enabled) {
        isEnabled = enabled;
    }

    public Boolean getUsed() {
        return isUsed;
    }

    public void setUsed(Boolean used) {
        isUsed = used;
    }
}
