package com.srdcloud.ideplugin.agent.model.tabby;

/**
 * <AUTHOR>
 * @date 2025/6/12
 */
public class ProjectInitializeRequest {
    private String projectPath;

    private String[] codeCompleteStrategy;

    public ProjectInitializeRequest() {
    }

    public ProjectInitializeRequest(String projectPath, String[] codeCompleteStrategy) {
        this.projectPath = projectPath;
        this.codeCompleteStrategy = codeCompleteStrategy;
    }

    public String getProjectPath() {
        return projectPath;
    }

    public void setProjectPath(String projectPath) {
        this.projectPath = projectPath;
    }

    public String[] getCodeCompleteStrategy() {
        return codeCompleteStrategy;
    }

    public void setCodeCompleteStrategy(String[] codeCompleteStrategy) {
        this.codeCompleteStrategy = codeCompleteStrategy;
    }
}
