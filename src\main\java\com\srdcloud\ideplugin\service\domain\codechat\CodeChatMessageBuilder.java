package com.srdcloud.ideplugin.service.domain.codechat;

import com.srdcloud.ideplugin.general.enums.QuestionType;
import com.srdcloud.ideplugin.general.utils.IdeUtil;
import com.srdcloud.ideplugin.general.utils.OsUtil;
import com.srdcloud.ideplugin.remote.domain.WorkItem.WorkItemInfo;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.CodeAIRequestPromptChat;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.QuoteItem;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.CodeMessage.*;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.CodeMessage;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.DialogCondition;
import com.srdcloud.ideplugin.webview.codechat.relatedfile.RelatedFile;

import java.util.List;

public class CodeChatMessageBuilder {
    private final String messageName;
    private final String reqId;
    private final String appGid;
    private final String clientType;
    private final String fileName;
    private final String prefix;
    private final String suffix;
    private final String language;
    private final String question;
    private final Integer kbId;
    private final Integer messageType;
    private final Integer maxNewTokens;
    private final List<String> stopWords;

    private final String version;

    private final String clientVersion;

    private final String clientPlatform;

    private final QuestionType questionType;

    private final String dialogId;

    private final String parentReqId;

    private final String promptTemplateId;

    private DialogCondition modelRouteCondition;
    private List<CodeAIRequestPromptChat> prompts;

    private List<RelatedFile> relatedFiles;

    /**
     * 用户选择的知识库引用内容
     */
    private QuoteItem quote;
    private List<ImportSnippets> importSnippets;

    private List<String> gitUrls;

    private List<String> diffList;

    private List<WorkItemInfo> workItemList;

    public CodeChatMessageBuilder(String messageName, String reqId, String appGid, String clientType, String fileName,
                                  String prefix, String suffix, String language, String question, Integer kbId, Integer messageType,
                                  Integer maxNewTokens, List<String> stopWords, String version, QuestionType questionType, String dialogId, String parentReqId,
                                  String promptTemplateId, DialogCondition modelRouteCondition, List<CodeAIRequestPromptChat> prompts,
                                  QuoteItem quote, List<CodeMessage.ImportSnippets> importSnippets, List<RelatedFile> relatedFiles, List<String> gitUrls,
                                  List<String> diffList,List<WorkItemInfo> workItemList) {
        this.messageName = messageName;
        this.reqId = reqId;
        this.appGid = appGid;
        this.clientType = clientType;
        this.fileName = fileName;
        this.prefix = prefix;
        this.suffix = suffix;
        this.language = language;
        this.question = question;
        this.kbId = kbId;
        this.messageType = messageType;
        this.maxNewTokens = maxNewTokens;
        this.stopWords = stopWords;
        this.version = version;
        this.dialogId = dialogId;
        this.clientVersion = IdeUtil.getIDEVersion();
        this.questionType = questionType;
        this.parentReqId = parentReqId;
        this.promptTemplateId = promptTemplateId;
        this.modelRouteCondition = modelRouteCondition;
        this.prompts = prompts;
        this.quote = quote;
        this.importSnippets = importSnippets;
        this.relatedFiles = relatedFiles;
        this.clientPlatform = OsUtil.getOs(false) + "-" + OsUtil.getArch();
        this.gitUrls = gitUrls;
        this.diffList = diffList;
        this.workItemList = workItemList;
    }

    public String getMessageName() {
        return messageName;
    }

    public String getReqId() {
        return reqId;
    }

    public String getAppGid() {
        return appGid;
    }

    public String getClientType() {
        return clientType;
    }

    public String getFileName() {
        return fileName;
    }

    public String getPrefix() {
        return prefix;
    }

    public String getSuffix() {
        return suffix;
    }

    public String getLanguage() {
        return language;
    }

    public String getQuestion() {
        return question;
    }

    public Integer getKbId() {
        return kbId;
    }

    public Integer getMessageType() {
        return messageType;
    }

    public Integer getMaxNewTokens() {
        return maxNewTokens;
    }

    public List<String> getStopWords() {
        return stopWords;
    }

    public String getVersion() {
        return version;
    }

    public String getClientVersion() {
        return clientVersion;
    }

    public QuestionType getQuestionType() {
        return questionType;
    }

    public String getDialogId() {
        return dialogId;
    }

    public String getParentReqId() {
        return parentReqId;
    }

    public String getPromptTemplateId() {
        return promptTemplateId;
    }

    public DialogCondition getModelRouteCondition() {
        return modelRouteCondition;
    }

    public void setModelRouteCondition(DialogCondition modelRouteCondition) {
        this.modelRouteCondition = modelRouteCondition;
    }

    public List<CodeAIRequestPromptChat> getPrompts() {
        return prompts;
    }

    public void setPrompts(List<CodeAIRequestPromptChat> prompts) {
        this.prompts = prompts;
    }

    public QuoteItem getQuote() {
        return quote;
    }

    public void setQuote(QuoteItem quote) {
        this.quote = quote;
    }

    public List<RelatedFile> getRelatedFiles() {
        return relatedFiles;
    }

    public void setRelatedFiles(List<RelatedFile> relatedFiles) {
        this.relatedFiles = relatedFiles;
    }

    public String getClientPlatform() {
        return clientPlatform;
    }

    public List<ImportSnippets> getImportSnippets() {
        return importSnippets;
    }

    public void setImportSnippets(List<ImportSnippets> importSnippets) {
        this.importSnippets = importSnippets;
    }

    public List<String> getGitUrls() {
        return gitUrls;
    }

    public void setGitUrls(List<String> gitUrls) {
        this.gitUrls = gitUrls;
    }

    public List<String> getDiffList() {
        return diffList;
    }

    public void setDiffList(List<String> diffList) {
        this.diffList = diffList;
    }

    public List<WorkItemInfo> getWorkItemList() {
        return workItemList;
    }

    public void setWorkItemList(List<WorkItemInfo> workItemList) {
        this.workItemList = workItemList;
    }
}
