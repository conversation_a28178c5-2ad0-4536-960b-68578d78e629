import {UpdateObject} from './updateObject';
import RelativeCodeObject from './relativeCodeObject';
import GoModInfo from "./goModInfo";

export default class CodeObjectIndexSystem {

    // 定义一个副本Set，用于存储包路径和对象名称拼接值
    private pkgObjSet: Set<string> = new Set<string>();

    /**
     * 检查"包路径+具体关联对象"是否存在于缓存系统内
     */
    public checkObjectInPackage(packagePath: string, objectName: string): boolean {
        console.log(`queryObjectInPackage,packagePath: ${packagePath}, objectName: ${objectName}`);
        const key = `${packagePath}|${objectName}`;
        return this.pkgObjSet.has(key);
    }

    /**
     * 检查js文件是否在项目中存在
     * @param filePath
     */
    public checkJsFileExists(filePath: string): boolean {
        console.log(`checkJsFileExists,filePath: ${filePath}`);
        const key = `${filePath}|`;
        return this.pkgObjSet.has(key);
    }

    // 定义一个map，本地存储go mod路径映射关系
    private goModMap: Map<string, string> = new Map<string, string>();

    /**
     * 建立go mod路径映射关系：哪个dir路径下，对应哪个go module名称
     * 临时废弃：改为插件本地存储
     */
    public updateGoModMap(moduleName: string, moduleDirPath: string): void {
        this.goModMap.set(moduleDirPath, moduleName);
    }

    /**
     * 传入文件路径，返回所属go module的名称
     * 临时废弃：改为插件本地存储
     */
    public findBelongModule(filePath: string): GoModInfo | null {
        let longestPrefix = '';
        let belongModuleName = '';

        // 贪心算法：遍历goModMap，找到最长相同前缀的模块目录
        for (const [moduleDirPath, moduleName] of this.goModMap.entries()) {
            if (filePath.startsWith(moduleDirPath) && moduleDirPath.length > longestPrefix.length) {
                longestPrefix = moduleDirPath;
                belongModuleName = moduleName;
            }
        }

        if (belongModuleName !== '' && belongModuleName.length > 0) {
            return new GoModInfo(longestPrefix, belongModuleName);
        }

        return null;
    }

    /**
     * 更新关联缓存：可以根据不同语言，进行不同处理
     */
    public updateByFile(
        languageExt: string,
        packagePath: string,
        fileName: string,
        objectsToUpdate: UpdateObject[]
    ): void {
        if (!Array.isArray(objectsToUpdate) || objectsToUpdate.length <= 0) {
            return
        }

        console.log(`updateCodeObjectIndexSystem,packagePath: ${packagePath}, fileName: ${fileName}, objectsToUpdate:` + JSON.stringify(objectsToUpdate, null, 2));
        // 本地副本记录包与对象路径拼接关系
        for (const updateObject of objectsToUpdate) {
            const fullPkgObj = `${packagePath}|${updateObject.objectName}`;
            this.pkgObjSet.add(fullPkgObj);
        }

        // 发往插件侧更新关联缓存
        window.treeSitterQuery({
            request: JSON.stringify({
                type: "updateCodeObjectIndexSystem",
                language: languageExt,
                packagePath: packagePath,
                fileName: fileName,
                objectsToUpdate: objectsToUpdate,
            }),
            persistent: false,
            onSuccess: function (response: any) {
                console.log(`updateCodeObjectIndexSystem onSuccess,response: ${response}`);
            },
            onFailure: function (error_code: string, error_message: string) {
                console.log(`updateCodeObjectIndexSystem,error_code:${error_code},error_message:${error_message}`);
            }
        })
    }

    /**
     * 找到关联对象，返回给插件侧
     */
    public queryByPackageAndObjectName(
        languageExt: string,
        packagePath: string,
        // filePath: string,
        objectName: string
    ): RelativeCodeObject | null {
        console.log(`return relative result,packagePath: ${packagePath}, objectName: ${objectName}`);

        return window.treeSitterQuery({
            request: JSON.stringify({
                type: "findRelativeObjectReturned",
                language: languageExt,
                packagePath: packagePath,
                // fileName: filePath,
                objectName: objectName,
            }),
            persistent: false,
            onSuccess: function (response: any) {
                console.log(`queryByPackageAndObjectName onSuccess,response: ${response}`);
                return response
            },
            onFailure: function (error_code: string, error_message: string) {
                console.log(`queryByPackageAndObjectName,error_code:${error_code},error_message:${error_message}`);
                return null
            }
        })
    }
}