package com.srdcloud.ideplugin.webview.codechat.push;

import com.intellij.ide.util.PropertiesComponent;
import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.general.utils.IdeUtil;
import com.srdcloud.ideplugin.general.utils.JsonUtil;
import com.srdcloud.ideplugin.service.domain.codechat.ChatPrintCacheConfig;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;
import com.srdcloud.ideplugin.webview.codechat.common.StatusEventType;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCode;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCommand;
import com.srdcloud.ideplugin.webview.codechat.login.response.LoginSuccessResponse;
import com.srdcloud.ideplugin.webview.codechat.login.response.LoginSuccessResponseCode;
import com.srdcloud.ideplugin.webview.codechat.login.response.LoginSuccessResponseConfig;
import com.srdcloud.ideplugin.webview.codechat.login.response.StatusCodeResponse;
import com.srdcloud.ideplugin.webview.base.domain.WebViewCode;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 状态推送处理器类，用于处理和推送各种状态变化事件到WebView。
 *
 * <AUTHOR>
 * @date 2025/1/13
 */
public class PushStatusHandler {

    private static final Logger logger = LoggerFactory.getLogger(PushStatusHandler.class);

    private final Project project;

    // 所属父级WebView实例
    private final CodeChatWebview parent;

    /**
     * 构造函数，初始化状态推送处理器。
     *
     * @param project 当前项目实例
     * @param parent  父级WebView实例
     */
    public PushStatusHandler(Project project, CodeChatWebview parent) {
        this.project = project;
        this.parent = parent;
    }

    /**
     * 主动推送，发送基本的状态变化消息给Webview。
     *
     * @param webViewRspCommand 响应命令
     * @param code              状态码
     */
    public void pushNormalStatusChange(String webViewRspCommand, int code) {
        WebViewCode webViewCode = new WebViewCode(code); // 创建WebViewCode对象
        StatusCodeResponse statusCodeResponse = new StatusCodeResponse(webViewRspCommand, webViewCode); // 创建状态响应对象
        parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(statusCodeResponse)); // 将状态响应对象转换为JSON并发送到Webview
    }

    /**
     * 状态监听，处理状态变化事件，并根据事件类型发送相应的消息到Webview。
     *
     * @param eventType 事件类型
     * @param code      状态码
     */
    public void onStatusChanged(int eventType, int code) {
        switch (eventType) {
            // 登录成功事件
            case StatusEventType.LOGIN_SUCCESS:
                // 推送Webview初始化业务信息
                LoginSuccessResponseConfig loginSuccessResponseConfig = new LoginSuccessResponseConfig();
                loginSuccessResponseConfig.setHost(ConfigWrapper.getServerHost());// 服务器主机地址
                loginSuccessResponseConfig.setClientVersion(IdeUtil.getPluginVersion());// 插件当前版本
                loginSuccessResponseConfig.setVersionDesc(PropertiesComponent.getInstance().getValue(Constants.CurrentVersionChange, IdeUtil.getPluginChangeNotes()));// 插件版本变更内容

                loginSuccessResponseConfig.setInputCharacterLimit(String.valueOf(PropertiesComponent.getInstance().
                        getInt(Constants.InputCharacterLimit, Constants.DefaultInputCharacterLimit)));// 输入字符限制
                loginSuccessResponseConfig.setSnippetsCharacterLimit(String.valueOf(PropertiesComponent.getInstance()
                        .getInt(Constants.SnippetsCharacterLimit, Constants.DefaultSnippetsCharacterLimit)));// 代码片段字符限制
                loginSuccessResponseConfig.setChatCharacterLimit(String.valueOf(PropertiesComponent.getInstance()
                        .getInt(Constants.ChatCharacterLimit, Constants.DefaultChatCharacterLimit)));// 问答字符数限制
                String ChatPrintCacheConfigStr = PropertiesComponent.getInstance()
                        .getValue(Constants.ChatPrintCacheConfig, "");
                if (StringUtils.isBlank(ChatPrintCacheConfigStr)) {
                    ChatPrintCacheConfigStr = JsonUtil.getInstance().toJson(new ChatPrintCacheConfig());
                }
                loginSuccessResponseConfig.setPrintCacheConfig(ChatPrintCacheConfigStr); // 恒速打印缓存设置

                // 创建登录成功响应对象
                LoginSuccessResponseCode loginSuccessResponseCode = new LoginSuccessResponseCode(WebViewRspCode.SUCCESS, loginSuccessResponseConfig);
                LoginSuccessResponse loginSuccessResponse = new LoginSuccessResponse(WebViewRspCommand.PUSH_LOGIN_STATUS_RESPONSE, loginSuccessResponseCode);

                // 将登录成功响应对象转换为JSON并发送到Webview
                parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(loginSuccessResponse));
                break;
            case StatusEventType.WSSERVER_RECONNECT:
                // WebSocket服务器重新连接成功事件，发送网络状态响应
                pushNormalStatusChange(WebViewRspCommand.PUSH_NETWORK_STATUS_RESPONSE, WebViewRspCode.WSSERVER_RECONNECT_SUCCESS);
                break;
            case StatusEventType.WSSERVER_ERROR:
                // WebSocket服务器错误事件，发送网络状态响应
                pushNormalStatusChange(WebViewRspCommand.PUSH_NETWORK_STATUS_RESPONSE, RtnCode.NO_CHANNEL);
                break;
            case StatusEventType.LOGOUT:
            case StatusEventType.LOGIN_EXPIRED:
                // 注销或登录过期事件，发送登录状态响应
                pushNormalStatusChange(WebViewRspCommand.PUSH_LOGIN_STATUS_RESPONSE, WebViewRspCode.NOT_LOGIN);
                break;
            default:
                // 其他未处理的事件
                break;
        }
    }
}
