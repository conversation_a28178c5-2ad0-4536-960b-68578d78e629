package com.srdcloud.ideplugin.marker

import com.intellij.codeInsight.daemon.GutterIconNavigationHandler
import com.intellij.ide.DataManager
import com.intellij.openapi.actionSystem.*
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.ex.EditorEx
import com.intellij.openapi.editor.ex.EditorGutterComponentEx
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.ui.popup.JBPopupFactory
import com.intellij.psi.PsiElement
import com.intellij.ui.awt.RelativePoint
import com.srdcloud.ideplugin.general.enums.ChatMessageType
//import secIdea.chat.sendToChatPanel
import java.awt.Point
import java.awt.event.MouseEvent
import javax.swing.SwingUtilities

class AIAssistantGutterIconClickAction(
    private val element: PsiElement,
    private val functionBean: LineMarkerFunctionBean
) : AnAction(), GutterIconNavigationHandler<PsiElement> {

    override fun actionPerformed(e: AnActionEvent) {
        showOptionsPopup(e)
    }

    private fun showOptionsPopup(e: AnActionEvent, mouseEvent: MouseEvent? = null) {
        val project = e.project ?: return
        val editor = e.getData(CommonDataKeys.EDITOR) ?: return

        val actions = mutableListOf<AnAction>()
        actions.add(createAction(ChatMessageType.EXPLAIN.desc, element))
        actions.add(createAction(ChatMessageType.COMMENT.desc, element))
        actions.add(createAction(ChatMessageType.UNITTEST.desc, element))
        actions.add(createAction(ChatMessageType.OPTIMIZE.desc, element))
        actions.add(createAction("开始聊天", element))


        val popupMenu = JBPopupFactory.getInstance()
            .createActionGroupPopup(
                "海云智码快捷操作",
                DefaultActionGroup(actions),
                e.dataContext,
                JBPopupFactory.ActionSelectionAid.SPEEDSEARCH,
                true
            )

        val point = if (mouseEvent != null) {
            // 如果是通过鼠标事件触发，直接使用鼠标事件的坐标
            Point(mouseEvent.x, mouseEvent.y)
        } else {
            // 否则，计算图标的位置
            calculateGutterIconPosition(editor, element)
        }

        val gutter = (editor as? EditorEx)?.gutterComponentEx
        // 转换坐标到屏幕坐标系
        SwingUtilities.convertPointToScreen(point, gutter)

        // 显示弹出菜单
        popupMenu.show(RelativePoint(point))
    }


    private fun calculateGutterIconPosition(editor: Editor, element: PsiElement): Point {
        val elementLine = editor.document.getLineNumber(element.textOffset)
        val lineStartOffset = editor.document.getLineStartOffset(elementLine)
        val lineY = editor.logicalPositionToXY(editor.offsetToLogicalPosition(lineStartOffset)).y
        val lineHeight = editor.lineHeight

        // 估算 gutter 宽度
        val gutterWidth = (editor.gutter as? EditorGutterComponentEx)?.width ?: 50

        return Point(gutterWidth - 5, lineY + lineHeight / 2)
    }

    private fun createAction(text: String, element: PsiElement): AnAction {
        return object : AnAction(text) {
            override fun actionPerformed(e: AnActionEvent) {
                val project = e.project ?: return
                val editor = FileEditorManager.getInstance(project).selectedTextEditor ?: return

                if (editor.selectionModel.selectedText.isNullOrEmpty()) {
                    // 选中方法或包含当前元素的最近方法
                    val methodOrFunctionToSelect = findMethodOrFunctionToSelect(element)
                    if (methodOrFunctionToSelect != null) {
                        selectElement(editor, methodOrFunctionToSelect)
                    } else {
                        // 如果没有找到方法或函数，就选中原始元素
                        selectElement(editor, element)
                    }
                }
                // 通过 Action ID 获取
                val actionManager = ActionManager.getInstance()

                val actionId = when (text) {
                    ChatMessageType.EXPLAIN.desc -> {
                        "RightMenuExplainAction"
                    }
                    ChatMessageType.COMMENT.desc -> {
                        "RightMenuCommentAction"
                    }
                    ChatMessageType.UNITTEST.desc -> {
                        "RightMenuTestAction"
                    }
                    ChatMessageType.OPTIMIZE.desc -> {
                        "RightMenuOptimizeAction"
                    }
                    else ->
                        "RightMenuStartChatAction"
                }
                val action = actionManager.getAction(actionId)
                if (action != null) {
                    val event = AnActionEvent.createFromAnAction(
                        action,
                        null,
                        ActionPlaces.UNKNOWN,
                        DataManager.getInstance().getDataContext()
                    )
                    action.actionPerformed(event)
                }
            }
        }
    }

    private fun findMethodOrFunctionToSelect(element: PsiElement): PsiElement? {
        var current: PsiElement? = element
        while (current != null) {
            if (isMethodOrFunction(current)) {
                return current
            }
            current = current.parent
        }
        return null
    }

    private fun isMethodOrFunction(element: PsiElement): Boolean {
        val elementType = try {
            element.node?.elementType?.toString() ?: ""
        } catch (e: Exception) {
            ""
        }
        val elementClass = element.javaClass.simpleName


        return when {
            // Java method
            elementType.contains("METHOD") || elementClass.contains("PsiMethod") -> true
            // Kotlin function
            elementType.contains("FUN") || elementClass.contains("KtNamedFunction") -> true
            // Python function
            elementType.contains("FUNCTION_DECLARATION") || elementClass.contains("PyFunction") -> true
            // JavaScript function
            elementType.contains("FUNCTION_DECLARATION") || elementType.contains("METHOD") -> true
            // TypeScript function or method
            elementType.contains("FUNCTION_DECLARATION") || elementType.contains("METHOD_SIGNATURE") -> true
            // Go function
            elementType.contains("FUNCTION_DECLARATION") || elementType.contains("METHOD_DECLARATION") -> true
            // C/C++ function
            elementType.contains("FUNCTION_DEFINITION") -> true
            // Rust function
            elementType.contains("FUNCTION_ITEM") -> true
            // Add more conditions for other languages as needed
            else -> {
                false
            }
        }
    }

    private fun selectElement(editor: Editor, element: PsiElement) {
        val startOffset = element.textRange.startOffset
        val endOffset = element.textRange.endOffset

        WriteCommandAction.runWriteCommandAction(element.project) {
            editor.selectionModel.setSelection(startOffset, endOffset)
        }
    }

    override fun navigate(e: MouseEvent, elt: PsiElement) {
        val dataContext = DataManager.getInstance().getDataContext(e.component)
        val event = AnActionEvent.createFromAnAction(this, e, ActionPlaces.UNKNOWN, dataContext)
        showOptionsPopup(event, e)
    }
}
