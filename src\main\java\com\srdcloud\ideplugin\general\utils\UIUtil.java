package com.srdcloud.ideplugin.general.utils;

import com.intellij.openapi.editor.colors.EditorColorsManager;
import com.intellij.openapi.editor.colors.EditorColorsScheme;
import com.intellij.openapi.util.IconLoader;
import com.intellij.ui.ColorUtil;

import javax.swing.*;
import java.awt.*;

/**
 * @description UI元素操作工具类
 */
public class UIUtil {
    public static Color darkBgColor = Color.decode("#24282C");

    public static Color lightBgColor = Color.decode("#FFFFFF");

    public static Color textHoverFrontColor = Color.decode("#307CFB");

    public static Color textHoverFrontDarkColor = Color.decode("#468CFF");

    public static Color textContentColor = Color.decode("#8894A1");

    public static Color textPlaceHolderColor = Color.decode("#8894A1");

    public static Color cardBackgroundDarkColor = Color.decode("#393D41");

    public static Color onClickCardBackgroundDarkColor = Color.decode("#263A55");

    // 版本一颜色
//    public static Color cardBackgroundColor = Color.decode("#F5F7F9");
    // 版本二颜色
    public static Color cardBackgroundColor = Color.decode("#E5E9ED");

    public static Color onClickCardBackgroundColor = Color.decode("#EAF1FE");

    public static Color disableTextColor = new Color(170, 183, 195, 150);

    public static Color getTextPlaceHolderColor() {
        if (judgeBackgroudDarkTheme()) {
            return textPlaceHolderColor;
        } else {
            return textPlaceHolderColor;
        }
    }

    public static Color getTextHoverColor() {
        if (judgeBackgroudDarkTheme()) {
            return textHoverFrontDarkColor;
        } else {
            return textHoverFrontColor;
        }
    }

    public static Color getCardBackgroundColor() {
        if (judgeBackgroudDarkTheme()) {
            return cardBackgroundDarkColor;
        } else {
            return cardBackgroundColor;
        }
    }

    public static Color getOnClickCardBackgroundColor() {
        if (judgeBackgroudDarkTheme()) {
            return onClickCardBackgroundDarkColor;
        }else {
            return onClickCardBackgroundColor;
        }
    }

    public static Color getBackground() {
        if (judgeBackgroudDarkTheme()) {
            return darkBgColor;
        } else {
            return lightBgColor;
        }
    }

    public static Color getLinkColor() {
        if (judgeBackgroudDarkTheme()) {
            return Color.white;
        } else {
            return textPlaceHolderColor;
        }
    }

    public static void setTextHoverColor(final JComponent component) {
        component.setForeground(getTextHoverColor());
    }

    public static void setTextContentColor(final JComponent component) {
        if (judgeBackgroudDarkTheme()) {
            component.setForeground(textContentColor);
        } else {
            component.setForeground(textContentColor);
        }
    }

    /**
     * 根据系统或IDE配置来决定是使用浅色还是深色的背景，设置UI组件统一背景颜色。
     */
    public static void setBackground(final JComponent component) {
        component.setBackground(getBackground());
    }


    /**
     * 判断当前是否为暗色主题
     *
     * @return
     */
    public static boolean judgeBackgroudDarkTheme() {
        // 获取当前的编辑器颜色方案
        EditorColorsScheme currentScheme = EditorColorsManager.getInstance().getGlobalScheme();
        return ColorUtil.isDark(currentScheme.getDefaultBackground());
    }


    /**
     * 封装IconLoader，并根据当前环境区分load方式
     * 请注意这里的IconPath都是从 resource目录开始的，所以
     * 如果资源在
     * src/main/resources/icons/srdcloud_square.svg 下，
     * 则示例写法是
     * /icons/srdcloud_square.svg
     * @return
     */
    public static Icon loadIcon(String secIconPath, String srdIconPath){
        return EnvUtil.isSec(
                IconLoader.getIcon(secIconPath, UIUtil.class),
                IconLoader.getIcon(srdIconPath, UIUtil.class)
                );
    }

}
