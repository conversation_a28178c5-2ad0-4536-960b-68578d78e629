package com.srdcloud.ideplugin.webview.codechat.composer.response;

import com.srdcloud.ideplugin.webview.codechat.composer.request.ComposerRequest;

public class ComposerChatResponseData extends ComposerResponseData {
    private String dialogId;
    private boolean done;
    private String content;

    public ComposerChatResponseData(String dialogId, boolean done, String content) {
        super(ComposerRequest.REQ_TYPE_COMPOSER_CHAT);
        this.dialogId = dialogId;
        this.done = done;
        this.content = content;
    }

    public String getDialogId() {
        return dialogId;
    }

    public void setDialogId(String dialogId) {
        this.dialogId = dialogId;
    }

    public boolean isDone() {
        return done;
    }

    public void setDone(boolean done) {
        this.done = done;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
