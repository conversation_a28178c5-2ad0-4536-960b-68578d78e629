package com.srdcloud.ideplugin.agent.commclient;

import com.intellij.ide.util.PropertiesComponent;
import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.agent.model.tabby.ProjectInitializeRequest;
import com.srdcloud.ideplugin.general.constants.AgentNameConstant;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import com.srdcloud.ideplugin.general.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import com.google.gson.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.function.Consumer;

/**
 * Tabby Agent 专用客户端
 * 扩展了基本的 AgentCommClient，添加了 Tabby 特定的方法
 */
public class TabbyAgentClient extends AgentCommClient {

    private final static String AGENT_NAME = AgentNameConstant.TABBY_AGENT;
    private final static Logger logger = LoggerFactory.getLogger(TabbyAgentClient.class);

    private boolean initialized = false;
    private boolean isReady = false;

    // 覆盖基类的 generatorTypes，提供 tabby Agent 特定的类型
    private static final Gson gson = new Gson();

    public static String getAgentName() {
        return AGENT_NAME;
    }

    @Override
    public boolean isEnabled() {
        return super.isEnabled() && initialized && isReady;
    }

    public TabbyAgentClient(Project project, @NotNull Process process, @NotNull AgentMessageReceiver messageReceiver) {
        super(project, process, messageReceiver);

        // 1、初始化agent
        this.request("initialize", new Object(), null, initializeResponse -> {
            logger.info("[cf] tabby agent initialized");
            DebugLogUtil.println("[cf] tabby agent initialized");

            initialized = true;

            // 2、初始化完毕，二次查询状态
            this.request("getStatus", new Object(), null, statusResponse -> {
                logger.info("[cf] tabby agent getStatus: {}", statusResponse);
                DebugLogUtil.println("[cf] tabby agent getStatus:" + JsonUtil.getInstance().toJson(statusResponse));

                isReady = true;

                // 3、开始初始化项目数据
                String[] codeCompleteStrategy = new String[]{"rlcc"};
                String codeCompleteStrategyStr = PropertiesComponent.getInstance().getValue(Constants.CodeCompleteStrategy, "");
                if (StringUtils.isNotBlank(codeCompleteStrategyStr)) {
                    codeCompleteStrategy = JsonUtil.getInstance().fromJson(codeCompleteStrategyStr, String[].class);
                }
                List<Object> argsList = Arrays.asList(project.getBasePath(), codeCompleteStrategy);
                this.request("projectInitialize", argsList, null, projectInitializeResponse -> {
                    logger.info("[cf] tabby agent projectInitialize: {}", projectInitializeResponse);
                    DebugLogUtil.println("[cf] tabby agent projectInitialized.");
                });
            });
        });

        // 调试用，获取agent环境参数设置
        this.request("getEnvParameters", new Object(), null, envResponse -> {
            DebugLogUtil.println("[cf] tabby agent getEnvParameters:" + JsonUtil.getInstance().toJson(envResponse));
        });
    }

    /**
     * 重写请求方法，使用 Tabby 特有的 [] 格式
     */
    @Override
    public void request(
            String func,
            Object args,
            String messageId,
            Consumer<Object> onResponse
    ) {
        String id = messageId != null ? messageId : uuid();

        JsonObject payload = new JsonObject();
        payload.addProperty("func", func);

        // 处理args参数，判断是否为集合对象
        JsonElement argsArray;
        if (args instanceof Collection) {
            // 如果是集合类型，转换为JsonArray
            argsArray = gson.toJsonTree(args);
        } else if (args != null) {
            // 如果不是集合类型，创建包含单个元素的JsonArray
            JsonArray array = new JsonArray();
            array.add(gson.toJsonTree(args));
            argsArray = array;
        } else {
            // 如果args为null，创建空JsonArray
            argsArray = new JsonArray();
        }
        payload.add("args", argsArray);

        // Tabby 使用 [id, {func, args}] 格式
        JsonArray message = new JsonArray();
        message.add(id);
        message.add(payload);

        responseListeners.put(id, onResponse);

        //DebugLogUtil.info("[cf] 向 tabby 发送消息：" + message);
        // fixme：本地调试
        DebugLogUtil.println("[cf] 向 tabby 发送消息：" + message);

        write(gson.toJson(message));
    }


    @Override
    protected void handleMessage(String json) {
        messageReceiver.onAgentMessageHandler(json);

        //DebugLogUtil.info("[cf] 从 tabby 收到消息：" + json);
        // fixme：本地调试
        DebugLogUtil.println("[cf] 从 tabby 收到消息：" + json);

        if (json.startsWith("[") && json.endsWith("]")) {
            Object[] message = gson.fromJson(json, Object[].class);
            Consumer<Object> listener = responseListeners.get(message[0]);

            if (listener != null) {
                listener.accept(JsonUtil.getInstance().toJson(message[1]));
                responseListeners.remove(message[0]);
            }
        }
    }
}