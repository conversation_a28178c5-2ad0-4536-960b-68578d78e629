package com.srdcloud.ideplugin.remote;

import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import com.srdcloud.ideplugin.remote.client.HttpClient;
import com.srdcloud.ideplugin.remote.client.ICommChannelEvent;
import com.srdcloud.ideplugin.remote.client.MyWebSocketClient;
import com.srdcloud.ideplugin.remote.domain.ApiResponse;
import com.srdcloud.ideplugin.service.CodeChatResponseReceiver;
import com.srdcloud.ideplugin.statusbar.Notify;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR> yangy
 * @create 2023/9/6 17:24
 */
public class CodeAICommHandler implements ICommChannelEvent {

    private static final Logger logger = LoggerFactory.getLogger(CodeAICommHandler.class);

    public static CodeAICommHandler instance;

    private MyWebSocketClient wsChannel;

    private String currRegCommand;

    // 双重校验锁单例
    public static CodeAICommHandler GetInstance(String serverUrl) {
        if (instance == null) {
            synchronized (CodeAICommHandler.class) {
                if (instance == null) {
                    instance = new CodeAICommHandler(serverUrl);
                }
            }
        }
        return instance;
    }

    public CodeAICommHandler(String serverUrl) {
        if (ConfigWrapper.ChannelType == Constants.ChannelStyle_Websocket) {
            wsChannel = new MyWebSocketClient(serverUrl, this);
        }
    }

    public int GetChannelStatus() {
        if (wsChannel == null) {
            return Constants.Channel_Disconnected;
        }
        return wsChannel.GetChannelStatus();
    }

    public boolean GetIsRemoteClose() {
        if (wsChannel == null) {
            return true;
        }
        return wsChannel.GetDisconnectedByRemoteClose();
    }

    /**
     * 创建ws连接，发送注册通道消息
     */
    public ApiResponse RegisterToCodeAI(String regMessageStr) {
        // 防止并发注册
        synchronized (this) {
            if (ConfigWrapper.ChannelType == Constants.ChannelStyle_Websocket) {
                if (wsChannel.GetChannelStatus() == Constants.Channel_Disconnected) {
                    currRegCommand = regMessageStr;
                    int connResult = wsChannel.ConnectToServer();
                    if (connResult != RtnCode.SUCCESS) {
                        return new ApiResponse(connResult, "");
                    }
                }
            }
        }
        return SendMessageToCodeAI(regMessageStr);
    }

    /**
     * 发送任意消息
     */
    public ApiResponse SendMessageToCodeAI(String msgStr) {
        // 调试用，debug模式下才打印
        DebugLogUtil.info("CodeAICommHandler SendMessageToCodeAI: " + msgStr);
        // fixme：本地调试时按需放开
        //DebugLogUtil.println("CodeAICommHandler SendMessageToCodeAI: " + msgStr);

        if (ConfigWrapper.ChannelType == Constants.ChannelStyle_Websocket) {
            if (wsChannel.GetChannelStatus() == Constants.Channel_Disconnected || wsChannel.GetChannelStatus() == Constants.Channel_Connecting) {
                return new ApiResponse(RtnCode.NO_CHANNEL, "");
            }

            return wsChannel.SendMessage(msgStr);
        } else if (ConfigWrapper.ChannelType == Constants.ChannelStyle_Http) {
            return HttpClient.doPost(ConfigWrapper.getCodeAIServerUrl(), msgStr, null);
        }

        return null;
    }

    /**
     * 断开socket通信
     */
    public void disconnect() {
        if (wsChannel != null) {
            wsChannel.disconnectAndRegisterChannel();
        }
    }

    /**
     * 海云安侧，退登后断开socket通信
     */
    public void logoutDisconnect() {
        if (wsChannel != null) {
            wsChannel.disconnect();
        }
    }


    @Override
    public void OnCommChannelIncomMessage(String incomMsg) {
        CodeChatResponseReceiver.OnReceiveMessageFromCodeAI(wsChannel, incomMsg);
    }

    @Override
    public void OnCommChannelEvent(int event, String reason) {
        // 连接成功
        if (event == Constants.Channel_Connected) {
            Notify.Companion.updateStatusNotify();
        } else {
            // 连接失败，尝试重连一次
            event = wsChannel.ReConnectToServer();
            Notify.Companion.updateStatusNotify();
        }

        // 还是失败，则走异常提问链路处理
        if (event != RtnCode.SUCCESS) {
            CodeChatResponseReceiver.onTaskErrorOrClose(null, false);
        }
    }


}
