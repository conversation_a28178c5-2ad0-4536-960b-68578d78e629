package com.srdcloud.ideplugin.remote.domain.PromptManage;

import com.srdcloud.ideplugin.service.domain.template.PromptTemplateCategory;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2024/5/13
 */
public class PromptTemplateCategoryListResponse {

    /**
     * 返回码
     */
    private int optResult;

    /**
     * 异常描述
     */
    private String msg;


    /**
     * 模板列表数据
     */
    private ArrayList<PromptTemplateCategory> data;

    public PromptTemplateCategoryListResponse(int optResult, String msg, ArrayList<PromptTemplateCategory> data) {
        this.optResult = optResult;
        this.msg = msg;
        this.data = data;
    }

    public int getOptResult() {
        return optResult;
    }

    public void setOptResult(int optResult) {
        this.optResult = optResult;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public ArrayList<PromptTemplateCategory> getData() {
        return data;
    }

    public void setData(ArrayList<PromptTemplateCategory> data) {
        this.data = data;
    }
}
