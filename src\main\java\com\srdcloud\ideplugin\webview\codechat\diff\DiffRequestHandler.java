package com.srdcloud.ideplugin.webview.codechat.diff;

import com.intellij.diff.DiffManager;
import com.intellij.diff.DiffRequestFactory;
import com.intellij.diff.chains.SimpleDiffRequestChain;
import com.intellij.diff.contents.DiffContent;
import com.intellij.diff.editor.ChainDiffVirtualFile;
import com.intellij.diff.requests.SimpleDiffRequest;
import com.intellij.diff.util.DiffUserDataKeys;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.command.WriteCommandAction;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.fileEditor.FileEditorManagerListener;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Key;
import com.intellij.openapi.vfs.LocalFileSystem;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.openapi.vfs.VirtualFileManager;
import com.srdcloud.ideplugin.diff.diffreport.DiffEditorCreateListener;
import com.srdcloud.ideplugin.general.utils.JsonUtil;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCode;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCommand;
import com.srdcloud.ideplugin.webview.codechat.diff.request.DiffCodeRequest;
import com.srdcloud.ideplugin.webview.codechat.diff.response.DiffCodeResponse;
import com.srdcloud.ideplugin.webview.base.domain.WebViewCode;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;

public class DiffRequestHandler {
    private static final Logger logger = LoggerFactory.getLogger(DiffRequestHandler.class);

    /**
     * 当前项目实例
     */
    private final Project project;

    /**
     * 父级CodeChatWebview实例
     */
    private final CodeChatWebview parent;

    /**
     * 缓存diff Map
     * key: diff文件，value: 源路径hash值
     */
    private ConcurrentHashMap<VirtualFile, String> diffMap = new ConcurrentHashMap<>();

    /**
     * 缓存diff Map
     * key: 源路径hash值, value: diff文件，
     */
    private ConcurrentHashMap<String, VirtualFile> diffMapByHash = new ConcurrentHashMap<>();

    private String pathHash;

    /**
     * 临时目录相对路径
     */
    private final String tmpRelativePath = "/build/tmp/diff";

    public DiffRequestHandler(Project project, CodeChatWebview parent) {
        this.project = project;
        this.parent = parent;
        addFileChangeListener();
    }

    /**
     * 解析请求：获取文件路径、生成内容、选中内容，然后进行文件处理
     */
    public void compareCodeDiff(String request) {

        // LightVirtualFile

        // 解析请求，获取文件路径、起始行号和内容
        DiffCodeRequest diffCodeRequest = JsonUtil.getInstance().fromJson(request, DiffCodeRequest.class);
        String filePath = diffCodeRequest.getData().getFilePath();
        String originalContent = diffCodeRequest.getData().getOriginalContent();
        String generatedContent = diffCodeRequest.getData().getGeneratedContent();

        // 将字符串路径转换为VirtualFile
        VirtualFile sourceVirtualFile = LocalFileSystem.getInstance().findFileByPath(filePath);
        if (sourceVirtualFile == null) {
            logger.warn("[cf] DiffRequestHandler compareCodeDiff skip,virtualFile convert failed:{}", filePath);
            return;
        }

        String fileHash = filePath.hashCode() + "." + sourceVirtualFile.getExtension();
        pathHash = fileHash;


        compareFileDiff(generatedContent.split("\n"), sourceVirtualFile, originalContent, fileHash);
    }

    /**
     * 文件处理：选中内容在源文件中检索出行号、源文件内容写入临时文件、生成内容按行号替换临时文件
     */
    private void compareFileDiff(String[] generatedContentArray, VirtualFile sourceVirtualFile, String sourceContent, String fileHash) {

        AtomicReference<VirtualFile> tempVirtualFile = new AtomicReference<>();
        // 设定临时目录
        String projectDirectory = project.getBasePath();
        String tempPath = projectDirectory + tmpRelativePath;
        // 随机生成临时文件名，扩展名与源文件一直
        String tempName = fileHash;

        // 判断原始文件的diff文件是否已经打开
        if (diffMapByHash.containsKey(fileHash)) {
            tempVirtualFile.set(LocalFileSystem.getInstance().findFileByPath(tempPath + "/" + tempName));

            ApplicationManager.getApplication().invokeLater(() -> {
                FileEditorManager.getInstance(project).openFile(diffMapByHash.get(fileHash), false);
            });
        }

        // todo：后续迭代优化，创建文件逻辑改用LightVirtualFile这个类去创建文档，避免在项目本地读写文件
        // LightVirtualFile

        else {

            // 如果临时目录不存在则创建
            File tempFile = new File(tempPath);
            VirtualFile tempDir;
            if (!tempFile.exists()) {
                boolean created = tempFile.mkdirs();
                if (created) {
                    // 如果目录是新建的，先刷新再转换为虚拟目录
                    tempDir = VirtualFileManager.getInstance().getFileSystem("file").refreshAndFindFileByPath(tempPath);

                    // todo：后续迭代优化，将tempFile的属性设置为隐藏

                } else {
                    tempDir = null;
                    logger.warn("[cf] DiffRequestHandler compareFileDiff skip,tempDir create fail:{}", tempPath);
                }
            } else {
                // 如果目录已存在直接转换为虚拟目录
                tempDir = VirtualFileManager.getInstance().getFileSystem("file").findFileByPath(tempPath);
            }

            if (tempDir == null || !tempDir.isDirectory()) {
                logger.warn("[cf] DiffRequestHandler compareFileDiff skip,tempDir convert VirtualFile fail:{}", tempPath);
                return;
            }

            // 创建临时文件
            WriteCommandAction.runWriteCommandAction(project, () -> {
                try {
                    tempVirtualFile.set(tempDir.createChildData(project, tempName));
                } catch (IOException e) {
                    logger.warn("[cf] DiffRequestHandler compareFileDiff skip,tempVirtualFile write fail:{}", tempName);
                    return;
                }
            });
        }

        VirtualFile finalTempVirtualFile = tempVirtualFile.get();
        WriteCommandAction.runWriteCommandAction(project, () -> {
            try {
                // 1、源文件处理：检索代码块、获取行号
                int startLineNumber = 0;
                int endLineNumber = 0;
                Document sourceDocument = FileDocumentManager.getInstance().getDocument(sourceVirtualFile);

                if (sourceDocument == null) {
                    logger.warn("[cf] DiffRequestHandler compareFileDiff skip,sourceDocument is null.");
                    return;
                }

                // 获取源文件内容
                String sourceFileContent = sourceDocument.getText();
                if (sourceFileContent == null) {
                    return;
                }

                // 检查源文件中生成内容是否存在
                if (sourceFileContent.contains(sourceContent)) {
                    // 获取生成内容在源文件中的起始行号、结束行号
                    int startIndex = sourceFileContent.indexOf(sourceContent);
                    if (startIndex != -1) {
                        // 获取字符偏移量
                        int endIndex = startIndex + sourceContent.length() - 1;
                        // 将字符偏移量转换为行号
                        startLineNumber = sourceDocument.getLineNumber(startIndex) + 1;
                        endLineNumber = sourceDocument.getLineNumber(endIndex) + 1;
                    }
                } else {
                    logger.warn("[cf] DiffRequestHandler compareFileDiff skip,sourceFileContent not contains diff content.");
                }

                // 2、临时目录和临时文件处理：创建、拷贝、修改
                Document tempDocument = FileDocumentManager.getInstance().getDocument(finalTempVirtualFile);
                if (tempDocument == null) {
                    logger.warn("[cf] DiffRequestHandler compareFileDiff skip,tempDocument is null.");
                    return;
                }

                // 读取源文件内容并拷贝进临时文件
                tempDocument.setText(sourceFileContent);

                // 按行号逐行修改临时文件内容
                int generatedContentLines = generatedContentArray.length;
                if (startLineNumber > 0) {
                    // 如果起始行号大于0，则检索到源文件保有选中内容
                    int selectedContentLines = endLineNumber - startLineNumber + 1;

                    // 按起始行号，删除临时文件中的选中内容
                    for (int i = 0; i < selectedContentLines; i++) {
                        int lineNumber = startLineNumber + i;
                        if (lineNumber > 0 && lineNumber <= tempDocument.getLineCount()) {
                            int lineStartOffset = tempDocument.getLineStartOffset(lineNumber - 1);
                            int lineEndOffset = tempDocument.getLineEndOffset(lineNumber - 1);
                            tempDocument.deleteString(lineStartOffset, lineEndOffset);
                        } else {
                            throw new IllegalArgumentException("Line number is out of range: " + lineNumber);
                        }
                    }

                    // 如果生成内容比选中内容多，则在选中内容后添加空行
                    int blankLines = generatedContentLines - selectedContentLines;
                    if (blankLines > 0) {
                        for (int i = 0; i < blankLines; i++) {
                            int lineNumber = startLineNumber + i;
                            int lineStartOffset = tempDocument.getLineStartOffset(lineNumber - 1);
                            tempDocument.insertString(lineStartOffset, "\n");
                        }
                    }

                    // 按起始行号，生成内容插入临时文件中
                    for (int i = 0; i < generatedContentLines; i++) {
                        int lineNumber = startLineNumber + i;
                        if (lineNumber > 0 && lineNumber <= tempDocument.getLineCount()) {
                            int lineStartOffset = tempDocument.getLineStartOffset(lineNumber - 1);
                            tempDocument.insertString(lineStartOffset, generatedContentArray[i]);
                        } else {
                            throw new IllegalArgumentException("Line number is out of range: " + lineNumber);
                        }
                    }
                } else {
                    // 如果起始行号为0，则没有检索到源文件包邮选中内容，直接将生成内容写入临时文件末尾
                    int tempDocumentLines = tempDocument.getLineCount();

                    // 文件尾部添加空行
                    for (int i = 0; i < generatedContentLines; i++) {
                        int lineNumber = tempDocumentLines + i;
                        int lineStartOffset = tempDocument.getLineStartOffset(lineNumber - 1);
                        tempDocument.insertString(lineStartOffset, "\n");
                    }

                    // 文件尾部添加生成内容
                    for (int i = 0; i < generatedContentLines; i++) {
                        int lineNumber = tempDocumentLines + i;
                        int lineStartOffset = tempDocument.getLineStartOffset(lineNumber - 1);
                        tempDocument.insertString(lineStartOffset, generatedContentArray[i]);
                    }
                }

                // 保存文件内容
                FileDocumentManager.getInstance().saveDocument(tempDocument);
            } catch (Exception e) {
                logger.warn("[cf] DiffRequestHandler compareFileDiff error:\n");
                e.printStackTrace();
            }
        });

        // 将字符串路径转换为 VirtualFile
        if (tempVirtualFile.get() == null) {
            logger.warn("[cf] DiffRequestHandler compareFileDiff skip,tempVirtualFile is null.");
            return;
        }

        // 显示diff
        if (!diffMapByHash.containsKey(fileHash)) {
            showDiff(sourceVirtualFile, tempVirtualFile.get());
        }

        // 返回webView响应
        WebViewCode data = new WebViewCode(WebViewRspCode.SUCCESS);
        DiffCodeResponse diffCodeResponse = new DiffCodeResponse(WebViewRspCommand.VIEW_DIFF, data);
        parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(diffCodeResponse));
    }

    /**
     * 显示差异界面，比较两个文件之间的差异
     */
    private void showDiff(VirtualFile file1, VirtualFile file2) {
        // 检查参数是否为空，如果有任何一个为空，则直接返回不执行后续操作
        if (file1 == null || file2 == null) {
            return;
        }

        // 创建差异请求对象，用于比较两个文件之间的差异
        SimpleDiffRequest diffRequest = (SimpleDiffRequest) DiffRequestFactory.getInstance().createFromFiles(
                project,
                file1,
                file2
        );

        // 显示差异界面
        ApplicationManager.getApplication().invokeLater(() -> {
            SimpleDiffRequestChain fileChain = new SimpleDiffRequestChain(diffRequest);
            ChainDiffVirtualFile diffFile = new ChainDiffVirtualFile(fileChain, "Diff");
//            diffRequest.putUserData(DiffEditorCreateListener.Companion.getDIFF_KEY(), "codechat");
            FileEditorManager.getInstance(project).openFile(diffFile, false);
        });
    }

    private void addFileChangeListener() {
        ApplicationManager.getApplication().getMessageBus().connect(parent).subscribe(FileEditorManagerListener.FILE_EDITOR_MANAGER, new FileEditorManagerListener() {

            @Override
            public void fileOpened(@NotNull FileEditorManager source, @NotNull VirtualFile file) {
                if (file instanceof ChainDiffVirtualFile && pathHash != null) {
                    diffMap.put(file, new String(pathHash));
                    diffMapByHash.put(new String(pathHash), file);
                    pathHash = null;
                }
            }

            @Override
            public void fileClosed(@NotNull FileEditorManager source, @NotNull VirtualFile file) {
                if (file instanceof ChainDiffVirtualFile && diffMap.containsKey(file)) {
                    // 删除临时文件
                    String tempPath = project.getBasePath() + tmpRelativePath + "/" + diffMap.get(file);
                    deleteFile(tempPath);

                    diffMapByHash.remove(diffMap.get(file));
                    diffMap.remove(file);
                }
            }
        });
    }

    private void deleteFile(String filePath) {
        WriteCommandAction.runWriteCommandAction(project, () -> {
            try {
                VirtualFile virtualFile = LocalFileSystem.getInstance().findFileByPath(filePath);

                if (virtualFile != null) {
                    virtualFile.delete(null);
                }

            } catch (Exception e) {
                logger.warn("[cf] DiffRequestHandler deleteFile:{} error:\n", filePath);
                e.printStackTrace();
            }
        });
    }

    public String getPathHash() {
        return pathHash;
    }

    public void setPathHash(String pathHash) {
        this.pathHash = pathHash;
    }
}
