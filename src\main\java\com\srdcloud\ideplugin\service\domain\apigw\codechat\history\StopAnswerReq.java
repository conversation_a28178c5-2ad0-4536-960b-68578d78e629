package com.srdcloud.ideplugin.service.domain.apigw.codechat.history;

import com.srdcloud.ideplugin.webview.codechat.relatedfile.RelatedFile;

import java.util.List;

/**
 * <AUTHOR> yangy
 * @create 2024/5/22 18:32
 */
public class StopAnswerReq {


    /**
     * 对话请求ID
     */
    private String reqId;


    /**
     * 产生本对话的AI原子能力标识
     */
    private String subService;

    /**
     * 问答对所属的对话ID
     */
    private String dialogId;

    /**
     * 本轮对话的模型路由条件
     */
    private DialogCondition modelRouteCondition;

    /**
     * 提问类型，取值为：
     * newAsk - 对话下新的提问
     * reAsk - 对话下原有问题重新提问
     */
    private String questionType;

    /**
     * 当questionType=newAsk时：
     *    1）如果本问答对是所属对话中的首轮问答，此参数无需携带；
     *    2）如果本问答对并非所属对话中首轮问答，此必须携带此参数，值为本问答对之前的回答reqId，用于寻址本问答对插入的位置。
     * 当questionType=reAsk时：
     *    取值为原有提问的reqId。
     */
    private String parentReqId;

    /**
     * 本对话的system prompt对象，其中role=system。
     * 如果本问答对是一个对话的首轮问答，且对话携带了system prompt的话，就携带此参数。
     */
    private String system;

    /**
     * 当questionType=newAsk时，为问答对的提问对象。ChatMessage结构定义参见“2.数据设计”一章；
     * 当questionType=reAsk时，无需携带此参数。
     */
    private String question;

    /**
     * 知识库id
     */
    private Integer kbId;

    /**
     * 问答对的回答对象。
     */
    private String answer;

    /**
     * 关联文件列表：停止回答的关联文件在webview端无法获取，在ide端进行维护并填入
     */
    private List<RelatedFile> files;

    public String getReqId() {
        return reqId;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    public String getSubService() {
        return subService;
    }

    public void setSubService(String subService) {
        this.subService = subService;
    }

    public String getDialogId() {
        return dialogId;
    }

    public void setDialogId(String dialogId) {
        this.dialogId = dialogId;
    }

    public String getQuestionType() {
        return questionType;
    }

    public void setQuestionType(String questionType) {
        this.questionType = questionType;
    }

    public String getParentReqId() {
        return parentReqId;
    }

    public void setParentReqId(String parentReqId) {
        this.parentReqId = parentReqId;
    }

    public String getSystem() {
        return system;
    }

    public void setSystem(String system) {
        this.system = system;
    }

    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    public Integer getKbId() {
        return kbId;
    }

    public void setKbId(Integer kbId) {
        this.kbId = kbId;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public DialogCondition getModelRouteCondition() {
        return modelRouteCondition;
    }

    public void setModelRouteCondition(DialogCondition modelRouteCondition) {
        this.modelRouteCondition = modelRouteCondition;
    }

    public List<RelatedFile> getFiles() {
        return files;
    }

    public void setFiles(List<RelatedFile> files) {
        this.files = files;
    }

    @Override
    public String toString() {
        return "StopAnswerReq{" +
                "reqId='" + reqId + '\'' +
                ", subService='" + subService + '\'' +
                ", dialogId='" + dialogId + '\'' +
                ", questionType='" + questionType + '\'' +
                ", parentReqId='" + parentReqId + '\'' +
                ", system='" + system + '\'' +
                ", question='" + question + '\'' +
                ", answer='" + answer + '\'' +
                ", files=" + files +
                '}';
    }
}
