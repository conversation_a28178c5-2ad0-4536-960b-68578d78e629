package com.srdcloud.ideplugin.webview.codechat.composer.response;

import com.srdcloud.ideplugin.diff.DiffFile;
import com.srdcloud.ideplugin.diff.DiffMsgData;
import com.srdcloud.ideplugin.webview.codechat.composer.request.ComposerRequest;

import java.util.List;


public class ReportDiffFilesData extends ComposerResponseData {

    String dialogId;
    DiffFile[] content;

    public ReportDiffFilesData(String dialogId, DiffFile[] content) {
        super(ComposerRequest.REQ_TYPE_REPORT_DIFF_FILES);
        this.dialogId = dialogId;
        this.content = content;
    }

    public String getDialogId() {
        return dialogId;
    }

    public DiffFile[] getContent() {
        return content;
    }

}
