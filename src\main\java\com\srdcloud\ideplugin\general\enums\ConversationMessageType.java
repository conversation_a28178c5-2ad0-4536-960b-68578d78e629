package com.srdcloud.ideplugin.general.enums;

/**
 * <AUTHOR>
 * @date 2024/4/11
 * @description 对话消息类型
 */
public enum ConversationMessageType {
    SYSTEM("system","系统角色，决定本轮对话的主题"),
    USER("user","用户角色，代表本条内容是用户的提问或回答"),
    ASSISTANT("assistant","助理角色，代表本条内容是AI的回答"),
    TIP("tip","提示信息");

    private String name;
    private String description;

    private ConversationMessageType(String name, String description) {
        this.name = name;
        this.description = description;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public static ConversationMessageType geConversationMessageTypeByName(String name){
        ConversationMessageType returnType = null;
        for (ConversationMessageType conversationMessageType : values()) {
            if(conversationMessageType.getName().equalsIgnoreCase(name)){
                returnType = conversationMessageType;
                break;
            }
        }
        return returnType;
    }
}
