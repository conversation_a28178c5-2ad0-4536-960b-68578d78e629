package com.srdcloud.ideplugin.assistant.codechatNative.ui.history;

import com.intellij.ide.plugins.newui.VerticalLayout;
import com.intellij.openapi.Disposable;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.Conversation;
import com.srdcloud.ideplugin.assistant.codechatNative.ui.prompttemplate.CodeChatPromptTemplateContentPanel;
import com.srdcloud.ideplugin.assistant.codechatNative.uicomponent.RoundedPanel;
import com.srdcloud.ideplugin.general.utils.IdeUtil;
import com.srdcloud.ideplugin.general.utils.UIUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;

/**
 * @author: 蔡一新
 * @date: 2024/6/19
 * @Desc 历史对话列表中，单个历史对话的显示控件，用于显示历史对话的信息和时间
 */
public class CodeChatHistoryContentPanel extends RoundedPanel implements Disposable {

    // conversation information
    private Conversation conversation;

    // Component State (Current Not in Use)
    private static final Logger logger = LoggerFactory.getLogger(CodeChatPromptTemplateContentPanel.class);

    // Children Panels
    private final JPanel titleComponent = new JPanel();
    private final JPanel historyTimeComponent =  new JPanel();

    private final JLabel titleLabel = new JLabel();
    private final JLabel historyTime = new JLabel();

    // 常数
    private final static int PANEL_HORIZONTAL_OFFSET = 0;
    private final static int PANEL_VERTICAL_OFFSET = 2;
    private final static int CARD_PANEL_CORNER_RADIUS = 10;


    public CodeChatHistoryContentPanel(Conversation conversation) {
        // 版本1，点击仅改变颜色底
//        super(CARD_PANEL_CORNER_RADIUS, PANEL_HORIZONTAL_OFFSET, PANEL_VERTICAL_OFFSET, UIUtil.getCardBackgroundColor());

        // 版本2，点击仅改变边框
        super(CARD_PANEL_CORNER_RADIUS, PANEL_HORIZONTAL_OFFSET, PANEL_VERTICAL_OFFSET,UIUtil.getBackground(), 1,  UIUtil.getCardBackgroundColor(), 1);

        this.conversation = conversation;

        // 初始化列表项UI展示
        initPanel();
    }


    // 初始化界面
    private void initPanel() {
        this.setLayout(new BorderLayout());
        titleComponent.setLayout(new VerticalLayout(10));
        historyTimeComponent.setLayout(new BorderLayout());
        titleComponent.setBackground(getBackground());
        historyTimeComponent.setBackground(getBackground());

        titleComponent.setOpaque(false);
        historyTimeComponent.setOpaque(false);

        titleLabel.setText(conversation.getTitle());

        if(conversation.getUpdateTime() != null) {
            historyTime.setText(conversation.getUpdateTime());
        }else {
            historyTime.setText(" ");
        }
        historyTime.setFont(IdeUtil.getIDELabelFont());

        // 添加到面板中
        titleComponent.add(titleLabel);
        titleComponent.add(historyTime);

        // 填充间距
        this.add(new JLabel("  "), BorderLayout.NORTH);
        this.add(new JLabel("  "), BorderLayout.WEST);
        this.add(new JLabel("  "), BorderLayout.SOUTH);
        this.add(new JLabel("  "), BorderLayout.EAST);
        this.add(titleComponent, BorderLayout.CENTER );
    }

    @Override
    public void dispose() {
        removeAll();
    }

    // 暂时处理方式 待优化
    @Override
    public void setOpaque(boolean isOpaque) {
        super.setOpaque(isOpaque);
    }

    // 被点击之后的效果展示
    public void onClick() {
            // 版本1，点击仅改变颜色底
//            setBackgroundColor(UIUtil.getOnClickCardBackgroundColor());
//            setBackgroundColorDepth(1);
        // 版本2，点击仅改变边框
        setBorderColor(UIUtil.getTextHoverColor());


        setLabelColor(UIUtil.getTextHoverColor());
    }

    // 失去点击之后对效果
    public void lostOnClick() {
        // 版本1，点击仅改变颜色底
//        setBackgroundColor(UIUtil.getCardBackgroundColor());
//        setBackgroundColorDepth(1);

        // 版本2，点击仅改变边框
        setBorderColor(UIUtil.getCardBackgroundColor());

        setLabelColor(getForeground());
    }

    // 鼠标未按下时的Hover效果展示
    public void onFocus() {
        setLabelColor(UIUtil.getTextHoverColor());
    }

    // 鼠标离开时的效果
    public void lostFocus() {
        setLabelColor(getForeground());
    }

    // 改变组件字体颜色
    public void setLabelColor(Color color) {
        titleLabel.setForeground(color);
        historyTime.setForeground(color);
    }
}
