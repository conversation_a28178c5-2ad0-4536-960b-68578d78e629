package com.srdcloud.ideplugin.assistant.codechatNative.uicomponent;

import com.intellij.ui.components.JBPanel;
import com.srdcloud.ideplugin.general.enums.MessageContentType;
import com.srdcloud.ideplugin.general.utils.HtmlUtil;
import com.srdcloud.ideplugin.general.utils.LanguageValidator;

import java.util.LinkedList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author: yangy
 * @date: 2023/7/3 10:03
 * @Desc 消息查看器
 */
public class MessageViewer extends JBPanel<MessageViewer> {
    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息类型：文本类、代码类
     */
    private MessageContentType type;

    /**
     * 代码编程语言
     */
    private String language;


    //=====逻辑函数=========
    public static final Pattern pattern = Pattern.compile("<pre><code.*?>");
    public static final Pattern pattern2 = Pattern.compile("<pre><code.*?language-(\\w+).*?>");

    /**
     * 根据消息，提取不同组成部分
     */
    public static List<MessageViewer> extractSegments(String message) {
        List<MessageViewer> segments = new LinkedList<>();
        int lastIndex = 0;
        while (true) {
            int matchLength = 11;
            String language = "";
            String fullLanguage = "";
            String preCodeStart = "<pre><code";

            Matcher matcher = pattern.matcher(message.substring(lastIndex));
            if (matcher.find()) {
                String matched = matcher.group();
                if (matched != null) {

                    Matcher matcher2 = pattern2.matcher(matched);
                    if (matcher2.find()) {
                        String m1 = matcher2.group(1);
                        if (m1 != null) {
                            language = m1;
                        }
                    }
                }
                matchLength = matched.length();
            }

            if (!language.isEmpty()) {
                fullLanguage = LanguageValidator.getFullLanguage(language);
                preCodeStart = "<pre><code class=\"language-" + language + "\">";
            }

            int preCodeStartIndex = message.indexOf(preCodeStart, lastIndex);

            // 纯文本内容
            if (extractTextViewer(message, segments, lastIndex, language, preCodeStartIndex))
                break;
            int preCodeEndIndex = message.indexOf("</code></pre>", preCodeStartIndex);
            if (extractTextViewer(message, segments, lastIndex, language, preCodeEndIndex)) break;

            if (preCodeStartIndex > lastIndex) {
                String textSegment = message.substring(lastIndex, preCodeStartIndex);
                if (!textSegment.trim().replace("\n", "").equals("`") && !textSegment.trim().replace("\n", "").equals("``")) {
                    segments.add(new MessageViewer(textSegment, MessageContentType.TEXT, language));
                }
            }
            // 代码内容
            String codeSegment = message.substring(preCodeStartIndex + matchLength, preCodeEndIndex);
            lastIndex = preCodeEndIndex + 13;
            MessageViewer messageVoCode = new MessageViewer(HtmlUtil.removePreCodeTag(codeSegment), MessageContentType.CODE, fullLanguage);
            segments.add(messageVoCode);
        }
        return segments;
    }

    private static boolean extractTextViewer(String message, List<MessageViewer> segments, int lastIndex, String language, int preCodeEndIndex) {
        if (preCodeEndIndex == -1) {
            String textSegment = message.substring(lastIndex);
            if (!textSegment.isEmpty() && !textSegment.trim().replace("\n", "").equals("`") && !textSegment.trim().replace("\n", "").equals("``")) {
                segments.add(new MessageViewer(textSegment, MessageContentType.TEXT, language));
            }
            return true;
        }
        return false;
    }


    // ======getter/setter==========
    public MessageViewer() {
    }

    public MessageViewer(String content, MessageContentType type) {
        this.content = content;
        this.type = type;
    }

    public MessageViewer(String content, MessageContentType type, String language) {
        this.content = content;
        this.type = type;
        this.language = language;
    }

    public String getContent() {
        return this.content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public MessageContentType getType() {
        return this.type;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

}