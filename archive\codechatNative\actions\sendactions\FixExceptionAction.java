package com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions;

import com.srdcloud.ideplugin.assistant.AssistantToolWindow;
import com.srdcloud.ideplugin.assistant.codechatNative.ui.CodeChatMainPanel;
import com.srdcloud.ideplugin.general.enums.ChatMessageType;
import com.srdcloud.ideplugin.general.enums.QuestionType;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.QuoteItem;

/**
 * <AUTHOR> yangy
 * @create 2024/4/25 17:13
 */
public class FixExceptionAction extends SendAction {

    @Override
    public void doActionPerformed(CodeChatMainPanel codeChatMainPanel, String data, Integer kbId, QuestionType questionType, QuoteItem quote) {
        AssistantToolWindow.toolWindowVisible(codeChatMainPanel.getProject());
        super.doActionPerformed(codeChatMainPanel, data, kbId, questionType, null);
    }

    protected ChatMessageType getChatMessageType() {
        return  ChatMessageType.FIX_EXCEPTION;
    }
}
