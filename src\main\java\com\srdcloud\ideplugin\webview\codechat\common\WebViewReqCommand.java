package com.srdcloud.ideplugin.webview.codechat.common;

/**
 * <AUTHOR>
 * @date 2025/1/10
 */
public class WebViewReqCommand {
    public static final String WEBVIEW_LOADED = "webview-loaded";
    public static final String CONVERSATION_LOAD = "conversation-load";
    public static final String CONVERSATION_ADD = "conversation-add";
    public static final String CONVERSATION_SWITCH = "conversation-switch";
    public static final String CONVERSATION_REMOVE = "conversation-remove";
    public static final String CONVERSATION_FEEDBACK = "conversation-feedback";
    public static final String CONVERSATION_EDIT_TITLE = "conversation-edit-title";
    public static final String CONVERSATION_REFRESH = "conversation-refresh";
    public static final String CHAT_REQUEST = "chat-request";
    public static final String LOGIN = "login";
    public static final String INSERT_CODE = "insert-code";
    public static final String INSERT_UNITTEST = "insert-unittest";
    public static final String RETRIVE_CODE_SELECTION = "retrive-code-selection";
    public static final String CANCEL_CHAT_REQUEST = "cancel-chat-request";
    public static final String STOP_CHAT_REQUEST = "stop-chat-request";
    public static final String SRD_CHAT_REQUEST = "srd-chat-request";
    public static final String DATA_REPORT = "data-report";
    public static final String CHECK_IF_LOGIN = "check-if-login";
    public static final String OPEN_EXTERNAL = "open-external";
    public static final String PROMPTS_REQUEST = "prompts-request";
    public static final String KNOWLEDGE_BASE_REQUEST = "knowledge-base-request";
    public static final String CODE_SECURITY_SCAN_REQUEST = "code-security-scan-request";
    public static final String COMPOSER_REQUEST = "composer-request";
    public static final String DIFF_VIEW_VERTICAL_REQUEST = "diff-view-vertical-request";
    public static final String INDEXING_REQUEST = "indexing-request";
    public static final String GET_IDE_UTILS_REQUEST = "get-ide-utils-request";
    public static final String OPEN_TEXT_DOCUMENT = "open-text-document";
    public static final String GET_DIRECTORY_STRUCTURE = "get-directory-structure";
    public static final String QA_FOR_RELATED_FILES_REQUEST = "qa-for-related-files-request";
    public static final String VIEW_DIFF = "view-diff";
    public static final String INVOKE_TERMINAL_CAPABILITY = "invoke-terminal-capability";
    public static final String WORKITEM_REQUEST = "workitem-request";
}
