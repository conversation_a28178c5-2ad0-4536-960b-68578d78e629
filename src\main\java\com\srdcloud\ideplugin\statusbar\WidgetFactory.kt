package com.srdcloud.ideplugin.statusbar

import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.wm.StatusBarWidget
import com.intellij.openapi.wm.impl.status.widget.StatusBarEditorBasedWidgetFactory
import com.srdcloud.ideplugin.general.utils.EnvUtil
import org.jetbrains.annotations.NonNls
import org.jetbrains.annotations.NotNull

class WidgetFactory : StatusBarEditorBasedWidgetFactory() {
    companion object {
        var DISPLAY_NAME: String = EnvUtil.isSec("海云智码","研发云CodeFree")
    }

    @NotNull
    override fun getId(): String {
        return Widget.WIDGET_ID
    }

    @NonNls
    @NotNull
    override fun getDisplayName(): String {
        return DISPLAY_NAME
    }

    @NotNull
    override fun createWidget(@NotNull project: Project): StatusBarWidget {
        return Widget(project)
    }

    override fun disposeWidget(@NotNull widget: StatusBarWidget) {
        Disposer.dispose(widget)
    }
}
