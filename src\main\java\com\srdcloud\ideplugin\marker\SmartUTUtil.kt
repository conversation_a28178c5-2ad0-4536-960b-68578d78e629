package com.srdcloud.ideplugin.marker

import com.intellij.psi.PsiElement

object SmartUTUtil {
    fun isJavaTestFile(element: Any): Boolean {
        val fileName = getFileName(element)
        return fileName.endsWith("Test.java") || fileName.startsWith("Test")
    }

    private fun getFileName(element: Any): String {
        try {
            val containingFileMethod = element.javaClass.getMethod("getContainingFile")
            val containingFile = containingFileMethod.invoke(element)
            val nameMethod = containingFile.javaClass.getMethod("getName")
            return nameMethod.invoke(containingFile) as String
        } catch (e: Exception) {
            println("Error getting file name: ${e.message}")
            return ""
        }
    }

    fun isPythonTestFile(element: PsiElement): Boolean {
        val file = element.containingFile
        return file.name.startsWith("test_") || file.name.endsWith("_test.py")
    }

    fun isCTestFile(element: PsiElement): Boolean {
        val file = element.containingFile
        return file.name.endsWith("_test.c") || file.name.endsWith("_test.cpp")
    }

    fun isGoTestFile(element: PsiElement): Boolean {
        val file = element.containingFile
        return file.name.endsWith("_test.go")
    }

    fun isJSTestFile(element: PsiElement): Boolean {
        val file = element.containingFile
        return file.name.endsWith(".test.js") || file.name.endsWith(".spec.js")
    }

}
