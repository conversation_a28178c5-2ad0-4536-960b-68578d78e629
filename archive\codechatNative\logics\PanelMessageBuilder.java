package com.srdcloud.ideplugin.assistant.codechatNative.logics;

import com.google.gson.JsonObject;

/**
 * @author: yangy
 * @date: 2023/8/24 16:52
 * @Desc 面板消息Json内容转换工具
 */
public class PanelMessageBuilder {

    private static JsonObject message(String role, String text) {
        JsonObject message = new JsonObject();
        message.addProperty("role", role);
        message.addProperty("content", text);
        return message;
    }

    /**
     * 用户发送的消息
     */
    public static JsonObject userMessage(String text) {
        return message("user", text);
    }

    /**
     * AI助手回答的消息
     */
    public static JsonObject assistantMessage(String text) {
        return message("assistant", text);
    }

}
