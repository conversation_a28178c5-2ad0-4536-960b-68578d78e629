package com.srdcloud.ideplugin.webview.codechat.conversation.request;

import com.srdcloud.ideplugin.webview.base.domain.WebViewCommand;

public class ConversationAddRequest extends WebViewCommand {

    private ConversationAddRequestData data;

    public ConversationAddRequest(String command, ConversationAddRequestData data) {
        super(command);
        this.data = data;
    }

    public ConversationAddRequestData getData() {
        return data;
    }

    public void setData(ConversationAddRequestData data) {
        this.data = data;
    }
}
