package com.srdcloud.ideplugin.service.domain.codechat;

import java.io.Serializable;

/**
 * @author: yangy
 * @date: 2023/7/23 20:23
 * @Desc
 */
public class CodeChatSystemPrompt implements Serializable {

    private String language;

    private String content;

    private String codePrefix;

    private String codeSuffix;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCodePrefix() {
        return codePrefix;
    }

    public void setCodePrefix(String codePrefix) {
        this.codePrefix = codePrefix;
    }

    public String getCodeSuffix() {
        return codeSuffix;
    }

    public void setCodeSuffix(String codeSuffix) {
        this.codeSuffix = codeSuffix;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }
}
