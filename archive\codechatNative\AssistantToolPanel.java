package com.srdcloud.ideplugin.assistant.codechatNative;

import com.intellij.openapi.Disposable;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.ui.components.JBTabbedPane;
import com.srdcloud.ideplugin.assistant.codechatNative.ui.CodeChatWindow;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import javax.swing.event.ChangeEvent;
import javax.swing.event.ChangeListener;
import java.awt.*;

/**
 * <AUTHOR>
 * @date 2024/5/6
 */
public class AssistantToolPanel implements Disposable {

    private static final Logger logger = LoggerFactory.getLogger(AssistantToolPanel.class);

    private Project project;

    private Boolean isMultiTab;

    public JBTabbedPane mainTabPanel;

    public JPanel mainSinglePanel;

    private CodeChatWindow codeChatWindow;

    public AssistantToolPanel(@NotNull Project project, @NotNull ToolWindow toolWindow, boolean isMultiTab) {
        super();
        this.project = project;
        this.isMultiTab = isMultiTab;
        // 获取项目唯一标识
        String projectLocationHash = project.getLocationHash();

        // 创建各功能窗面板
        codeChatWindow = new CodeChatWindow(project);

        // 缓存各功能面板实例与项目关系映射
        project.putUserData(AssistantToolWindow.ACTIVE_ASSISTANT_TOOL_WINDOW_KEY, toolWindow);
        project.putUserData(AssistantToolWindow.ACTIVE_CODE_CHAT_TAB_KEY, codeChatWindow.getPanel());

        AssistantToolWindow.CODE_CHAT_TAB_KEY_MAP.put(projectLocationHash, AssistantToolWindow.ACTIVE_CODE_CHAT_TAB_KEY);
        AssistantToolWindow.ASSISTANT_TOOL_WINDOW_KEY_MAP.put(projectLocationHash, AssistantToolWindow.ACTIVE_ASSISTANT_TOOL_WINDOW_KEY);

        // 单功能布局
        if (!isMultiTab) {
            mainSinglePanel = new JPanel(new BorderLayout());
            mainSinglePanel.setVisible(true);
            mainSinglePanel.add(codeChatWindow.getContent(), BorderLayout.CENTER);
        } else {
            // 预埋：多功能tab布局
            mainTabPanel = new JBTabbedPane(JTabbedPane.TOP, JTabbedPane.WRAP_TAB_LAYOUT);
            mainTabPanel.setVisible(true);
            mainTabPanel.addTab("开发问答", null, codeChatWindow.getContent(), "编程技术提问");
            // 预埋：添加其他功能入口
            // 将当前项目中创建出来的toolWindow实例和tab实例，缓存到project的userData空间，用key标识
            mainTabPanel.addChangeListener(new ChangeListener() {
                @Override
                public void stateChanged(ChangeEvent e) {
                    project.putUserData(AssistantToolWindow.ACTIVE_CODE_CHAT_TAB_KEY, codeChatWindow.getPanel());
                    project.putUserData(AssistantToolWindow.ACTIVE_ASSISTANT_TOOL_WINDOW_KEY, toolWindow);
                    AssistantToolWindow.CODE_CHAT_TAB_KEY_MAP.put(projectLocationHash, AssistantToolWindow.ACTIVE_CODE_CHAT_TAB_KEY);
                    AssistantToolWindow.ASSISTANT_TOOL_WINDOW_KEY_MAP.put(projectLocationHash, AssistantToolWindow.ACTIVE_ASSISTANT_TOOL_WINDOW_KEY);
                }
            });
        }
    }

    public JComponent getMainPanelContent() {
        if (this.isMultiTab) {
            return mainTabPanel;
        }
        return mainSinglePanel;
    }

    public CodeChatWindow getCodeChatWindow() {
        return codeChatWindow;
    }

    @Override
    public void dispose() {

    }
}
