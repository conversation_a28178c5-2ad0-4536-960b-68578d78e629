package com.srdcloud.ideplugin.service.domain.component;

/**
 * 制品库组件DTO数据结构
 * <AUTHOR>
 */
public class ComponentDTO {

    private int atomsharestate;

    private boolean  atomyPublishstate;

    private String componentid;

    private String latestVersion;

    private String name;

    private int needCredentials;

    private int quality;

    private int rank;

    private int resourceType;

    private int shareMode;

    private int useCount;

    public int getAtomsharestate() {
        return atomsharestate;
    }

    public void setAtomsharestate(int atomsharestate) {
        this.atomsharestate = atomsharestate;
    }

    public boolean isAtomyPublishstate() {
        return atomyPublishstate;
    }

    public void setAtomyPublishstate(boolean atomyPublishstate) {
        this.atomyPublishstate = atomyPublishstate;
    }

    public String getComponentid() {
        return componentid;
    }

    public void setComponentid(String componentid) {
        this.componentid = componentid;
    }

    public String getLatestVersion() {
        return latestVersion;
    }

    public void setLatestVersion(String latestVersion) {
        this.latestVersion = latestVersion;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getNeedCredentials() {
        return needCredentials;
    }

    public void setNeedCredentials(int needCredentials) {
        this.needCredentials = needCredentials;
    }

    public int getQuality() {
        return quality;
    }

    public void setQuality(int quality) {
        this.quality = quality;
    }

    public int getRank() {
        return rank;
    }

    public void setRank(int rank) {
        this.rank = rank;
    }

    public int getResourceType() {
        return resourceType;
    }

    public void setResourceType(int resourceType) {
        this.resourceType = resourceType;
    }

    public int getShareMode() {
        return shareMode;
    }

    public void setShareMode(int shareMode) {
        this.shareMode = shareMode;
    }

    public int getUseCount() {
        return useCount;
    }

    public void setUseCount(int useCount) {
        this.useCount = useCount;
    }
}
