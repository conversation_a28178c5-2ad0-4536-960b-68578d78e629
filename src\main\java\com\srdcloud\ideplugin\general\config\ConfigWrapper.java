package com.srdcloud.ideplugin.general.config;

import com.intellij.DynamicBundle;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.enums.AnswerMode;
import com.srdcloud.ideplugin.general.utils.EnvUtil;

import java.util.Objects;

import static com.srdcloud.ideplugin.general.utils.PluginSettingsForSecUtilKt.pluginSettings;
import com.srdcloud.ideplugin.general.utils.IdeUtil;
import com.srdcloud.ideplugin.general.utils.PluginUtil;

public class ConfigWrapper {
    //========================= 应用信息&环境通信配置 ====================================

    // 应用名
    public static String appName = AppBundle.message("app.name");

    // 环境
    public static final String env = PluginUtil.Companion.getVersion().endsWith("beta") ?
            Constants.PROFILE_DEV : (PluginUtil.Companion.getVersion().endsWith("test") ?
            Constants.PROFILE_TEST : Constants.PROFILE_PROD);

    // 不同环境的动态配置
    public static final DynamicBundle pipelineBundle = env.equals(Constants.PROFILE_DEV) ? new PipelineBundleDev() : (env.equals(Constants.PROFILE_TEST) ? new PipelineBundleTest() : new PipelineBundle());

    /**
     * 内部版本标识，用于区分内部版才可见的功能：
     * - oauthLoginApiUri：配置值
     * - ToolWindow header栏的反馈、帮助按钮
     * - /help 快捷指令的文案
     * - 指令模板功能
     * - 问答ops操作区：知识库相关功能
     * - VersionCheckerTask：升级提醒功能
     */
    public static boolean isInnerVersion = Constants.PROFILE_TRUE.equalsIgnoreCase(pipelineBundle.getMessage("innerVersion"));

    // 调试模式标识
    public static boolean isDebugMode = Constants.PROFILE_TRUE.equalsIgnoreCase(pipelineBundle.getMessage("debugMode"));
    public static boolean isWebviewLog = Constants.PROFILE_TRUE.equalsIgnoreCase(pipelineBundle.getMessage("webviewLog"));
    public static boolean isTreeSitterMount = Constants.PROFILE_TRUE.equalsIgnoreCase(pipelineBundle.getMessage("treeSitterMount"));

    // 网关鉴权id和秘钥
    public static String oauthClientId = pipelineBundle.getMessage("oauthClientId");

    public static String oauthClientSecret = pipelineBundle.getMessage("oauthClientSecret");

    // 插件后端通信网关域名地址：通过流水线动态写入
    // TODO Secidea 后续改成我们本地读取配置栏内填写的信息,目前填写本地版本号,请手工启动 llm-prompt-server
    public static String getServerDomain() {
        return EnvUtil.isSec() ?
                Objects.requireNonNullElse(pluginSettings().getAddress(), "") :
                pipelineBundle.getMessage("serverDomain");
    }
    public static String getServerHost() {
        return EnvUtil.isSec(getServerDomain(), "https://" + getServerDomain());
    }
    public static String getServerUrl() {
        return EnvUtil.isSec(
                getServerDomain().endsWith("/") ? getServerDomain() : getServerDomain() + "/",
                "https://" + getServerDomain() + "/");
    }
    public static String getCodeAIServerUrl() {
        return EnvUtil.isSec(
                getServerDomain().replace("http", "ws") + "/" + AppBundle.message("code.ai.server.path"),
                "wss://" + getServerDomain() + "/" + AppBundle.message("code.ai.server.path"));
    }


    // 不同服务API path路径
    public static String HelpDocsPageUrl = getServerUrl() + AppBundle.message("help.docs.page.path");
    public static String PromptCenterPageUrl = getServerUrl() + AppBundle.message("prompt.center.page.path");
    public static String FeedBackPageUrl = getServerUrl() + AppBundle.message("feedback.page.path");
    public static String ComponentPageUrl = getServerUrl() + AppBundle.message("component.page.path");

    public static String oauthLoginPageUri = AppBundle.message("oauth.server.login.page.path");
    public static String oauthLoginApiUri = isInnerVersion ? AppBundle.message("oauth.server.login.api.path") : "api/usercenterbackend/" + AppBundle.message("oauth.server.login.api.path");
    public static String oauthClientRedUrl = AppBundle.message("oauth.client.redirect.url");
    public static String oauthServerRedPath = AppBundle.message("oauth.server.redirect.path");
    public static String userInvalidPath = AppBundle.message(
            "server.error.user.invalid.redirect.path");
    public static String userForbiddenPath = AppBundle.message(
            "server.error.user.forbidden.redirect.path");
    //========================= 固定配置 ====================================

    public static int ChannelType = Constants.ChannelStyle_Websocket;

    public static String ClientAnswerMode = AnswerMode.SYNC.getValue();

    public static String ClientType = "idea";

    public static String Language = "JAVA";

    public static String ConversationConditionVersion = "v1";

    public static int AsyncMessageRecvTimeout = 100;

    public static int SyncMessageRecvTimeout = 600;

    public static int WebsocketClientHeartBeatTimeout = 4;

    public static int WebsocketClientHeartBeatInterval = 10;

    public static boolean isNeedWsClientHeartbeat = true;
}
