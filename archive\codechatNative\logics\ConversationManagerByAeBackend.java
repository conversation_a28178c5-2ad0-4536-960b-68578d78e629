package com.srdcloud.ideplugin.assistant.codechatNative.logics;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.Conversation;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.ConversationMessage;
import com.srdcloud.ideplugin.general.enums.SubServiceType;
import com.srdcloud.ideplugin.remote.ChatHistoryHandler;
import com.srdcloud.ideplugin.remote.domain.Dialog.GetDialogResponse;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.Dialog;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;


/**
 * <AUTHOR> yangy
 * @create 2024/5/14 14:25
 * 会话状态管理器
 */
public class ConversationManagerByAeBackend {

    private static final Logger logger = LoggerFactory.getLogger(ConversationManagerByAeBackend.class);

    // 会话id
    private String conversationId = "";

    // 当前会话上一次请求id
    private String parentReqId = null;

    // 当前会话上一条消息id
    private String parentMessageId = UUID.randomUUID().toString();

    // 会话映射map：id与会话
    private LinkedHashMap<String, Conversation> conversations = new LinkedHashMap<>();

    public ConversationManagerByAeBackend() {
    }

    public static ConversationManagerByAeBackend getInstance(@NotNull Project project) {
        return project.getService(ConversationManagerByAeBackend.class);
    }

    public String getParentMessageId() {
        return this.parentMessageId;
    }

    public void setParentMessageId(String parentMessageId) {
        this.parentMessageId = parentMessageId;
    }

    public String getConversationId() {
        return this.conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public Map<String, Conversation> getConversations() {
        return this.conversations;
    }

    public void addConversation(Conversation conversation) {
        this.conversations.put(conversation.getId(), conversation);
    }

    public String getParentReqId() {
        return parentReqId;
    }

    public void setParentReqId(String parentReqId) {
        this.parentReqId = parentReqId;
    }

    public void removeConversation(Conversation conversation) {
        this.removeConversation(conversation.getId());
    }

    public void removeConversation(String conversationId) {
        this.conversations.remove(conversationId);
    }


    public void loadHistoryConversations(Map<String, Conversation> conversationsMap) {
        this.conversations = new LinkedHashMap();
        this.conversations.putAll(conversationsMap);
    }

    /**
     * 查询远端会话信息，填充当前会话
     */
    public static void setConversationByDialog(Conversation conversation) {
        GetDialogResponse getDialogResponse = ChatHistoryHandler.getDialog(conversation.getSubServiceType() != null ? conversation.getSubServiceType() : SubServiceType.ASSISTANT.getName(), conversation.getId());
        if (getDialogResponse != null && getDialogResponse.getDialog() != null) {

            Dialog dialog = getDialogResponse.getDialog();

            // 填充会话基本信息
            conversation.setTitle(dialog.getTitle());
            conversation.setCreateTime(dialog.getCreateTime());
            conversation.setUpdateTime(dialog.getUpdateTime());

            // 填充会话限定条件
            conversation.setModelRouteCondition(dialog.getModelRouteCondition());

            // 填充问答轮信息
            conversation.setMessagesByDialog(dialog.getQuestions());
        }
    }

    public String toJson(Map<String, Conversation> conversations) {
        JsonArray object = new JsonArray();
        int convOrder = 0;
        Iterator var4 = conversations.entrySet().iterator();

        while (var4.hasNext()) {
            Map.Entry<String, Conversation> entry = (Map.Entry) var4.next();
            Conversation conversation = (Conversation) entry.getValue();
            JsonObject convObject = new JsonObject();
            convObject.addProperty("id", conversation.getId());
            if (conversation.getTitle() != null && conversation.getTitle().length() > 100) {
                convObject.addProperty("title", conversation.getTitle().substring(0, 100));
            } else {
                convObject.addProperty("title", conversation.getTitle());
            }
            JsonArray messages = new JsonArray();
            List<ConversationMessage> listConversationMessages = conversation.getListConversationMessages();

            if (listConversationMessages != null && !listConversationMessages.isEmpty()) {
                for (int i = 0; i < listConversationMessages.size(); ++i) {
                    ConversationMessage message = (ConversationMessage) listConversationMessages.get(i);
                    if (!message.tip()) {
                        JsonObject messageObject = new JsonObject();
                        messageObject.addProperty("content", message.getContent());
                        messageObject.addProperty("me", message.me());
                        messageObject.addProperty("tip", message.tip());
                        messageObject.addProperty("messageOrder", i);
                        messageObject.addProperty("dateTime", message.getDateTime());
                        messageObject.addProperty("chatMessageType", message.getChatMessageType().getType());
                        messages.add(messageObject);
                    }
                }
            }

            convObject.add("conversations", messages);
            convObject.addProperty("conversationOrder", convOrder++);
            object.add(convObject);
        }

        return object.toString();
    }
}
