package com.srdcloud.ideplugin.webview.codechat.composer.response;

import com.srdcloud.ideplugin.webview.codechat.composer.request.ComposerRequest;

public class UndoFileDiffResponseData extends ComposerResponseData {
    private String path;
    private String error;

    public UndoFileDiffResponseData(String path, String error) {
        super(ComposerRequest.REQ_TYPE_UNDO_FILE_DIFF);
        this.path = path;
        this.error = error;
    }

    public String getPath() {
        return path;
    }

    public String getError() {
        return null;
    }
}