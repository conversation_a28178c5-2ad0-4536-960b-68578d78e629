package com.srdcloud.ideplugin.webview.codechat.indexing;

import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.codeindex.CodeIndexService;
import com.srdcloud.ideplugin.general.utils.JsonUtil;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;
import com.srdcloud.ideplugin.webview.codechat.indexing.request.IndexingRequest;
import com.srdcloud.ideplugin.webview.codechat.indexing.request.IndexingRequestData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.srdcloud.ideplugin.webview.codechat.common.IndexingReqType.START_INDEXING;
import static com.srdcloud.ideplugin.webview.codechat.common.IndexingReqType.SET_INDEXING_PAUSED;

public class IndexingHandler {
    public static final Logger logger = LoggerFactory.getLogger(IndexingHandler.class);

    private final Project project;

    private final CodeChatWebview parent;

    public IndexingHandler(Project project, CodeChatWebview parent) {
        this.project = project;
        this.parent = parent;
    }

    public void processIndexingRequest(String request) {
        IndexingRequest indexingRequest = JsonUtil.getInstance().fromJson(request, IndexingRequest.class);
        IndexingRequestData indexingRequestData = indexingRequest.getData();
        switch (indexingRequestData.getReqType()) {
            case START_INDEXING:
                //启动索引
                CodeIndexService codeIndexService = CodeIndexService.Companion.getInstance(project);
                codeIndexService.startIndexing(10);
                break;
            case SET_INDEXING_PAUSED:
                // todo
                break;
            default:
                // todo: handle exception
        }
    }
}
