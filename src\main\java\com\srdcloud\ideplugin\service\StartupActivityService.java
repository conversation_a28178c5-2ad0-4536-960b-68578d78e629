package com.srdcloud.ideplugin.service;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.startup.StartupActivity;
import com.intellij.openapi.vfs.VirtualFileManager;
import com.intellij.util.messages.MessageBusConnection;
import com.srdcloud.ideplugin.agent.AgentManager;
// import com.srdcloud.ideplugin.codecomplete.rlcc.FileChangeListener;
import com.srdcloud.ideplugin.codeindex.CodeIndexService;
import com.srdcloud.ideplugin.general.utils.*;
import com.srdcloud.ideplugin.login.LoginUtils;
import com.srdcloud.ideplugin.settings.SecIdeaProjectSettingsConfigurable;
import com.srdcloud.ideplugin.webview.treesitter.WebviewTreeSitter;
import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc.FileChangeListener;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import com.srdcloud.ideplugin.general.utils.IdeUtil;
import com.srdcloud.ideplugin.general.utils.LocalStorageUtil;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @author: Yixin
 * 项目启动时的初始化活动服务类
 * 实现StartupActivity接口，在项目启动时执行初始化任务
 */
public class StartupActivityService implements StartupActivity {

    private static final Logger logger = LoggerFactory.getLogger(StartupActivityService.class);

    /**
     * 项目启动，且indexing完成后，执行此方法
     *
     * @param project
     */
    @Override
    public void runActivity(@NotNull Project project) {
        try {
            logger.info("[cf] projectOpened:{},IDEType:{},IDEVersion:{},PluginVersion:{}", project.getName(), IdeUtil.getIDEType(), IdeUtil.getIDEVersion(), IdeUtil.getPluginVersion());
            DebugLogUtil.println(String.format("[cf] projectOpened:%s,IDEType:%s,IDEVersion:%s,PluginVersion:%s", project.getName(), IdeUtil.getIDEType(), IdeUtil.getIDEVersion(), IdeUtil.getPluginVersion()));

            //初始化LoginUtils
            LoginUtils.INSTANCE.initProject(project);
            //初始化配置文件,不初始化的话，默认为懒加载，即idea打开配置页面时才加载
            SecIdeaProjectSettingsConfigurable configurable = new SecIdeaProjectSettingsConfigurable(project);

            // 启动agent
            logger.info("[cf] Try initializing agents in StartupActivity...");
            if (LocalStorageUtil.checkIsLogin()) {
                ApplicationManager.getApplication().executeOnPooledThread(() -> {
                    AgentManager.tryInitialize(project);
                });
            } else {
                logger.info("[cf] User not login, skip initializing agents in StartupActivity...");
            }

            // 添加文件变动监听器，处理文件变动逻辑事件
            MessageBusConnection connection = project.getMessageBus().connect();
            connection.subscribe(VirtualFileManager.VFS_CHANGES, new FileChangeListener(project));

            // 非默认项目出现
            if (!project.isDefault()) {
                // Treesitter Webview加载，改为显式load
                // 已下线，rlcc内置在tabby中
                // WebviewTreeSitterLoadTask.getInstance(project).addTaskItem(project);
                // 延后30S执行，避免影响chat窗口资源加载
                //ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();
                //executorService.schedule(() -> {
                //    WebviewTreeSitter.getInstance(project).loadWebview();
                //}, 30, TimeUnit.SECONDS);

                // CodeChat Webview界面刷新:针对241往下的版本，关闭osr后如果在indexing期间展开过一次ToolWindow，则需要手动刷新一次界面来触发渲染
                // 废弃：不起效，走下面的用户引导提示
                //CodeChatWebview.getInstance(project).refreshUI();

                // 提示用户遇到样式问题需要刷新界面
                // 废弃：用户习惯已培养，无需特意提醒
                //MessageBalloonNotificationUtil.showCommonNotificationWithConfirm(project, "欢迎使用CodeFree,如遇显示问题请点击右上角刷新");

            }

            // 启动VersionCheckerForSecTask
            if (EnvUtil.isSec()) {
                new VersionCheckerForSecTask(project).queue();
            }

            // 预埋：其他项目启动时需要处理的逻辑(注：如有耗时操作，需要异步执行）
            //        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            //            ReadAction.run(() -> {
            //                // 耗时操作
            //            });
            //        });
        } catch (Exception e) {
            logger.warn("[cf] StartupActivityService runActivity error:\n");
            e.printStackTrace();
        }
    }
}
