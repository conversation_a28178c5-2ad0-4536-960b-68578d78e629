package com.srdcloud.ideplugin.webview.codechat.diff.response;

import com.srdcloud.ideplugin.webview.base.domain.WebViewCode;
import com.srdcloud.ideplugin.webview.base.domain.WebViewCommand;

public class DiffCodeResponse extends WebViewCommand {

    private WebViewCode data;

    public DiffCodeResponse(String command, WebViewCode data) {
        this.command = command;
        this.data = data;
    }

    public void setData(WebViewCode data) {
        this.data = data;
    }

    public WebViewCode getData() {
        return data;
    }

}
