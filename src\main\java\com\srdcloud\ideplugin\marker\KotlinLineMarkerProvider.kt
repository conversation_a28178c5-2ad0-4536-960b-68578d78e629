package com.srdcloud.ideplugin.marker

import com.intellij.codeInsight.daemon.RelatedItemLineMarkerInfo
import com.intellij.codeInsight.daemon.RelatedItemLineMarkerProvider
import com.intellij.openapi.editor.markup.GutterIconRenderer
import com.intellij.psi.PsiElement
import secIdea.marker.AIAssistantIcons

class KotlinLineMarkerProvider : RelatedItemLineMarkerProvider() {
    override fun collectNavigationMarkers(
        element: PsiElement,
        result: MutableCollection<in RelatedItemLineMarkerInfo<*>>
    ) {
        if (!isKtNamedFunction(element)) return

        val project = element.project
        val splitService = SplitService.getInstance(project)
        val functionBean = LineMarkerFunctionBean().apply {
            isUtValid = isValidForUnitTest(element)
            isAnnotateValid = true
            isLineAnotateValid = isValidForLineAnnotate(element)
            isCodeExplain = true
            isCodeSplit = splitService.isValidForSplit(element)
            isOptimization = true
        }

        if (functionBean.getFunctionNum() > 0) {
            val nameIdentifier = getNameIdentifier(element) ?: element
            val markerInfo = RelatedItemLineMarkerInfo(
                nameIdentifier,
                element.textRange,
                AIAssistantIcons.LINE_MARKER_ICON,
                { "海云智码快捷操作" },
                AIAssistantGutterIconClickAction(element, functionBean),
                GutterIconRenderer.Alignment.RIGHT
            ) { emptyList() }
            result.add(markerInfo)
        }
    }

    private fun isKtNamedFunction(element: PsiElement): Boolean {
        return element.javaClass.simpleName == "KtNamedFunction"
    }

    private fun getNameIdentifier(element: PsiElement): PsiElement? {
        return try {
            val nameIdentifierMethod = element.javaClass.getMethod("getNameIdentifier")
            nameIdentifierMethod.invoke(element) as? PsiElement
        } catch (e: Exception) {
            println("Error getting nameIdentifier: ${e.message}")
            null
        }
    }

    private fun isValidForUnitTest(function: PsiElement): Boolean {
        // Implement Kotlin-specific unit test validation logic using reflection if needed
        return true
    }

    private fun isValidForLineAnnotate(function: PsiElement): Boolean {
        val elementInfo = LineMarkerFunctionBean.getFunctionLinesCount(function)
        return elementInfo?.let { it.endOffset - it.startOffset <= SplitService.CODE_MAX_LENGTH } ?: false
    }
}
