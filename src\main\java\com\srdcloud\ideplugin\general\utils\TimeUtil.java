package com.srdcloud.ideplugin.general.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/19
 */
public class TimeUtil {
    private static final Logger logger = LoggerFactory.getLogger(TimeUtil.class);

    public static DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public static long getNowTimeSecTimestamp() {
        return Instant.now().getEpochSecond();
    }

    public static String getNowTimeDatetimeStr() {
        return LocalDateTime.now().format(dateTimeFormatter);
    }

    public static Date parseTimeStrToDate(String str) throws ParseException {
        return sdf.parse(str);
    }

}
