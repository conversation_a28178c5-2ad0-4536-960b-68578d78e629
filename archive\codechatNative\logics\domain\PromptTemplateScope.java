package com.srdcloud.ideplugin.assistant.codechatNative.logics.domain;

/**
 * <AUTHOR>
 * @date 2024/5/13
 * @desc 模板范围
 */
public class PromptTemplateScope {

    /**
     * 范围标识
     */
    private String key;

    /**
     * 范围显示文案
     */
    private String name;


    public PromptTemplateScope(String key, String name) {
        this.key = key;
        this.name = name;
    }


    // getter and setter...
    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    // 下拉时只显示标题
    @Override
    public String toString() {
        return name;
    }
}
