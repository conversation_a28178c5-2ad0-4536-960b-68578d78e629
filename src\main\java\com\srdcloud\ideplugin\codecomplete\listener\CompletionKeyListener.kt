package com.srdcloud.ideplugin.codecomplete.listener

import com.intellij.openapi.editor.Editor
import com.srdcloud.ideplugin.codecomplete.handle.CompletionContext.Companion.resetInlineCompletionContext
import java.awt.event.KeyAdapter
import java.awt.event.KeyEvent

/**
 * <AUTHOR>
 * @date 2025/6/6
 * @desc 取消补全提示
 */
@Deprecated("没用上，改为走 EscapeInlineCompletionHandler")
class CompletionKeyListener(private val editor: Editor) : KeyAdapter() {
    // 以下按键是采纳、触发下一条建议等
    private val usedKeys = listOf(
        KeyEvent.VK_ALT,
        KeyEvent.VK_OPEN_BRACKET,
        KeyEvent.VK_CLOSE_BRACKET,
        KeyEvent.VK_TAB,
        KeyEvent.VK_CONTROL
    )

    override fun keyReleased(event: KeyEvent) {
        // 除了进一步补全操作外的按键，都取消掉补全提示
        if (usedKeys.contains(event.keyCode)) {
            return
        }
        editor.resetInlineCompletionContext()
    }
}
