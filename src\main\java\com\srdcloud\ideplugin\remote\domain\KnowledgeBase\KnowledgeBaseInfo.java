package com.srdcloud.ideplugin.remote.domain.KnowledgeBase;

/**
 * <AUTHOR>
 * @date 2024/8/5
 * @desc 知识库基本信息实体类
 */
public class KnowledgeBaseInfo {
    private Integer kbId;
    private String kbName;
    private String kbLevel;
    private String kbInfo;
    private String organizationName;
    private Boolean isDeleted;
    private Boolean isEnabled;

    public KnowledgeBaseInfo(Integer kbId, String kbName, String kbLevel, String kbInfo, String organizationName, Boolean isDeleted, Boolean isEnabled) {
        this.kbId = kbId;
        this.kbName = kbName;
        this.kbLevel = kbLevel;
        this.kbInfo = kbInfo;
        this.organizationName = organizationName;
        this.isDeleted = isDeleted;
        this.isEnabled = isEnabled;
    }

    public Integer getKbId() {
        return kbId;
    }

    public void setKbId(Integer kbId) {
        this.kbId = kbId;
    }

    public String getKbName() {
        return kbName;
    }

    public void setKbName(String kbName) {
        this.kbName = kbName;
    }

    public String getKbLevel() {
        return kbLevel;
    }

    public void setKbLevel(String kbLevel) {
        this.kbLevel = kbLevel;
    }

    public String getKbInfo() {
        return kbInfo;
    }

    public void setKbInfo(String kbInfo) {
        this.kbInfo = kbInfo;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public Boolean getDeleted() {
        return isDeleted;
    }

    public void setDeleted(Boolean deleted) {
        isDeleted = deleted;
    }

    public Boolean getEnabled() {
        return isEnabled;
    }

    public void setEnabled(Boolean enabled) {
        isEnabled = enabled;
    }
}
