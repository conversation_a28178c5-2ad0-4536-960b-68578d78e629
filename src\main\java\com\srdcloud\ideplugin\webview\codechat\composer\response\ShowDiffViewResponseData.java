package com.srdcloud.ideplugin.webview.codechat.composer.response;

import com.srdcloud.ideplugin.webview.codechat.composer.request.ComposerRequest;

public class ShowDiffViewResponseData extends ComposerResponseData {
    private String path;
    private String error;

    public ShowDiffViewResponseData(String path, String error) {
        super(ComposerRequest.REQ_TYPE_OPEN_DIFF_VIEW_VERTICAL);
        this.path = path;
        this.error = error;
    }
 
    public String getPath() {
        return path;
    }

    public String getError() {
        return error;
    }
    
} 