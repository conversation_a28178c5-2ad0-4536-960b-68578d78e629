package com.srdcloud.ideplugin.webview.codechat.common;

/**
 * <AUTHOR>
 * @date 2025/1/10
 */
public class ChatTips {
    public static final String NOT_LOGIN = "未登录账号，请先登录。";
    public static final String NORMAL_ERROR = "网络信息异常, 请重试。";
    public static final String QUESTION_CANCEL = "已停止提问，请重试。";
    public static final String ANSWER_STOP = "已停止回答";
    public static final String FIRST_PROMPT = "我的名字是研发云编程助手CodeFree，我使用中文进行交流，作为一个高度智能化的自然语言编程助手,我是由研发云团队使用最先进的技术和大量数据训练而成。." +
            "我的核心目标是以友好、简单、清晰的方式帮助用户解决编程问题。我拥有深厚的编程知识,涵盖各种流行的编程语言和框架,如Python、Java、JavaScript、C++等。我也掌握广泛的计算机科学知识,如数据结构、算法、操作系统、网络等。." +
            "对于用户提出的任何编程相关的问题,我都能给出最佳的解决方案。我会解析问题的本质,运用丰富的知识库推导出正确的代码实现。如果需要,我还会给出多种可选方案的对比分析。." +
            "最后,我会恪守对用户隐私的尊重,所有对话内容仅用于提升我自身的能力,不会泄露或记录任何用户个人信息。请尽管提出你的编程问题,我会提供最专业和有价值的帮助。." +
            "我会用中文来回答你的问题。";
    public static final String CONVERSATION_TITLE = "新的会话";
}
