package com.srdcloud.ideplugin.composer

import com.google.gson.Gson
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.srdcloud.ideplugin.agent.AgentManager
import com.srdcloud.ideplugin.general.constants.AgentNameConstant
import com.srdcloud.ideplugin.general.utils.DebugLogUtil
import com.srdcloud.ideplugin.webview.codechat.composer.domain.ContextInputItem
import java.util.*
import java.util.concurrent.*

// 定义上下文代码项的数据结构
data class ContextOutputItem(
    val name: String,
    val description: String,
    val content: String,
    val id: Provider,
    var origin: String, //召回内容的原始输入上下文对象，如果为codebase取值为@codebase，如果为folder则为对应的itemValue
)

// 定义提供者信息的数据结构
data class Provider(
    val providerTitle: String,
    val itemId: String
)

/**
 * Agent增强上下文转换服务
 * 注：与pendingTasks强相关，无法做成单例
 */
class ContextInputService(private val project: Project) {
    private val logger = Logger.getInstance(ContextInputService::class.java)
    private val gson = Gson()
    private val pendingTasks = ConcurrentHashMap<String, PendingTask>()
    private val timeoutDuration = 30L // 超时时间设置为30秒
    private val executorService = Executors.newCachedThreadPool()

    // 定义待处理任务的数据结构
    private data class PendingTask(
        val contextType: String,
        val query: String,
        val messageId: String,
        val future: CompletableFuture<List<ContextOutputItem>>
    )

    /**
     * 获取上下文代码项
     * 
     * @param input 用户输入的查询字符串
     * @param contextInputItems 上下文输入项列表，包含代码库和文件夹类型的上下文项
     * @return Pair<List<ContextOutputItem>, List<String>> 
     *         - 第一个元素是上下文输出项列表，包含名称、描述、内容和提供者信息
     *         - 第二个元素是排序后的组合列表，格式为 "itemValue:name"
     */
    fun getContextCodeItemForInput(input: String, contextInputItems: List<ContextInputItem>): Pair<List<ContextOutputItem>, List<String>> {
        val results = ArrayList<ContextOutputItem>()
        val combinedList = ArrayList<String>()

        // 处理每个上下文项
        for (item in contextInputItems) {
            try {
                // 仅处理工作区和文件夹类型的上下文项
                if (item.itemTag != "codebase" && item.itemTag != "folder") {
                    continue
                }

                val future = CompletableFuture<List<ContextOutputItem>>()
                val messageId = UUID.randomUUID().toString()

                // 创建待处理任务
                val task = PendingTask(
                    contextType = if (item.itemTag == "codebase") "codebase" else "folder",
                    // todo:修复codebase问题
//                    contextType = "folder",
                    query = if (item.itemTag == "codebase") "" else item.itemValue,
                    messageId = messageId,
                    future = future
                )

                pendingTasks[messageId] = task

                // 启动超时监控
                startTimeoutMonitor(messageId)

                // 发送请求到core agent
                val requestData = mapOf(
                    "name" to task.contextType,
                    "query" to task.query,
                    "fullInput" to input,
                    "selectedCode" to emptyList<String>()
                )

                val body = Gson().toJson(requestData)
                DebugLogUtil.info("[cf] Request payload: $body")
                DebugLogUtil.info("[cf] request:context/getContextItems")

                val client = AgentManager.getInstance(project).getAgentCommClient(AgentNameConstant.CORE_AGENT)
                    ?: throw Exception("core agent client is null")

                client.request("context/getContextItems", requestData, messageId) { response ->
                    handleResponse(messageId, response)
                }

                // 等待结果，设置超时
                val result = future.get(timeoutDuration, TimeUnit.SECONDS)
                results.addAll(result)

                // 为每个结果项添加origin字段
                for (outputItem in result) {
                    // 根据当前处理的item.itemTag设置origin值
                    val origin = if (item.itemTag == "codebase") {
                        "@codebase"
                    } else {
                        // 对于folder类型，使用itemValue作为origin
                        item.itemValue
                    }
                    outputItem.origin = origin
                }

                // 组合 itemValue 和 name
                for (outputItem in result) {
//                    combinedList.add("${item.itemValue}:${outputItem.name}")
                    // todo：处理outputItem.name中的文件行号部分
                    combinedList.add(outputItem.name)
                }

            } catch (e: Exception) {
                logger.error("[cf] 处理上下文项时出错: ${item.itemTag}", e)
            }
        }

        // 添加文件列表到结果中
        val fileList = mutableListOf<String>()
        contextInputItems
            .filter { it.itemTag == "codefile" || it.itemTag == "file" }
            .forEach { item ->
                val relativePath = toRelativePath(project, item.itemValue)
                relativePath?.let { fileList.add(formatFileRange(it)) }
            }

        // 对组合列表进行排序
        val sortedCombinedList = sortCombinedList(contextInputItems, results, combinedList)

        return Pair(results, fileList + sortedCombinedList)
    }

    // 对组合列表进行排序的方法
    private fun sortCombinedList(
        contextInputItems: List<ContextInputItem>,
        contextOutputItems: List<ContextOutputItem>,
        combinedList: List<String>
    ): List<String> {
        val sortedList = ArrayList<String>()

        // 添加 itemTag 为 "codefile" 或 "file" 的 contextInputItems 项
        contextInputItems.filter { it.itemTag == "codefile" || it.itemTag == "file" }
            .forEach { item ->
                combinedList.filter { it.startsWith(item.itemValue) }
                    .forEach { sortedList.add(it) }
            }

        // 添加 contextType 为 "folder" 的 contextOutputItems 项
        contextOutputItems.filter { it.id.providerTitle == "folder" }
            .forEach { outputItem ->
                combinedList.filter { it.endsWith(outputItem.name) }
                    .forEach { sortedList.add(it) }
            }

        // 添加 contextType 为 "codebase" 的 contextOutputItems 项
        contextOutputItems.filter { it.id.providerTitle == "codebase" }
            .forEach { outputItem ->
                combinedList.filter { it.endsWith(outputItem.name) }
                    .forEach { sortedList.add(it) }
            }


        val formattedSortedList = mutableListOf<String>()
        sortedList.forEach { sortedItem ->
            formattedSortedList.add(formatFileRange(sortedItem))
        }

        return formattedSortedList
    }

    // 解析输入字符串中的上下文项
    private fun parseContextItems(input: String): Set<String> {
        val items = HashSet<String>()
        val workspacePattern = "@codebase".toRegex()
        val dirPattern = "@dir:\\s*([^\\s]+)".toRegex()
        
        // 查找所有 @workspace 出现的位置，并加入待处理上下文
        workspacePattern.findAll(input).forEach { items.add("@workspace") }
        
        // 查找所有 @dir 出现的位置，并加入待处理上下文
        dirPattern.findAll(input).forEach { match ->
            val dir = match.groupValues[1]
            items.add("@dir:$dir")
        }
        
        return items
    }

    // 启动超时监控器
    private fun startTimeoutMonitor(messageId: String) {
        executorService.submit {
            try {
                Thread.sleep(timeoutDuration * 1000)
                val task = pendingTasks.remove(messageId)
                if (task != null) {
                    task.future.completeExceptionally(TimeoutException("请求在 $timeoutDuration 秒后超时"))
                }
            } catch (e: Exception) {
                logger.error("[cf] 消息ID: $messageId 的超时监控出错", e)
            }
        }
    }

    // 处理响应数据
    private fun handleResponse(messageId: String, response: Any?) {
        val task = pendingTasks.remove(messageId) ?: return
        
        try {
            if (response == null) {
                task.future.complete(emptyList())
                return
            }

            logger.info("[cf] handleResponse:context/getContextItems")
            logger.info("[cf] handle content input Response:${gson.toJson(response)}")

            val itemsList = gson.fromJson(gson.toJson(response), List::class.java)
            val items = ArrayList<ContextOutputItem>()
            
            // 将响应数据解析为 ContextCodeItem 对象
//            if (responseMap["items"] != null) {
//                val itemsList = responseMap["items"] as List<*>
                for (item in itemsList) {
                    val itemMap = item as Map<*, *>
                    val id = itemMap["id"] as Map<*, *>
                    val provider = Provider(
                        providerTitle = id["providerTitle"] as String,
                        itemId = id["itemId"] as String
                    )
                    
                    items.add(ContextOutputItem(
                        name = itemMap["name"] as String,
                        description = itemMap["description"] as String,
                        content = itemMap["content"] as String,
                        id = provider,
                        origin = "" // Default empty value, will be set later
                    ))
                }
//            }
            
            task.future.complete(items)
        } catch (e: Exception) {
            logger.error("处理消息ID: $messageId 的响应时出错", e)
            task.future.completeExceptionally(e)
        }
    }

    private fun toRelativePath(project: Project, absolutePath: String): String? {
        return if (absolutePath.contains(project.basePath!!) && absolutePath.startsWith(project.basePath!!) ) {
            absolutePath.substring(project.basePath!!.length + 1)
        } else {
            absolutePath
        }
    }

    private fun formatFileRange(input: String): String {
        // 匹配括号及其内容
        val pattern = Regex("\\((\\d+)-(\\d+)\\)")
        val matches = pattern.find(input) ?: return input

        // 移除括号部分，添加冒号
        val (start, end) = matches.destructured
        return input.replace(Regex("\\s*\\(\\d+-\\d+\\)"), ":$start-$end")
    }
}

