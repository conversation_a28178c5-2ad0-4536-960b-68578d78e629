<script setup lang="ts">

import Parser from "web-tree-sitter";
import CodeObjectIndexSystem from "./rlc/codeObjectIndexSystem";
import messageSender from "./rlc/messageSender";
import type {IRelativeCodeFinder} from "./rlc/IRelativeCodeFinder";
import {JavaParser} from "./rlc/Java/JavaParser";
import {GoParser} from "./rlc/Go/GoParser";
import {JavaScriptParser} from "./rlc/JavaScript/javaScriptParser";

// =====  跨文件解析器初始化 =====
// 1、等待web-tree-sitter异步初始化完成
await Parser.init({
  locateFile(scriptName: string, scriptDirectory: string) {
    // console.log({ scriptName: scriptName, scriptDirectory: scriptDirectory })
    return scriptName;
  },
});

//2、创建各语言解析器
// 支持的跨文件解析语言列表：对标ProjectCodeIndexer中的supportedRlccLanguages，保持一致
const languageList = ["java", "go","javascript"]
const codeObjectIndexSystem = new CodeObjectIndexSystem();
// 不同语言的跨文件解析器Map
const parserDict: { [key: string]: IRelativeCodeFinder } = {}
for (const language of languageList) {
  const parser = new Parser()
  // 加载语言解析器，需要下载对应的tree-sitter语言wasm文件,放在public目录下
  const Lang = await Parser.Language.load("tree-sitter-" + language + ".wasm");
  parser.setLanguage(Lang);

  let languageParser = null;
  switch (language) {
    case "java":
      languageParser = new JavaParser(parser, codeObjectIndexSystem);
      break;
    case "go":
      languageParser = new GoParser(parser, codeObjectIndexSystem);
      break;
    case "javascript":
      languageParser = new JavaScriptParser(parser, codeObjectIndexSystem);
      break;
      //预埋：其他语言解析器扩展

    default:
      break;
  }

  if (languageParser) {
    parserDict[language] = languageParser
  }
}

//=====  注册Webview消息监听器  =====
window.addEventListener('message', (data) => {
  // 1、查询关联对象消息
  if (data.data.type == "findRelativeObjectRequest") {
    try {
      const language = data.data.language;
      const path = data.data.path;
      const content = data.data.data;
      const row = data.data.row;
      const col = data.data.col;
      const goModMapJson = data.data.goModMapJson;

      // 根据语言使用对应解析器
      if (!parserDict[language].FindRelativeObject(language, path, content, row, col, goModMapJson)) {
        window.treeSitterQuery({
          request: JSON.stringify({
            type: "findRelativeObjectNull",
          }),
          persistent: false,
          onSuccess: function (response: any) {

          },
          onFailure: function (error_code: string, error_message: string) {
            console.log(`FindRelativeObject,error_code:${error_code},error_message:${error_message}`);
          }
        })
      }
    } catch (error) {
      // todo:定位具体错误
      messageSender.sendErrorMessage("Web Tree-Sitter Find Relative Object fail");
    }
  }

  // 2、解析代码文件消息
  if (data.data.type == "parseCodeFileRequest") {
    try {
      // 根据语言使用解析器
      const language = data.data.language;
      parserDict[language].ParseFile(language, data.data.path, data.data.data);
    } catch (error) {
      // todo:定位具体错误
      messageSender.sendErrorMessage("Web Tree-Sitter Parse Code file fail");
    }
  }

  // 3、根据插件主题颜色，设置Webview样式
  if (data.data.type == "backgroundColor") {
    if (data.data.color == "dark") {
      document.body.style.backgroundColor = "rgb(51,51,51)";
    } else {
      document.body.style.backgroundColor = "rgb(209,209,209)";
    }
  }

  // 4、建立go mod路径映射
  // 临时废弃：在插件本地缓存
  // if (data.data.type == "goModMapping") {
  //   const moduleName = data.data.moduleName;
  //   const moduleDirPath = data.data.moduleDirPath;
  //   codeObjectIndexSystem.updateGoModMap(moduleName, moduleDirPath);
  // }
})


// =====  跨文件工作环境准备完成，通知插件侧可以进行后续工作  =====
try {
  window.treeSitterQuery({
    request: JSON.stringify({
      type: "webviewTreeSitterLoaded",
    }),
    persistent: false,
    onSuccess: function (response: any) {
      console.log("webviewTreeSitterLoaded,start parse all code file")
    },
    onFailure: function (error_code: string, error_message: string) {
      console.log(`webviewTreeSitterLoaded onFailure,error_code:${error_code},error_message:${error_message}`);
    }
  })
} catch (error) {
  // todo:定位具体错误
  messageSender.sendErrorMessage("Web Tree-Sitter Start fail");
}

</script>

<template>
</template>

<style scoped>
/* :global(body) {
  background-color: rgb(209,209,209);
} */
</style>
