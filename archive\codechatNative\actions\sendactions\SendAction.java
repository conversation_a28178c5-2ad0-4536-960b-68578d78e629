package com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions;

import com.alibaba.fastjson2.util.DateUtils;
import com.google.common.collect.Lists;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.EditorFactory;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Key;
import com.intellij.openapi.vfs.VirtualFile;
import com.srdcloud.ideplugin.assistant.AssistantToolWindow;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.CodeChatCompleteEngin;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.ConversationManagerByAeBackend;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.EditorSelectionListener;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.Conversation;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.ConversationMessage;
import com.srdcloud.ideplugin.assistant.codechatNative.ui.CodeChatMainPanel;
import com.srdcloud.ideplugin.assistant.codechatNative.ui.message.CodeChatMessageComponent;
import com.srdcloud.ideplugin.assistant.codechatNative.ui.message.CodeChatMessageGroupComponent;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.enums.ChatMessageType;
import com.srdcloud.ideplugin.general.enums.Language;
import com.srdcloud.ideplugin.general.enums.QuestionType;
import com.srdcloud.ideplugin.general.utils.IdeUtil;
import com.srdcloud.ideplugin.general.utils.MessageBalloonNotificationUtil;
import com.srdcloud.ideplugin.service.LoginService;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.QuoteItem;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.Objects;


/**
 * <AUTHOR> yangy
 * @create 2023/6/15 9:52
 * @description : 发送对话
 */
public class SendAction extends AnAction {

    private static final Logger logger = LoggerFactory.getLogger(SendAction.class);

    private LoginService loginService;

    private Project project;

    /**
     * @param e Carries information on the invocation place
     *
     * 用于获取当前项目，并且执行发送请求
     */
    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {
        Project project = e.getProject();
        if (project == null) {
            project = IdeUtil.findCurrentProject();
        }
        assert project != null;

        // 展开编程助手对话窗
        AssistantToolWindow.toolWindowVisible(project);

        // 根据不同项目，加载对应项目实例打开的编程助手面板的内容
        Key key = AssistantToolWindow.CODE_CHAT_TAB_KEY_MAP.get(project.getLocationHash());
        if (key == null) {
            key = IdeUtil.buildCodeChatTabKey(project.getLocationHash());
        }
        Object mainPanel = project.getUserData(key);
        if (mainPanel == null) {
            MessageBalloonNotificationUtil.showBalloonNotificationByReason(project, "插件组件加载中，请稍后", Constants.RtnCode_Main_Panel_Is_Not_Loaded);
            return;
        }

        // 获取当前编辑器
        Editor documentEditor = FileEditorManager.getInstance(project).getSelectedTextEditor();
        if(documentEditor==null){
            MessageBalloonNotificationUtil.showBalloonNotificationByReason(project, "请先选择代码", RtnCode.RtnCode_Not_Select_Text);
            return;
        }

        Document currentDocument = documentEditor.getDocument();
        Editor[] editors = EditorFactory.getInstance().getEditors(currentDocument);
        // 索引为0的编辑器就是当前获得焦点的
        Editor currentEditor = editors[0];
        // 获取选中的文本
        String selectText = currentEditor.getSelectionModel().getSelectedText();

        // 将获取的
        doActionPerformed((CodeChatMainPanel) mainPanel, selectText, null, QuestionType.NEW_ASK, null);
    }



    public void chatLogin() {
        if (loginService == null) {
            loginService = LoginService.GetInstance();
        }
    }

    /**
     * @param codeChatMainPanel 主页面，用于获取当前的选中的文本
     * @param data 需要发送请求的文本
     * @param questionType 发送的问题类型
     *
     * 执行请求发送，并且对输入内容进行处理，识别代码和文本
     * 用于发起新提问
     */
    public void doActionPerformed(CodeChatMainPanel codeChatMainPanel, String data, Integer kbId, QuestionType questionType, QuoteItem quote) {
        // Filter the empty text
        if (StringUtils.isEmpty(data)) {
            MessageBalloonNotificationUtil.showBalloonNotificationByReason(codeChatMainPanel.getProject(), "请先选择代码", RtnCode.RtnCode_Not_Select_Text);
            return;
        }

        chatLogin();

        project = codeChatMainPanel.getProject();

        // 消息类型判断
        ChatMessageType chatMessageType = getChatMessageType();
        // 判断是否为右键菜单相关功能
        boolean isRightMenu = ChatMessageType.isRightMenu(chatMessageType.getType());

        // 如果是右键popup菜单执行的快捷对话，则特殊处理
        if (isRightMenu) {
            // 获取当前编辑器
            Document currentDocument = Objects.requireNonNull(FileEditorManager.getInstance(project).getSelectedTextEditor()).getDocument();

            if (!data.isEmpty()) {
                VirtualFile virtualFile = FileDocumentManager.getInstance().getFile(currentDocument);
                if (virtualFile == null) {
                    MessageBalloonNotificationUtil.showBalloonNotificationByReason(project, "请先选择代码", RtnCode.RtnCode_Not_Select_Text);
                    return;
                }
                // 基于选中内容，拼接md格式代码块
                String extName = Language.Companion.detectLanguageName(virtualFile.getName());
                if (!extName.isEmpty() && !Constants.UNKNOWN.equals(extName)) {
                    data = "```" + extName.toLowerCase() + "\n" + data + "\n```";
                } else {
                    data = "```code\n" + data + "\n```";
                }
                data = data + "\n " + chatMessageType.getDesc();
            } else {
                MessageBalloonNotificationUtil.showBalloonNotificationByReason(project, "请先选择代码", RtnCode.RtnCode_Not_Select_Text);
                return;
            }

            //右键功能每次都是一次新会话
            CodeChatCompleteEngin.payloadPromptsChats.clear();

            // 检查并停止当前提问（如果有）
            codeChatMainPanel.checkAndStopCurrentQuestionAnswer();

            // 截取到上限
            String selectCodeSub = data;
            if (selectCodeSub.length() > 100) {
                selectCodeSub = selectCodeSub.substring(0, 100);
            }

            // 添加新会话
            codeChatMainPanel.conversationListPanel.addAndSelectNewConversation(project, chatMessageType.getDesc() + ",选中代码:" + selectCodeSub, ChatMessageType.getSubServiceTypeByMessageType(chatMessageType.getType()).getName());

            // 清除代码选中悬浮框内容
            EditorSelectionListener editorSelectionListener = codeChatMainPanel.getMySelectionListener();
            if (editorSelectionListener != null) {
                editorSelectionListener.flowPanelDisable();
            }
        } else if (chatMessageType.type == ChatMessageType.FIX_EXCEPTION.getType()) {
            //处理异常报错提问
            CodeChatCompleteEngin.payloadPromptsChats.clear();
            // 检查并停止当前提问（如果有）
            codeChatMainPanel.checkAndStopCurrentQuestionAnswer();
            String selectCodeSub = data;
            if (selectCodeSub.length() > 100) {
                selectCodeSub = selectCodeSub.substring(0, 100);
            }
            codeChatMainPanel.conversationListPanel.addAndSelectNewConversation(project, chatMessageType.getDesc() + ":" + selectCodeSub, ChatMessageType.getSubServiceTypeByMessageType(chatMessageType.getType()).getName());
        }

        // 获取当前会话
        Conversation conv = CodeChatCompleteEngin.getCurrentConversation(codeChatMainPanel);
        conv.setNewConversationAtLocal(false);

        String dataReplace = data;
        //if (!data.isEmpty() && !isRightMenu && (codeChatMainPanel.getFlowCodeViewPanelText() == null || codeChatMainPanel.getFlowCodeViewPanelText().isEmpty()) && chatMessageType.type != ChatMessageType.FIX_EXCEPTION.getType()) {
            // 为什么要替换掉用户的内容？
            //dataReplace = data.replaceAll("(?<=\\d)\\.", "\\\\.").replace("<", "&lt;").replace(">", "&gt;").replace("\n", "<br>");
        //}

        // 开始通信，渲染界面呈现通信中相关效果
        codeChatMainPanel.aroundRequest(true);

        // 构建消息UI组件
        CodeChatMessageGroupComponent contentPanel = codeChatMainPanel.getMessageAreaPanel();
        String nowDate = DateUtils.format(new Date());
        ArrayList<CodeChatMessageComponent> batchMessageComponentList = Lists.newArrayListWithExpectedSize(2);

        //新提问：保存提问的有效信息，后续正常接收到回答后再设置提问id
        ConversationMessage questionMessage = ConversationMessage.buildQuestionMessage(dataReplace, chatMessageType);
        questionMessage.setDateTime(nowDate);
        questionMessage.setFeedback("none");
        //0831版本：点选知识库API发起进一步提问，保存所用kbId以及具体引用
        questionMessage.setKbId(kbId);
        questionMessage.setQuoteItem(quote);

        conv.addConversationMessage(questionMessage, false);
        CodeChatMessageComponent questionComponent = new CodeChatMessageComponent(codeChatMainPanel, questionMessage);
        batchMessageComponentList.add(questionComponent);

        //新回答：构建消息数据，后续正常接收到回答后再加入会话
        ConversationMessage answerMessage = ConversationMessage.buildInvalidMessage("等待回复中...", chatMessageType);
        answerMessage.setFeedback("none");
        conv.addConversationMessage(answerMessage, false);

        //新回答：构建消息显示，一开始并不是有效内容，直到后端有回答出现
        CodeChatMessageComponent answerComponent = new CodeChatMessageComponent(codeChatMainPanel, answerMessage);
        batchMessageComponentList.add(answerComponent);

        contentPanel.batchAddNewMessageComponentToUI(batchMessageComponentList, false);
        // 点击发送键，切换到消息卡片
        codeChatMainPanel.changeRightPartCenter(codeChatMainPanel.codeChatMessageGroupCardName);

        //刷新上一个回答的操作区，不显示重新生成按钮
        CodeChatMessageComponent previousAnswerComponent = (CodeChatMessageComponent) codeChatMainPanel.messageAreaPanel.getPreviousAnswerComponent();
        if (previousAnswerComponent != null) {
            previousAnswerComponent.displayActionPanel();
        }

        conv.setSubServiceType(ChatMessageType.getSubServiceTypeByMessageType(chatMessageType.getType()).getName());

        if (conv.getTitle().equals(Constants.NEW_CONVERSATION_NAME)) {
            if (data.startsWith("```") && data.contains("\n")) {
                conv.setTitle(data.replaceFirst(".*\n", ""));
            } else {
                conv.setTitle(data);
            }
        }

        try {
            ConversationManagerByAeBackend.getInstance(project).addConversation(conv);
            //发送新提问，然后接收新回答：刷新显示、更新数据并加入会话、设置提问id
            CodeChatCompleteEngin codeChatCompleteEngin = new CodeChatCompleteEngin();

            // 向上获取上一个有效的回答id
            codeChatCompleteEngin.chatSend(codeChatMainPanel, answerComponent, data, kbId, chatMessageType.getType(), chatMessageType, questionType, conv.getId(), conv.getLastValidConversationAnswerRegId(), answerMessage, quote);

            // 清空聊天输入内容
            codeChatMainPanel.getInputTextArea().getTextArea().setText(null);
            // 更新聊天窗口面板内容
            SwingUtilities.invokeLater(contentPanel::updateLayoutAndAutoScroll);
        } catch (Exception e) {
            answerComponent.setAnswer(e.getMessage());
            answerComponent.setMessageContent(e.getMessage(), chatMessageType, false);
            codeChatMainPanel.aroundRequest(false);
            logger.error("[cf] GPT: Request failed, error={}", e.getMessage());
        }
    }

    /**
     * @param codeChatMainPanel
     * @param question
     * @param questionReqId
     * @param oldAnswerComponent
     *
     * 用于发起重新回答请求
     */
    public void doRegenerationPerformed(CodeChatMainPanel codeChatMainPanel, String question, String questionReqId, Integer kbId, ChatMessageType chatMessageType, CodeChatMessageComponent oldAnswerComponent, QuoteItem quote) {
        //logger.info("zhangwei1 doRegenerationPerformed：questionReqId="+questionReqId);
        if (codeChatMainPanel == null || question == null || questionReqId == null || oldAnswerComponent == null) {
            //logger.info("zhangwei1 doRegenerationPerformed: param null");
            return;
        }

        //显示停止键
        codeChatMainPanel.aroundRequest(true);

        Conversation conv = CodeChatCompleteEngin.getCurrentConversation(codeChatMainPanel);
        //ChatMessageType chatMessageType = getChatMessageType();
        CodeChatMessageGroupComponent contentPanel = codeChatMainPanel.getMessageAreaPanel();

        //重回答：删除消息区原来的回答显示
        contentPanel.deleteMessageComponentToUI(oldAnswerComponent);
        //重回答：构建消息数据，正常接收到回答后再加入会话
        ConversationMessage answerMessage = ConversationMessage.buildInvalidMessage("等待回复中...", chatMessageType);
        answerMessage.setFeedback("none");
        conv.addConversationMessage(answerMessage, true);

        //重回答：构建消息显示，一开始并不是有效内容，直到后端有回答出现
        CodeChatMessageComponent answerComponent = new CodeChatMessageComponent(codeChatMainPanel, answerMessage);
        contentPanel.addNewMessageComponentToUI(answerComponent);

        try {
            //对原问题发起重新生成的重提问，然后接收重回答：刷新显示、更新数据并加入会话、设置提问id
            CodeChatCompleteEngin codeChatCompleteEngin = new CodeChatCompleteEngin();
            codeChatCompleteEngin.chatSend(codeChatMainPanel, answerComponent, question, kbId, chatMessageType.getType(), chatMessageType, QuestionType.RE_ASK, conv.getId(), questionReqId, answerMessage, quote);
        } catch (Exception e) {
            answerComponent.setAnswer(e.getMessage());
            answerComponent.setMessageContent(e.getMessage(), chatMessageType, false);
            codeChatMainPanel.aroundRequest(false);
            logger.error("[cf] GPT: Request failed, error={}", e.getMessage());
        }
    }

    protected ChatMessageType getChatMessageType() {
        return ChatMessageType.CHAT_GENERATE;
    }

    public String getCMD() {
        return "";
    }
}
