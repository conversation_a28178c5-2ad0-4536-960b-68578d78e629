package com.srdcloud.ideplugin.codecomplete.listener

import com.intellij.openapi.editor.EditorKind
import com.intellij.openapi.editor.event.EditorFactoryEvent
import com.intellij.openapi.editor.event.EditorFactoryListener
import com.intellij.openapi.editor.impl.EditorImpl
import kotlinx.coroutines.*

/**
 * 编辑器工厂监听器，用于监听编辑器的创建、销毁等事件
 */
class CompletionEditorFactoryListener : EditorFactoryListener {

    // 监听编辑器创建事件
    override fun editorCreated(event: EditorFactoryEvent) {
        val editor = event.editor
        if (editor.project == null || editor !is EditorImpl || editor.editorKind != EditorKind.MAIN_EDITOR) return

        // 为创建的editor实例，绑定document变动监听
        CompletionDocumentListener(
            editor,
            CoroutineScope(Dispatchers.Default + SupervisorJob())
        ).listenForChanges()
    }
}

