package com.srdcloud.ideplugin.general.utils;

import com.intellij.openapi.project.Project;
import git4idea.repo.GitRemote;
import git4idea.repo.GitRepository;
import git4idea.repo.GitRepositoryManager;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

public class GitUtil {
    private static final Logger logger = LoggerFactory.getLogger(GitUtil.class);

    /**
     * 获取当前项目origin地址的项目标识码
     */
    public static String getOriginGitProjectCode(Project project) {
        if (Objects.isNull(project)) {
            return null;
        }

        String gitUrl = getGitRepositoryUrl(project);
        if (StringUtils.isBlank(gitUrl)) {
            logger.warn("[cf] getOriginGitProjectCode skip,gitUrl is null.");
            return null;
        }

        String projectCode = extractProjectCode(gitUrl);
        if (StringUtils.isBlank(projectCode)) {
            logger.warn("[cf] getOriginGitProjectCode skip,projectCode is null.");
            return null;
        }

        return projectCode;
    }


    /**
     * 根据git地址，提前项目标识码
     *
     * @param gitUrl
     * @return
     */
    public static String extractProjectCode(String gitUrl) {
        if (gitUrl == null || !gitUrl.contains("srdcloud.cn")) {
            return null;
        }

        // 截取 srdcloud.cn 之后的内容
        int startIndex = gitUrl.indexOf("srdcloud.cn") + "srdcloud.cn".length();
        String pathPart = gitUrl.substring(startIndex);

        // 去除开头的 / 或 : （例如 ssh 地址中的端口后缀）
        if (pathPart.startsWith("/") || pathPart.startsWith(":")) {
            pathPart = pathPart.substring(1);
        }

        // 去除最后一个 / 及之后的内容
        int lastIndex = pathPart.lastIndexOf("/");
        if (lastIndex == -1) {
            return null; // 没有足够层级
        }

        String projectPath = pathPart.substring(0, lastIndex);

        // 按 / 分割路径，过滤掉无效的前缀部分（如纯数字、"a" 等）
        String[] parts = projectPath.split("/");
        StringBuilder result = new StringBuilder();

        boolean validStart = false;
        for (String part : parts) {
            // 判断是否是有效的标识码段（简单规则：不是纯数字或特殊占位符）
            if (!part.matches("\\d+") && !part.equals("a") && !part.equals("subB")) {
                validStart = true;
                result.append(part).append("/");
            } else if (!validStart) {
                // 跳过无效前缀
                continue;
            }
        }

        if (result.length() > 0) {
            result.setLength(result.length() - 1); // 删除末尾多余的 /
        }

        return result.length() > 0 ? result.toString() : null;
    }

    /**
     * 获取当前工程origin git地址【单个】
     *
     * @param project
     * @return
     */
    public static String getGitRepositoryUrl(Project project) {
        String remoteUrl = "";

        // 获取 GitRepositoryManager
        GitRepositoryManager gitRepositoryManager = git4idea.GitUtil.getRepositoryManager(project);
        // 获取项目中的所有 Git 仓库
        List<GitRepository> repositories = gitRepositoryManager.getRepositories();
        // 如果项目中有至少一个 Git 仓库
        if (!repositories.isEmpty()) {
            // 获取第一个仓库
            GitRepository gitRepository = repositories.get(0);
            try {
                // 获取remote地址
                Collection<GitRemote> gitRemotes = gitRepository.getRemotes();
                if (!gitRemotes.isEmpty()) {
                    for (GitRemote gitRemote : gitRemotes) {
                        if (gitRemote.getFirstUrl() != null && !gitRemote.getFirstUrl().isEmpty()) {
                            remoteUrl = gitRemote.getFirstUrl();
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                // ignore exception
            }
        }
        return remoteUrl;
    }


    /**
     * 获取项目的多个仓库地址
     *
     * @param project
     * @return
     */
    public static List<String> getGitUrls(Project project) {
        List<String> gitUrls = new ArrayList<>();

        // 获取 GitRepositoryManager
        GitRepositoryManager gitRepositoryManager = git4idea.GitUtil.getRepositoryManager(project);
        // 获取项目中的所有 Git 仓库
        List<GitRepository> repositories = gitRepositoryManager.getRepositories();
        // 如果项目中有至少一个 Git 仓库
        if (!repositories.isEmpty()) {
            for (GitRepository gitRepository : repositories) {
                try {
                    // 获取remote地址
                    Collection<GitRemote> gitRemotes = gitRepository.getRemotes();
                    if (!gitRemotes.isEmpty()) {
                        for (GitRemote gitRemote : gitRemotes) {
                            gitUrls.add(gitRemote.getFirstUrl());
                        }
                    }
                } catch (Exception e) {
                    // ignore exception
                }
            }
        }
        return gitUrls;
    }
}
