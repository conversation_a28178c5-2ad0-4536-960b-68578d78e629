package com.srdcloud.ideplugin.remote.domain.WorkItem;

/**
 * <AUTHOR>
 * @date 2025/5/13
 * @desc 工作项基本信息实体类
 */
public class WorkItemInfo {
    private String id;                  //id
    private String workItemKey;         //编号
    private String description;         //描述
    private String title;               //标题
    private String creatorLoginName;    //创建者登录名
    private String creatorDisplayName;  //创建者显示名
    private String assigneeLoginName;   //指派人登录名
    private String assigneeDisplayName; //指派人显示名
    private String workitemTypeKey;     //工作项类型key
    private String workitemTypeName;    //工作项类型名称
    private String statusKey;           //状态key
    private String statusName;          //状态名称
    private String statusGroupKey;      //状态组key
    private String url;                 //链接

    public WorkItemInfo(String id, String workItemKey, String description, String title,
                        String creatorLoginName, String creatorDisplayName, String assigneeLoginName,
                        String assigneeDisplayName, String workitemTypeKey, String workitemTypeName,
                        String statusKey, String statusName, String statusGroupKey, String url) {
        this.id = id;
        this.workItemKey = workItemKey;
        this.description = description;
        this.title = title;
        this.creatorLoginName = creatorLoginName;
        this.creatorDisplayName = creatorDisplayName;
        this.assigneeLoginName = assigneeLoginName;
        this.assigneeDisplayName = assigneeDisplayName;
        this.workitemTypeKey = workitemTypeKey;
        this.workitemTypeName = workitemTypeName;
        this.statusKey = statusKey;
        this.statusName = statusName;
        this.statusGroupKey = statusGroupKey;
        this.url = url;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getWorkItemKey() {
        return workItemKey;
    }

    public void setWorkItemKey(String workItemKey) {
        this.workItemKey = workItemKey;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCreatorLoginName() {
        return creatorLoginName;
    }

    public void setCreatorLoginName(String creatorLoginName) {
        this.creatorLoginName = creatorLoginName;
    }

    public String getCreatorDisplayName() {
        return creatorDisplayName;
    }

    public void setCreatorDisplayName(String creatorDisplayName) {
        this.creatorDisplayName = creatorDisplayName;
    }

    public String getAssigneeLoginName() {
        return assigneeLoginName;
    }

    public void setAssigneeLoginName(String assigneeLoginName) {
        this.assigneeLoginName = assigneeLoginName;
    }

    public String getAssigneeDisplayName() {
        return assigneeDisplayName;
    }

    public void setAssigneeDisplayName(String assigneeDisplayName) {
        this.assigneeDisplayName = assigneeDisplayName;
    }

    public String getWorkitemTypeKey() {
        return workitemTypeKey;
    }

    public void setWorkitemTypeKey(String workitemTypeKey) {
        this.workitemTypeKey = workitemTypeKey;
    }

    public String getWorkitemTypeName() {
        return workitemTypeName;
    }

    public void setWorkitemTypeName(String workitemTypeName) {
        this.workitemTypeName = workitemTypeName;
    }

    public String getStatusKey() {
        return statusKey;
    }

    public void setStatusKey(String statusKey) {
        this.statusKey = statusKey;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getStatusGroupKey() {
        return statusGroupKey;
    }

    public void setStatusGroupKey(String statusGroupKey) {
        this.statusGroupKey = statusGroupKey;
    }

    public String getUrl() { return url; }

    public void setUrl(String url) {
        this.url = url;
    }
}
