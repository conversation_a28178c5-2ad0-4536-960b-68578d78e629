package com.srdcloud.ideplugin.service.domain.oauth;

/**
 * Oauth鉴权响应数据结构
 * <AUTHOR>
 */
public class AuthResponse {
    private String access_token;

    private String token_type;

    private Integer expires_in;

    private String scope;

    private String uid;

    private String info;

    private String state;

    private String redirect_uri;

    private String ori_session_id;

    public void setOri_session_id(String ori_session_id) {
        this.ori_session_id = ori_session_id;
    }

    public String getOri_session_id() {
        return ori_session_id;
    }

    public void setAccess_token(String access_token) {
        this.access_token = access_token;
    }

    public void setToken_type(String token_type) {
        this.token_type = token_type;
    }

    public void setExpires_in(Integer expires_in) {
        this.expires_in = expires_in;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public void setState(String state) {
        this.state = state;
    }

    public void setRedirect_uri(String redirect_uri) {
        this.redirect_uri = redirect_uri;
    }

    public String getAccess_token() {
        return access_token;
    }

    public String getToken_type() {
        return token_type;
    }

    public Integer getExpires_in() {
        return expires_in;
    }

    public String getScope() {
        return scope;
    }

    public String getUid() {
        return uid;
    }

    public String getInfo() {
        return info;
    }

    public String getState() {
        return state;
    }

    public String getRedirect_uri() {
        return redirect_uri;
    }

    @Override
    public String toString() {
        return "AuthResponse{" +
                "access_token='" + access_token + '\'' +
                ", token_type='" + token_type + '\'' +
                ", expires_in=" + expires_in +
                ", scope='" + scope + '\'' +
                ", uid='" + uid + '\'' +
                ", info='" + info + '\'' +
                ", state='" + state + '\'' +
                ", redirect_uri='" + redirect_uri + '\'' +
                ", ori_session_id='" + ori_session_id + '\'' +
                '}';
    }
}
