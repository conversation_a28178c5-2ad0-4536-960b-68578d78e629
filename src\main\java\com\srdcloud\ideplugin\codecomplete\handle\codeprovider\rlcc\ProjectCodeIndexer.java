package com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc;

import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc.domain.RelativeCodeObject;
import com.srdcloud.ideplugin.general.enums.LanguageExt;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import com.srdcloud.ideplugin.general.utils.FileUtil;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 代码工程全局对象索引及关联代码对象搜索类
 * 实现代码补全跨文件关联代码对象功能
 * 本类的接口暴露给插件业务层调用
 */
@Deprecated
public class ProjectCodeIndexer {
    private static final Logger logger = LoggerFactory.getLogger(ProjectCodeIndexer.class);

    private Project myProject;

    // 全局代码对象索引系统类
    // 在插件启动时，将整个代码工程主要代码文件解释，由各语言的ParseFile方法将每个代码文件进行语法树解释后，将主要的代码对象储存在这里
    // 同时，代码工程本身的文件变更也会影响本索引系统，因此增加文件删除、文件更名、包路径更新等操作，以维护索引的正确性
    // 在每次代码补全时，各代码语言的CodeFinder类将会从本索引系统中查询需要关联的代码对象
    private final CodeObjectIndexSystem codeObjectIndexSystem;

    // 跨文件关联处理器
    private final RlccHandler rlccHandler;


    // 使用LRU算法储存最近找到的代码关联对象
    // 触发代码补全，不是每个代码位置都能找到关联的代码对象，有时需要将前面几次关联出来的对象保存起来，在下一个位置进行补全时，就可以直接关联了
    // 例如在上一行声明了一个类的变量后，关联到该类对象，回车下一行进行行补全时，就可以直接使用上一次关联出的该类对象来生成代码
    class FoundRelativeCodeLRU<K, V> {
        private final int capacity;
        private final LinkedHashMap<K, V> cache;

        public FoundRelativeCodeLRU(int capacity) {
            this.capacity = capacity;
            this.cache = new LinkedHashMap<K, V>(capacity, 0.75f, true) {
                @Override
                protected boolean removeEldestEntry(Map.Entry<K, V> eldest) {
                    return size() > capacity;
                }
            };
        }

        public synchronized void put(K key, V value) {
            cache.put(key, value);
        }

        public synchronized void remove(K key) {
            cache.remove(key);
        }

        public synchronized Collection<V> getAllEntries() {
            return cache.values();
        }
    }

    // 声明储存的代码关联LRU对象
    private final FoundRelativeCodeLRU<String, RelativeCodeObject> foundRelativeCodes;

    // 获取本类对象，托管给项目Service进行隔离
    public static ProjectCodeIndexer getInstance(@NotNull Project project) {
        return project.getService(ProjectCodeIndexer.class);
    }

    // 支持的跨文件解析语言列表：后续新增其他语言支持时，添加数组元素即可；在Treesitter的webview侧，同步新增对应的语言扩展后缀名，及其对应Parser即可
    public static List<String> supportedRlccLanguages = Collections.unmodifiableList(Arrays.asList(
            LanguageExt.JAVA.getType(),
            LanguageExt.GO.getType(),
            LanguageExt.JAVASCRIPT.getType()));

    /**
     * 通过文件后缀检查是否支持某种语言的跨文件解析
     */
    public static boolean checkRlccSupport(String languageExt) {
        if (languageExt == null || languageExt.isEmpty()) {
            return false;
        }

        return supportedRlccLanguages.contains(FileUtil.getLanguageTypeByExt(languageExt));
    }

    public ProjectCodeIndexer(Project project) {
        myProject = project;

        // LRU容量：第一版暂定为4,以免打垮补全模型
        foundRelativeCodes = new FoundRelativeCodeLRU<>(4);

        // 项目隔离的缓存与跨文件处理器
        codeObjectIndexSystem = new CodeObjectIndexSystem();
        rlccHandler = new RlccHandler(myProject);
    }

    /**
     * 建立go mod映射关系
     */
    /**
     * 建立go mod路径映射
     * @param moduleName
     * @param moduleDirPath
     */
    public void buildGoModMapping(String moduleName, String moduleDirPath) {
        codeObjectIndexSystem.updateGoModMap(moduleName, moduleDirPath);
    }

    /**
     * 解释代码文件，将代码文件中的主要语法对象解释出来，并存入代码对象缓存索引系统，以提供后续跨文件对象关联使用
     * 一般在插件启动的时候遍历工程代码文件并对每个文件调用本方法，或者有文件发生非用户输入更新时调用本方法
     *
     */
    public void ParseFile(String languageExt, String filePath, String codeFileContent) {
        if (!checkRlccSupport(languageExt)) {
            DebugLogUtil.warn(String.format("ParseFile skip by checkRlccSupport,filePath：%s,languageExt:%s", filePath, languageExt));
            return;
        }


        foundRelativeCodes.remove(filePath);
        rlccHandler.ParseFile(filePath, codeFileContent, languageExt);
    }

    /**
     * 查找当前光标位置处所代码相关联的对象信息。主要为了代码补全跨文件对象关联。在用户更新代码编辑器代码时触发本方法调用
     * 当用户在输入代码，符合触发代码补全条件时，调用本接口
     *
     * @param languageExt     代码语言类型
     * @param codeFilePath    代码文件名称，为代码文件在工程中的相对路径，如:/src/main/java/com/example/main.java
     * @param codeFileContent 代码文件内容，为用户在输入后最新的代码文件内容
     * @param line            当前代码光标所在行数，从0开始计数
     * @param column          当前代码光标所在列数，从0开始计数
     * @return
     */
    public List<RelativeCodeObject> FindRelativeObject(String languageExt, String codeFilePath, String codeFileContent,
                                                             int line, int column) {
        if (!checkRlccSupport(languageExt)) {
            DebugLogUtil.warn(String.format("ParseFile skip by checkRlccSupport,codeFilePath：%s,languageExt:%s", codeFilePath, languageExt));
            return Collections.emptyList();
        }

        // 针对go语言：需要透传代码工程中go.mod声明的模块名与包绝对路径的映射关系
        String jsonExt = "";
        if (languageExt.equals(LanguageExt.GO.getExt())) {
            jsonExt = codeObjectIndexSystem.getGoModMap();
        }

        RelativeCodeObject rco = rlccHandler.FindRelativeObject(codeFilePath, codeFileContent, line, column, languageExt,jsonExt);
        // 如果找到了相关的代码对象，则存入全局LRU缓存中
        if (rco != null) {
            // 对比LRU中是否有相同的缓存存在，如果有，则无需更新LRU了，直接将现有返回
            Collection<RelativeCodeObject> rcos = foundRelativeCodes.getAllEntries();
            for (RelativeCodeObject r : rcos) {
                if (r.getRelativeObject().equalsIgnoreCase(rco.getRelativeObject()) &&
                        r.getRelativeText().equals(rco.getRelativeText())) {
                    // 已经存在，则直接返回LRU缓存，避免重复插入。
                    return new ArrayList<>(rcos);
                }
            }
            // 否则，加入LRUCache缓存
            foundRelativeCodes.put(rco.getRelativeObject(), rco);
        }
        return new ArrayList<>(foundRelativeCodes.getAllEntries());
    }

    /**
     * 删除代码工程中的一个文件
     * 当代码工程中的一个文件被用户删除后，调用本方法以触发全局代码索引系统的相应处理，将该文件以及文件中的代码对象从索引中删除
     *
     * @param fileName 被删除的文件名称，为代码文件在工程中的相对路径，如:src/main/java/com/example/main.java
     */
    public void DeleteFile(String fileName) {
        foundRelativeCodes.remove(fileName);
        codeObjectIndexSystem.deleteByFileName(fileName);
    }

    public CodeObjectIndexSystem getCodeObjectIndexSystem() {
        return codeObjectIndexSystem;
    }
}
