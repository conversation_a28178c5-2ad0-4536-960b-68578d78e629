package com.srdcloud.ideplugin.remote.domain.PromptManage;

/**
 * <AUTHOR>
 * @date 2024/5/13
 */
public class Client {
    private String type;
    private String version;
    private String pluginVersion;

    /**
     * 项目git地址字段命名
     */
    private String gitUrl;

    /**
     * 项目名字段命名
     */
    private String projectName;


    public Client(String type, String version, String pluginVersion) {
        this.type = type;
        this.version = version;
        this.pluginVersion = pluginVersion;
    }

    public Client(String type, String version, String pluginVersion, String gitUrl, String projectName) {
        this.type = type;
        this.version = version;
        this.pluginVersion = pluginVersion;
        this.gitUrl = gitUrl;
        this.projectName = projectName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getPluginVersion() {
        return pluginVersion;
    }

    public void setPluginVersion(String pluginVersion) {
        this.pluginVersion = pluginVersion;
    }


    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getGitUrl() {
        return gitUrl;
    }

    public void setGitUrl(String gitUrl) {
        this.gitUrl = gitUrl;
    }
}
