package com.srdcloud.ideplugin.webview.codechat.login;

import com.intellij.openapi.Disposable;
import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import com.srdcloud.ideplugin.general.utils.JsonUtil;
import com.srdcloud.ideplugin.service.LoginService;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;
import com.srdcloud.ideplugin.webview.codechat.common.ChatTips;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCode;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCommand;
import com.srdcloud.ideplugin.webview.base.domain.ErrorResponse;
import com.srdcloud.ideplugin.webview.base.domain.ErrorResponseCode;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.LinkedBlockingQueue;

/**
 * <AUTHOR>
 */
public class LoginCheckTask implements Disposable {
    private static final Logger logger = LoggerFactory.getLogger(LoginCheckTask.class);

    // 无界阻塞队列
    private LinkedBlockingQueue<CodeChatWebview> taskQueue = new LinkedBlockingQueue<>();

    // 工作线程
    private Thread workThread;

    public static LoginCheckTask getInstance(@NotNull Project project) {
        return project.getService(LoginCheckTask.class);
    }

    public LoginCheckTask(Project project) {
        // 启动工作线程，监听阻塞队列
        workThread = new Thread(() -> {
            try {
                while (true) {
                    // 从队列中获取一个元素，如果队列为空，则此方法会阻塞
                    CodeChatWebview webview = taskQueue.take();

                    // 检查登录状态，回传登录状态检查结果
                    handleTask(webview);
                }
            } catch (InterruptedException e) {
                logger.info("[cf] LoginCheckTask workThread exited.");
                DebugLogUtil.println("LoginCheckTask workThread exited.");
            } catch (Exception e) {
                logger.warn("[cf] LoginCheckTask workThread exception:");
                e.printStackTrace();
            }
        });
        workThread.setDaemon(true);
        workThread.start();
    }

    /**
     * 新增任务
     */
    public void addTaskItem(@NotNull CodeChatWebview webview) {
        taskQueue.offer(webview);
    }

    /**
     * 检查用户是否已登录，并返回相应的响应。
     */
    public void handleTask(final CodeChatWebview webview) {
        if (webview == null) {
            return;
        }


        // 初始化错误响应码，默认为成功
        ErrorResponseCode errorResponseCode = new ErrorResponseCode(WebViewRspCode.SUCCESS, "");

        // 获取登录服务实例并检查登录状态
        if (LoginService.getSecideaLoginStatus() != Constants.LoginStatus_OK) {
            // 如果未登录，设置错误响应码
            errorResponseCode = new ErrorResponseCode(WebViewRspCode.NOT_LOGIN, ChatTips.NOT_LOGIN);
        }

        // 创建错误响应对象
        ErrorResponse errorResponse = new ErrorResponse(WebViewRspCommand.CHECK_IF_LOGIN_RESPONSE, errorResponseCode);

        // 将响应消息发送到 WebView
        webview.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(errorResponse));
    }


    @Override
    public void dispose() {
        DebugLogUtil.println("LoginCheckTask disposed.");
        taskQueue.clear();
        workThread.interrupt();
    }
}
