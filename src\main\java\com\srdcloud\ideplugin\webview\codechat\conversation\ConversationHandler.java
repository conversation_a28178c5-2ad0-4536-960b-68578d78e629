package com.srdcloud.ideplugin.webview.codechat.conversation;

import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.codechat.ConversionManager;
import com.srdcloud.ideplugin.codechat.domain.Conversation;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.general.enums.SubServiceType;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import com.srdcloud.ideplugin.general.utils.JsonUtil;
import com.srdcloud.ideplugin.general.utils.MessageBalloonNotificationUtil;
import com.srdcloud.ideplugin.remote.ChatHistoryHandler;
import com.srdcloud.ideplugin.remote.domain.Dialog.ChatHistoryCommonResponse;
import com.srdcloud.ideplugin.remote.domain.Dialog.GetDialogResponse;
import com.srdcloud.ideplugin.remote.domain.Dialog.ListDialogsResponse;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;
import com.srdcloud.ideplugin.webview.codechat.chat.CodeChatEngin;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCode;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCommand;
import com.srdcloud.ideplugin.webview.codechat.conversation.request.*;
import com.srdcloud.ideplugin.webview.codechat.conversation.response.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/26
 * @desc 会话请求处理
 */
public class ConversationHandler {
    private static final Logger logger = LoggerFactory.getLogger(ConversationHandler.class);

    private final Project project;

    private final CodeChatWebview parent;

    private final CodeChatEngin codeChatEngin;

    private final ConversionManager conversionManager;


    public ConversationHandler(Project project, CodeChatWebview parent, ConversionManager conversionManager, CodeChatEngin codeChatEngin) {
        this.project = project;
        this.parent = parent;
        this.conversionManager = conversionManager;
        this.codeChatEngin = codeChatEngin;
    }

    /**
     * 已废弃：旧版本逻辑，从本地存储文件加载会话数据
     */
    @Deprecated
    public void conversationLoad() {
        // 暂无用到
        DebugLogUtil.println("no used,conversation-load");
    }

    /**
     * 刷新会话列表
     *
     * @param request
     */
    public void conversationRefresh(String request) {
        ConversationRefreshRequest conversationRefreshRequest = JsonUtil.getInstance().fromJson(request, ConversationRefreshRequest.class);
        ChatHistoryRequest req = conversationRefreshRequest.getData();

        // 加载远端会话列表到本地
        ListDialogsResponse listDialogsResponse = this.conversionManager.generateConversations(req);

        // 回传会话数据：总是回传本地持有的会话，即使远端刷新失败
        ConversationResponseCode conversationResponseCode = new ConversationResponseCode(listDialogsResponse.getOptResult(), this.conversionManager.getTopConversations());
        ConversationResponse conversationResponse = new ConversationResponse(WebViewRspCommand.CONVERSATION_REFRESHED, conversationResponseCode);
        parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(conversationResponse));
    }

    /**
     * 新增一个会话并激活
     *
     * @param request
     */
    public void conversationAdd(String request) {
        ConversationAddRequest conversationAddRequest = JsonUtil.getInstance().fromJson(request, ConversationAddRequest.class);
        ConversationAddRequestData requestData = conversationAddRequest.getData();
        // 如果本地会话列表为空，先尝试加载一次远端会话到本地
        if (CollectionUtils.isNotEmpty(this.conversionManager.getAllConversations())) {
            ChatHistoryRequest historyRequest = new ChatHistoryRequest();
            historyRequest.setSubService(SubServiceType.getSubServiceTypeAll());
            historyRequest.setPageNum(1);
            historyRequest.setPageDataCount(30);
            this.conversionManager.generateConversations(historyRequest);
        }

        // 创建一个新会话
        Conversation newConversation = this.conversionManager.addNewConversation(Objects.nonNull(requestData) ? requestData.getTitle() : Constants.NEW_CONVERSATION_NAME);
        // 新会话加入本地会话管理器
        this.conversionManager.addConversation(newConversation);
        // 设置到conversationEngin进行持有
        this.codeChatEngin.selectConversation(newConversation);

        // 回传新建会话后的会话列表
        ConversationResponseCode conversationResponseCode = new ConversationResponseCode(WebViewRspCode.SUCCESS, this.conversionManager.getTopConversations());
        ConversationResponse conversationResponse = new ConversationResponse(WebViewRspCommand.CONVERSATION_ADDED, conversationResponseCode);
        parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(conversationResponse));
    }

    /**
     * 修改会话标题
     *
     * @param request
     */
    public void conversationEditTitle(String request) {
        ConversationEditTitleRequest conversationEditTitleRequest = JsonUtil.getInstance().fromJson(request, ConversationEditTitleRequest.class);
        String dialogId = conversationEditTitleRequest.getData().getDialogId();
        String title = conversationEditTitleRequest.getData().getTitle();

        // 获取对话
        Conversation conv = this.conversionManager.getConversation(dialogId);

        // 如果当前会话不存在，直接返回最新会话数据
        if (conv == null) {
            ConversationChangedResponseCode conversationChangedResponseCode = new ConversationChangedResponseCode(WebViewRspCode.SUCCESS, this.conversionManager.getTopConversations());
            ConversationChangedResponse conversationChangedResponse = new ConversationChangedResponse(WebViewRspCommand.CONVERSATION_CHANGED, conversationChangedResponseCode);
            parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(conversationChangedResponse));
            return;
        }

        // 会话存在，则修改远端标题
        ChatHistoryCommonResponse editTitleResponse = ChatHistoryHandler.editDialogTitle(dialogId, title);
        if (RtnCode.SUCCESS == editTitleResponse.getOptResult()) {
            // 修改本地会话标题数据
            conv.setTitle(title);
            this.conversionManager.saveConversationTitle(conv);
        } else {
            MessageBalloonNotificationUtil.showCommonNotificationWithConfirm(this.project, editTitleResponse.getMsg());
        }

        // 总是回传最新会话列表
        ConversationChangedResponseCode conversationChangedResponseCode = new ConversationChangedResponseCode(WebViewRspCode.SUCCESS, this.conversionManager.getTopConversations());
        ConversationChangedResponse conversationChangedResponse = new ConversationChangedResponse(WebViewRspCommand.CONVERSATION_CHANGED, conversationChangedResponseCode);
        parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(conversationChangedResponse));
    }

    /**
     * 删除会话
     *
     * @param request
     */
    public void conversationRemove(String request) {
        ConversationSwitchRequest conversationRemoveRequest = JsonUtil.getInstance().fromJson(request, ConversationSwitchRequest.class);
        String dialogId = conversationRemoveRequest.getData().getDialogId();

        // 通过会话管理器，删除远端与本地会话
        this.conversionManager.removeConversation(dialogId);

        // 总是回传最新会话列表
        ConversationResponseCode conversationResponseCode = new ConversationResponseCode(WebViewRspCode.SUCCESS, this.conversionManager.getTopConversations());
        ConversationResponse conversationResponse = new ConversationResponse(WebViewRspCommand.CONVERSATION_REMOVED, conversationResponseCode);
        parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(conversationResponse));

        // 如果删除的是当前激活的会话，则重置对话引擎
        if (dialogId.equalsIgnoreCase(this.codeChatEngin.getCurrentConversation().getId())) {
            this.codeChatEngin.setCurrentConversation(null);
            this.codeChatEngin.setCurrentQuestion(null);
        }
    }


    /**
     * 切换会话
     *
     * @param request
     */
    public void conversationSwitch(String request) {
        ConversationSwitchRequest conversationSwitchRequest = JsonUtil.getInstance().fromJson(request, ConversationSwitchRequest.class);
        if (Objects.isNull(conversationSwitchRequest) || Objects.isNull(conversationSwitchRequest.getData()) || StringUtils.isBlank(conversationSwitchRequest.getData().getDialogId())) {
            logger.warn("[cf] conversationSwitchRequest skip,dialogId is null or blank.");
            return;
        }

        String dialogId = conversationSwitchRequest.getData().getDialogId();

        // 1、从会话管理器获取目标会话
        Conversation conversation = this.conversionManager.getConversation(dialogId);

        // 2、切换会话
        if (Objects.nonNull(conversation)) {
            // 切换对话引擎，持有当前会话
            codeChatEngin.selectConversation(conversation);

            // 会话是本地会话，无需反查，直接返回响应
            if (conversation.isNewConversation()) {
                ConversationSwitchResponseOptResult conversationSwitchResponseOptResult = new ConversationSwitchResponseOptResult(WebViewRspCode.SUCCESS, conversation);
                ConversationSwitchResponseCode conversationSwitchResponseCode = new ConversationSwitchResponseCode(WebViewRspCode.SUCCESS, conversationSwitchResponseOptResult);
                ConversationSwitchResponse conversationSwitchResponse = new ConversationSwitchResponse(WebViewRspCommand.SWITCH_CONVERSATION_RESPONSE, conversationSwitchResponseCode);
                parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(conversationSwitchResponse));
                return;
            }

            // 非本地会话，则发起反查
            // 更新会话数据：从远端加载到本地
            GetDialogResponse dialogResponse = this.conversionManager.updateConversation(conversation);

            // 回传切换后的会话数据
            ConversationSwitchResponseOptResult conversationSwitchResponseOptResult = new ConversationSwitchResponseOptResult(dialogResponse.getOptResult(), conversation);
            ConversationSwitchResponseCode conversationSwitchResponseCode = new ConversationSwitchResponseCode(dialogResponse.getOptResult(), conversationSwitchResponseOptResult);
            ConversationSwitchResponse conversationSwitchResponse = new ConversationSwitchResponse(WebViewRspCommand.SWITCH_CONVERSATION_RESPONSE, conversationSwitchResponseCode);
            parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(conversationSwitchResponse));
        }
    }
}
