package com.srdcloud.ideplugin.service

import com.intellij.openapi.components.PersistentStateComponent
import com.intellij.openapi.components.RoamingType
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.State
import com.intellij.openapi.components.Storage
import com.intellij.util.xmlb.XmlSerializerUtil
import com.srdcloud.ideplugin.general.constants.RtnCode.LOGOUT
import java.io.File
/**
 *  Secidea插件配置类，通过pluginSettings()获取实例
 */
@Service
@State(
    name = "SrdCloudSecIdeaApplicationSettings",
    storages = [Storage("com.srdcloud.ideplugin.service.xml", roamingType = RoamingType.DISABLED)]
)
class SecIdeaApplicationSettingsStateService : PersistentStateComponent<SecIdeaApplicationSettingsStateService> {

    var completionMode: String? = null
    var shortcutMode: String? = null
    var address: String? = null
        set(value) {
            // 监听地址变化，但从文件里初始化时不监听
            if (field != null && field != value) {
                LoginService.logout(value, LOGOUT)
            }
            field = value
        }
    var secretKey: String? = null
    var testConnection: Boolean = false
    var newPackageSaveDir: String? = "${System.getProperty("user.home")}${File.separator}.secidea${File.separator}updates"
    var isAutoDownloadNewPackage: Boolean = false
    var hasUpdateTip: Boolean = false
    var updateTipTimeInterval: String? = "1h"
    var customInstruction = ""

    var sbomTimeout = 900000
    // 新增：表示语言信息的数据类
    data class LanguageInfo(
        val name: String,
        val extension: String,
        var isDisabled: Boolean
    )

    override fun getState(): SecIdeaApplicationSettingsStateService = this

    override fun loadState(state: SecIdeaApplicationSettingsStateService) {
        XmlSerializerUtil.copyBean(state, this)
    }

    // 修改：使用新的languageMap来检查语言是否被禁用
    fun isLanguageDisabled(languageExtension: String): Boolean {
        return languageMap[languageExtension]?.isDisabled ?: false
    }

    // 修改：使用新的languageMap来启用语言
    fun enableLanguage(languageExtension: String) {
        languageMap[languageExtension]?.isDisabled = false
    }

    // 修改：使用新的languageMap来禁用语言
    fun disableLanguage(languageExtension: String) {
        languageMap[languageExtension]?.isDisabled = true
    }

    // 新增：获取所有禁用的语言
    fun getDisabledLanguages(): List<String> {
        return languageMap.filter { it.value.isDisabled }.map { it.key }
    }

    // 新增：设置禁用的语言
    fun setDisabledLanguages(disabledLanguages: List<String>) {
        languageMap.forEach { (key, value) ->
            value.isDisabled = key in disabledLanguages
        }
    }

    companion object {
        // 新增：共用的语言映射
        var languageMap: MutableMap<String, LanguageInfo> = mutableMapOf(
            "abap" to LanguageInfo("ABAP", "abap", false),
            "as" to LanguageInfo("ActionScript", "as", false),
            "ada" to LanguageInfo("Ada", "ada", false),
            "adoc" to LanguageInfo("AsciiDoc", "adoc", false),
            "am" to LanguageInfo("Makefile", "am", false),
            "applescript" to LanguageInfo("AppleScript", "applescript", false),
            "arc" to LanguageInfo("Arc", "arc", false),
            "asp" to LanguageInfo("ASP", "asp", false),
            "asm" to LanguageInfo("Assembly", "asm", false),
            "ahk" to LanguageInfo("AutoHotkey", "ahk", false),
            "au3" to LanguageInfo("AutoIt", "au3", false),
            "awk" to LanguageInfo("Awk", "awk", false),
            "bat" to LanguageInfo("Batch", "bat", false),
            "bzl" to LanguageInfo("Bazel", "bzl", false),
            "bib" to LanguageInfo("BibTeX", "bib", false),
            "bison" to LanguageInfo("Bison", "bison", false),
            "bb" to LanguageInfo("BitBake", "bb", false),
            "blade.php" to LanguageInfo("Blade", "blade.php", false),
            "sh" to LanguageInfo("Bash", "sh", false),
            "c" to LanguageInfo("C", "c", false),
            "cs" to LanguageInfo("C#", "cs", false),
            "cpp" to LanguageInfo("C++", "cpp", false),
            "cmake" to LanguageInfo("CMake", "cmake", false),
            "cob" to LanguageInfo("COBOL", "cob", false),
            "coffee" to LanguageInfo("CoffeeScript", "coffee", false),
            "cfm" to LanguageInfo("ColdFusion", "cfm", false),
            "clj" to LanguageInfo("Clojure", "clj", false),
            "css" to LanguageInfo("CSS", "css", false),
            "csv" to LanguageInfo("CSV", "csv", false),
            "cu" to LanguageInfo("CUDA", "cu", false),
            "d" to LanguageInfo("D", "d", false),
            "dart" to LanguageInfo("Dart", "dart", false),
            "dpr" to LanguageInfo("Delphi", "dpr", false),
            "pas" to LanguageInfo("Pascal", "pas", false),
            "diff" to LanguageInfo("Diff", "diff", false),
            "patch" to LanguageInfo("Patch", "patch", false),
            "dockerfile" to LanguageInfo("Dockerfile", "dockerfile", false),
            "dtd" to LanguageInfo("DTD", "dtd", false),
            "erl" to LanguageInfo("Erlang", "erl", false),
            "ex" to LanguageInfo("Elixir", "ex", false),
            "exs" to LanguageInfo("Elixir Script", "exs", false),
            "elm" to LanguageInfo("Elm", "elm", false),
            "eex" to LanguageInfo("EEx", "eex", false),
            "leex" to LanguageInfo("LiveEEx", "leex", false),
            "fs" to LanguageInfo("F#", "fs", false),
            "f" to LanguageInfo("Fortran", "f", false),
            "f90" to LanguageInfo("Fortran90", "f90", false),
            "fish" to LanguageInfo("Fish", "fish", false),
            "fth" to LanguageInfo("Forth", "fth", false),
            "glsl" to LanguageInfo("GLSL", "glsl", false),
            "go" to LanguageInfo("Go", "go", false),
            "gql" to LanguageInfo("GraphQL", "gql", false),
            "groovy" to LanguageInfo("Groovy", "groovy", false),
            "gradle" to LanguageInfo("Gradle", "gradle", false),
            "haml" to LanguageInfo("Haml", "haml", false),
            "hs" to LanguageInfo("Haskell", "hs", false),
            "hcl" to LanguageInfo("HCL", "hcl", false),
            "hlsl" to LanguageInfo("HLSL", "hlsl", false),
            "html" to LanguageInfo("HTML", "html", false),
            "htm" to LanguageInfo("HTML", "htm", false),
            "http" to LanguageInfo("HTTP", "http", false),
            "hx" to LanguageInfo("Haxe", "hx", false),
            "ini" to LanguageInfo("INI", "ini", false),
            "java" to LanguageInfo("Java", "java", false),
            "js" to LanguageInfo("JavaScript", "js", false),
            "jsx" to LanguageInfo("JSX", "jsx", false),
            "json" to LanguageInfo("JSON", "json", false),
            "jl" to LanguageInfo("Julia", "jl", false),
            "kt" to LanguageInfo("Kotlin", "kt", false),
            "kts" to LanguageInfo("Kotlin Script", "kts", false),
            "tex" to LanguageInfo("LaTeX", "tex", false),
            "less" to LanguageInfo("Less", "less", false),
            "lisp" to LanguageInfo("Lisp", "lisp", false),
            "lua" to LanguageInfo("Lua", "lua", false),
            "m" to LanguageInfo("MATLAB/Objective-C", "m", false),
            "mm" to LanguageInfo("Objective-C++", "mm", false),
            "swift" to LanguageInfo("Swift", "swift", false),
            "md" to LanguageInfo("Markdown", "md", false),
            "makefile" to LanguageInfo("Makefile", "makefile", false),
            "nix" to LanguageInfo("Nix", "nix", false),
            "ml" to LanguageInfo("OCaml", "ml", false),
            "pl" to LanguageInfo("Perl", "pl", false),
            "php" to LanguageInfo("PHP", "php", false),
            "ps1" to LanguageInfo("PowerShell", "ps1", false),
            "proto" to LanguageInfo("Protocol Buffers", "proto", false),
            "py" to LanguageInfo("Python", "py", false),
            "r" to LanguageInfo("R", "r", false),
            "rb" to LanguageInfo("Ruby", "rb", false),
            "rs" to LanguageInfo("Rust", "rs", false),
            "sass" to LanguageInfo("Sass", "sass", false),
            "scala" to LanguageInfo("Scala", "scala", false),
            "scss" to LanguageInfo("SCSS", "scss", false),
            "sql" to LanguageInfo("SQL", "sql", false),
            "styl" to LanguageInfo("Stylus", "styl", false),
            "svg" to LanguageInfo("SVG", "svg", false),
            "swift" to LanguageInfo("Swift", "swift", false),
            "tcl" to LanguageInfo("Tcl", "tcl", false),
            "tf" to LanguageInfo("Terraform", "tf", false),
            "ts" to LanguageInfo("TypeScript", "ts", false),
            "tsx" to LanguageInfo("TSX", "tsx", false),
            "twig" to LanguageInfo("Twig", "twig", false),
            "txt" to LanguageInfo("Text", "txt", false),
            "vb" to LanguageInfo("VB.NET", "vb", false),
            "vue" to LanguageInfo("Vue", "vue", false),
            "xml" to LanguageInfo("XML", "xml", false),
            "xsl" to LanguageInfo("XSLT", "xsl", false),
            "yaml" to LanguageInfo("YAML", "yaml", false),
            "yml" to LanguageInfo("YAML", "yml", false),
            "zig" to LanguageInfo("Zig", "zig", false)
        )
    }
}

