package com.srdcloud.ideplugin.webview.codechat.chat.request;

import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.StopAnswerReq;
import com.srdcloud.ideplugin.webview.base.domain.WebViewCommand;

public class StopChatRequest extends WebViewCommand {

    private StopAnswerReq data;

    public StopChatRequest(String command, StopAnswerReq data) {
        this.command = command;
        this.data = data;
    }

    public void setData(StopAnswerReq data) {
        this.data = data;
    }

    public StopAnswerReq getData() {
        return data;
    }

}
