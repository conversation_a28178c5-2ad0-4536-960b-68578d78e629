package com.srdcloud.ideplugin.webview.codechat.composer.response;

import com.srdcloud.ideplugin.webview.base.domain.WebViewCommand;

public class ComposerResponse extends WebViewCommand {

    private ComposerResponseData data;

    public ComposerResponse(String command, ComposerResponseData data) {
        super(command);
        this.data = data;
    }

    public ComposerResponseData getData() {
        return data;
    }

    public void setData(ComposerResponseData data) {
        this.data = data;
    }
}
