package com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc.domain;

/**
 * <AUTHOR>
 * @date 2024/12/30
 * @desc 关联信息缓存类
 */
public class CacheObject {
    // 对象表主键，用于唯一索引一个代码对象，同cacheTable中的key
    String primaryKey;

    // 关联对象所在的包路径
    String packagePath;

    // 关联对象所在的文件名称，为不包含路径的文件名称，如main.java
    String fileName;

    /**
     * 关联的数据类型
     *
     * @see CacheObjectType
     */
    String objectType;

    // 关联对象的名称：类名、结构体名、枚举名、包名等
    String objectName;

    /**
     * 关联对象的内容：根据不同语言不同，提取不同的关联信息
     * java类：类定义、成员变量、方法签名等
     * go结构体：结构体定义、成员变量、method签名等
     * go包：Structs、独立Functions等
     */
    String objectBody;

    public CacheObject(String primaryKey, String packagePath, String fileName, String objectType, String objectName, String objectBody) {
        this.primaryKey = primaryKey;
        this.packagePath = packagePath;
        this.fileName = fileName;
        this.objectType = objectType;
        this.objectName = objectName;
        this.objectBody = objectBody;
    }

    public String getPrimaryKey() {
        return primaryKey;
    }

    public void setPrimaryKey(String primaryKey) {
        this.primaryKey = primaryKey;
    }

    public String getPackagePath() {
        return packagePath;
    }

    public void setPackagePath(String packagePath) {
        this.packagePath = packagePath;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getObjectType() {
        return objectType;
    }

    public void setObjectType(String objectType) {
        this.objectType = objectType;
    }

    public String getObjectName() {
        return objectName;
    }

    public void setObjectName(String objectName) {
        this.objectName = objectName;
    }

    public String getObjectBody() {
        return objectBody;
    }

    public void setObjectBody(String objectBody) {
        this.objectBody = objectBody;
    }
}
