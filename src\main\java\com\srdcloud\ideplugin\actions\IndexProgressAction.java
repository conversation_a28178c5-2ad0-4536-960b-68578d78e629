package com.srdcloud.ideplugin.actions;

import com.intellij.openapi.actionSystem.ActionUpdateThread;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.Presentation;
import com.intellij.openapi.actionSystem.ex.CustomComponentAction;
import com.intellij.openapi.actionSystem.impl.ActionButton;
import com.intellij.openapi.project.DumbAware;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.popup.JBPopup;
import com.intellij.openapi.ui.popup.JBPopupFactory;

import com.intellij.ui.components.JBLabel;
import com.intellij.ui.components.JBPanel;
import com.intellij.util.ui.JBUI;
import com.intellij.util.ui.UIUtil;
import com.srdcloud.ideplugin.codeindex.CodeIndexService;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 索引进度展示 Action
 * 其中封装了一个ActionButton，在工具栏中显示索引进度，鼠标悬停时显示详细进度信息
 */
public class IndexProgressAction extends AnAction implements CustomComponentAction, DumbAware {
    private final Project project;
    private final Icon icon;
    private IndexProgressButton component;

    public IndexProgressAction() {
        // 无参构造函数，用于 plugin.xml 注册
        this.project = null;
        this.icon = null;
    }

    public IndexProgressAction(Project project, Icon icon) {
        this.project = project;
        this.icon = icon;
    }

    @Override
    public void update(@NotNull AnActionEvent e) {
        Presentation presentation = e.getPresentation();
        if (icon != null) {
            presentation.setIcon(icon);
        }
        // 根据项目状态决定是否可见/可用
        presentation.setVisible(project != null && !project.isDisposed());
        presentation.setEnabled(presentation.isVisible());
    }

    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {
        // 点击时不执行任何操作，只显示悬浮弹窗
    }

    @Override
    public @NotNull ActionUpdateThread getActionUpdateThread() {
        return ActionUpdateThread.BGT;
    }

    @Override
    public @NotNull JComponent createCustomComponent(@NotNull Presentation presentation, @NotNull String place) {
        if (component == null) {
            component = new IndexProgressButton(project, icon);
        }
        return component;
    }

    /**
     * 索引进度按钮组件
     * 当鼠标悬停时，会显示一个带有实时更新的索引进度百分比的弹窗。
     */
    private static class IndexProgressButton extends ActionButton {

        private static final Logger logger = LoggerFactory.getLogger(IndexProgressButton.class);

        private final Project project;
        private JBPopup progressPopup;
        private Timer visibilityTimer;
        private Timer progressUpdateTimer;
        private JLabel progressLabel;
        private final AtomicBoolean isMouseOverPopup = new AtomicBoolean(false);
        private final AtomicBoolean isMouseOverComponent = new AtomicBoolean(false);

        public IndexProgressButton(Project project, Icon icon) {
            super(new InternalAction(), createPresentation(icon), "IndexProgress", JBUI.size(22));
            this.project = project;
            
            if (project != null) {
                try {
                    setupMouseListeners();
                    startVisibilityTimer();
                } catch (Exception e) {
                    logger.error("[cf] Error initializing IndexProgressButton, do dispose.", e);
                    dispose();
                }
            }
        }

        private static Presentation createPresentation(Icon icon) {
            Presentation presentation = new Presentation();
            presentation.setIcon(icon);
            return presentation;
        }

        private static class InternalAction extends AnAction {
            public InternalAction() {

            }

            @Override
            public void actionPerformed(@NotNull AnActionEvent e) {
                // 点击时不执行任何操作
            }
        }

        private void startVisibilityTimer() {
            visibilityTimer = new Timer(500, e -> updateVisibility());
            visibilityTimer.setInitialDelay(0);
            visibilityTimer.start();
        }

        private void setupMouseListeners() {
            addMouseListener(new MouseAdapter() {
                @Override
                public void mouseEntered(MouseEvent e) {
                    isMouseOverComponent.set(true);
                    try {
                        showProgressPopup();
                    } catch (Exception ex) {
                        logger.warn("[cf] Error showing progress popup.", ex);
                    }
                }

                @Override
                public void mouseExited(MouseEvent e) {
                    isMouseOverComponent.set(false);
                    scheduleHidePopup();
                }
            });
        }

        private void scheduleHidePopup() {
            Timer closeTimer = new Timer(100, event -> {
                if (!isMouseOverComponent.get() && !isMouseOverPopup.get()) {
                    hideProgressPopup();
                }
            });
            closeTimer.setRepeats(false);
            closeTimer.start();
        }

        private void updateVisibility() {
            if (project == null || project.isDisposed()) {
                dispose();
                setVisible(false);
                return;
            }

            try {
                boolean indexing = CodeIndexService.Companion.getIndexStatus(project)
                        == CodeIndexService.IndexStatus.INDEXING;

                if (isVisible() != indexing) {
                    setVisible(indexing);

                    if (!indexing) {
                        hideProgressPopup();
                    }

                    Container parent = getParent();
                    if (parent != null) {
                        parent.revalidate();
                        parent.repaint();
                    }
                }
            } catch (Exception e) {
                logger.error("[cf] Error updating visibility, do dispose.", e);
                dispose();
                setVisible(false);
            }
        }

        private void showProgressPopup() {
            if (progressPopup != null && !progressPopup.isDisposed()) {
                return;
            }

            if (!isVisible() || project == null) {
                return;
            }

            // 创建内容面板
            JBPanel<JBPanel<?>> panel = new JBPanel<>(new BorderLayout());
            panel.setBorder(JBUI.Borders.empty(8, 12));

            // 创建进度标签
            progressLabel = new JBLabel();
            progressLabel.setFont(UIUtil.getLabelFont());
            panel.add(progressLabel, BorderLayout.CENTER);

            updateProgressText();

            // 监听鼠标在弹窗上的移动
            panel.addMouseListener(new MouseAdapter() {
                @Override
                public void mouseEntered(MouseEvent e) {
                    isMouseOverPopup.set(true);
                }

                @Override
                public void mouseExited(MouseEvent e) {
                    isMouseOverPopup.set(false);
                    scheduleHidePopup();
                }
            });

            // 创建弹窗
            progressPopup = JBPopupFactory.getInstance()
                    .createComponentPopupBuilder(panel, null)
                    .setRequestFocus(false)
                    .setFocusable(false)
                    .setCancelOnClickOutside(false)
                    .setCancelOnWindowDeactivation(true)
                    .setShowBorder(true)
                    .setShowShadow(true)
                    .createPopup();

            // 计算位置
            Point location = getLocationOnScreen();
            location.y += getHeight() + 5;

            progressPopup.showInScreenCoordinates(this, location);

            // 开始更新进度
            startProgressUpdateTimer();
        }

        private void startProgressUpdateTimer() {
            progressUpdateTimer = new Timer(250, e -> updateProgressText());
            progressUpdateTimer.setRepeats(true);
            progressUpdateTimer.start();
        }

        private void hideProgressPopup() {
            if (progressPopup != null && !progressPopup.isDisposed()) {
                progressPopup.cancel();
                progressPopup = null;
            }

            if (progressUpdateTimer != null) {
                progressUpdateTimer.stop();
                progressUpdateTimer = null;
            }

            progressLabel = null;
        }

        private void updateProgressText() {
            if (progressLabel == null || !isVisible() || project == null) {
                return;
            }

            if (project.isDisposed()) {
                hideProgressPopup();
                return;
            }

            if (CodeIndexService.Companion.getIndexStatus(project) == CodeIndexService.IndexStatus.INDEXING) {
                int progress = CodeIndexService.Companion.getIndexProgress(project);
                progressLabel.setText(String.format("正在更新索引(%d%%)", progress));

                if (progressPopup != null && !progressPopup.isDisposed()) {
                    progressPopup.pack(true, true);
                }
            } else {
                hideProgressPopup();
            }
        }

        private void stopAllTimers() {
            if (visibilityTimer != null) {
                visibilityTimer.stop();
                visibilityTimer = null;
            }

            if (progressUpdateTimer != null) {
                progressUpdateTimer.stop();
                progressUpdateTimer = null;
            }
        }

        private void dispose() {
            // 清理组件资源
            try {
                stopAllTimers();
                hideProgressPopup();
                isMouseOverComponent.set(false);
                isMouseOverPopup.set(false);
            } catch (Exception e) {
                logger.warn("[cf] Exception during IndexProgressButton dispose.", e);
            }
        }
    }
}