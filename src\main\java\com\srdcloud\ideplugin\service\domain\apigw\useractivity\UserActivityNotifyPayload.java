package com.srdcloud.ideplugin.service.domain.apigw.useractivity;

import com.srdcloud.ideplugin.general.enums.ActivityType;
import com.srdcloud.ideplugin.service.domain.apigw.ApigwWebsocketRequestClient;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/4/25
 * @desc 用户行为埋点上报请求体
 */
public class UserActivityNotifyPayload implements Serializable {

    /**
     * 客户端信息
     */
    private ApigwWebsocketRequestClient client;

    /**
     * @see ActivityType
     */
    private String activityType;

    /**
     * 行数
     * 需要精确到小数点后一位：当前行的计数用生成代码字符数除以整行字符数
     */
    private Double lines;

    /**
     * 次数
     */
    private Integer count;

    /**
     * 是否为自动补全
     */
    private Boolean isAuto;

    /**
     * 延迟时间（毫秒）
     */
    private Long latency;

    public UserActivityNotifyPayload() {
    }

    public ApigwWebsocketRequestClient getClient() {
        return client;
    }

    public void setClient(ApigwWebsocketRequestClient client) {
        this.client = client;
    }

    public String getActivityType() {
        return activityType;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType;
    }

    public Double getLines() {
        return lines;
    }

    public void setLines(Double lines) {
        this.lines = lines;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Boolean getIsAuto() {
        return isAuto;
    }

    public void setIsAuto(Boolean isAuto) {
        this.isAuto = isAuto;
    }

    public Long getLatency() {
        return latency;
    }

    public void setLatency(Long latency) {
        this.latency = latency;
    }
}
