package com.srdcloud.ideplugin.webview.base.handler;

import com.srdcloud.ideplugin.general.utils.EnvUtil;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import org.cef.callback.CefCallback;
import org.cef.handler.CefLoadHandler;
import org.cef.handler.CefResourceHandler;
import org.cef.misc.IntRef;
import org.cef.misc.StringRef;
import org.cef.network.CefRequest;
import org.cef.network.CefResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.Objects;

// 该代码源自JB官网
public class CustomResourceHandler implements CefResourceHandler {
    private static final Logger logger = LoggerFactory.getLogger(CustomResourceHandler.class);

    private ResourceHandlerState state = new ClosedConnection();

    @Override
    public boolean processRequest(CefRequest cefRequest, CefCallback cefCallback) {
        String url = cefRequest.getURL();
        if (url != null && !url.isEmpty()) {
            // fixme:调试用，监控资源加载转发行为
            DebugLogUtil.println("Webview CustomResourceHandler processRequest origin url:" + url);

            // 把http:// 替换为 web-views/，走插件 resources/web-views/ 目录下进行资源文件加载
            url = EnvUtil.isSec(
                    url.replace("http://" , "web-views-sec/"),
                    url.replace("http://" , "web-views/")
            );
            URL newUrl = getClass().getClassLoader().getResource(url);

            // fixme:调试用，监控资源加载转发行为
            DebugLogUtil.println("Webview CustomResourceHandler processRequest new url:" + url);

            if (Objects.isNull(newUrl)) {
                logger.warn("[cf] CustomResourceHandler processRequest fail,new url:{}", url);
                return false;
            }
            try {
                state = new OpenedConnection(newUrl.openConnection());
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            cefCallback.Continue();
            return true;
        }

        return false;
    }

    @Override
    public void getResponseHeaders(CefResponse cefResponse, IntRef intRef, StringRef stringRef) {
        state.getResponseHeaders(cefResponse, intRef, stringRef);
    }

    @Override
    public boolean readResponse(byte[] bytes, int designedBytesToRead, IntRef intRef, CefCallback cefCallback) {
        try {
            return state.readResponse(bytes, designedBytesToRead, intRef, cefCallback);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void cancel() {
//        try {
//            state.close();
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
//        state = new ClosedConnection();
    }


    public static interface ResourceHandlerState {

        // Method to get response headers
        void getResponseHeaders(CefResponse cefResponse, IntRef responseLength, StringRef redirectUrl);

        // Method to read response
        boolean readResponse(byte[] dataOut, int designedBytesToRead, IntRef bytesRead, CefCallback callback) throws IOException;

        // Method to close the resource handler
        // Note: In Java, this method will need to be implemented in each class that implements this interface
        void close() throws IOException;
    }

    public static class OpenedConnection implements ResourceHandlerState {

        private URLConnection connection;
        private InputStream inputStream;

        public OpenedConnection(URLConnection connection) throws IOException {

            this.connection = connection;
            this.inputStream = connection.getInputStream();

        }

        @Override
        public void getResponseHeaders(CefResponse cefResponse, IntRef responseLength, StringRef redirectUrl) {
            try {
                String url = connection.getURL().toString();

                if (url.contains("css")) cefResponse.setMimeType("text/css");
                else if (url.contains("js")) cefResponse.setMimeType("text/javascript");
                else if (url.contains("html")) cefResponse.setMimeType("text/html");
                else cefResponse.setMimeType(connection.getContentType());

                responseLength.set(inputStream.available());
                cefResponse.setStatus(200);

            } catch (Exception e) {
                cefResponse.setError(CefLoadHandler.ErrorCode.ERR_FILE_NOT_FOUND);
                cefResponse.setStatus(404);
                cefResponse.setStatusText(e.getLocalizedMessage());
            }
        }

        @Override
        public boolean readResponse(byte[] dataOut, int designedBytesToRead, IntRef bytesRead, CefCallback callback) throws IOException {

            int available = inputStream.available();
            if (available > 0) {
                int maxBytesToRead = Math.min(designedBytesToRead, available);
                int readNumberOfReadBytes = inputStream.read(dataOut, 0, maxBytesToRead);
                bytesRead.set(readNumberOfReadBytes);
                return true;
            } else {
                inputStream.close();
            }
            return false;
        }

        @Override
        public void close() throws IOException {
            inputStream.close();
        }
    }

    public static class ClosedConnection implements ResourceHandlerState {

        @Override
        public void getResponseHeaders(CefResponse cefResponse, IntRef responseLength, StringRef redirectUrl) {
            cefResponse.setStatus(404);
        }

        @Override
        public boolean readResponse(byte[] dataOut, int designedBytesToRead, IntRef bytesRead, CefCallback callback) {
            return false;
        }

        @Override
        public void close() {

        }
    }


}
