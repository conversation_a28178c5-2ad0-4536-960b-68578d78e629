package com.srdcloud.ideplugin.service.domain.apigw.codechat;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2024/8/5
 * @desc 知识库引用条目
 */
public class QuoteItem {
    /**
     * 引用的元数据
     */
    private QuoteMetadata metadata;

    /**
     * 引用内容展示标题
     */
    @SerializedName("page_content")
    private String pageContent;

    /**
     * 引用类型：api、guide、document、Web-帮助文档
     */
    private String type;

    public QuoteMetadata getMetadata() {
        return metadata;
    }

    public void setMetadata(QuoteMetadata metadata) {
        this.metadata = metadata;
    }

    public String getPageContent() {
        return pageContent;
    }

    public void setPageContent(String pageContent) {
        this.pageContent = pageContent;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
