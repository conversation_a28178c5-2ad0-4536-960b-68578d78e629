package com.srdcloud.ideplugin.service.domain.apigw.codechat.history;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/6/6
 * @desc 对话限定条件具体内容
 */
public class DialogConditionPayload implements Serializable {

    private Integer templateId;

    public DialogConditionPayload() {
    }

    public Integer getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Integer templateId) {
        this.templateId = templateId;
    }
}
