package com.srdcloud.ideplugin.codecomplete.domain

import com.intellij.openapi.application.ReadAction
import com.intellij.openapi.editor.Document
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.event.DocumentEvent
import com.intellij.openapi.editor.impl.EditorImpl
import com.intellij.psi.PsiFile
import com.intellij.psi.PsiManager

/**
 * <AUTHOR>
 * @date 2025/6/6
 * @desc 补全请求
 */
data class CompletionRequest(
    val file: PsiFile,
    val editor: Editor,
    val document: Document,
    val startOffset: Int,
    val endOffset: Int,
    val type: CompletionType = CompletionType.MANUAL, // 默认手动
) {

    companion object {
        /**
         * 从DocumentEvent创建补全请求，补全类型为 CompletionType.AUTO 自动补全
         */
        fun fromDocumentEvent(event: DocumentEvent, editor: EditorImpl) = fileInEditor(editor)?.let {
            CompletionRequest(
                it,
                editor,
                event.document,
                event.offset,
                0,
                CompletionType.AUTO
            )
        }

        /**
         * 从编辑器实例手动触发补全请求，补全类型为 CompletionType.MANUAL 手动补全
         */
        fun fromEditor(editor: EditorImpl): CompletionRequest? = fileInEditor(editor)?.let {
            CompletionRequest(
                it,
                editor,
                editor.document,
                0,
                editor.caretModel.offset
            )
        }

        private fun fileInEditor(editor: EditorImpl): PsiFile? {
            val virtualFile = editor.virtualFile ?: return null
            val project = editor.project ?: return null
            return ReadAction.compute<PsiFile, Throwable> { PsiManager.getInstance(project).findFile(virtualFile) }
        }
    }
}

