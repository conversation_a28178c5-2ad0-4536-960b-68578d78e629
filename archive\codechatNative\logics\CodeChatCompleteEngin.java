package com.srdcloud.ideplugin.assistant.codechatNative.logics;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.util.DateUtils;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.Conversation;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.ConversationMessage;
import com.srdcloud.ideplugin.assistant.codechatNative.ui.CodeChatMainPanel;
import com.srdcloud.ideplugin.assistant.codechatNative.ui.message.CodeChatMessageComponent;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.enums.*;
import com.srdcloud.ideplugin.general.utils.CodeUtil;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import com.srdcloud.ideplugin.general.utils.HtmlUtil;
import com.srdcloud.ideplugin.general.utils.MessageBalloonNotificationUtil;
import com.srdcloud.ideplugin.remote.ChatHistoryHandler;
import com.srdcloud.ideplugin.service.QuestionTask;
import com.srdcloud.ideplugin.service.UserActivityReportService;
import com.srdcloud.ideplugin.service.domain.apigw.ApigwWebsocketRespPayload;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.CodeChatPrompt;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.CodeChatQuote;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.QuoteItem;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.DialogCondition;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.DialogConditionPayload;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.StopAnswerReq;
import com.srdcloud.ideplugin.service.interfaces.IQuestionTaskEventHandler;
import com.srdcloud.ideplugin.statusbar.Notify;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> yangy
 * @create 2023/9/6 15:18
 * 编程助手问答处理引擎
 */
public class CodeChatCompleteEngin implements IQuestionTaskEventHandler {

    private static Logger logger = LoggerFactory.getLogger(CodeChatCompleteEngin.class);

    private QuestionTask currQuestion;

    private static int currentIndex = 0;

    private static String CODE_END = "`";

    // 等待响应(未收到任何一条下行)
    public static boolean isWaitResp = false;

    // 正在返回答案
    public static boolean onAnswerStatus = false;

    private Project project;
    public static CodeChatMainPanel codeChatMainPanel;
    public CodeChatMessageComponent component;
    private ConversationMessage message;


    // 记录当前应用中的会话
    private static Conversation currConversation;

    // 当前会话限定条件
    public static DialogCondition currModelRouteCondition;

    // 当前会话限定条件所用模板Id
    public static String currConversationConditionTemplateId;

    // 当前会话问答轮
    public static LinkedList<CodeChatPrompt> payloadPromptsChats = new LinkedList<>();

    private String currQuestionQuestionType = QuestionType.NEW_ASK.getName();
    private String currQuestionQuestionData;
    private Integer currQuestionQuestionKbId;
    private String currQuestionParentReqId;

    // 当前次提问所使用的模板Id(点选模板时记录，发送消息后重置、清空聊天框内容时重置)
    public static String currUsePromptTemplateId;

    public void chatSend(CodeChatMainPanel mp, CodeChatMessageComponent cp, String data, Integer kbId, Integer manualType, ChatMessageType chatMessageType, QuestionType questionType, String dialogId, String parentReqId, ConversationMessage message, QuoteItem quote) {
        currQuestion = new QuestionTask(this, AnswerMode.ASYNC.getValue());
        this.project = mp.getProject();
        codeChatMainPanel = mp;
        //回答的view
        this.component = cp;
        //回答的model
        this.message = message;
        codeChatMainPanel.setRequestHolder(currQuestion);
        //新提问或重提问
        currQuestionQuestionType = questionType.getName();

        //当前提问的上次回答id
        currQuestionParentReqId = parentReqId;

        // 禁用输入框
        codeChatMainPanel.disableInputArea();
        codeChatMainPanel.disableAddNewConversation();

        // 新建会话过程并点选模板提问，自行拼接Condition
        if (Objects.isNull(currModelRouteCondition) || Objects.isNull(currModelRouteCondition.getPayload())) {
            currModelRouteCondition = buildDialogCondition();
        }

        // 当有新的模板点选提问行为，则滚动更新currConversationConditionTemplateId，否则延续当前轮会话上一次conditionTemplateId
        if (StringUtils.isNotBlank(currUsePromptTemplateId)) {
            currConversationConditionTemplateId = currUsePromptTemplateId;
            currModelRouteCondition.getPayload().setTemplateId(Integer.valueOf(currConversationConditionTemplateId));
        }

        // 发起提问
        String reqId = UUID.randomUUID().toString();
        Integer askResult = currQuestion.AskQuestion(reqId, data, kbId, Constants.QUESTION_TASK_TYPE_CHAT, null, null, null, manualType, null, questionType, dialogId, parentReqId, currUsePromptTemplateId, quote, null);
        // 提问后，重置临时提问Prompt模板ID
        CodeChatCompleteEngin.currUsePromptTemplateId = null;
        // 根据提问结果，处理相应交互
        if (askResult == RtnCode.NOT_LOGIN || askResult == RtnCode.NO_CHANNEL) {
            onTaskError(reqId, askResult, null);
        } else {
            currQuestionQuestionData = data;
            currQuestionQuestionKbId = kbId;
            isWaitResp = true;
            onAnswerStatus = true;
            // 返回重新设置parent id
            ConversationManagerByAeBackend.getInstance(project).setParentReqId(reqId);
        }
        currentIndex = 0;
    }

    public static DialogCondition buildDialogCondition() {
        final DialogCondition condition = new DialogCondition();
        final DialogConditionPayload payload = new DialogConditionPayload();
        payload.setTemplateId(StringUtils.isNotBlank(currConversationConditionTemplateId) ? Integer.valueOf(currConversationConditionTemplateId) : null);
        // 预埋：后续可扩展更多会话限定规则

        condition.setVersion(ConfigWrapper.ConversationConditionVersion);
        condition.setPayload(payload);
        return condition;
    }

    /**
     * 设置当前对话引擎中激活的会话
     */
    public static void setCurrentConversation(Conversation conv) {
        currConversation = conv;
    }

    /**
     * 获取当前对话引擎中激活的会话
     *
     * @return
     */
    public static Conversation getCurrentConversation(CodeChatMainPanel codeChatMainPanel) {
        if (currConversation == null) {
            currConversation = codeChatMainPanel.conversationListPanel.getSelectedConversation();
        }
        return currConversation;
    }

    public static ConcurrentHashMap<String, ScheduledExecutorService> executorServiceMap = new ConcurrentHashMap<>();
    public static ConcurrentHashMap<String, Answer> answerMap = new ConcurrentHashMap<String, Answer>();

    private static final int DELAY_PER_CHAR = 10;

    public static class Answer {
        int isEnd;
        String answer;

        ChatMessageType chatMessageType;

        String dateTime;

        boolean resetAnswer;

        public int getIsEnd() {
            return isEnd;
        }

        public void setIsEnd(int isEnd) {
            this.isEnd = isEnd;
        }

        public String getAnswer() {
            return answer;
        }

        public void setAnswer(String answer) {
            this.answer = answer;
        }

        public ChatMessageType getChatMessageType() {
            if (chatMessageType == null) {
                return ChatMessageType.CHAT_GENERATE;
            }
            return chatMessageType;
        }

        public void setChatMessageType(ChatMessageType chatMessageType) {
            this.chatMessageType = chatMessageType;
        }

        public String getDateTime() {
            if (dateTime == null || dateTime.isEmpty()) {
                return DateUtils.format(new Date());
            }
            return dateTime;
        }

        public void setDateTime(String dateTime) {
            this.dateTime = dateTime;
        }

        public boolean isResetAnswer() {
            return resetAnswer;
        }

        public void setResetAnswer(boolean resetAnswer) {
            this.resetAnswer = resetAnswer;
        }
    }

    @Override
    public void onAnswer(String reqId, int isEnd, String answer, int seqNo, final ApigwWebsocketRespPayload payload) {
        // 收到应答
        isWaitResp = false;
        // 判断是否为当前交互提问的应答
        if (!reqId.equals(currQuestion.GetReqId())) {
            logger.error("[cf] CodeChatCompleteEngin OnAnswer error,reqId:{} not match currQuestion reqId.", reqId);
            return;
        }

        int rtnCode = RtnCode.SUCCESS;
        String errMsg = null;
        boolean resetAnswer = false;
        boolean validContent;
        CodeChatQuote quote = null;
        if (payload != null) {
            rtnCode = payload.getRetCode();
            errMsg = payload.getErrMsg();
            quote = payload.getQuote();
        }

        // 0614版本：敏感词而导致回退的应答，需要携带到后续问答的异常信息，纳入answer处理，但是不纳入点赞点踩有效内容范围
        // 0831版本：知识库删除或被禁用
        if (QuestionTask.needConvertErrMsgToAnswer(rtnCode)) {
            // 异常情况，回退原有答案内容
            answer = StringUtils.isNotBlank(errMsg) ? errMsg : (Constants.Rtn_Messages_Map.getOrDefault(rtnCode, "响应异常，请稍后重试。"));
            resetAnswer = true;
            logger.warn("[cf] CodeChatCompleteEngin OnAnswer resetAnswer,reqId:{},rtnCode:{},resetAnswerContent:{}.", reqId, rtnCode, answer);
        }

        DebugLogUtil.info(String.format("CodeChatCompleteEngin OnAnswer reqId:%s,seqNo:%s,rtnCode:%s,isEnd:%s,answer:%s,resetAnswer:%s", reqId, seqNo, rtnCode, isEnd, answer, resetAnswer));

        try {

            // 异常情况：如果服务端只回传一条空信息并且是结束的状态，则为无效响应
            if ((isEnd == Constants.IS_ANSWER_END_TRUE && (answer == null || answer.isEmpty()) && seqNo == 0)) {
                logger.error("[cf] CodeChatCompleteEngin OnAnswer inValid,answer:{}", answer);
                component.setMessageContent("服务异常，请稍后重新提问当前问题。", ChatMessageType.CHAT_GENERATE, false);
                codeChatMainPanel.aroundRequest(false);
                codeChatMainPanel.getSerialThreadPool().shutdown();
                onAnswerStatus = false;
                if (currQuestion != null) {
                    currQuestion.removeQuestionMapByReqId(reqId);
                }
                //保存响应的错误消息
                return;
            }

            // 正常情况：提取应答内容，进行界面渲染，加入对话上下文prompts列表
            onAnswerStatus = true;
            ConversationManagerByAeBackend.getInstance(project).setParentReqId(reqId);
            ConversationManagerByAeBackend.getInstance(project).setParentMessageId(reqId);
            component.setAnswer(answer);
            component.setReqId(reqId);

            Answer answerNew = new Answer();
            answerNew.setAnswer(answer);
            answerNew.setIsEnd(isEnd);
            answerNew.setResetAnswer(resetAnswer);
            answerNew.setDateTime(DateUtils.format(new Date()));

            if (currConversation != null) {
                component.setDialogId(currConversation.getId());
                if (currConversation.getListConversationMessages() != null && !currConversation.getListConversationMessages().isEmpty()) {
                    ConversationMessage lastMessage = currConversation.getListConversationMessages().get(currConversation.getListConversationMessages().size() - 1);
                    if (lastMessage.getChatMessageType() != null) {
                        answerNew.setChatMessageType(lastMessage.getChatMessageType());
                    }
                }
            }

            // 如果当前regId已被加入线程池打印map，则持续更新answer即可
            if (executorServiceMap.get(reqId) != null) {
                answerMap.put(reqId, answerNew);
            } else {
                // 请求id第一次出现，则新开一个单线程池来进行该请求回答的持续打印操作
                ScheduledExecutorService executor = executorServiceMap.computeIfAbsent(reqId, id -> {
                    return Executors.newSingleThreadScheduledExecutor();
                });

                // 通过10ms固定执行频率进行本请求答案打印，模拟打字机效果
                executor.scheduleAtFixedRate(() -> {
                    // 显示内容
                    Map<String, Answer> currentMap = new HashMap<>(answerMap);
                    Answer answerCurrent = currentMap.get(reqId);
                    if (answerCurrent == null || answerCurrent.getAnswer() == null || answerCurrent.getAnswer().isEmpty()) {
                        answerMap.put(reqId, answerNew);
                        answerCurrent = answerNew;
                    }

                    // 异常情况二：异常响应需要回退答案直接打印回退答案
                    if (answerCurrent.isResetAnswer()) {
                        currentIndex = answerCurrent.getAnswer().length();
                        String answerOrigin = answerCurrent.getAnswer().substring(0, currentIndex);
                        String answerHtml = HtmlUtil.md2html(answerOrigin);
                        setAnswerToMessageComponent(isEnd, answerOrigin, answerHtml, answerCurrent.getChatMessageType());
                        currentIndex++;
                    }

                    // 正常情况：逐个下标移动，打印字符
                    if (currentIndex <= answerCurrent.getAnswer().length()) {
                        int subLength = currentIndex;
                        if (currentIndex > answerCurrent.getAnswer().length()) {
                            subLength = answerCurrent.getAnswer().length();
                            currentIndex = answerCurrent.getAnswer().length();
                        }
                        // 截取答案
                        String answerOrigin = answerCurrent.getAnswer().substring(0, subLength);
                        if (answerOrigin.endsWith(CODE_END) && subLength < answerCurrent.getAnswer().length()) {
                            currentIndex = currentIndex + 8;
                        } else {
                            // 转换html后渲染到panel
                            String answerHtml = HtmlUtil.md2html(answerOrigin);
                            setAnswerToMessageComponent(isEnd, answerOrigin, answerHtml, answerCurrent.getChatMessageType());

                            // 移动光标，下次线程调度时打印下一位字符，模拟打字机效果
                            currentIndex++;
                        }
                    } else {
                        // 渲染光标移动到已接收答案内容末尾，则判断是网络导致后续未返回或是已经到达end
                        // 判断是否收到结束信号
                        if (answerCurrent.getIsEnd() == Constants.IS_ANSWER_END_TRUE) {
                            //新回答或重回答：回答结束，变为有效内容，更新显示操作区域图标
                            codeChatMainPanel.aroundRequest(false);

                            message.setContent(answerCurrent.getAnswer());
                            message.setDateTime(answerCurrent.getDateTime());
                            message.setReqId(reqId);

                            codeChatMainPanel.getSerialThreadPool().shutdown();
                            executor.shutdown();
                            executorServiceMap.remove(reqId);
                            answerMap.remove(reqId);
                            onAnswerStatus = false;

                            component.displayActionPanel();
                        }
                    }
                }, 0, DELAY_PER_CHAR, TimeUnit.MILLISECONDS);
            }

            // 下行收到回答返回结束，进行相关清理操作
            if (isEnd == Constants.IS_ANSWER_END_TRUE) {
                // debug用
                //System.out.printf(JSONObject.toJSONString(payload));

                // 保存对话上下文
                CodeChatPrompt codeChatPrompt = new CodeChatPrompt();
                codeChatPrompt.setContent(answer);
                codeChatPrompt.setRole(PromptRoleType.ASSISTANT.getType());
                payloadPromptsChats.add(codeChatPrompt);

                // 保存响应的有效回答消息
                if (resetAnswer) {
                    message.setNodeType(MessageNodeType.VALID_ERROR);
                } else {
                    message.setNodeType(MessageNodeType.ANSWER);
                }

                // 保存消息的扩展描述、知识库应用
                message.setQuote(quote);

                //设置回答消息对应的提问消息id
                ConversationMessage parent = message.getParent();
                if (currQuestionQuestionType.equals(QuestionType.NEW_ASK.getName())) {
                    parent.setReqId(reqId);
                } else {
                    parent.setReqId(currQuestionParentReqId);
                }

                codeChatMainPanel.enableInputArea();
                component.displayActionPanel();

                // 用UI控件，展示知识库引用内容
                component.displayQuote();

                // 从聊天引擎移除当前已结束的问题
                if (currQuestion != null) {
                    currQuestion.removeQuestionMapByReqId(reqId);
                }

                currQuestion = null;

                // 上报代码行为统计
                codeChatActivityReport(answer);
            }

        } catch (Exception e) {
            logger.error("[cf] CodeChatCompleteEngin OnAnswer error:{}", e.getMessage());
            component.setMessageContent("网络信息异常, 请重试。", ChatMessageType.CHAT_GENERATE, false);
            codeChatMainPanel.aroundRequest(false);
            codeChatMainPanel.getSerialThreadPool().shutdown();
            onAnswerStatus = false;
            if (currQuestion != null) {
                currQuestion.removeQuestionMapByReqId(reqId);
            }
            e.printStackTrace();
        }
    }

    private void codeChatActivityReport(final String answer) {
        // 计算是否包含代码块
        if (CodeUtil.countCodeBlocks(answer) == 0) {
            return;
        }
        // 提取所有代码块
        ArrayList<String> codeBlocks = CodeUtil.extractCodeBlocks(answer);
        if (CollectionUtils.isEmpty(codeBlocks)) {
            return;
        }
        // 拼接代码块在一起
        StringBuilder sb = new StringBuilder();
        for (String s : codeBlocks) {
            sb.append(s);
        }
        // 进行上报
        UserActivityReportService.codeActivityReport(UserActivityType.chat_gen_code, sb.toString(), null);
    }

    public void stopAnswer(String reqId) {
        Conversation conversation = currConversation;
        try {
            String answer = Objects.nonNull(answerMap) && Objects.nonNull(answerMap.get(reqId)) ? answerMap.get(reqId).getAnswer() : "";
            if (conversation != null) {
                StopAnswerReq stopAnswerReq = new StopAnswerReq();
                stopAnswerReq.setReqId(reqId);
                stopAnswerReq.setDialogId(conversation.getId());
                stopAnswerReq.setQuestionType(currQuestionQuestionType);
                stopAnswerReq.setSubService(conversation.getSubServiceType());
                stopAnswerReq.setQuestion(currQuestionQuestionData);
                stopAnswerReq.setKbId(currQuestionQuestionKbId);
                stopAnswerReq.setAnswer(answer);
                stopAnswerReq.setParentReqId(currQuestionParentReqId);
                stopAnswerReq.setModelRouteCondition(currModelRouteCondition);
                // 停止本次回答，尝试一次原则，不影响主流程执行
                // 此处不能异步，会导致停止回答case下，历史会话列表刷新不一致
                ChatHistoryHandler.stopAnswer(stopAnswerReq);
            }
        } catch (Exception e) {
            logger.error("[cf] stopAnswer regId:{},error:{}", reqId, e.getMessage());
        }
    }

    private void setAnswerToMessageComponent(int isEnd, String answerOrigin, String answerHtml, ChatMessageType chatMessageType) {
        component.setAnswer(answerOrigin);
        component.setMessageContent(answerHtml, chatMessageType, false);
    }

    @Override
    public void onTaskError(String regId, int eventId, final ApigwWebsocketRespPayload payload) {
        logger.warn("[cf] CodeChatCompleteEngin OnTaskError......regId:{},eventId:{},isWaitResp:{},payload:{}", regId, eventId, isWaitResp, JSON.toJSONString(payload));
        if (eventId == Constants.RtnCode_CANCEL) {
            // 客户端触发“停止回答”操作
            if (isWaitResp) {
                // 等待回答过程中按结束键,更新本次回答的regId,否则下次提问没有正确的parentId
                ConversationManagerByAeBackend.getInstance(project).setParentReqId(regId);
                ConversationManagerByAeBackend.getInstance(project).setParentMessageId(regId);
                component.setMessageContent("已停止回答", ChatMessageType.CHAT_GENERATE, false);
                message.setContent("已停止回答");
                message.setNodeType(MessageNodeType.VALID_ERROR);
                message.setReqId(regId);
            } else {
                // 更新消息类型状态为可有效消息，并更新UI
                if (answerMap.containsKey(regId)) {
                    Answer answerCurrent = answerMap.get(regId);
                    message.setNodeType(MessageNodeType.ANSWER);
                    message.setReqId(regId);
                    message.setContent(answerCurrent.getAnswer());
                }

                ConversationMessage parent = message.getParent();
                if (currQuestionQuestionType.equals(QuestionType.NEW_ASK.getName())) {
                    parent.setReqId(regId);
                } else {
                    parent.setReqId(currQuestionParentReqId);
                }
            }

            // 更新消息类型状态为可显示错误，并更新UI
            component.displayActionPanel();
            codeChatMainPanel.enableInputArea();
        } else {
            // 变化状态栏
            Notify.Companion.setNotifyMsg(Constants.Rtn_Messages_Map.getOrDefault(eventId, ""));
            ApplicationManager.getApplication().invokeLater(Notify.Companion::updateStatusNotify);

            String answer = "网络信息异常, 请重试";

            // 尝试根据异常码，提取对应文案
            if (Constants.Rtn_Messages_Map.containsKey(eventId)) {
                answer = Constants.Rtn_Messages_Map.get(eventId);
            }

            // 特殊场景：未登录，弹出提醒
            if (eventId == RtnCode.NOT_LOGIN) {
                answer = "未登录账号，请先登录";

                ApplicationManager.getApplication().invokeLater(() -> {
                    MessageBalloonNotificationUtil.showBalloonNotificationByReason(project, "未登录账号，请先登录。", eventId);
                });
            }

            // 特殊场景：用户被禁用
            //if (eventId == RtnCode.USER_FORBIDDEN) {
            //    LoginService.GetInstance().setUserStatus(Constants.UserStatus_NOK);
            //    answer = "服务被禁用";
            //    ApplicationManager.getApplication().invokeLater(() -> {
            //        MessageDialogUtil.showUserBanDialog();
            //    });
            //}

            // 特殊场景：WebSocket断连，尝试重新建立连接
            if (eventId == RtnCode.NO_CHANNEL) {
                answer = "请检查网络";
            }

            // 如果是重新提问出现的网络异常
            if (currQuestionQuestionType.equals(QuestionType.RE_ASK.getName()) && eventId != RtnCode.NO_CHANNEL && eventId != RtnCode.USER_FORBIDDEN) {
                answer = "网络信息异常, 不支持继续问答，请切换分支";
            }

            // 渲染异常通信问答
            Answer answerNew = new Answer();
            answerNew.setDateTime(DateUtils.format(new Date()));
            answerNew.setAnswer(answer);
            component.setMessageContent(answer, ChatMessageType.CHAT_GENERATE, false);

            // 输入框控制：未登录或channel异常时，不允许继续操作
            if (eventId == RtnCode.NOT_LOGIN || eventId == RtnCode.NO_CHANNEL) {
                codeChatMainPanel.disableInputArea();
            } else {
                // 非重新回答多分支路径下的业务异常，允许接着问
                if (currQuestionQuestionType.equals(QuestionType.NEW_ASK.getName()) || message.getParent() == null || message.getParent().getChildren().size() == 1) {
                    codeChatMainPanel.enableInputArea();
                }
            }

            answerMap.put(regId, answerNew);
            component.displayActionPanel();
        }

        // UI停止等待效果
        if (eventId != Constants.RtnCode_CANCEL) {
            codeChatMainPanel.aroundRequest(false);
        }
        codeChatMainPanel.getSerialThreadPool().shutdown();
        // 移除问答请求结果监听
        if (regId != null && executorServiceMap != null && !executorServiceMap.isEmpty()) {
            ScheduledExecutorService executorService = executorServiceMap.get(regId);
            if (executorService != null) {
                executorService.shutdown();
                executorServiceMap.remove(regId);
                answerMap.remove(regId);
            }
        }

        // 重置等待状态
        isWaitResp = false;
        onAnswerStatus = false;
    }

    private void onTaskErrorWebView(String regId, int eventId, final ApigwWebsocketRespPayload payload) {

    }
}
