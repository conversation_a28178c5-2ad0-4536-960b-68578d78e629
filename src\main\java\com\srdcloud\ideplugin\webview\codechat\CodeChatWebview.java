package com.srdcloud.ideplugin.webview.codechat;

import com.intellij.openapi.Disposable;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.fileEditor.FileEditorManagerEvent;
import com.intellij.openapi.fileEditor.FileEditorManagerListener;
import com.intellij.openapi.project.Project;
import com.intellij.ui.jcef.JBCefApp;
import com.intellij.ui.jcef.JBCefBrowser;
import com.srdcloud.ideplugin.agent.AgentManager;
import com.srdcloud.ideplugin.codechat.ConversionManager;
import com.srdcloud.ideplugin.codechat.WebviewCodeSelectionListener;
import com.srdcloud.ideplugin.composer.ComposerService;
import com.srdcloud.ideplugin.composer.ContextInputService;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.constants.AgentNameConstant;
import com.srdcloud.ideplugin.general.utils.*;
import com.srdcloud.ideplugin.webview.base.WebView;
import com.srdcloud.ideplugin.webview.base.domain.WebViewCommandRequest;
import com.srdcloud.ideplugin.webview.codechat.actions.ActionsHandler;
import com.srdcloud.ideplugin.webview.codechat.chat.CodeChatEngin;
import com.srdcloud.ideplugin.webview.codechat.common.StatusEventType;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewReqCommand;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCode;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCommand;
import com.srdcloud.ideplugin.webview.codechat.composer.ComposerHandler;
import com.srdcloud.ideplugin.webview.codechat.composer.DiffViewVerticalHandler;
import com.srdcloud.ideplugin.webview.codechat.conversation.ConversationHandler;
import com.srdcloud.ideplugin.webview.codechat.datareport.DataReportRequestHandler;
import com.srdcloud.ideplugin.webview.codechat.composer.DiffViewVerticalHandler;
import com.srdcloud.ideplugin.webview.codechat.diff.DiffRequestHandler;
import com.srdcloud.ideplugin.webview.codechat.getideutils.GetIdeUtilsHandler;
import com.srdcloud.ideplugin.webview.codechat.indexing.IndexingHandler;
import com.srdcloud.ideplugin.webview.codechat.knowledgebase.KnowledgeBaseHandler;
import com.srdcloud.ideplugin.webview.codechat.login.LoginRequestHandler;
import com.srdcloud.ideplugin.webview.codechat.message.OpenExternalRequest;
import com.srdcloud.ideplugin.webview.base.domain.PushThemeChangedResponse;
import com.srdcloud.ideplugin.webview.base.domain.PushThemeChangedResponseData;
import com.srdcloud.ideplugin.webview.codechat.prompts.PromptsRequestHandler;
import com.srdcloud.ideplugin.webview.codechat.push.PushStatusHandler;
import com.srdcloud.ideplugin.webview.codechat.terminal.InvokeTerminalHandler;
import com.srdcloud.ideplugin.webview.codechat.workitem.WorkItemHandler;
import com.srdcloud.ideplugin.webview.codesecurity.CodeSecurityScanHandler;
import org.apache.commons.lang3.StringUtils;
import org.cef.CefSettings;
import org.cef.browser.CefBrowser;
import org.cef.browser.CefFrame;
import org.cef.browser.CefMessageRouter;
import org.cef.callback.CefQueryCallback;
import org.cef.handler.CefDisplayHandlerAdapter;
import org.cef.handler.CefLoadHandler;
import org.cef.handler.CefMessageRouterHandlerAdapter;
import org.cef.network.CefRequest;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;
import java.io.IOException;
import java.lang.reflect.Array;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.LinkedBlockingQueue;
import com.srdcloud.ideplugin.composer.history.AgentWindowIdUtil;

/**
 * 用于加载开发问答webview窗口，并处理业务消息通信
 */
public class CodeChatWebview implements Disposable {
    private static final Logger logger = LoggerFactory.getLogger(CodeChatWebview.class);

    // 资源载入路径
    private final static String RESOURCE_DOMAIN_NAME = "chat";
    private final static String RESOURCE_URL = "http://" + RESOURCE_DOMAIN_NAME + "/index.html";
//    private final static String CODE_CHAT_URL = "http://localhost:3000";

    private Project project;

    // webview组件实例：加载网页资源并运行
    private WebView webView;

    // webview实例是否已经被加载成功，加载成功后才可以通信：2020版本需要展开ToolWindow才能触发懒加载；2022之后会自动触发加载
    private boolean loaded = false;
    // 静态资源是否已加载成功
    private boolean webviewContentLoaded = false;

    // reload限流判断：前后两次间隔不能少于RELOAD_INTERVAL
    private long webviewLoadTimeStamp = System.currentTimeMillis();
    private final static long RELOAD_INTERVAL = 5 * 1000;

    // webview展示外框，作为刷新webview的容器
    private JPanel contentContainer = new JPanel(new BorderLayout());

    // 定义一个不支持JCEF的Panel，展示相关引导文案（引导用户进行设置或寻求qq群支撑）
    private JCEFUnsupportedViewPanel jcefUnsupportedViewPanel = new JCEFUnsupportedViewPanel();

    // 通道配置，管理通讯通道命名
    private static final CefMessageRouter.CefMessageRouterConfig config = new CefMessageRouter.CefMessageRouterConfig("ideQuery", "ideQueryCancel");

    // 会话数据管理器
    private ConversionManager conversionManager;

    // ----------  不同业务模块的消息处理器  ----------
    // 问答处理器：处理当前对话问答
    private CodeChatEngin codeChatEngin;

    // Composer问答处理器
    private ComposerHandler composerHandler;

    // DiffViewVertical处理器
    private DiffViewVerticalHandler diffViewVerticalHandler;

    // Indexing处理器
    private IndexingHandler indexingHandler;

    // GetIdeUtils处理器
    private GetIdeUtilsHandler getIdeUtilsHandler;

    // 会话业务处理器
    private ConversationHandler conversationHandler;

    // 登录处理器：处理登录相关请求
    private LoginRequestHandler loginRequestHandler;

    // 提示词处理器：处理提示词相关请求
    private PromptsRequestHandler promptsRequestHandler;

    // 知识库处理器：处理知识库相关请求
    private KnowledgeBaseHandler knowledgeBaseHandler;

    // 工作项处理器：处理工作项相关请求
    private WorkItemHandler workItemHandler;

    // 安全助手处理器：处理安全助手请求
    private CodeSecurityScanHandler codeSecurityScanHandler;

    // 指标上报处理器：处理采纳率等指标上报
    private DataReportRequestHandler dataReportRequestHandler;

    // Action相关处理器：处理右键菜单、异常解析功能
    private ActionsHandler actionHandler;

    // 状态变化主动推送
    private PushStatusHandler pushStatusHandler;

    // 查看变更
    private DiffRequestHandler diffRequestHandler;

    // 执行terminal处理器
    private InvokeTerminalHandler invokeTerminalHandler;

    // 关联文件管理器
//    private RelatedFileChatHandler relatedFileChatHandler;

    // 编辑器选中事件
    private WebviewCodeSelectionListener webviewCodeSelectionListener;
    private Editor editorLocal;
    private final LinkedBlockingQueue<String> cachedCodeSelectionResponseQueue = new LinkedBlockingQueue<>();

    private String[] lastSelectFileName = {};


    private CodeChatWebview(Project project) {
        this.project = project;

        contentContainer.setVisible(true);
        contentContainer.setOpaque(false);

        jcefUnsupportedViewPanel.setVisible(true);
        jcefUnsupportedViewPanel.setOpaque(false);

        // Service级别仅创建实例，不加载资源但是需要先创建实例，否则自动登录不能发送消息
        loadWebview();
    }

    public static CodeChatWebview getInstance(@NotNull Project project) {
        return project.getService(CodeChatWebview.class);
    }

    // 初始化Webview实例
    public void loadWebview() {
        logger.info("load CodeChatWebview...");
        DebugLogUtil.println("load CodeChatWebview...");

        // 判断是否支持JCEF
        if (!JBCefApp.isSupported()) {
            logger.error("[cf]CodeChatWebview loadWebview fail,JCEF is not supported.");
            loaded = false;
            contentContainer.add(jcefUnsupportedViewPanel, BorderLayout.CENTER);
            return;
        }

        // 创建webview实例
        webView = new WebView(RESOURCE_DOMAIN_NAME);
        loaded = false;
        webviewContentLoaded = false;

        // 创建各业务消息处理器
        // 会话数据管理
        conversionManager = new ConversionManager(project);
        // 问答对话
        codeChatEngin = new CodeChatEngin(project, this, conversionManager);
        // Composer问答
        composerHandler = new ComposerHandler(project, this);
        // DeffViewVertical处理
        diffViewVerticalHandler = new DiffViewVerticalHandler(project, this);
        // Indexing处理
        indexingHandler = new IndexingHandler(project, this);
        // GetIdeUtils处理
        getIdeUtilsHandler = new GetIdeUtilsHandler(project, this);
        // 会话管理业务
        conversationHandler = new ConversationHandler(project, this, conversionManager, codeChatEngin);
        // diff
        diffRequestHandler = new DiffRequestHandler(project, this);
        // invokeTerminal处理
        invokeTerminalHandler = new InvokeTerminalHandler(project, this);

        // 注册请求处理器
        codeSecurityScanHandler = new CodeSecurityScanHandler(project, this);
        loginRequestHandler = new LoginRequestHandler(project, this);
        promptsRequestHandler = new PromptsRequestHandler(project, this);
        knowledgeBaseHandler = new KnowledgeBaseHandler(project, this);
        dataReportRequestHandler = new DataReportRequestHandler(project, this);
        actionHandler = new ActionsHandler(project, this);
        pushStatusHandler = new PushStatusHandler(project, this);
//        relatedFileChatHandler = new RelatedFileChatHandler(project, this);
        workItemHandler = new WorkItemHandler(project, this);

        // 预埋：文件拖拽注册监听方法调用

    }

    /**
     * 加载Webview资源：需要与Webview实例创建区分开
     */
    public void loadResource() {
        DebugLogUtil.println("CodeChatWebview createBrowser and loadURL.");

        //---进行自定义设置---
        // 注册浏览器加载生命周期监听
        addLoadListener();

        // 浏览器console日志监听
        addConsoleListener();

        // 注册业务消息监听与处理逻辑
        addMessageRouter();

        DebugLogUtil.println("load CodeChatWebview Resource...");
        logger.info("load CodeChatWebview Resource...");

        webView.getBrowser().getCefBrowser().createImmediately();
        webView.getBrowser().loadURL(RESOURCE_URL);
        contentContainer.add(webView.getBrowser().getComponent(), BorderLayout.CENTER);

        // 注册代码选中监听器
        addSelectionCodeListener();

        // 更新Webview最近一次加载时间
        webviewLoadTimeStamp = System.currentTimeMillis();
    }

    // 限流：两次reload不能少于RELOAD_INTERVAL
    public boolean checkReloadAble() {
        long nowTimeStamp = System.currentTimeMillis();
        return nowTimeStamp - webviewLoadTimeStamp >= RELOAD_INTERVAL;
    }

    // 重新装载webview
    public void reloadWebview() {
        // 判断是否支持JCEF
        if (!JBCefApp.isSupported()) {
            return;
        }

        // 此处需要重新注册app的scheme处理
        // 加载资源的时候出现页面空白屏幕，刷新需要重新载入资源
        webView.reRegisterAppSchemeHandler();

        // 释放掉旧的webview资源
        if (Objects.nonNull(webView)) {
            contentContainer.remove(webView.getBrowser().getComponent());
            webView.getBrowser().dispose();
            webView = null;
        }

        // 重新装载Webview窗口
        loadWebview();

        // 重新加载资源
        loadResource();
    }

    /**
     * 只刷新UI，保证尺寸正确
     */
    public void refreshUI() {
        SwingUtilities.invokeLater(() -> {
            if (this.webView == null) {
                return;
            }

            webView.getBrowser().getComponent().revalidate();
            webView.getBrowser().getComponent().repaint();
            contentContainer.revalidate();
            contentContainer.repaint();
        });
    }

    /**
     * 添加webview browser实例加载过程监听器，监控加载状态
     */
    private void addLoadListener() {
        JBCefBrowser browser = webView.getBrowser();
        browser.getJBCefClient().addLoadHandler(new CefLoadHandler() {

            // Web视图状态改变
            @Override
            public void onLoadingStateChange(CefBrowser browser, boolean isLoading, boolean canGoBack, boolean canGoForward) {
            }

            // Web视图开始进入加载状态，该状态下Web视图还无法处理数据
            @Override
            public void onLoadStart(CefBrowser browser, CefFrame frame, CefRequest.TransitionType transitionType) {
            }

            // Web视图加载完成
            @Override
            public void onLoadEnd(CefBrowser browser, CefFrame frame, int httpStatusCode) {
                DebugLogUtil.println("CodeChatWebview loaded,httpStatusCode:" + httpStatusCode);
                logger.info("CodeChatWebview loaded,httpStatusCode:{}", httpStatusCode);
                // browser加载成功，可以进行后续通信
                loaded = true;
            }

            // Web视图加载发生错误
            @Override
            public void onLoadError(CefBrowser browser, CefFrame frame, ErrorCode errorCode, String errorText, String failedUrl) {
                logger.warn("[cf] CodeChatWebview error,failedUrl:{},errorCode:{},errorText:{}", failedUrl, errorCode.name(), errorText);
                MessageBalloonNotificationUtil.showCommonNotification(project, "窗口加载异常,请点击右上角按钮重试");
            }
        }, browser.getCefBrowser());
    }

    /**
     * 控制台输出监听器，处理来自webview的控制台输出信息，用于调试使用
     */
    private void addConsoleListener() {
        JBCefBrowser browser = webView.getBrowser();
        browser.getJBCefClient().getCefClient().addDisplayHandler(new CefDisplayHandlerAdapter() {
            @Override
            public boolean onConsoleMessage(CefBrowser browser, CefSettings.LogSeverity level, String message, String source, int line) {
                if (CefSettings.LogSeverity.LOGSEVERITY_WARNING == level || CefSettings.LogSeverity.LOGSEVERITY_ERROR == level || CefSettings.LogSeverity.LOGSEVERITY_FATAL == level || CefSettings.LogSeverity.LOGSEVERITY_DISABLE == level) {
                    logger.warn("[cf] CodeChatWebview console log,{}:{}", level.name(), message);
                } else {
                    if (EnvUtil.checkWebviewLogAble()) {
                        DebugLogUtil.println("CodeChatWebview console log: " + message);
                    }
                }
                return super.onConsoleMessage(browser, level, message, source, line);
            }
        });
    }

    /**
     * webview消息通信处理实现
     * 根据不同业务消息，分发到不同的handler进行处理
     */
    private void addMessageRouter() {
        CefMessageRouter messageRouter = CefMessageRouter.create(config, new CefMessageRouterHandlerAdapter() {
            public boolean onQuery(CefBrowser browser, CefFrame frame, long queryId, String request, boolean persistent, CefQueryCallback callback) {
                if (StringUtils.isBlank(request)) {
                    logger.warn("[cf] CodeChatWebview onQuery request is blank");
                    return true;
                }

                try {
                    // 解析WebView发送的请求中的command消息
                    WebViewCommandRequest webViewCommandRequest = JsonUtil.getInstance().fromJson(request, WebViewCommandRequest.class);
                    if (Objects.isNull(webViewCommandRequest)) {
                        logger.warn("[cf] CodeChatWebview webViewCommandRequest is null");
                        return true;
                    }

                    if (EnvUtil.checkWebviewLogAble()) {
                        DebugLogUtil.println("handle CodeChatWebview onQuery request: " + request);
                    }

                    // 根据不同指令，进行业务处理
                    switch (webViewCommandRequest.getCommand()) {
                        // webView加载完成
                        case WebViewReqCommand.WEBVIEW_LOADED:
                            DebugLogUtil.println("CodeChatWebview webviewContentLoaded.");
                            logger.info("CodeChatWebview webviewContentLoaded.");

                            loaded = true;
                            webviewContentLoaded = true;
                            webView.setWebviewBackground();
                            refreshUI();
                            sendInitStatus();
                            sendCacheCodeSelectionResponse();
                            break;
                        // 判断是否已登录
                        case WebViewReqCommand.CHECK_IF_LOGIN:
                            // 不能入线程池，由于消息轮询，会占满IDE线程
                            //ApplicationManager.getApplication().executeOnPooledThread(() -> {
                            //    loginRequestHandler.checkIfLogin();
                            //});

                            loginRequestHandler.checkIfLogin();

                            break;
                        // 发起登录
                        case WebViewReqCommand.LOGIN:
                            loginRequestHandler.login();
                            break;
                        // 请求当前编辑器是否已选择代码，有，则返回
                        case WebViewReqCommand.RETRIVE_CODE_SELECTION:
                            // 暂无处理，改为主动推送 CODE_SELECTION_CHANGED 消息
                            break;
                        // 刷新会话
                        case WebViewReqCommand.CONVERSATION_REFRESH:
                            // 不能做异步，会导致界面select错乱
                            //ApplicationManager.getApplication().executeOnPooledThread(() -> {
                            //    conversationHandler.conversationRefresh(request);
                            //});

                            conversationHandler.conversationRefresh(request);
                            break;
                        // 新增会话
                        case WebViewReqCommand.CONVERSATION_ADD:
                            conversationHandler.conversationAdd(request);
                            break;
                        // 切换会话
                        case WebViewReqCommand.CONVERSATION_SWITCH:
                            //ApplicationManager.getApplication().executeOnPooledThread(() -> {
                            //    conversationHandler.conversationSwitch(request);
                            //});

                            // 不能做异步，会导致界面select错乱
                            conversationHandler.conversationSwitch(request);
                            break;
                        // 删除会话
                        case WebViewReqCommand.CONVERSATION_REMOVE:
//                            ComposerService.getInstance(project).testContentRequest();
                            //ApplicationManager.getApplication().executeOnPooledThread(() -> {
                            //    conversationHandler.conversationRemove(request);
                            //});

                            // 不能做异步，会导致界面select错乱
                            conversationHandler.conversationRemove(request);
                            break;
                        // 编辑会话标题
                        case WebViewReqCommand.CONVERSATION_EDIT_TITLE:
                            //ApplicationManager.getApplication().executeOnPooledThread(() -> {
                            //    conversationHandler.conversationEditTitle(request);
                            //});

                            // 不能做异步，会导致界面select错乱
                            conversationHandler.conversationEditTitle(request);
                            break;
                        // 点赞点踩反馈
                        case WebViewReqCommand.CONVERSATION_FEEDBACK:
                            ApplicationManager.getApplication().executeOnPooledThread(() -> {
                                codeChatEngin.feedBack(request);
                            });
                            break;
                        // 发起提问
                        case WebViewReqCommand.CHAT_REQUEST:
                            // 向当前选中的对话发出提问
                            codeChatEngin.chatRequest(request);
                            break;
                        // 停止当前回答：不能异步
                        case WebViewReqCommand.STOP_CHAT_REQUEST:
                            codeChatEngin.stopChat(request);
                            break;
                        // Composer问答
                        case WebViewReqCommand.COMPOSER_REQUEST:
                            composerHandler.processComposerRequest(request);
                            break;
                        // diffViewVertical处理
                        case WebViewReqCommand.DIFF_VIEW_VERTICAL_REQUEST:
                            diffViewVerticalHandler.processDiffViewVerticalRequest(request);
                            break;
                        // indexing处理
                        case WebViewReqCommand.INDEXING_REQUEST:
                            indexingHandler.processIndexingRequest(request);
                            break;
                        // 获取代码工程、文件以及目录
                        case WebViewReqCommand.GET_IDE_UTILS_REQUEST:
                            getIdeUtilsHandler.processGetIdeUtilsRequest(request);
                            break;
                        // 上报统计数据
                        case WebViewReqCommand.DATA_REPORT:
                            dataReportRequestHandler.processDataReportRequest(request);
                            break;
                        // 插入代码到编辑器中
                        case WebViewReqCommand.INSERT_CODE:
                            codeChatEngin.insertCodeToEditor(request);
                            break;
                        // 插入单元测试代码到编辑器中
                        case WebViewReqCommand.INSERT_UNITTEST:
                            codeChatEngin.insertUnitTestToProject(request);
                            break;
                        // 打开外部url
                        case WebViewReqCommand.OPEN_EXTERNAL:
                            OpenExternalRequest openExternalRequest = JsonUtil.getInstance().fromJson(request, OpenExternalRequest.class);
                            String path = openExternalRequest.getData().getPath();
                            if (StringUtils.isEmpty(path)) {
                                break;
                            }

                            // 如果不是完整url的，则在前面加serverHost
                            if (!path.toLowerCase().startsWith("http")) {
                                path = ConfigWrapper.getServerHost() + path;
                            }

                            Desktop.getDesktop().browse(URI.create(path));
                            break;
                        // 查询提问模板
                        case WebViewReqCommand.PROMPTS_REQUEST:
                            ApplicationManager.getApplication().executeOnPooledThread(() -> {
                                promptsRequestHandler.processPromptsRequest(request);
                            });
                            break;
                        // 查询知识库
                        case WebViewReqCommand.KNOWLEDGE_BASE_REQUEST:
                            ApplicationManager.getApplication().executeOnPooledThread(() -> {
                                knowledgeBaseHandler.processKnowledgeBaseRequest(request);
                            });
                            break;
                        // 工作项相关
                        case WebViewReqCommand.WORKITEM_REQUEST:
                            workItemHandler.processWorkItemRequest(request);
                            break;
                        // 查看变更
                        case WebViewReqCommand.VIEW_DIFF:
                            // 此处不能用invokeLater，会报错
                            diffRequestHandler.compareCodeDiff(request);
                            break;
                        // 在编辑器打开目标文件
                        case WebViewReqCommand.OPEN_TEXT_DOCUMENT:
                            // 此处不能用invokeLater，会报错
                            getIdeUtilsHandler.openTextDocument(request);
                            break;
                        case WebViewReqCommand.INVOKE_TERMINAL_CAPABILITY:
                            invokeTerminalHandler.processInvokeTerminal(request);
                            break;
//                        // 获取工程目录（废弃）
//                        case WebViewReqCommand.GET_DIRECTORY_STRUCTURE:
//                            ApplicationManager.getApplication().executeOnPooledThread(() -> {
//                                relatedFileChatHandler.getDirectoryStructure(request);
//                            });
//                            break;
                        // 查询最近打开的关联文件（废弃）
//                        case WebViewReqCommand.QA_FOR_RELATED_FILES_REQUEST:
//                            ApplicationManager.getApplication().executeOnPooledThread(() -> {
//                                relatedFileChatHandler.getRelatedFiles(request);
//                            });
//                            break;
//                        // 代码安全扫描（已下线）
//                        case WebViewReqCommand.CODE_SECURITY_SCAN_REQUEST:
//                            codeSecurityScanHandler.processSecurityRequest(request);
//                            break;
//                        // 智能问答（已下线）
//                        case WebViewReqCommand.SRD_CHAT_REQUEST:
//                            break;
                        // 加载本地会话数据（已废弃）
//                        case WebViewReqCommand.CONVERSATION_LOAD:
//                            conversationHandler.conversationLoad();
//                            break;
                        // 取消当前请求（已废弃）
//                        case WebViewReqCommand.CANCEL_CHAT_REQUEST:
//                            codeChatEngin.cancelChat(request);
//                            break;
                        default:
                            logger.warn("[cf] CodeChatWebview unknown command:{},request:{}", webViewCommandRequest.getCommand(), request);
                            break;
                    }
                } catch (Exception e) {
                    logger.warn("[cf] CodeChatWebview handle request error,request:{},error:\n", request);
                    e.printStackTrace();
                }
                return true;
            }
        });

        webView.getBrowser().getJBCefClient().getCefClient().addMessageRouter(messageRouter);
    }

    /**
     * 延后到indexing建立完成后添加
     */
    public void addSelectionCodeListener() {
        // == 定义编辑器选中事件监听器，选中代码则展示预览面板
        webviewCodeSelectionListener = new WebviewCodeSelectionListener(this);

        try {
            // ---- 获取当前编辑器
            if (project == null || project.isDisposed()) {
                logger.warn("[cf] addSelectionCodeListener skip,project is null or disposed");
                return;
            }

            FileEditorManager fileEditorManager = FileEditorManager.getInstance(project);
            if (fileEditorManager != null) {
                editorLocal = fileEditorManager.getSelectedTextEditor();
            }

            // ---- 注册选择监听器
            if (editorLocal != null && editorLocal.getSelectionModel() != null) {
                editorLocal.getSelectionModel().addSelectionListener(webviewCodeSelectionListener);
            }

            // -- 定义编辑器页面管理事件监听器
            project.getMessageBus().connect().subscribe(FileEditorManagerListener.FILE_EDITOR_MANAGER, new FileEditorManagerListener() {
                /**
                 * 切换编辑器打开文件时
                 */
                @Override
                public void selectionChanged(@NotNull FileEditorManagerEvent event) {
                    // 移除旧的编辑器选中内容监听器
                    if (editorLocal != null && editorLocal.getSelectionModel() != null) {
                        editorLocal.getSelectionModel().removeSelectionListener(webviewCodeSelectionListener);
                    }
                    // 针对新打开的文件页面添加代码选中监听器
                    editorLocal = FileEditorManager.getInstance(event.getManager().getProject()).getSelectedTextEditor();
                    if (editorLocal != null) {
                        editorLocal.getSelectionModel().addSelectionListener(webviewCodeSelectionListener);
                    }
                }
            });
        } catch (Exception e) {
            logger.warn("[cf] addSelectionCodeListener error:{}", e.getMessage());
        }

        //ApplicationManager.getApplication().invokeLater(() -> {
        //
        //});
    }

    /**
     * 判断浏览器实例是否加载完成
     */
    public boolean isLoaded() {
        return loaded;
    }

    /**
     * 判断Webview内容是否加载完成
     */
    public boolean isWebviewContentLoaded() {
        return webviewContentLoaded;
    }

    /**
     * 发送消息到webview，伴随UI加载完毕检查
     *
     * @param message
     */
    public boolean sentMessageToWebviewWithContentLoadCheck(String message) {
        if (webView != null && isLoaded() && isWebviewContentLoaded()) {
            webView.sentMessageToWebview(message);
            return true;
        }
        logger.warn("[cf] CodeChatWebview sentMessageToWebviewWithContentLoadCheck fail,webview content not loaded,message:{}", message);
        return false;
    }

    /**
     * 发送消息到webview，并伴随加载检查
     *
     * @param message
     */
    public boolean sentMessageToWebviewWithLoadCheck(String message) {
        if (webView != null && isLoaded()) {
            webView.sentMessageToWebview(message);
            return true;
        } else {
            DebugLogUtil.warn("[cf] CodeChatWebview sentMessageToWebviewWithLoadCheck fail,webview not loaded,message cached:" + message);
            getCachedCodeSelectionResponseQueue().add(message);
            return false;
        }
    }

    /**
     * 发送初始状态
     */
    private void sendInitStatus() {
        if (LocalStorageUtil.checkIsLogin()) {
            DebugLogUtil.println("CodeChatWebview send init status: WSSERVER_RECONNECT,LOGIN_SUCCESS");

            this.pushStatusHandler.onStatusChanged(StatusEventType.WSSERVER_RECONNECT, WebViewRspCode.SUCCESS);
            this.pushStatusHandler.onStatusChanged(StatusEventType.LOGIN_SUCCESS, WebViewRspCode.SUCCESS);
        }
    }

    /**
     * 发送Webview加载完成前缓存的通信消息
     */
    private void sendCacheCodeSelectionResponse() {
        try {
            // 延迟一会儿再发送
            try {
                Thread.sleep(1000);
            } catch (Exception ex) {
                throw new RuntimeException(ex);
            }

            // 循环遍历 cachedCodeSelectionResponseQueue 逐个去除元素进行处理
            while (!cachedCodeSelectionResponseQueue.isEmpty()) {
                String msg = cachedCodeSelectionResponseQueue.poll();
                if (msg != null) {
                    DebugLogUtil.println("CodeChatWebview send cached message: " + msg);
                    webView.sentMessageToWebview(msg);
                }
            }
            // 刷新UI尺寸
            //refreshUI();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public JComponent getContentComponent() {
        // 如果还没创建实例，则创建实例
        if (Objects.isNull(webView)) {
            loadWebview();
        }

        // 如果还没加载，则加载资源
        if (Objects.nonNull(webView) && !isLoaded()) {
            loadResource();
        }

        return contentContainer;
    }

    public ConversationHandler getConversationRequestHandler() {
        return conversationHandler;
    }

    public KnowledgeBaseHandler getKnowledgeBaseHandler() {
        return knowledgeBaseHandler;
    }

    public CodeSecurityScanHandler getCodeSecurityScanHandler() {
        return codeSecurityScanHandler;
    }

    public CodeChatEngin getCodeChatEngin() {
        return codeChatEngin;
    }

    public DataReportRequestHandler getDataReportRequestHandler() {
        return dataReportRequestHandler;
    }

    public ActionsHandler getActionHandler() {
        return actionHandler;
    }

    public PushStatusHandler getPushStatusHandler() {
        return pushStatusHandler;
    }

    public LinkedBlockingQueue<String> getCachedCodeSelectionResponseQueue() {
        return cachedCodeSelectionResponseQueue;
    }

    @Override
    public void dispose() {
        DebugLogUtil.println("CodeChatWebview disposed.");

        if (webView != null) {
            webView.getBrowser().dispose();
            webView = null;
        }
        loaded = false;
        webviewContentLoaded = false;
        contentContainer = null;
    }

    public GetIdeUtilsHandler getGetIdeUtilsHandler() {
        return getIdeUtilsHandler;
    }
}
