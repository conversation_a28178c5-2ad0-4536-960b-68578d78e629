package com.srdcloud.ideplugin.assistant.codechatNative;

import com.google.common.collect.Lists;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.project.DumbAwareAction;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Key;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowFactory;
import com.intellij.openapi.wm.ToolWindowManager;
import com.intellij.openapi.wm.ex.ToolWindowEx;
import com.intellij.ui.content.Content;
import com.srdcloud.ideplugin.common.icons.MyIcons;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.icons.GPTIcons;
import com.srdcloud.ideplugin.general.utils.BrowseUtil;
import com.srdcloud.ideplugin.general.utils.IdeUtil;
import com.srdcloud.ideplugin.general.utils.LocalStorageUtil;
import com.srdcloud.ideplugin.general.utils.UIUtil;
import com.srdcloud.ideplugin.webview.treesitter.WebviewTreeSitter;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 编程助手 toolWindow
 *
 * <AUTHOR>
 */
public class AssistantToolWindow implements ToolWindowFactory {

    private static final Logger logger = LoggerFactory.getLogger(AssistantToolWindow.class);

    /**
     * 本地缓存：本次IDE使用期间，不同项目打开的toolWindow实例唯一标识key与项目名之间的映射关系
     */
    public static Map<String, Key> ASSISTANT_TOOL_WINDOW_KEY_MAP = new HashMap<>();

    /**
     * 当前处于活跃状态的项目的key标识【全局】
     */
    public static Key ACTIVE_ASSISTANT_TOOL_WINDOW_KEY = null;

    /**
     * 本地缓存：本次IDE使用期间，不同项目的开发问答tab页的唯一标识key 与 项目名之间的映射关系
     */
    public static Map<String, Key> CODE_CHAT_TAB_KEY_MAP = new HashMap<>();

    /**
     * 当前处于活跃状态的开发问答页key
     */
    public static Key ACTIVE_CODE_CHAT_TAB_KEY = null;

    @Override
    public void createToolWindowContent(@NotNull Project project, @NotNull ToolWindow toolWindow) {
        // 暗黑模式，修改侧边栏logo
        boolean isDark = UIUtil.judgeBackgroudDarkTheme();
        if (isDark) {
            toolWindow.setIcon(GPTIcons.TOOL_WINDOW_DARK);
        }

        // toolWindow扩展
        ToolWindowEx tw = (ToolWindowEx) toolWindow;

        // 获取项目唯一标识
        String projectLocationHash = project.getLocationHash();
        logger.info("[cf] createToolWindowContent init,projectLocationHash:<{}>,projectName:<{}>", projectLocationHash, project.getName());

        // 初始化toolWindow实例、各功能tab面板实例唯一标识key
        ACTIVE_CODE_CHAT_TAB_KEY = IdeUtil.buildCodeChatTabKey(projectLocationHash);
        ACTIVE_ASSISTANT_TOOL_WINDOW_KEY = IdeUtil.buildToolWindowKey(projectLocationHash);

        AssistantToolPanel assistantToolPanel;

        // 内外部版本隔离
        // -- 外部版本没有扩展 toolWindowHeader
        // -- 外部版本没有安全扫描
        if (ConfigWrapper.isInnerVersion) {
            configureToolWindowHeader(tw);
            assistantToolPanel = new AssistantToolPanel(project, toolWindow, true);
        } else {
            assistantToolPanel = new AssistantToolPanel(project, toolWindow, false);
        }

        Content mainContent = toolWindow.getContentManager().getFactory().createContent(assistantToolPanel.getMainPanelContent(), null, false);
        //Content mainContent = toolWindow.getContentManager().getFactory().createContent(assistantToolPanel.getMainMultiTabPanel(), null, false);

        mainContent.setCloseable(false);
        toolWindow.getContentManager().addContent(mainContent, 0);

        // 默认隐藏
        toolWindow.hide();

        // 调试模式下，挂载WebviewTreeSitter到窗口左侧，提供调试入口
        if (LocalStorageUtil.checkDebugAble()) {
            JPanel webViewContainer = new JPanel();
            WebviewTreeSitter treeSitterInstance = WebviewTreeSitter.getInstance(project);
            Component webView = treeSitterInstance.getTreeSitterWebViewComponent();
            webViewContainer.setOpaque(false);
            webViewContainer.setPreferredSize(new Dimension(1, 100));
            webViewContainer.setMaximumSize(new Dimension(1, 100));
            webView.setPreferredSize(new Dimension(1, 100));
            webViewContainer.add(webView);
            tw.getDecorator().add(webViewContainer, BorderLayout.WEST);
        }
    }

    /**
     * 设置 ToolWindowHeader 的内容
     */
    private void configureToolWindowHeader(ToolWindowEx toolWindowEx) {
        final List<AnAction> actions = Lists.newArrayListWithExpectedSize(3);
        boolean isDark = UIUtil.judgeBackgroudDarkTheme();

        Icon help = MyIcons.Help;
        Icon feedback = MyIcons.FeedBack;
        // 暗黑主题
        if (isDark) {
            help = MyIcons.HelpDark;
            feedback = MyIcons.FeedBackDark;
        }

        AnAction helpAction = new DumbAwareAction("帮助", "Open help in browser.", help) {
            @Override
            public void actionPerformed(@NotNull AnActionEvent e) {
                BrowseUtil.Companion.browse(ConfigWrapper.HelpDocsPageUrl);
            }
        };
        actions.add(helpAction);

        AnAction feedBackAction = new DumbAwareAction("反馈", "Open feedback in browser.", feedback) {
            @Override
            public void actionPerformed(@NotNull AnActionEvent e) {
                BrowseUtil.Companion.browse(ConfigWrapper.FeedBackPageUrl);
            }
        };
        actions.add(feedBackAction);

        // 将 Action 添加到 ToolWindow 的标题栏
        toolWindowEx.setTitleActions(actions);
    }

    public static void toolWindowVisible(Project project1) {
        Key key = AssistantToolWindow.ASSISTANT_TOOL_WINDOW_KEY_MAP.get(project1.getLocationHash());
        if (key == null) {
            key = IdeUtil.buildToolWindowKey(project1.getLocationHash());
        }
        Object toolWindow = project1.getUserData(key);
        if (toolWindow != null) {
            ToolWindow toolWindow1 = (ToolWindow) toolWindow;
            if (!toolWindow1.isVisible()) {
                // 如果Tool Window处于隐藏状态，则显示
                toolWindow1.show(null);
            }
        } else {
            ToolWindow toolWindow2 = ToolWindowManager.getInstance(project1).getToolWindow("研发云CodeFree");
            if (toolWindow2 != null) {
                if (!toolWindow2.isVisible()) {
                    // 如果Tool Window处于隐藏状态，则显示
                    toolWindow2.show(null);
                }
            }

        }
    }
}
