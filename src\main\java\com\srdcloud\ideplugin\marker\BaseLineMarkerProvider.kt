package com.srdcloud.ideplugin.marker

import com.intellij.codeInsight.daemon.RelatedItemLineMarkerInfo
import com.intellij.codeInsight.daemon.RelatedItemLineMarkerProvider
import com.intellij.openapi.editor.markup.GutterIconRenderer
import com.intellij.psi.PsiElement
import secIdea.marker.AIAssistantIcons

abstract class BaseLineMarkerProvider : RelatedItemLineMarkerProvider() {

    override fun collectNavigationMarkers(
        element: PsiElement,
        result: MutableCollection<in RelatedItemLineMarkerInfo<*>>
    ) {
        if (!isApplicable(element)) return

        val functionBean = LineMarkerFunctionBean()
        filterFunction(element, functionBean)

        if (functionBean.getFunctionNum() > 0) {
            val clickAction = AIAssistantGutterIconClickAction(element, functionBean)
            val markerInfo = RelatedItemLineMarkerInfo(
                element,
                element.textRange,
                AIAssistantIcons.LINE_MARKER_ICON,
                { _ -> "海云智码快捷操作" },
                clickAction,
                GutterIconRenderer.Alignment.RIGHT
            ) { emptyList() }
            result.add(markerInfo)
        }
    }

    abstract fun isApplicable(element: PsiElement): Boolean
    abstract fun filterFunction(element: PsiElement, bean: LineMarkerFunctionBean)
}
