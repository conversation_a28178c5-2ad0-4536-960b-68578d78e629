package com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc

import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.LocalFileSystem
import com.intellij.openapi.vfs.VfsUtilCore
import com.intellij.openapi.vfs.VirtualFile
import com.srdcloud.ideplugin.general.enums.LanguageExt
import com.srdcloud.ideplugin.general.utils.FileUtil

/**
 * <AUTHOR>
 * @date 2025/1/10
 * 用于跨文件关联的文件信息
 */
class RlccFile {

    companion object {

        data class FileInfo(val filePath: String, val content: String)

        private val logger2 = logger<FileChangeListener>()

        /**
         * 获取文件信息：路径、内容、包名
         */
        @JvmStatic
        fun VirtualFile.getFileInfo(project: Project): FileInfo? {
            try {
                if (isDirectory) return null
                // 找到项目根目录
                val basePath = project.basePath ?: return null
                // 检查路径合法性：当前文件是否为本工程内的文件
                if (!path.startsWith(basePath, true)) {
                    return null
                }

                // 计算根目录~当前文件的相对路径（适用于Java、Python、Kotlin等文件用于计算文件所在包路径）
                val findFileByPath = LocalFileSystem.getInstance().findFileByPath(basePath) ?: return null
                var filePath = VfsUtilCore.getRelativePath(this, findFileByPath) ?: return null

                // 根据文件名获取文件扩展名
                val ext = FileUtil.getExtension(path)

                // 0117版本：如果是go文件，或者javascript文件（后缀为js、cjs)，则filePath取绝对路径
                if (ext == LanguageExt.GO.getExt() || ext == LanguageExt.JAVASCRIPT.getExt() || ext == "cjs") {
                    filePath = path
                }

                // 检查是否为支持的文件类型
                if (!ProjectCodeIndexer.checkRlccSupport(ext)) {
                    return null
                }

                // 最后判断一下文件是否存在：针对临时编译文件等会被并发清理掉
                if (!this.exists()) {
                    return null
                }
                return FileInfo(filePath, VfsUtilCore.loadText(this))
            } catch (e: Throwable) {
                logger2.warn("[cf rlcc] getRlccFileInfo error:", e)
            }
            return null
        }
    }
}