package com.srdcloud.ideplugin.webview.codechat.login.response;


public class LoginSuccessResponseConfig {

    private String host;

    private String releaseVersion; // vsc专用字段，jb可不传

    private String clientVersion; // 当前版本号

    private String versionDesc; // 版本更新内容

    private String InputCharacterLimit;

    private String SnippetsCharacterLimit;

    private String ChatCharacterLimit; // 问答字符数限制

    private String PrintCacheConfig; // 打字机效果缓存配置


    public LoginSuccessResponseConfig() {
    }

    public LoginSuccessResponseConfig(String host, String releaseVersion, String inputCharacterLimit, String snippetsCharacterLimit, String chatCharacterLimit) {
        this.host = host;
        this.releaseVersion = releaseVersion;
        InputCharacterLimit = inputCharacterLimit;
        SnippetsCharacterLimit = snippetsCharacterLimit;
        ChatCharacterLimit = chatCharacterLimit;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getReleaseVersion() {
        return releaseVersion;
    }

    public void setReleaseVersion(String releaseVersion) {
        this.releaseVersion = releaseVersion;
    }

    public String getInputCharacterLimit() {
        return InputCharacterLimit;
    }

    public void setInputCharacterLimit(String inputCharacterLimit) {
        InputCharacterLimit = inputCharacterLimit;
    }

    public String getSnippetsCharacterLimit() {
        return SnippetsCharacterLimit;
    }

    public void setSnippetsCharacterLimit(String snippetsCharacterLimit) {
        SnippetsCharacterLimit = snippetsCharacterLimit;
    }

    public String getPrintCacheConfig() {
        return PrintCacheConfig;
    }

    public void setPrintCacheConfig(String printCacheConfig) {
        this.PrintCacheConfig = printCacheConfig;
    }

    public String getChatCharacterLimit() {
        return ChatCharacterLimit;
    }

    public void setChatCharacterLimit(String chatCharacterLimit) {
        ChatCharacterLimit = chatCharacterLimit;
    }

    public String getVersionDesc() {
        return versionDesc;
    }

    public void setVersionDesc(String versionDesc) {
        this.versionDesc = versionDesc;
    }

    public String getClientVersion() {
        return clientVersion;
    }

    public void setClientVersion(String clientVersion) {
        this.clientVersion = clientVersion;
    }
}
