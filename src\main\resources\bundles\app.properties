# \u63D2\u4EF6\u57FA\u7840\u4FE1\u606F\u914D\u7F6E
app.name=SRD CodeFree

# ws\u901A\u4FE1\u63A5\u53E3\u8DEF\u5F84\u914D\u7F6E
code.ai.server.path=websocket/peerAppgw

# \u6D4F\u89C8\u5668\u7F51\u9875\u8BBF\u95EE\u8DEF\u5F84\u914D\u7F6E
help.docs.page.path = helpcenter/content?id=1189244999559761920
prompt.center.page.path = smartassist/codefree?templateCenterOpen=1
feedback.page.path = feedback/feedback
component.page.path = composq/copilot

# oauth\u8BA4\u8BC1\u63A5\u53E3\u4E0E\u7ED3\u679C\u9875
oauth.server.login.page.path=login/oauth
oauth.server.login.api.path=login/oauth
oauth.client.redirect.url=http://127.0.0.1
oauth.server.redirect.path=login/oauth-srdcloud-redirect
server.error.user.invalid.redirect.path=srv-error/nosubscribe
server.error.user.forbidden.redirect.path=srv-error/forbidden

