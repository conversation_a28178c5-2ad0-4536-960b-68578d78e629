package com.srdcloud.ideplugin.statusbar

import com.intellij.openapi.application.ApplicationManager
import com.intellij.util.messages.Topic
import java.util.concurrent.atomic.AtomicLong

class Notify {
    /**
     * 静态单例对象
     */
    companion object {
        var notifyMsg = "";

        // 定义应用级topic对象
        @Topic.AppLevel
        val STATUS_BAR_UPDATE_TOPIC = Topic(IChangeStatusBarEventProcess::class.java)
        
        // 限流相关变量
        private val lastNotifyTime = AtomicLong(0)
        private const val THROTTLE_INTERVAL_MS = 500 // 默认限流间隔，毫秒

        /**
         * 限流版的状态更新通知
         * 确保通知不会过于频繁地调用主线程
         * @param forceUpdate 是否强制更新，不考虑限流（用于重要状态变更）
         */
        @JvmStatic
        fun throttledUpdateNotify(forceUpdate: Boolean = false) {
            val currentTime = System.currentTimeMillis()
            val lastTime = lastNotifyTime.get()
            
            if (forceUpdate || currentTime - lastTime >= THROTTLE_INTERVAL_MS) {
                // 使用CAS操作确保线程安全
                lastNotifyTime.compareAndSet(lastTime, currentTime)
                updateStatusNotify()
            }
        }

        // 发布一条状态更新mq到 TOPIC，触发实现了 IChangeStatusBarEventProcess 接口的订阅者们执行状态栏更新行为
        fun updateStatusNotify() {
            ApplicationManager.getApplication().invokeLater {
                ApplicationManager.getApplication().messageBus.syncPublisher(STATUS_BAR_UPDATE_TOPIC).statusUpdate()
            };
        }
    }
}

