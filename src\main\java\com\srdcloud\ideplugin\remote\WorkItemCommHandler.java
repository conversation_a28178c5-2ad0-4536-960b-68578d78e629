package com.srdcloud.ideplugin.remote;

import com.google.common.collect.Maps;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.general.utils.*;
import com.srdcloud.ideplugin.remote.client.FastFailHttpClient;
import com.srdcloud.ideplugin.remote.domain.ApiResponse;
import com.srdcloud.ideplugin.remote.domain.WorkItem.WorkItemListResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @description 工作项后端接口通信对接
 */
public class WorkItemCommHandler {

    private static final Logger logger = LoggerFactory.getLogger(WorkItemCommHandler.class);

    // 查询工作项列表
    private static final String workItemListPath = "api/agilebackend/workitem/srd-zte-workitems/simple-search";

    /**
     * 获取工作项列表
     */
    public static WorkItemListResponse getWorkItemList(String projectCode, String searchText) {
        WorkItemListResponse response = new WorkItemListResponse(RtnCode.OFFLINE, "网络条件异常，请稍后重试.", null);
        if (!LocalStorageUtil.checkNetCondition()) {
            logger.warn("[cf] getWorkItemList skip, net condition fail.");
            return response;
        }

        final String baseUrl = ConfigWrapper.getServerUrl() + workItemListPath;
        final HashMap<String, String> params = Maps.newHashMapWithExpectedSize(2);
        params.put("projectCode", projectCode);
        if (StringUtils.isNotBlank(searchText)) {
            params.put("searchText", searchText);
        }

        final String queryUrl = UrlUtil.buildUrlWithParams(baseUrl, params);
        final HashMap<String, String> headers = generateAuthHeaders();
        final ApiResponse apiResponse = FastFailHttpClient.doGet(queryUrl, headers);

        if (apiResponse == null) {
            logger.warn("[cf] WorkItemCommHandler, getWorkItemList is null}");
            return new WorkItemListResponse(RtnCode.OFFLINE, "网络异常", null);
        }

        if (RtnCode.SUCCESS != apiResponse.getRtnCode()) {
            logger.warn("[cf] WorkItemCommHandler getWorkItemList,rtnCode:{},msg:{}", apiResponse.getRtnCode(), apiResponse.getMessage());
            return new WorkItemListResponse(apiResponse.getRtnCode(), apiResponse.getMessage(), null);
        }

        return JsonUtil.getInstance().fromJson(apiResponse.getMessage(), WorkItemListResponse.class);
    }

    /**
     * 生成网关层鉴权头域字段
     */
    private static HashMap<String, String> generateAuthHeaders() {
        HashMap<String, String> headers = Maps.newHashMapWithExpectedSize(4);
        headers.put("apiKey", LocalStorageUtil.getApikey());
        headers.put("userid", LocalStorageUtil.getUserId());
        headers.put("invokerId", LocalStorageUtil.getUserId());
        headers.put("x-dup-id", String.valueOf(TimeUtil.getNowTimeSecTimestamp() + "-" + MyRandomUtil.generateRandomString(8)));
        return headers;
    }
}
