package com.srdcloud.ideplugin.assistant.codechatNative.logics.domain;

import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.icons.GPTIcons;
import com.srdcloud.ideplugin.general.utils.TimeUtil;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.ChatMessageSimple;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.DialogCondition;

import javax.swing.*;
import java.util.LinkedList;
import java.util.Queue;
import java.util.UUID;
import java.util.concurrent.ConcurrentLinkedDeque;

/**
 * @author: yangy
 * @date: 2023/8/24 15:09
 * @Desc 会话
 */
public class Conversation {
    /**
     * 会话id
     */
    private String id;

    /**
     * 标题
     */
    private String title;

    /**
     * 产生本对话的时间，格式为yyyy-MM-dd HH:mm:ss
     */
    private String createTime;

    /**
     * 会话最近更新时间，格式为yyyy-MM-dd HH:mm:ss
     */
    private String updateTime;

    /**
     * 图标
     */
    private final Icon icon;

    /**
     * 会话顺序(本地)
     */
    private int conversationOrder;

    /**
     * 是否仅在客户端新建且存在的会话，未发送过任何消息
     */
    private boolean newConversationAtLocal;

    /**
     * AI原子能力标识
     */
    private String subServiceType;

    /**
     * 当前选中知识库
     */
    private Integer kbId = null;

    /**
     * 本对话限定条件
     */
    private DialogCondition modelRouteCondition;

    /**
     * 本次会话消息树
     */
    private final ConversationMessagesTree tree = new ConversationMessagesTree();

    public Conversation() {
        this(Constants.NEW_CONVERSATION_NAME, GPTIcons.MESSAGE);
    }

    public Conversation(String title) {
        this(title, GPTIcons.MESSAGE);
    }

    public Conversation(String id, String title, boolean newConversationAtLocal) {
        this(id, title, GPTIcons.MESSAGE, TimeUtil.getNowTimeDatetimeStr(), TimeUtil.getNowTimeDatetimeStr(), newConversationAtLocal);
    }

    public Conversation(String id, String title, Icon icon) {
        this(title, icon);
        this.id = id;
    }

    public Conversation(String title, Icon icon) {
        this.title = title;
        this.icon = icon;
    }

    public Conversation(String id, String title, Icon icon, String createTime, String updateTime, boolean newConversationAtLocal) {
        this.id = id;
        this.title = title;
        this.icon = icon;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.newConversationAtLocal = newConversationAtLocal;
    }

    public static Conversation defaultConversationWithTitle(String title, boolean newConversationAtLocal) {
        return new Conversation(UUID.randomUUID().toString(), title, newConversationAtLocal);
    }

    public boolean checkIsNewConversation() {
        return newConversationAtLocal;
    }

    public String getId() {
        return this.id;
    }

    public String getTitle() {
        return this.title;
    }

    public Icon getIcon() {
        return this.icon;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getConversationOrder() {
        return this.conversationOrder;
    }

    public void setConversationOrder(int conversationOrder) {
        this.conversationOrder = conversationOrder;
    }

    public Queue<ConversationMessage> getConversationMessages() {
        return new ConcurrentLinkedDeque<>(tree.getMessagesList(true));
    }

    public LinkedList<ConversationMessage> getListConversationMessages() {
        return tree.getMessagesList(false);
    }

    public void addConversationMessage(ConversationMessage message, boolean isReAsk) {
        tree.addNewMessage(message, isReAsk);
    }


    public String getSubServiceType() {
        return subServiceType;
    }

    public void setSubServiceType(String subServiceType) {
        this.subServiceType = subServiceType;
    }

    public DialogCondition getModelRouteCondition() {
        return modelRouteCondition;
    }

    public void setModelRouteCondition(DialogCondition modelRouteCondition) {
        this.modelRouteCondition = modelRouteCondition;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public boolean isNewConversationAtLocal() {
        return newConversationAtLocal;
    }

    public void setNewConversationAtLocal(boolean newConversationAtLocal) {
        this.newConversationAtLocal = newConversationAtLocal;
    }

    public void setMessagesByDialog(ChatMessageSimple questions) {
        tree.initTreeByDialogQuestions(questions);
    }

    public ConversationMessagesTree getTree() {
        return tree;
    }

    /**
     * @return 最后一个有效回答或者有效错误的ID
     * <p>
     * 获取最后一个有效回答或者有效错误的ID，用于发起新的提问
     */
    public String getLastValidConversationAnswerRegId() {
        return tree.getLastValidConversationAnswerRegId();
    }


    public Integer getKbId() {
        return kbId;
    }

    public void updateKbId() {

        if (getListConversationMessages().isEmpty()) {
            kbId = null;
            return;
        }
        // 从当前展示问答路径的最后一个“question”节点提取知识库
        kbId = getListConversationMessages().getLast().getParent().getKbId();
    }

    public void setKbId(Integer kbId) {
        this.kbId = kbId;
    }
}
