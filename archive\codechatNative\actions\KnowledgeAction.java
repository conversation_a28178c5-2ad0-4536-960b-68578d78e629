package com.srdcloud.ideplugin.assistant.codechatNative.actions;

import com.srdcloud.ideplugin.remote.domain.KnowledgeBase.KnowledgeBaseInfo;

public class KnowledgeAction {

    KnowledgeBaseInfo knowledgeBaseInfo;

    public KnowledgeAction(KnowledgeBaseInfo knowledgeBaseInfo){
        this.knowledgeBaseInfo = knowledgeBaseInfo;
    }

    public void execute() {
        // 执行
    }

    public String getKnowledgeBaseName() {
        return knowledgeBaseInfo.getKbName();
    }

    public String getOrgName() {
        return knowledgeBaseInfo.getOrganizationName();
    }

    public int getKnowledgeBaseId() {
        return knowledgeBaseInfo.getKbId();
    }

    public KnowledgeBaseInfo getKnowledgeBaseInfo() {
        return knowledgeBaseInfo;
    }
}
