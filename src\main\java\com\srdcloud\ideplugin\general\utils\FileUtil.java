package com.srdcloud.ideplugin.general.utils;

import com.google.gson.JsonObject;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.LocalFileSystem;
import com.intellij.openapi.vfs.VfsUtilCore;
import com.intellij.openapi.vfs.VirtualFile;
import com.srdcloud.ideplugin.agent.process.AgentProcess;
import com.srdcloud.ideplugin.general.enums.LanguageExt;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.attribute.*;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.intellij.icons.AllIcons.FileTypes.Json;

/**
 * @author: yangy
 * @date: 2023/8/24 15:36
 * @Desc
 */
public class FileUtil {
    public FileUtil() {
    }

    private static final Logger logger = LoggerFactory.getLogger(FileUtil.class);

    /**
     * 获取项目根目录
     */
    public static VirtualFile getProjectBaseDir(final Project project) {
        String basePath = project.getBasePath();
        if (basePath != null) {
            return LocalFileSystem.getInstance().findFileByPath(basePath);
        }
        return null;
    }

    // 获取文件相对于项目根目录的相对路径
    public static String getRelativePath(VirtualFile file, Project project) {
        VirtualFile baseDir = project.getBaseDir(); // 获取项目根目录
        return VfsUtilCore.getRelativePath(file, baseDir, '/');
    }

    public static boolean exists(String filePath) {
        return (new File(filePath)).exists();
    }

    public static boolean createDirectory(File directory) {
        return !directory.exists() ? directory.mkdirs() : true;
    }

    public static void writeToFile(String filePath, String content) {
        try {
            BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(filePath), StandardCharsets.UTF_8));

            try {
                writer.write(content);
            } catch (Throwable var6) {
                try {
                    writer.close();
                } catch (Throwable var5) {
                    var6.addSuppressed(var5);
                }

                throw var6;
            }

            writer.close();
        } catch (IOException var7) {
            var7.printStackTrace();
        }

    }

    public static void appendToFile(File file, String content) {
        try {
            BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file, true), StandardCharsets.UTF_8));

            try {
                writer.write(content);
            } catch (Throwable var6) {
                try {
                    writer.close();
                } catch (Throwable var5) {
                    var6.addSuppressed(var5);
                }

                throw var6;
            }

            writer.close();
        } catch (IOException var7) {
            var7.printStackTrace();
        }

    }

    public static boolean delete(String file) {
        return (new File(file)).delete();
    }

    public static File createFile(String filePath) {
        File file = new File(filePath);

        try {
            if (file.createNewFile()) {
                return file;
            }
        } catch (IOException var3) {
            var3.printStackTrace();
        }

        return null;
    }

    public static String loadTextAndClose(String filePath) {
        StringBuilder sb = new StringBuilder();

        try {
            BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(filePath), StandardCharsets.UTF_8));

            String line;
            try {
                while ((line = reader.readLine()) != null) {
                    sb.append(line).append("\n");
                }
            } catch (Throwable var6) {
                try {
                    reader.close();
                } catch (Throwable var5) {
                    var6.addSuppressed(var5);
                }

                throw var6;
            }

            reader.close();
        } catch (IOException var7) {
            var7.printStackTrace();
        }

        return sb.toString();
    }

    public static String getFileName(String filePath) {
        int lastSeparatorIndex = Math.max(filePath.lastIndexOf(47), filePath.lastIndexOf(92));
        return lastSeparatorIndex > -1 && lastSeparatorIndex < filePath.length() - 1 ? filePath.substring(lastSeparatorIndex + 1) : filePath;
    }

    public static String getRelativePath(String basePath, String filePath) {
        return !filePath.startsWith(basePath) ? filePath : filePath.substring(basePath.length());
    }

    /**
     * 获取文件树：每个节点包含绝对路径和相对路径
     *
     * @param projectPath
     * @return
     */
    public static StringBuilder getFileTreeWithBothPath(String projectPath) {
        StringBuilder tree = new StringBuilder();
        File projectDir = new File(projectPath);
        String absolutePrefix = projectPath.substring(0, projectPath.lastIndexOf("/")) + "/";

        tree.append("[\n");
        generateDirectoryTreeWithBothPath(projectDir, "", absolutePrefix, tree, true);
        tree.append("]");

        return tree;
    }


    // 获取文件树结构
    // 注：该方法获取项目相对路径
    public static StringBuilder getFileTree(String projectPath) {
        File projectDir = new File(projectPath);
        StringBuilder tree = new StringBuilder();

        tree.append("[\n");
        generateDirectoryTree(projectDir, "./", tree);
        tree.deleteCharAt(tree.length() - 2);
        tree.append("]");

        return tree;
    }

    // 获取文件树结构
    // 注：该方法获取绝对路径
    public static StringBuilder getFullPathFileTree(String filePath) {
        File projectDir = new File(filePath);
        StringBuilder tree = new StringBuilder();

        tree.append("[\n");

        int index = filePath.lastIndexOf("/");
        generateDirectoryTree(projectDir, filePath.substring(0, index) + "/", tree);
        tree.deleteCharAt(tree.length() - 2);
        tree.append("]");

        return tree;
    }

    // 递归生成包含：name、relativePath、path、size的文件树结构，如果有子级，则用children包裹
    private static void generateDirectoryTreeWithBothPath(File directory,
                                                          String relativePrefix,
                                                          String absolutePrefix,
                                                          StringBuilder tree,
                                                          boolean isLastChild) {

        String currRelativeDirName = directory.getName();
        String currDirName = directory.getName();

        tree.append("{\n");
        tree.append("name: '").append(currDirName).append("',\n");
        // 相对路径：特殊处理，根目录用 ./ 表示，无目录名
        if (StringUtils.isBlank(relativePrefix)) {
            relativePrefix = ".";
            currRelativeDirName = "";
            tree.append("relativePath: '").append(relativePrefix).append("/").append("',\n");
        } else {
            tree.append("relativePath: '").append(relativePrefix).append(currRelativeDirName).append("',\n");
        }

        // 绝对路径
        tree.append("path: '").append(absolutePrefix).append(currDirName).append("',\n");

        // 如果目录，则递归处理子文件夹和文件
        if (directory.isDirectory()) {
            if (directory.listFiles() != null) {
                tree.append("children: [\n");
                for (int i = 0; i < directory.listFiles().length; i++) {
                    File file = directory.listFiles()[i];
                    if (ExclusionRules.needExcludeFile(file)){
                        continue;
                    }
                    generateDirectoryTreeWithBothPath(
                            file,
                            relativePrefix + currRelativeDirName + "/",
                            absolutePrefix + currDirName + "/",
                            tree,
                            i == directory.listFiles().length - 1
                    );

                }
                tree.append("],\n");
            }
        }
        tree.append("size: ").append(directory.length()).append("\n");
        if (isLastChild) {
            tree.append("}\n");
        } else {
            tree.append("},\n");
        }
    }

    // 递归生成文件树结构
    private static void generateDirectoryTree(File directory, String prefix, StringBuilder tree) {

        tree.append("{\n");

        tree.append("name: '").append(directory.getName()).append("',\n");

        if (directory.isDirectory()) {
            if (directory.listFiles() != null) {
                tree.append("children: [\n");
                for (File file : directory.listFiles()) {
                    if (ExclusionRules.needExcludeFile(file)) {
                        continue;
                    }
                    // 不排除的，则进行处理
                    generateDirectoryTree(file, prefix + directory.getName() + "/", tree);
                }
                tree.append("]\n");
            }
        } else {
            tree.append("path: '").append(prefix).append(directory.getName()).append("', \nsize: ").append(directory.length());
        }

        tree.append("},\n");
    }

    // 压缩文件列表
    public static void zipFileList(List<String> fileList, String zipFilename) throws IOException {
        try (
                FileOutputStream fos = new FileOutputStream(zipFilename);
                ZipOutputStream zipOut = new ZipOutputStream(fos);
        ) {
            for (String item : fileList) {
                File srcFile = new File(item);
                zipFile(srcFile, zipOut, "");
            }
        }
    }

    /// 压缩目录
    public static void zipDirectory(String dir, String zipFilePath) throws IOException {
        File directory = new File(dir);

        try (
                FileOutputStream fos = new FileOutputStream(zipFilePath);
                ZipOutputStream zipOut = new ZipOutputStream(fos);
        ) {
            zipFile(directory, zipOut, "");
        }
    }

    // 压缩文件
    private static void zipFile(File file, ZipOutputStream zipOut, String basePath) throws IOException {
        if (file.isDirectory()) {
            File[] children = file.listFiles();
            if (children != null) {
                for (File child : children) {
                    zipFile(child, zipOut, basePath + file.getName() + "/");
                }
            }
        } else {
            try (FileInputStream fis = new FileInputStream(file)) {
                ZipEntry zipEntry = new ZipEntry(basePath + file.getName());
                zipOut.putNextEntry(zipEntry);
                byte[] bytes = new byte[1024];
                int length;
                while ((length = fis.read(bytes)) >= 0) {
                    zipOut.write(bytes, 0, length);
                }
            }
        }
    }

    // 删除目录及其所有子目录和文件
    public static void deleteDirectory(File directory) {
        if (directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    deleteDirectory(file);
                }
            }
        }
        directory.delete();
    }


    /**
     * 通过扩展名获得文件的编程语言类型
     *
     * @param extension
     * @return
     */
    public static String getLanguageTypeByExt(String extension) {
        if (extension == null || extension.isEmpty()) {
            return "";
        }

        // 根据后缀判断文件编程语言类型
        return LanguageExt.getTypeByExt(extension);
    }


    /**
     * 获取文件扩展名
     *
     * @param fileName
     * @return
     */
    public static String getExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "";
        }
        // 获取最后一个点后面的后缀
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        return extension;
    }

    public static String getFileContent(String filePath) throws IOException {
        VirtualFile file = LocalFileSystem.getInstance().findFileByPath(filePath);
        if (file != null && file.exists() && file.isWritable()) {
            return new String(file.contentsToByteArray(), file.getCharset());
        } else {
            // todo：找不到文件, 添加日志
            return null;
        }
    }

    /**
     * 验证文件的MD5哈希值
     *
     * @param filePath    文件路径
     * @param expectedMd5 期望的MD5值
     * @throws IOException              如果文件读取失败
     * @throws NoSuchAlgorithmException 如果MD5算法不可用
     * @throws RuntimeException         如果MD5校验失败
     */
    public static void validateFileMd5(String filePath, String expectedMd5) throws IOException, NoSuchAlgorithmException {
        if (expectedMd5 == null || expectedMd5.isEmpty()) {
            DebugLogUtil.warn("Expected MD5 is empty, skipping validation for: " + filePath);
            return;
        }

        File file = new File(filePath);
        if (!file.exists() || !file.isFile()) {
            throw new IOException("File does not exist or is not a regular file: " + filePath);
        }

        String actualMd5 = calculateMd5(file);
        if (!expectedMd5.equalsIgnoreCase(actualMd5)) {
            throw new RuntimeException("MD5 validation failed for file: " + filePath +
                    "\nExpected: " + expectedMd5 +
                    "\nActual: " + actualMd5);
        }

        DebugLogUtil.info("MD5 validation successful for file: " + filePath);
    }

    /**
     * 计算文件的MD5哈希值
     *
     * @param file 文件
     * @return MD5哈希值（16进制字符串）
     * @throws IOException              如果文件读取失败
     * @throws NoSuchAlgorithmException 如果MD5算法不可用
     */
    public static String calculateMd5(File file) throws IOException, NoSuchAlgorithmException {
        MessageDigest digest = MessageDigest.getInstance("MD5");
        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                digest.update(buffer, 0, bytesRead);
            }
        }

        // 将字节数组转换为16进制字符串
        byte[] md5Bytes = digest.digest();
        StringBuilder sb = new StringBuilder();
        for (byte b : md5Bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    public static void setFilePermission(String path) {
        String os = System.getProperty("os.name").toLowerCase();

        Path filePath = Path.of(path);
        try {
            if (os.contains("win")) {
                // 处理win下文件权限问题
                setWinExecutePermission(filePath);
            } else {
                // 处理Unix下系统权限问题
                setUnixExecutePermission(filePath);
            }
        } catch (IOException e) {
            // 日志
            logger.warn("[cf] Failed to set system execute permission in {}:", os, e);
        }
    }

    /**
     * 设置 Unix 下路径执行权限
     */
    public static void setUnixExecutePermission(Path filePath) throws IOException {
        Set<PosixFilePermission> set = new HashSet<>();
        set.add(PosixFilePermission.OWNER_READ);
        set.add(PosixFilePermission.OWNER_WRITE);
        set.add(PosixFilePermission.OWNER_EXECUTE);

        Files.setPosixFilePermissions(filePath, set);
    }

    /**
     * 设置 Windows 下路径执行权限
     */
    public static void setWinExecutePermission(Path filePath) throws IOException {
        AclFileAttributeView aclView = Files.getFileAttributeView(filePath, AclFileAttributeView.class);
        if (aclView != null) {
            UserPrincipal user = FileSystems.getDefault()
                    .getUserPrincipalLookupService()
                    .lookupPrincipalByName(System.getProperty("user.name"));
            AclEntry entry = AclEntry.newBuilder()
                    .setType(AclEntryType.ALLOW)
                    .setPrincipal(user)
                    .setPermissions(AclEntryPermission.EXECUTE, AclEntryPermission.READ_DATA, AclEntryPermission.WRITE_DATA)
                    .build();
            List<AclEntry> aclEntries = aclView.getAcl();
            aclEntries.add(entry);
            aclView.setAcl(aclEntries);
            DebugLogUtil.println("Execution permission granted for Windows: " + filePath);
        }
    }

}
