var Se=Object.defineProperty;var Re=(e,t,r)=>t in e?Se(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var _e=(e,t,r)=>Re(e,typeof t!="symbol"?t+"":t,r);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))s(n);new MutationObserver(n=>{for(const o of n)if(o.type==="childList")for(const a of o.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&s(a)}).observe(document,{childList:!0,subtree:!0});function r(n){const o={};return n.integrity&&(o.integrity=n.integrity),n.referrerPolicy&&(o.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?o.credentials="include":n.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(n){if(n.ep)return;n.ep=!0;const o=r(n);fetch(n.href,o)}})();/**
* @vue/shared v3.5.12
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function makeMap(e){const t=Object.create(null);for(const r of e.split(","))t[r]=1;return r=>r in t}const EMPTY_OBJ={},EMPTY_ARR=[],NOOP=()=>{},NO=()=>!1,isOn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),isModelListener=e=>e.startsWith("onUpdate:"),extend=Object.assign,remove=(e,t)=>{const r=e.indexOf(t);r>-1&&e.splice(r,1)},hasOwnProperty$1=Object.prototype.hasOwnProperty,hasOwn=(e,t)=>hasOwnProperty$1.call(e,t),isArray=Array.isArray,isMap=e=>toTypeString(e)==="[object Map]",isSet=e=>toTypeString(e)==="[object Set]",isFunction=e=>typeof e=="function",isString=e=>typeof e=="string",isSymbol=e=>typeof e=="symbol",isObject=e=>e!==null&&typeof e=="object",isPromise=e=>(isObject(e)||isFunction(e))&&isFunction(e.then)&&isFunction(e.catch),objectToString=Object.prototype.toString,toTypeString=e=>objectToString.call(e),toRawType=e=>toTypeString(e).slice(8,-1),isPlainObject=e=>toTypeString(e)==="[object Object]",isIntegerKey=e=>isString(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,isReservedProp=makeMap(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),cacheStringFunction=e=>{const t=Object.create(null);return r=>t[r]||(t[r]=e(r))},camelizeRE=/-(\w)/g,camelize=cacheStringFunction(e=>e.replace(camelizeRE,(t,r)=>r?r.toUpperCase():"")),hyphenateRE=/\B([A-Z])/g,hyphenate=cacheStringFunction(e=>e.replace(hyphenateRE,"-$1").toLowerCase()),capitalize=cacheStringFunction(e=>e.charAt(0).toUpperCase()+e.slice(1)),toHandlerKey=cacheStringFunction(e=>e?`on${capitalize(e)}`:""),hasChanged=(e,t)=>!Object.is(e,t),invokeArrayFns=(e,...t)=>{for(let r=0;r<e.length;r++)e[r](...t)},def=(e,t,r,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:r})},looseToNumber=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let _globalThis;const getGlobalThis=()=>_globalThis||(_globalThis=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function normalizeStyle(e){if(isArray(e)){const t={};for(let r=0;r<e.length;r++){const s=e[r],n=isString(s)?parseStringStyle(s):normalizeStyle(s);if(n)for(const o in n)t[o]=n[o]}return t}else if(isString(e)||isObject(e))return e}const listDelimiterRE=/;(?![^(]*\))/g,propertyDelimiterRE=/:([^]+)/,styleCommentRE=/\/\*[^]*?\*\//g;function parseStringStyle(e){const t={};return e.replace(styleCommentRE,"").split(listDelimiterRE).forEach(r=>{if(r){const s=r.split(propertyDelimiterRE);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function normalizeClass(e){let t="";if(isString(e))t=e;else if(isArray(e))for(let r=0;r<e.length;r++){const s=normalizeClass(e[r]);s&&(t+=s+" ")}else if(isObject(e))for(const r in e)e[r]&&(t+=r+" ");return t.trim()}const specialBooleanAttrs="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",isSpecialBooleanAttr=makeMap(specialBooleanAttrs);function includeBooleanAttr(e){return!!e||e===""}/**
* @vue/reactivity v3.5.12
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let activeEffectScope;class EffectScope{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=activeEffectScope,!t&&activeEffectScope&&(this.index=(activeEffectScope.scopes||(activeEffectScope.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,r;if(this.scopes)for(t=0,r=this.scopes.length;t<r;t++)this.scopes[t].pause();for(t=0,r=this.effects.length;t<r;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,r;if(this.scopes)for(t=0,r=this.scopes.length;t<r;t++)this.scopes[t].resume();for(t=0,r=this.effects.length;t<r;t++)this.effects[t].resume()}}run(t){if(this._active){const r=activeEffectScope;try{return activeEffectScope=this,t()}finally{activeEffectScope=r}}}on(){activeEffectScope=this}off(){activeEffectScope=this.parent}stop(t){if(this._active){let r,s;for(r=0,s=this.effects.length;r<s;r++)this.effects[r].stop();for(r=0,s=this.cleanups.length;r<s;r++)this.cleanups[r]();if(this.scopes)for(r=0,s=this.scopes.length;r<s;r++)this.scopes[r].stop(!0);if(!this.detached&&this.parent&&!t){const n=this.parent.scopes.pop();n&&n!==this&&(this.parent.scopes[this.index]=n,n.index=this.index)}this.parent=void 0,this._active=!1}}}function getCurrentScope(){return activeEffectScope}let activeSub;const pausedQueueEffects=new WeakSet;class ReactiveEffect{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,activeEffectScope&&activeEffectScope.active&&activeEffectScope.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,pausedQueueEffects.has(this)&&(pausedQueueEffects.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||batch(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,cleanupEffect(this),prepareDeps(this);const t=activeSub,r=shouldTrack;activeSub=this,shouldTrack=!0;try{return this.fn()}finally{cleanupDeps(this),activeSub=t,shouldTrack=r,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)removeSub(t);this.deps=this.depsTail=void 0,cleanupEffect(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?pausedQueueEffects.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){isDirty(this)&&this.run()}get dirty(){return isDirty(this)}}let batchDepth=0,batchedSub,batchedComputed;function batch(e,t=!1){if(e.flags|=8,t){e.next=batchedComputed,batchedComputed=e;return}e.next=batchedSub,batchedSub=e}function startBatch(){batchDepth++}function endBatch(){if(--batchDepth>0)return;if(batchedComputed){let t=batchedComputed;for(batchedComputed=void 0;t;){const r=t.next;t.next=void 0,t.flags&=-9,t=r}}let e;for(;batchedSub;){let t=batchedSub;for(batchedSub=void 0;t;){const r=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=r}}if(e)throw e}function prepareDeps(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function cleanupDeps(e){let t,r=e.depsTail,s=r;for(;s;){const n=s.prevDep;s.version===-1?(s===r&&(r=n),removeSub(s),removeDep(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=n}e.deps=t,e.depsTail=r}function isDirty(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(refreshComputed(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function refreshComputed(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===globalVersion))return;e.globalVersion=globalVersion;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!isDirty(e)){e.flags&=-3;return}const r=activeSub,s=shouldTrack;activeSub=e,shouldTrack=!0;try{prepareDeps(e);const n=e.fn(e._value);(t.version===0||hasChanged(n,e._value))&&(e._value=n,t.version++)}catch(n){throw t.version++,n}finally{activeSub=r,shouldTrack=s,cleanupDeps(e),e.flags&=-3}}function removeSub(e,t=!1){const{dep:r,prevSub:s,nextSub:n}=e;if(s&&(s.nextSub=n,e.prevSub=void 0),n&&(n.prevSub=s,e.nextSub=void 0),r.subs===e&&(r.subs=s,!s&&r.computed)){r.computed.flags&=-5;for(let o=r.computed.deps;o;o=o.nextDep)removeSub(o,!0)}!t&&!--r.sc&&r.map&&r.map.delete(r.key)}function removeDep(e){const{prevDep:t,nextDep:r}=e;t&&(t.nextDep=r,e.prevDep=void 0),r&&(r.prevDep=t,e.nextDep=void 0)}let shouldTrack=!0;const trackStack=[];function pauseTracking(){trackStack.push(shouldTrack),shouldTrack=!1}function resetTracking(){const e=trackStack.pop();shouldTrack=e===void 0?!0:e}function cleanupEffect(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const r=activeSub;activeSub=void 0;try{t()}finally{activeSub=r}}}let globalVersion=0;class Link{constructor(t,r){this.sub=t,this.dep=r,this.version=r.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Dep{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!activeSub||!shouldTrack||activeSub===this.computed)return;let r=this.activeLink;if(r===void 0||r.sub!==activeSub)r=this.activeLink=new Link(activeSub,this),activeSub.deps?(r.prevDep=activeSub.depsTail,activeSub.depsTail.nextDep=r,activeSub.depsTail=r):activeSub.deps=activeSub.depsTail=r,addSub(r);else if(r.version===-1&&(r.version=this.version,r.nextDep)){const s=r.nextDep;s.prevDep=r.prevDep,r.prevDep&&(r.prevDep.nextDep=s),r.prevDep=activeSub.depsTail,r.nextDep=void 0,activeSub.depsTail.nextDep=r,activeSub.depsTail=r,activeSub.deps===r&&(activeSub.deps=s)}return r}trigger(t){this.version++,globalVersion++,this.notify(t)}notify(t){startBatch();try{for(let r=this.subs;r;r=r.prevSub)r.sub.notify()&&r.sub.dep.notify()}finally{endBatch()}}}function addSub(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)addSub(s)}const r=e.dep.subs;r!==e&&(e.prevSub=r,r&&(r.nextSub=e)),e.dep.subs=e}}const targetMap=new WeakMap,ITERATE_KEY=Symbol(""),MAP_KEY_ITERATE_KEY=Symbol(""),ARRAY_ITERATE_KEY=Symbol("");function track(e,t,r){if(shouldTrack&&activeSub){let s=targetMap.get(e);s||targetMap.set(e,s=new Map);let n=s.get(r);n||(s.set(r,n=new Dep),n.map=s,n.key=r),n.track()}}function trigger(e,t,r,s,n,o){const a=targetMap.get(e);if(!a){globalVersion++;return}const l=c=>{c&&c.trigger()};if(startBatch(),t==="clear")a.forEach(l);else{const c=isArray(e),_=c&&isIntegerKey(r);if(c&&r==="length"){const u=Number(s);a.forEach((f,m)=>{(m==="length"||m===ARRAY_ITERATE_KEY||!isSymbol(m)&&m>=u)&&l(f)})}else switch((r!==void 0||a.has(void 0))&&l(a.get(r)),_&&l(a.get(ARRAY_ITERATE_KEY)),t){case"add":c?_&&l(a.get("length")):(l(a.get(ITERATE_KEY)),isMap(e)&&l(a.get(MAP_KEY_ITERATE_KEY)));break;case"delete":c||(l(a.get(ITERATE_KEY)),isMap(e)&&l(a.get(MAP_KEY_ITERATE_KEY)));break;case"set":isMap(e)&&l(a.get(ITERATE_KEY));break}}endBatch()}function reactiveReadArray(e){const t=toRaw(e);return t===e?t:(track(t,"iterate",ARRAY_ITERATE_KEY),isShallow(e)?t:t.map(toReactive))}function shallowReadArray(e){return track(e=toRaw(e),"iterate",ARRAY_ITERATE_KEY),e}const arrayInstrumentations={__proto__:null,[Symbol.iterator](){return iterator(this,Symbol.iterator,toReactive)},concat(...e){return reactiveReadArray(this).concat(...e.map(t=>isArray(t)?reactiveReadArray(t):t))},entries(){return iterator(this,"entries",e=>(e[1]=toReactive(e[1]),e))},every(e,t){return apply(this,"every",e,t,void 0,arguments)},filter(e,t){return apply(this,"filter",e,t,r=>r.map(toReactive),arguments)},find(e,t){return apply(this,"find",e,t,toReactive,arguments)},findIndex(e,t){return apply(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return apply(this,"findLast",e,t,toReactive,arguments)},findLastIndex(e,t){return apply(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return apply(this,"forEach",e,t,void 0,arguments)},includes(...e){return searchProxy(this,"includes",e)},indexOf(...e){return searchProxy(this,"indexOf",e)},join(e){return reactiveReadArray(this).join(e)},lastIndexOf(...e){return searchProxy(this,"lastIndexOf",e)},map(e,t){return apply(this,"map",e,t,void 0,arguments)},pop(){return noTracking(this,"pop")},push(...e){return noTracking(this,"push",e)},reduce(e,...t){return reduce(this,"reduce",e,t)},reduceRight(e,...t){return reduce(this,"reduceRight",e,t)},shift(){return noTracking(this,"shift")},some(e,t){return apply(this,"some",e,t,void 0,arguments)},splice(...e){return noTracking(this,"splice",e)},toReversed(){return reactiveReadArray(this).toReversed()},toSorted(e){return reactiveReadArray(this).toSorted(e)},toSpliced(...e){return reactiveReadArray(this).toSpliced(...e)},unshift(...e){return noTracking(this,"unshift",e)},values(){return iterator(this,"values",toReactive)}};function iterator(e,t,r){const s=shallowReadArray(e),n=s[t]();return s!==e&&!isShallow(e)&&(n._next=n.next,n.next=()=>{const o=n._next();return o.value&&(o.value=r(o.value)),o}),n}const arrayProto=Array.prototype;function apply(e,t,r,s,n,o){const a=shallowReadArray(e),l=a!==e&&!isShallow(e),c=a[t];if(c!==arrayProto[t]){const f=c.apply(e,o);return l?toReactive(f):f}let _=r;a!==e&&(l?_=function(f,m){return r.call(this,toReactive(f),m,e)}:r.length>2&&(_=function(f,m){return r.call(this,f,m,e)}));const u=c.call(a,_,s);return l&&n?n(u):u}function reduce(e,t,r,s){const n=shallowReadArray(e);let o=r;return n!==e&&(isShallow(e)?r.length>3&&(o=function(a,l,c){return r.call(this,a,l,c,e)}):o=function(a,l,c){return r.call(this,a,toReactive(l),c,e)}),n[t](o,...s)}function searchProxy(e,t,r){const s=toRaw(e);track(s,"iterate",ARRAY_ITERATE_KEY);const n=s[t](...r);return(n===-1||n===!1)&&isProxy(r[0])?(r[0]=toRaw(r[0]),s[t](...r)):n}function noTracking(e,t,r=[]){pauseTracking(),startBatch();const s=toRaw(e)[t].apply(e,r);return endBatch(),resetTracking(),s}const isNonTrackableKeys=makeMap("__proto__,__v_isRef,__isVue"),builtInSymbols=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(isSymbol));function hasOwnProperty(e){isSymbol(e)||(e=String(e));const t=toRaw(this);return track(t,"has",e),t.hasOwnProperty(e)}class BaseReactiveHandler{constructor(t=!1,r=!1){this._isReadonly=t,this._isShallow=r}get(t,r,s){const n=this._isReadonly,o=this._isShallow;if(r==="__v_isReactive")return!n;if(r==="__v_isReadonly")return n;if(r==="__v_isShallow")return o;if(r==="__v_raw")return s===(n?o?shallowReadonlyMap:readonlyMap:o?shallowReactiveMap:reactiveMap).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const a=isArray(t);if(!n){let c;if(a&&(c=arrayInstrumentations[r]))return c;if(r==="hasOwnProperty")return hasOwnProperty}const l=Reflect.get(t,r,isRef(t)?t:s);return(isSymbol(r)?builtInSymbols.has(r):isNonTrackableKeys(r))||(n||track(t,"get",r),o)?l:isRef(l)?a&&isIntegerKey(r)?l:l.value:isObject(l)?n?readonly(l):reactive(l):l}}class MutableReactiveHandler extends BaseReactiveHandler{constructor(t=!1){super(!1,t)}set(t,r,s,n){let o=t[r];if(!this._isShallow){const c=isReadonly(o);if(!isShallow(s)&&!isReadonly(s)&&(o=toRaw(o),s=toRaw(s)),!isArray(t)&&isRef(o)&&!isRef(s))return c?!1:(o.value=s,!0)}const a=isArray(t)&&isIntegerKey(r)?Number(r)<t.length:hasOwn(t,r),l=Reflect.set(t,r,s,isRef(t)?t:n);return t===toRaw(n)&&(a?hasChanged(s,o)&&trigger(t,"set",r,s):trigger(t,"add",r,s)),l}deleteProperty(t,r){const s=hasOwn(t,r);t[r];const n=Reflect.deleteProperty(t,r);return n&&s&&trigger(t,"delete",r,void 0),n}has(t,r){const s=Reflect.has(t,r);return(!isSymbol(r)||!builtInSymbols.has(r))&&track(t,"has",r),s}ownKeys(t){return track(t,"iterate",isArray(t)?"length":ITERATE_KEY),Reflect.ownKeys(t)}}class ReadonlyReactiveHandler extends BaseReactiveHandler{constructor(t=!1){super(!0,t)}set(t,r){return!0}deleteProperty(t,r){return!0}}const mutableHandlers=new MutableReactiveHandler,readonlyHandlers=new ReadonlyReactiveHandler,shallowReactiveHandlers=new MutableReactiveHandler(!0),shallowReadonlyHandlers=new ReadonlyReactiveHandler(!0),toShallow=e=>e,getProto=e=>Reflect.getPrototypeOf(e);function createIterableMethod(e,t,r){return function(...s){const n=this.__v_raw,o=toRaw(n),a=isMap(o),l=e==="entries"||e===Symbol.iterator&&a,c=e==="keys"&&a,_=n[e](...s),u=r?toShallow:t?toReadonly:toReactive;return!t&&track(o,"iterate",c?MAP_KEY_ITERATE_KEY:ITERATE_KEY),{next(){const{value:f,done:m}=_.next();return m?{value:f,done:m}:{value:l?[u(f[0]),u(f[1])]:u(f),done:m}},[Symbol.iterator](){return this}}}}function createReadonlyMethod(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function createInstrumentations(e,t){const r={get(n){const o=this.__v_raw,a=toRaw(o),l=toRaw(n);e||(hasChanged(n,l)&&track(a,"get",n),track(a,"get",l));const{has:c}=getProto(a),_=t?toShallow:e?toReadonly:toReactive;if(c.call(a,n))return _(o.get(n));if(c.call(a,l))return _(o.get(l));o!==a&&o.get(n)},get size(){const n=this.__v_raw;return!e&&track(toRaw(n),"iterate",ITERATE_KEY),Reflect.get(n,"size",n)},has(n){const o=this.__v_raw,a=toRaw(o),l=toRaw(n);return e||(hasChanged(n,l)&&track(a,"has",n),track(a,"has",l)),n===l?o.has(n):o.has(n)||o.has(l)},forEach(n,o){const a=this,l=a.__v_raw,c=toRaw(l),_=t?toShallow:e?toReadonly:toReactive;return!e&&track(c,"iterate",ITERATE_KEY),l.forEach((u,f)=>n.call(o,_(u),_(f),a))}};return extend(r,e?{add:createReadonlyMethod("add"),set:createReadonlyMethod("set"),delete:createReadonlyMethod("delete"),clear:createReadonlyMethod("clear")}:{add(n){!t&&!isShallow(n)&&!isReadonly(n)&&(n=toRaw(n));const o=toRaw(this);return getProto(o).has.call(o,n)||(o.add(n),trigger(o,"add",n,n)),this},set(n,o){!t&&!isShallow(o)&&!isReadonly(o)&&(o=toRaw(o));const a=toRaw(this),{has:l,get:c}=getProto(a);let _=l.call(a,n);_||(n=toRaw(n),_=l.call(a,n));const u=c.call(a,n);return a.set(n,o),_?hasChanged(o,u)&&trigger(a,"set",n,o):trigger(a,"add",n,o),this},delete(n){const o=toRaw(this),{has:a,get:l}=getProto(o);let c=a.call(o,n);c||(n=toRaw(n),c=a.call(o,n)),l&&l.call(o,n);const _=o.delete(n);return c&&trigger(o,"delete",n,void 0),_},clear(){const n=toRaw(this),o=n.size!==0,a=n.clear();return o&&trigger(n,"clear",void 0,void 0),a}}),["keys","values","entries",Symbol.iterator].forEach(n=>{r[n]=createIterableMethod(n,e,t)}),r}function createInstrumentationGetter(e,t){const r=createInstrumentations(e,t);return(s,n,o)=>n==="__v_isReactive"?!e:n==="__v_isReadonly"?e:n==="__v_raw"?s:Reflect.get(hasOwn(r,n)&&n in s?r:s,n,o)}const mutableCollectionHandlers={get:createInstrumentationGetter(!1,!1)},shallowCollectionHandlers={get:createInstrumentationGetter(!1,!0)},readonlyCollectionHandlers={get:createInstrumentationGetter(!0,!1)},shallowReadonlyCollectionHandlers={get:createInstrumentationGetter(!0,!0)},reactiveMap=new WeakMap,shallowReactiveMap=new WeakMap,readonlyMap=new WeakMap,shallowReadonlyMap=new WeakMap;function targetTypeMap(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function getTargetType(e){return e.__v_skip||!Object.isExtensible(e)?0:targetTypeMap(toRawType(e))}function reactive(e){return isReadonly(e)?e:createReactiveObject(e,!1,mutableHandlers,mutableCollectionHandlers,reactiveMap)}function shallowReactive(e){return createReactiveObject(e,!1,shallowReactiveHandlers,shallowCollectionHandlers,shallowReactiveMap)}function readonly(e){return createReactiveObject(e,!0,readonlyHandlers,readonlyCollectionHandlers,readonlyMap)}function shallowReadonly(e){return createReactiveObject(e,!0,shallowReadonlyHandlers,shallowReadonlyCollectionHandlers,shallowReadonlyMap)}function createReactiveObject(e,t,r,s,n){if(!isObject(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=n.get(e);if(o)return o;const a=getTargetType(e);if(a===0)return e;const l=new Proxy(e,a===2?s:r);return n.set(e,l),l}function isReactive(e){return isReadonly(e)?isReactive(e.__v_raw):!!(e&&e.__v_isReactive)}function isReadonly(e){return!!(e&&e.__v_isReadonly)}function isShallow(e){return!!(e&&e.__v_isShallow)}function isProxy(e){return e?!!e.__v_raw:!1}function toRaw(e){const t=e&&e.__v_raw;return t?toRaw(t):e}function markRaw(e){return!hasOwn(e,"__v_skip")&&Object.isExtensible(e)&&def(e,"__v_skip",!0),e}const toReactive=e=>isObject(e)?reactive(e):e,toReadonly=e=>isObject(e)?readonly(e):e;function isRef(e){return e?e.__v_isRef===!0:!1}function unref(e){return isRef(e)?e.value:e}const shallowUnwrapHandlers={get:(e,t,r)=>t==="__v_raw"?e:unref(Reflect.get(e,t,r)),set:(e,t,r,s)=>{const n=e[t];return isRef(n)&&!isRef(r)?(n.value=r,!0):Reflect.set(e,t,r,s)}};function proxyRefs(e){return isReactive(e)?e:new Proxy(e,shallowUnwrapHandlers)}class ComputedRefImpl{constructor(t,r,s){this.fn=t,this.setter=r,this._value=void 0,this.dep=new Dep(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=globalVersion-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!r,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&activeSub!==this)return batch(this,!0),!0}get value(){const t=this.dep.track();return refreshComputed(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function computed$1(e,t,r=!1){let s,n;return isFunction(e)?s=e:(s=e.get,n=e.set),new ComputedRefImpl(s,n,r)}const INITIAL_WATCHER_VALUE={},cleanupMap=new WeakMap;let activeWatcher;function onWatcherCleanup(e,t=!1,r=activeWatcher){if(r){let s=cleanupMap.get(r);s||cleanupMap.set(r,s=[]),s.push(e)}}function watch$1(e,t,r=EMPTY_OBJ){const{immediate:s,deep:n,once:o,scheduler:a,augmentJob:l,call:c}=r,_=R=>n?R:isShallow(R)||n===!1||n===0?traverse(R,1):traverse(R);let u,f,m,g,w=!1,b=!1;if(isRef(e)?(f=()=>e.value,w=isShallow(e)):isReactive(e)?(f=()=>_(e),w=!0):isArray(e)?(b=!0,w=e.some(R=>isReactive(R)||isShallow(R)),f=()=>e.map(R=>{if(isRef(R))return R.value;if(isReactive(R))return _(R);if(isFunction(R))return c?c(R,2):R()})):isFunction(e)?t?f=c?()=>c(e,2):e:f=()=>{if(m){pauseTracking();try{m()}finally{resetTracking()}}const R=activeWatcher;activeWatcher=u;try{return c?c(e,3,[g]):e(g)}finally{activeWatcher=R}}:f=NOOP,t&&n){const R=f,L=n===!0?1/0:n;f=()=>traverse(R(),L)}const P=getCurrentScope(),I=()=>{u.stop(),P&&remove(P.effects,u)};if(o&&t){const R=t;t=(...L)=>{R(...L),I()}}let E=b?new Array(e.length).fill(INITIAL_WATCHER_VALUE):INITIAL_WATCHER_VALUE;const O=R=>{if(!(!(u.flags&1)||!u.dirty&&!R))if(t){const L=u.run();if(n||w||(b?L.some((H,k)=>hasChanged(H,E[k])):hasChanged(L,E))){m&&m();const H=activeWatcher;activeWatcher=u;try{const k=[L,E===INITIAL_WATCHER_VALUE?void 0:b&&E[0]===INITIAL_WATCHER_VALUE?[]:E,g];c?c(t,3,k):t(...k),E=L}finally{activeWatcher=H}}}else u.run()};return l&&l(O),u=new ReactiveEffect(f),u.scheduler=a?()=>a(O,!1):O,g=R=>onWatcherCleanup(R,!1,u),m=u.onStop=()=>{const R=cleanupMap.get(u);if(R){if(c)c(R,4);else for(const L of R)L();cleanupMap.delete(u)}},t?s?O(!0):E=u.run():a?a(O.bind(null,!0),!0):u.run(),I.pause=u.pause.bind(u),I.resume=u.resume.bind(u),I.stop=I,I}function traverse(e,t=1/0,r){if(t<=0||!isObject(e)||e.__v_skip||(r=r||new Set,r.has(e)))return e;if(r.add(e),t--,isRef(e))traverse(e.value,t,r);else if(isArray(e))for(let s=0;s<e.length;s++)traverse(e[s],t,r);else if(isSet(e)||isMap(e))e.forEach(s=>{traverse(s,t,r)});else if(isPlainObject(e)){for(const s in e)traverse(e[s],t,r);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&traverse(e[s],t,r)}return e}/**
* @vue/runtime-core v3.5.12
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const stack=[];let isWarning=!1;function warn$1(e,...t){if(isWarning)return;isWarning=!0,pauseTracking();const r=stack.length?stack[stack.length-1].component:null,s=r&&r.appContext.config.warnHandler,n=getComponentTrace();if(s)callWithErrorHandling(s,r,11,[e+t.map(o=>{var a,l;return(l=(a=o.toString)==null?void 0:a.call(o))!=null?l:JSON.stringify(o)}).join(""),r&&r.proxy,n.map(({vnode:o})=>`at <${formatComponentName(r,o.type)}>`).join(`
`),n]);else{const o=[`[Vue warn]: ${e}`,...t];n.length&&o.push(`
`,...formatTrace(n)),console.warn(...o)}resetTracking(),isWarning=!1}function getComponentTrace(){let e=stack[stack.length-1];if(!e)return[];const t=[];for(;e;){const r=t[0];r&&r.vnode===e?r.recurseCount++:t.push({vnode:e,recurseCount:0});const s=e.component&&e.component.parent;e=s&&s.vnode}return t}function formatTrace(e){const t=[];return e.forEach((r,s)=>{t.push(...s===0?[]:[`
`],...formatTraceEntry(r))}),t}function formatTraceEntry({vnode:e,recurseCount:t}){const r=t>0?`... (${t} recursive calls)`:"",s=e.component?e.component.parent==null:!1,n=` at <${formatComponentName(e.component,e.type,s)}`,o=">"+r;return e.props?[n,...formatProps(e.props),o]:[n+o]}function formatProps(e){const t=[],r=Object.keys(e);return r.slice(0,3).forEach(s=>{t.push(...formatProp(s,e[s]))}),r.length>3&&t.push(" ..."),t}function formatProp(e,t,r){return isString(t)?(t=JSON.stringify(t),r?t:[`${e}=${t}`]):typeof t=="number"||typeof t=="boolean"||t==null?r?t:[`${e}=${t}`]:isRef(t)?(t=formatProp(e,toRaw(t.value),!0),r?t:[`${e}=Ref<`,t,">"]):isFunction(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=toRaw(t),r?t:[`${e}=`,t])}function callWithErrorHandling(e,t,r,s){try{return s?e(...s):e()}catch(n){handleError(n,t,r)}}function callWithAsyncErrorHandling(e,t,r,s){if(isFunction(e)){const n=callWithErrorHandling(e,t,r,s);return n&&isPromise(n)&&n.catch(o=>{handleError(o,t,r)}),n}if(isArray(e)){const n=[];for(let o=0;o<e.length;o++)n.push(callWithAsyncErrorHandling(e[o],t,r,s));return n}}function handleError(e,t,r,s=!0){const n=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:a}=t&&t.appContext.config||EMPTY_OBJ;if(t){let l=t.parent;const c=t.proxy,_=`https://vuejs.org/error-reference/#runtime-${r}`;for(;l;){const u=l.ec;if(u){for(let f=0;f<u.length;f++)if(u[f](e,c,_)===!1)return}l=l.parent}if(o){pauseTracking(),callWithErrorHandling(o,null,10,[e,c,_]),resetTracking();return}}logError(e,r,n,s,a)}function logError(e,t,r,s=!0,n=!1){if(n)throw e;console.error(e)}const queue=[];let flushIndex=-1;const pendingPostFlushCbs=[];let activePostFlushCbs=null,postFlushIndex=0;const resolvedPromise=Promise.resolve();let currentFlushPromise=null;function nextTick(e){const t=currentFlushPromise||resolvedPromise;return e?t.then(this?e.bind(this):e):t}function findInsertionIndex(e){let t=flushIndex+1,r=queue.length;for(;t<r;){const s=t+r>>>1,n=queue[s],o=getId(n);o<e||o===e&&n.flags&2?t=s+1:r=s}return t}function queueJob(e){if(!(e.flags&1)){const t=getId(e),r=queue[queue.length-1];!r||!(e.flags&2)&&t>=getId(r)?queue.push(e):queue.splice(findInsertionIndex(t),0,e),e.flags|=1,queueFlush()}}function queueFlush(){currentFlushPromise||(currentFlushPromise=resolvedPromise.then(flushJobs))}function queuePostFlushCb(e){isArray(e)?pendingPostFlushCbs.push(...e):activePostFlushCbs&&e.id===-1?activePostFlushCbs.splice(postFlushIndex+1,0,e):e.flags&1||(pendingPostFlushCbs.push(e),e.flags|=1),queueFlush()}function flushPreFlushCbs(e,t,r=flushIndex+1){for(;r<queue.length;r++){const s=queue[r];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;queue.splice(r,1),r--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function flushPostFlushCbs(e){if(pendingPostFlushCbs.length){const t=[...new Set(pendingPostFlushCbs)].sort((r,s)=>getId(r)-getId(s));if(pendingPostFlushCbs.length=0,activePostFlushCbs){activePostFlushCbs.push(...t);return}for(activePostFlushCbs=t,postFlushIndex=0;postFlushIndex<activePostFlushCbs.length;postFlushIndex++){const r=activePostFlushCbs[postFlushIndex];r.flags&4&&(r.flags&=-2),r.flags&8||r(),r.flags&=-2}activePostFlushCbs=null,postFlushIndex=0}}const getId=e=>e.id==null?e.flags&2?-1:1/0:e.id;function flushJobs(e){try{for(flushIndex=0;flushIndex<queue.length;flushIndex++){const t=queue[flushIndex];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),callWithErrorHandling(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;flushIndex<queue.length;flushIndex++){const t=queue[flushIndex];t&&(t.flags&=-2)}flushIndex=-1,queue.length=0,flushPostFlushCbs(),currentFlushPromise=null,(queue.length||pendingPostFlushCbs.length)&&flushJobs()}}let currentRenderingInstance=null,currentScopeId=null;function setCurrentRenderingInstance(e){const t=currentRenderingInstance;return currentRenderingInstance=e,currentScopeId=e&&e.type.__scopeId||null,t}function withCtx(e,t=currentRenderingInstance,r){if(!t||e._n)return e;const s=(...n)=>{s._d&&setBlockTracking(-1);const o=setCurrentRenderingInstance(t);let a;try{a=e(...n)}finally{setCurrentRenderingInstance(o),s._d&&setBlockTracking(1)}return a};return s._n=!0,s._c=!0,s._d=!0,s}function invokeDirectiveHook(e,t,r,s){const n=e.dirs,o=t&&t.dirs;for(let a=0;a<n.length;a++){const l=n[a];o&&(l.oldValue=o[a].value);let c=l.dir[s];c&&(pauseTracking(),callWithAsyncErrorHandling(c,r,8,[e.el,l,e,t]),resetTracking())}}const TeleportEndKey=Symbol("_vte"),isTeleport=e=>e.__isTeleport;function setTransitionHooks(e,t){e.shapeFlag&6&&e.component?(e.transition=t,setTransitionHooks(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function defineComponent(e,t){return isFunction(e)?extend({name:e.name},t,{setup:e}):e}function markAsyncBoundary(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function setRef(e,t,r,s,n=!1){if(isArray(e)){e.forEach((w,b)=>setRef(w,t&&(isArray(t)?t[b]:t),r,s,n));return}if(isAsyncWrapper(s)&&!n)return;const o=s.shapeFlag&4?getComponentPublicInstance(s.component):s.el,a=n?null:o,{i:l,r:c}=e,_=t&&t.r,u=l.refs===EMPTY_OBJ?l.refs={}:l.refs,f=l.setupState,m=toRaw(f),g=f===EMPTY_OBJ?()=>!1:w=>hasOwn(m,w);if(_!=null&&_!==c&&(isString(_)?(u[_]=null,g(_)&&(f[_]=null)):isRef(_)&&(_.value=null)),isFunction(c))callWithErrorHandling(c,l,12,[a,u]);else{const w=isString(c),b=isRef(c);if(w||b){const P=()=>{if(e.f){const I=w?g(c)?f[c]:u[c]:c.value;n?isArray(I)&&remove(I,o):isArray(I)?I.includes(o)||I.push(o):w?(u[c]=[o],g(c)&&(f[c]=u[c])):(c.value=[o],e.k&&(u[e.k]=c.value))}else w?(u[c]=a,g(c)&&(f[c]=a)):b&&(c.value=a,e.k&&(u[e.k]=a))};a?(P.id=-1,queuePostRenderEffect(P,r)):P()}}}getGlobalThis().requestIdleCallback;getGlobalThis().cancelIdleCallback;const isAsyncWrapper=e=>!!e.type.__asyncLoader,isKeepAlive=e=>e.type.__isKeepAlive;function onActivated(e,t){registerKeepAliveHook(e,"a",t)}function onDeactivated(e,t){registerKeepAliveHook(e,"da",t)}function registerKeepAliveHook(e,t,r=currentInstance){const s=e.__wdc||(e.__wdc=()=>{let n=r;for(;n;){if(n.isDeactivated)return;n=n.parent}return e()});if(injectHook(t,s,r),r){let n=r.parent;for(;n&&n.parent;)isKeepAlive(n.parent.vnode)&&injectToKeepAliveRoot(s,t,r,n),n=n.parent}}function injectToKeepAliveRoot(e,t,r,s){const n=injectHook(t,e,s,!0);onUnmounted(()=>{remove(s[t],n)},r)}function injectHook(e,t,r=currentInstance,s=!1){if(r){const n=r[e]||(r[e]=[]),o=t.__weh||(t.__weh=(...a)=>{pauseTracking();const l=setCurrentInstance(r),c=callWithAsyncErrorHandling(t,r,e,a);return l(),resetTracking(),c});return s?n.unshift(o):n.push(o),o}}const createHook=e=>(t,r=currentInstance)=>{(!isInSSRComponentSetup||e==="sp")&&injectHook(e,(...s)=>t(...s),r)},onBeforeMount=createHook("bm"),onMounted=createHook("m"),onBeforeUpdate=createHook("bu"),onUpdated=createHook("u"),onBeforeUnmount=createHook("bum"),onUnmounted=createHook("um"),onServerPrefetch=createHook("sp"),onRenderTriggered=createHook("rtg"),onRenderTracked=createHook("rtc");function onErrorCaptured(e,t=currentInstance){injectHook("ec",e,t)}const NULL_DYNAMIC_COMPONENT=Symbol.for("v-ndc"),getPublicInstance=e=>e?isStatefulComponent(e)?getComponentPublicInstance(e):getPublicInstance(e.parent):null,publicPropertiesMap=extend(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>getPublicInstance(e.parent),$root:e=>getPublicInstance(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>resolveMergedOptions(e),$forceUpdate:e=>e.f||(e.f=()=>{queueJob(e.update)}),$nextTick:e=>e.n||(e.n=nextTick.bind(e.proxy)),$watch:e=>instanceWatch.bind(e)}),hasSetupBinding=(e,t)=>e!==EMPTY_OBJ&&!e.__isScriptSetup&&hasOwn(e,t),PublicInstanceProxyHandlers={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:r,setupState:s,data:n,props:o,accessCache:a,type:l,appContext:c}=e;let _;if(t[0]!=="$"){const g=a[t];if(g!==void 0)switch(g){case 1:return s[t];case 2:return n[t];case 4:return r[t];case 3:return o[t]}else{if(hasSetupBinding(s,t))return a[t]=1,s[t];if(n!==EMPTY_OBJ&&hasOwn(n,t))return a[t]=2,n[t];if((_=e.propsOptions[0])&&hasOwn(_,t))return a[t]=3,o[t];if(r!==EMPTY_OBJ&&hasOwn(r,t))return a[t]=4,r[t];shouldCacheAccess&&(a[t]=0)}}const u=publicPropertiesMap[t];let f,m;if(u)return t==="$attrs"&&track(e.attrs,"get",""),u(e);if((f=l.__cssModules)&&(f=f[t]))return f;if(r!==EMPTY_OBJ&&hasOwn(r,t))return a[t]=4,r[t];if(m=c.config.globalProperties,hasOwn(m,t))return m[t]},set({_:e},t,r){const{data:s,setupState:n,ctx:o}=e;return hasSetupBinding(n,t)?(n[t]=r,!0):s!==EMPTY_OBJ&&hasOwn(s,t)?(s[t]=r,!0):hasOwn(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=r,!0)},has({_:{data:e,setupState:t,accessCache:r,ctx:s,appContext:n,propsOptions:o}},a){let l;return!!r[a]||e!==EMPTY_OBJ&&hasOwn(e,a)||hasSetupBinding(t,a)||(l=o[0])&&hasOwn(l,a)||hasOwn(s,a)||hasOwn(publicPropertiesMap,a)||hasOwn(n.config.globalProperties,a)},defineProperty(e,t,r){return r.get!=null?e._.accessCache[t]=0:hasOwn(r,"value")&&this.set(e,t,r.value,null),Reflect.defineProperty(e,t,r)}};function normalizePropsOrEmits(e){return isArray(e)?e.reduce((t,r)=>(t[r]=null,t),{}):e}function withAsyncContext(e){const t=getCurrentInstance();let r=e();return unsetCurrentInstance(),isPromise(r)&&(r=r.catch(s=>{throw setCurrentInstance(t),s})),[r,()=>setCurrentInstance(t)]}let shouldCacheAccess=!0;function applyOptions(e){const t=resolveMergedOptions(e),r=e.proxy,s=e.ctx;shouldCacheAccess=!1,t.beforeCreate&&callHook(t.beforeCreate,e,"bc");const{data:n,computed:o,methods:a,watch:l,provide:c,inject:_,created:u,beforeMount:f,mounted:m,beforeUpdate:g,updated:w,activated:b,deactivated:P,beforeDestroy:I,beforeUnmount:E,destroyed:O,unmounted:R,render:L,renderTracked:H,renderTriggered:k,errorCaptured:W,serverPrefetch:K,expose:z,inheritAttrs:te,components:X,directives:le,filters:J}=t;if(_&&resolveInjections(_,s,null),a)for(const U in a){const V=a[U];isFunction(V)&&(s[U]=V.bind(r))}if(n){const U=n.call(r,r);isObject(U)&&(e.data=reactive(U))}if(shouldCacheAccess=!0,o)for(const U in o){const V=o[U],se=isFunction(V)?V.bind(r,r):isFunction(V.get)?V.get.bind(r,r):NOOP,Y=!isFunction(V)&&isFunction(V.set)?V.set.bind(r):NOOP,re=computed({get:se,set:Y});Object.defineProperty(s,U,{enumerable:!0,configurable:!0,get:()=>re.value,set:ae=>re.value=ae})}if(l)for(const U in l)createWatcher(l[U],s,r,U);if(c){const U=isFunction(c)?c.call(r):c;Reflect.ownKeys(U).forEach(V=>{provide(V,U[V])})}u&&callHook(u,e,"c");function Z(U,V){isArray(V)?V.forEach(se=>U(se.bind(r))):V&&U(V.bind(r))}if(Z(onBeforeMount,f),Z(onMounted,m),Z(onBeforeUpdate,g),Z(onUpdated,w),Z(onActivated,b),Z(onDeactivated,P),Z(onErrorCaptured,W),Z(onRenderTracked,H),Z(onRenderTriggered,k),Z(onBeforeUnmount,E),Z(onUnmounted,R),Z(onServerPrefetch,K),isArray(z))if(z.length){const U=e.exposed||(e.exposed={});z.forEach(V=>{Object.defineProperty(U,V,{get:()=>r[V],set:se=>r[V]=se})})}else e.exposed||(e.exposed={});L&&e.render===NOOP&&(e.render=L),te!=null&&(e.inheritAttrs=te),X&&(e.components=X),le&&(e.directives=le),K&&markAsyncBoundary(e)}function resolveInjections(e,t,r=NOOP){isArray(e)&&(e=normalizeInject(e));for(const s in e){const n=e[s];let o;isObject(n)?"default"in n?o=inject(n.from||s,n.default,!0):o=inject(n.from||s):o=inject(n),isRef(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:a=>o.value=a}):t[s]=o}}function callHook(e,t,r){callWithAsyncErrorHandling(isArray(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,r)}function createWatcher(e,t,r,s){let n=s.includes(".")?createPathGetter(r,s):()=>r[s];if(isString(e)){const o=t[e];isFunction(o)&&watch(n,o)}else if(isFunction(e))watch(n,e.bind(r));else if(isObject(e))if(isArray(e))e.forEach(o=>createWatcher(o,t,r,s));else{const o=isFunction(e.handler)?e.handler.bind(r):t[e.handler];isFunction(o)&&watch(n,o,e)}}function resolveMergedOptions(e){const t=e.type,{mixins:r,extends:s}=t,{mixins:n,optionsCache:o,config:{optionMergeStrategies:a}}=e.appContext,l=o.get(t);let c;return l?c=l:!n.length&&!r&&!s?c=t:(c={},n.length&&n.forEach(_=>mergeOptions(c,_,a,!0)),mergeOptions(c,t,a)),isObject(t)&&o.set(t,c),c}function mergeOptions(e,t,r,s=!1){const{mixins:n,extends:o}=t;o&&mergeOptions(e,o,r,!0),n&&n.forEach(a=>mergeOptions(e,a,r,!0));for(const a in t)if(!(s&&a==="expose")){const l=internalOptionMergeStrats[a]||r&&r[a];e[a]=l?l(e[a],t[a]):t[a]}return e}const internalOptionMergeStrats={data:mergeDataFn,props:mergeEmitsOrPropsOptions,emits:mergeEmitsOrPropsOptions,methods:mergeObjectOptions,computed:mergeObjectOptions,beforeCreate:mergeAsArray,created:mergeAsArray,beforeMount:mergeAsArray,mounted:mergeAsArray,beforeUpdate:mergeAsArray,updated:mergeAsArray,beforeDestroy:mergeAsArray,beforeUnmount:mergeAsArray,destroyed:mergeAsArray,unmounted:mergeAsArray,activated:mergeAsArray,deactivated:mergeAsArray,errorCaptured:mergeAsArray,serverPrefetch:mergeAsArray,components:mergeObjectOptions,directives:mergeObjectOptions,watch:mergeWatchOptions,provide:mergeDataFn,inject:mergeInject};function mergeDataFn(e,t){return t?e?function(){return extend(isFunction(e)?e.call(this,this):e,isFunction(t)?t.call(this,this):t)}:t:e}function mergeInject(e,t){return mergeObjectOptions(normalizeInject(e),normalizeInject(t))}function normalizeInject(e){if(isArray(e)){const t={};for(let r=0;r<e.length;r++)t[e[r]]=e[r];return t}return e}function mergeAsArray(e,t){return e?[...new Set([].concat(e,t))]:t}function mergeObjectOptions(e,t){return e?extend(Object.create(null),e,t):t}function mergeEmitsOrPropsOptions(e,t){return e?isArray(e)&&isArray(t)?[...new Set([...e,...t])]:extend(Object.create(null),normalizePropsOrEmits(e),normalizePropsOrEmits(t??{})):t}function mergeWatchOptions(e,t){if(!e)return t;if(!t)return e;const r=extend(Object.create(null),e);for(const s in t)r[s]=mergeAsArray(e[s],t[s]);return r}function createAppContext(){return{app:null,config:{isNativeTag:NO,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let uid$1=0;function createAppAPI(e,t){return function(s,n=null){isFunction(s)||(s=extend({},s)),n!=null&&!isObject(n)&&(n=null);const o=createAppContext(),a=new WeakSet,l=[];let c=!1;const _=o.app={_uid:uid$1++,_component:s,_props:n,_container:null,_context:o,_instance:null,version,get config(){return o.config},set config(u){},use(u,...f){return a.has(u)||(u&&isFunction(u.install)?(a.add(u),u.install(_,...f)):isFunction(u)&&(a.add(u),u(_,...f))),_},mixin(u){return o.mixins.includes(u)||o.mixins.push(u),_},component(u,f){return f?(o.components[u]=f,_):o.components[u]},directive(u,f){return f?(o.directives[u]=f,_):o.directives[u]},mount(u,f,m){if(!c){const g=_._ceVNode||createVNode(s,n);return g.appContext=o,m===!0?m="svg":m===!1&&(m=void 0),f&&t?t(g,u):e(g,u,m),c=!0,_._container=u,u.__vue_app__=_,getComponentPublicInstance(g.component)}},onUnmount(u){l.push(u)},unmount(){c&&(callWithAsyncErrorHandling(l,_._instance,16),e(null,_._container),delete _._container.__vue_app__)},provide(u,f){return o.provides[u]=f,_},runWithContext(u){const f=currentApp;currentApp=_;try{return u()}finally{currentApp=f}}};return _}}let currentApp=null;function provide(e,t){if(currentInstance){let r=currentInstance.provides;const s=currentInstance.parent&&currentInstance.parent.provides;s===r&&(r=currentInstance.provides=Object.create(s)),r[e]=t}}function inject(e,t,r=!1){const s=currentInstance||currentRenderingInstance;if(s||currentApp){const n=currentApp?currentApp._context.provides:s?s.parent==null?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(n&&e in n)return n[e];if(arguments.length>1)return r&&isFunction(t)?t.call(s&&s.proxy):t}}const internalObjectProto={},createInternalObject=()=>Object.create(internalObjectProto),isInternalObject=e=>Object.getPrototypeOf(e)===internalObjectProto;function initProps(e,t,r,s=!1){const n={},o=createInternalObject();e.propsDefaults=Object.create(null),setFullProps(e,t,n,o);for(const a in e.propsOptions[0])a in n||(n[a]=void 0);r?e.props=s?n:shallowReactive(n):e.type.props?e.props=n:e.props=o,e.attrs=o}function updateProps(e,t,r,s){const{props:n,attrs:o,vnode:{patchFlag:a}}=e,l=toRaw(n),[c]=e.propsOptions;let _=!1;if((s||a>0)&&!(a&16)){if(a&8){const u=e.vnode.dynamicProps;for(let f=0;f<u.length;f++){let m=u[f];if(isEmitListener(e.emitsOptions,m))continue;const g=t[m];if(c)if(hasOwn(o,m))g!==o[m]&&(o[m]=g,_=!0);else{const w=camelize(m);n[w]=resolvePropValue(c,l,w,g,e,!1)}else g!==o[m]&&(o[m]=g,_=!0)}}}else{setFullProps(e,t,n,o)&&(_=!0);let u;for(const f in l)(!t||!hasOwn(t,f)&&((u=hyphenate(f))===f||!hasOwn(t,u)))&&(c?r&&(r[f]!==void 0||r[u]!==void 0)&&(n[f]=resolvePropValue(c,l,f,void 0,e,!0)):delete n[f]);if(o!==l)for(const f in o)(!t||!hasOwn(t,f))&&(delete o[f],_=!0)}_&&trigger(e.attrs,"set","")}function setFullProps(e,t,r,s){const[n,o]=e.propsOptions;let a=!1,l;if(t)for(let c in t){if(isReservedProp(c))continue;const _=t[c];let u;n&&hasOwn(n,u=camelize(c))?!o||!o.includes(u)?r[u]=_:(l||(l={}))[u]=_:isEmitListener(e.emitsOptions,c)||(!(c in s)||_!==s[c])&&(s[c]=_,a=!0)}if(o){const c=toRaw(r),_=l||EMPTY_OBJ;for(let u=0;u<o.length;u++){const f=o[u];r[f]=resolvePropValue(n,c,f,_[f],e,!hasOwn(_,f))}}return a}function resolvePropValue(e,t,r,s,n,o){const a=e[r];if(a!=null){const l=hasOwn(a,"default");if(l&&s===void 0){const c=a.default;if(a.type!==Function&&!a.skipFactory&&isFunction(c)){const{propsDefaults:_}=n;if(r in _)s=_[r];else{const u=setCurrentInstance(n);s=_[r]=c.call(null,t),u()}}else s=c;n.ce&&n.ce._setProp(r,s)}a[0]&&(o&&!l?s=!1:a[1]&&(s===""||s===hyphenate(r))&&(s=!0))}return s}const mixinPropsCache=new WeakMap;function normalizePropsOptions(e,t,r=!1){const s=r?mixinPropsCache:t.propsCache,n=s.get(e);if(n)return n;const o=e.props,a={},l=[];let c=!1;if(!isFunction(e)){const u=f=>{c=!0;const[m,g]=normalizePropsOptions(f,t,!0);extend(a,m),g&&l.push(...g)};!r&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!o&&!c)return isObject(e)&&s.set(e,EMPTY_ARR),EMPTY_ARR;if(isArray(o))for(let u=0;u<o.length;u++){const f=camelize(o[u]);validatePropName(f)&&(a[f]=EMPTY_OBJ)}else if(o)for(const u in o){const f=camelize(u);if(validatePropName(f)){const m=o[u],g=a[f]=isArray(m)||isFunction(m)?{type:m}:extend({},m),w=g.type;let b=!1,P=!0;if(isArray(w))for(let I=0;I<w.length;++I){const E=w[I],O=isFunction(E)&&E.name;if(O==="Boolean"){b=!0;break}else O==="String"&&(P=!1)}else b=isFunction(w)&&w.name==="Boolean";g[0]=b,g[1]=P,(b||hasOwn(g,"default"))&&l.push(f)}}const _=[a,l];return isObject(e)&&s.set(e,_),_}function validatePropName(e){return e[0]!=="$"&&!isReservedProp(e)}const isInternalKey=e=>e[0]==="_"||e==="$stable",normalizeSlotValue=e=>isArray(e)?e.map(normalizeVNode):[normalizeVNode(e)],normalizeSlot=(e,t,r)=>{if(t._n)return t;const s=withCtx((...n)=>normalizeSlotValue(t(...n)),r);return s._c=!1,s},normalizeObjectSlots=(e,t,r)=>{const s=e._ctx;for(const n in e){if(isInternalKey(n))continue;const o=e[n];if(isFunction(o))t[n]=normalizeSlot(n,o,s);else if(o!=null){const a=normalizeSlotValue(o);t[n]=()=>a}}},normalizeVNodeSlots=(e,t)=>{const r=normalizeSlotValue(t);e.slots.default=()=>r},assignSlots=(e,t,r)=>{for(const s in t)(r||s!=="_")&&(e[s]=t[s])},initSlots=(e,t,r)=>{const s=e.slots=createInternalObject();if(e.vnode.shapeFlag&32){const n=t._;n?(assignSlots(s,t,r),r&&def(s,"_",n,!0)):normalizeObjectSlots(t,s)}else t&&normalizeVNodeSlots(e,t)},updateSlots=(e,t,r)=>{const{vnode:s,slots:n}=e;let o=!0,a=EMPTY_OBJ;if(s.shapeFlag&32){const l=t._;l?r&&l===1?o=!1:assignSlots(n,t,r):(o=!t.$stable,normalizeObjectSlots(t,n)),a=t}else t&&(normalizeVNodeSlots(e,t),a={default:1});if(o)for(const l in n)!isInternalKey(l)&&a[l]==null&&delete n[l]},queuePostRenderEffect=queueEffectWithSuspense;function createRenderer(e){return baseCreateRenderer(e)}function baseCreateRenderer(e,t){const r=getGlobalThis();r.__VUE__=!0;const{insert:s,remove:n,patchProp:o,createElement:a,createText:l,createComment:c,setText:_,setElementText:u,parentNode:f,nextSibling:m,setScopeId:g=NOOP,insertStaticContent:w}=e,b=(d,h,y,x=null,v=null,T=null,A=void 0,M=null,F=!!h.dynamicChildren)=>{if(d===h)return;d&&!isSameVNodeType(d,h)&&(x=he(d),ae(d,v,T,!0),d=null),h.patchFlag===-2&&(F=!1,h.dynamicChildren=null);const{type:S,ref:D,shapeFlag:N}=h;switch(S){case Text:P(d,h,y,x);break;case Comment:I(d,h,y,x);break;case Static:d==null&&E(h,y,x,A);break;case Fragment:X(d,h,y,x,v,T,A,M,F);break;default:N&1?L(d,h,y,x,v,T,A,M,F):N&6?le(d,h,y,x,v,T,A,M,F):(N&64||N&128)&&S.process(d,h,y,x,v,T,A,M,F,fe)}D!=null&&v&&setRef(D,d&&d.ref,T,h||d,!h)},P=(d,h,y,x)=>{if(d==null)s(h.el=l(h.children),y,x);else{const v=h.el=d.el;h.children!==d.children&&_(v,h.children)}},I=(d,h,y,x)=>{d==null?s(h.el=c(h.children||""),y,x):h.el=d.el},E=(d,h,y,x)=>{[d.el,d.anchor]=w(d.children,h,y,x,d.el,d.anchor)},O=({el:d,anchor:h},y,x)=>{let v;for(;d&&d!==h;)v=m(d),s(d,y,x),d=v;s(h,y,x)},R=({el:d,anchor:h})=>{let y;for(;d&&d!==h;)y=m(d),n(d),d=y;n(h)},L=(d,h,y,x,v,T,A,M,F)=>{h.type==="svg"?A="svg":h.type==="math"&&(A="mathml"),d==null?H(h,y,x,v,T,A,M,F):K(d,h,v,T,A,M,F)},H=(d,h,y,x,v,T,A,M)=>{let F,S;const{props:D,shapeFlag:N,transition:j,dirs:B}=d;if(F=d.el=a(d.type,T,D&&D.is,D),N&8?u(F,d.children):N&16&&W(d.children,F,null,x,v,resolveChildrenNamespace(d,T),A,M),B&&invokeDirectiveHook(d,null,x,"created"),k(F,d,d.scopeId,A,x),D){for(const G in D)G!=="value"&&!isReservedProp(G)&&o(F,G,null,D[G],T,x);"value"in D&&o(F,"value",null,D.value,T),(S=D.onVnodeBeforeMount)&&invokeVNodeHook(S,x,d)}B&&invokeDirectiveHook(d,null,x,"beforeMount");const $=needTransition(v,j);$&&j.beforeEnter(F),s(F,h,y),((S=D&&D.onVnodeMounted)||$||B)&&queuePostRenderEffect(()=>{S&&invokeVNodeHook(S,x,d),$&&j.enter(F),B&&invokeDirectiveHook(d,null,x,"mounted")},v)},k=(d,h,y,x,v)=>{if(y&&g(d,y),x)for(let T=0;T<x.length;T++)g(d,x[T]);if(v){let T=v.subTree;if(h===T||isSuspense(T.type)&&(T.ssContent===h||T.ssFallback===h)){const A=v.vnode;k(d,A,A.scopeId,A.slotScopeIds,v.parent)}}},W=(d,h,y,x,v,T,A,M,F=0)=>{for(let S=F;S<d.length;S++){const D=d[S]=M?cloneIfMounted(d[S]):normalizeVNode(d[S]);b(null,D,h,y,x,v,T,A,M)}},K=(d,h,y,x,v,T,A)=>{const M=h.el=d.el;let{patchFlag:F,dynamicChildren:S,dirs:D}=h;F|=d.patchFlag&16;const N=d.props||EMPTY_OBJ,j=h.props||EMPTY_OBJ;let B;if(y&&toggleRecurse(y,!1),(B=j.onVnodeBeforeUpdate)&&invokeVNodeHook(B,y,h,d),D&&invokeDirectiveHook(h,d,y,"beforeUpdate"),y&&toggleRecurse(y,!0),(N.innerHTML&&j.innerHTML==null||N.textContent&&j.textContent==null)&&u(M,""),S?z(d.dynamicChildren,S,M,y,x,resolveChildrenNamespace(h,v),T):A||V(d,h,M,null,y,x,resolveChildrenNamespace(h,v),T,!1),F>0){if(F&16)te(M,N,j,y,v);else if(F&2&&N.class!==j.class&&o(M,"class",null,j.class,v),F&4&&o(M,"style",N.style,j.style,v),F&8){const $=h.dynamicProps;for(let G=0;G<$.length;G++){const q=$[G],ne=N[q],Q=j[q];(Q!==ne||q==="value")&&o(M,q,ne,Q,v,y)}}F&1&&d.children!==h.children&&u(M,h.children)}else!A&&S==null&&te(M,N,j,y,v);((B=j.onVnodeUpdated)||D)&&queuePostRenderEffect(()=>{B&&invokeVNodeHook(B,y,h,d),D&&invokeDirectiveHook(h,d,y,"updated")},x)},z=(d,h,y,x,v,T,A)=>{for(let M=0;M<h.length;M++){const F=d[M],S=h[M],D=F.el&&(F.type===Fragment||!isSameVNodeType(F,S)||F.shapeFlag&70)?f(F.el):y;b(F,S,D,null,x,v,T,A,!0)}},te=(d,h,y,x,v)=>{if(h!==y){if(h!==EMPTY_OBJ)for(const T in h)!isReservedProp(T)&&!(T in y)&&o(d,T,h[T],null,v,x);for(const T in y){if(isReservedProp(T))continue;const A=y[T],M=h[T];A!==M&&T!=="value"&&o(d,T,M,A,v,x)}"value"in y&&o(d,"value",h.value,y.value,v)}},X=(d,h,y,x,v,T,A,M,F)=>{const S=h.el=d?d.el:l(""),D=h.anchor=d?d.anchor:l("");let{patchFlag:N,dynamicChildren:j,slotScopeIds:B}=h;B&&(M=M?M.concat(B):B),d==null?(s(S,y,x),s(D,y,x),W(h.children||[],y,D,v,T,A,M,F)):N>0&&N&64&&j&&d.dynamicChildren?(z(d.dynamicChildren,j,y,v,T,A,M),(h.key!=null||v&&h===v.subTree)&&traverseStaticChildren(d,h,!0)):V(d,h,y,D,v,T,A,M,F)},le=(d,h,y,x,v,T,A,M,F)=>{h.slotScopeIds=M,d==null?h.shapeFlag&512?v.ctx.activate(h,y,x,A,F):J(h,y,x,v,T,A,F):ee(d,h,F)},J=(d,h,y,x,v,T,A)=>{const M=d.component=createComponentInstance(d,x,v);if(isKeepAlive(d)&&(M.ctx.renderer=fe),setupComponent(M,!1,A),M.asyncDep){if(v&&v.registerDep(M,Z,A),!d.el){const F=M.subTree=createVNode(Comment);I(null,F,h,y)}}else Z(M,d,h,y,v,T,A)},ee=(d,h,y)=>{const x=h.component=d.component;if(shouldUpdateComponent(d,h,y))if(x.asyncDep&&!x.asyncResolved){U(x,h,y);return}else x.next=h,x.update();else h.el=d.el,x.vnode=h},Z=(d,h,y,x,v,T,A)=>{const M=()=>{if(d.isMounted){let{next:N,bu:j,u:B,parent:$,vnode:G}=d;{const ie=locateNonHydratedAsyncRoot(d);if(ie){N&&(N.el=G.el,U(d,N,A)),ie.asyncDep.then(()=>{d.isUnmounted||M()});return}}let q=N,ne;toggleRecurse(d,!1),N?(N.el=G.el,U(d,N,A)):N=G,j&&invokeArrayFns(j),(ne=N.props&&N.props.onVnodeBeforeUpdate)&&invokeVNodeHook(ne,$,N,G),toggleRecurse(d,!0);const Q=renderComponentRoot(d),ce=d.subTree;d.subTree=Q,b(ce,Q,f(ce.el),he(ce),d,v,T),N.el=Q.el,q===null&&updateHOCHostEl(d,Q.el),B&&queuePostRenderEffect(B,v),(ne=N.props&&N.props.onVnodeUpdated)&&queuePostRenderEffect(()=>invokeVNodeHook(ne,$,N,G),v)}else{let N;const{el:j,props:B}=h,{bm:$,m:G,parent:q,root:ne,type:Q}=d,ce=isAsyncWrapper(h);if(toggleRecurse(d,!1),$&&invokeArrayFns($),!ce&&(N=B&&B.onVnodeBeforeMount)&&invokeVNodeHook(N,q,h),toggleRecurse(d,!0),j&&we){const ie=()=>{d.subTree=renderComponentRoot(d),we(j,d.subTree,d,v,null)};ce&&Q.__asyncHydrate?Q.__asyncHydrate(j,d,ie):ie()}else{ne.ce&&ne.ce._injectChildStyle(Q);const ie=d.subTree=renderComponentRoot(d);b(null,ie,y,x,d,v,T),h.el=ie.el}if(G&&queuePostRenderEffect(G,v),!ce&&(N=B&&B.onVnodeMounted)){const ie=h;queuePostRenderEffect(()=>invokeVNodeHook(N,q,ie),v)}(h.shapeFlag&256||q&&isAsyncWrapper(q.vnode)&&q.vnode.shapeFlag&256)&&d.a&&queuePostRenderEffect(d.a,v),d.isMounted=!0,h=y=x=null}};d.scope.on();const F=d.effect=new ReactiveEffect(M);d.scope.off();const S=d.update=F.run.bind(F),D=d.job=F.runIfDirty.bind(F);D.i=d,D.id=d.uid,F.scheduler=()=>queueJob(D),toggleRecurse(d,!0),S()},U=(d,h,y)=>{h.component=d;const x=d.vnode.props;d.vnode=h,d.next=null,updateProps(d,h.props,x,y),updateSlots(d,h.children,y),pauseTracking(),flushPreFlushCbs(d),resetTracking()},V=(d,h,y,x,v,T,A,M,F=!1)=>{const S=d&&d.children,D=d?d.shapeFlag:0,N=h.children,{patchFlag:j,shapeFlag:B}=h;if(j>0){if(j&128){Y(S,N,y,x,v,T,A,M,F);return}else if(j&256){se(S,N,y,x,v,T,A,M,F);return}}B&8?(D&16&&de(S,v,T),N!==S&&u(y,N)):D&16?B&16?Y(S,N,y,x,v,T,A,M,F):de(S,v,T,!0):(D&8&&u(y,""),B&16&&W(N,y,x,v,T,A,M,F))},se=(d,h,y,x,v,T,A,M,F)=>{d=d||EMPTY_ARR,h=h||EMPTY_ARR;const S=d.length,D=h.length,N=Math.min(S,D);let j;for(j=0;j<N;j++){const B=h[j]=F?cloneIfMounted(h[j]):normalizeVNode(h[j]);b(d[j],B,y,null,v,T,A,M,F)}S>D?de(d,v,T,!0,!1,N):W(h,y,x,v,T,A,M,F,N)},Y=(d,h,y,x,v,T,A,M,F)=>{let S=0;const D=h.length;let N=d.length-1,j=D-1;for(;S<=N&&S<=j;){const B=d[S],$=h[S]=F?cloneIfMounted(h[S]):normalizeVNode(h[S]);if(isSameVNodeType(B,$))b(B,$,y,null,v,T,A,M,F);else break;S++}for(;S<=N&&S<=j;){const B=d[N],$=h[j]=F?cloneIfMounted(h[j]):normalizeVNode(h[j]);if(isSameVNodeType(B,$))b(B,$,y,null,v,T,A,M,F);else break;N--,j--}if(S>N){if(S<=j){const B=j+1,$=B<D?h[B].el:x;for(;S<=j;)b(null,h[S]=F?cloneIfMounted(h[S]):normalizeVNode(h[S]),y,$,v,T,A,M,F),S++}}else if(S>j)for(;S<=N;)ae(d[S],v,T,!0),S++;else{const B=S,$=S,G=new Map;for(S=$;S<=j;S++){const oe=h[S]=F?cloneIfMounted(h[S]):normalizeVNode(h[S]);oe.key!=null&&G.set(oe.key,S)}let q,ne=0;const Q=j-$+1;let ce=!1,ie=0;const pe=new Array(Q);for(S=0;S<Q;S++)pe[S]=0;for(S=B;S<=N;S++){const oe=d[S];if(ne>=Q){ae(oe,v,T,!0);continue}let ue;if(oe.key!=null)ue=G.get(oe.key);else for(q=$;q<=j;q++)if(pe[q-$]===0&&isSameVNodeType(oe,h[q])){ue=q;break}ue===void 0?ae(oe,v,T,!0):(pe[ue-$]=S+1,ue>=ie?ie=ue:ce=!0,b(oe,h[ue],y,null,v,T,A,M,F),ne++)}const Ee=ce?getSequence(pe):EMPTY_ARR;for(q=Ee.length-1,S=Q-1;S>=0;S--){const oe=$+S,ue=h[oe],ve=oe+1<D?h[oe+1].el:x;pe[S]===0?b(null,ue,y,ve,v,T,A,M,F):ce&&(q<0||S!==Ee[q]?re(ue,y,ve,2):q--)}}},re=(d,h,y,x,v=null)=>{const{el:T,type:A,transition:M,children:F,shapeFlag:S}=d;if(S&6){re(d.component.subTree,h,y,x);return}if(S&128){d.suspense.move(h,y,x);return}if(S&64){A.move(d,h,y,fe);return}if(A===Fragment){s(T,h,y);for(let N=0;N<F.length;N++)re(F[N],h,y,x);s(d.anchor,h,y);return}if(A===Static){O(d,h,y);return}if(x!==2&&S&1&&M)if(x===0)M.beforeEnter(T),s(T,h,y),queuePostRenderEffect(()=>M.enter(T),v);else{const{leave:N,delayLeave:j,afterLeave:B}=M,$=()=>s(T,h,y),G=()=>{N(T,()=>{$(),B&&B()})};j?j(T,$,G):G()}else s(T,h,y)},ae=(d,h,y,x=!1,v=!1)=>{const{type:T,props:A,ref:M,children:F,dynamicChildren:S,shapeFlag:D,patchFlag:N,dirs:j,cacheIndex:B}=d;if(N===-2&&(v=!1),M!=null&&setRef(M,null,y,d,!0),B!=null&&(h.renderCache[B]=void 0),D&256){h.ctx.deactivate(d);return}const $=D&1&&j,G=!isAsyncWrapper(d);let q;if(G&&(q=A&&A.onVnodeBeforeUnmount)&&invokeVNodeHook(q,h,d),D&6)xe(d.component,y,x);else{if(D&128){d.suspense.unmount(y,x);return}$&&invokeDirectiveHook(d,null,h,"beforeUnmount"),D&64?d.type.remove(d,h,y,fe,x):S&&!S.hasOnce&&(T!==Fragment||N>0&&N&64)?de(S,h,y,!1,!0):(T===Fragment&&N&384||!v&&D&16)&&de(F,h,y),x&&ge(d)}(G&&(q=A&&A.onVnodeUnmounted)||$)&&queuePostRenderEffect(()=>{q&&invokeVNodeHook(q,h,d),$&&invokeDirectiveHook(d,null,h,"unmounted")},y)},ge=d=>{const{type:h,el:y,anchor:x,transition:v}=d;if(h===Fragment){Te(y,x);return}if(h===Static){R(d);return}const T=()=>{n(y),v&&!v.persisted&&v.afterLeave&&v.afterLeave()};if(d.shapeFlag&1&&v&&!v.persisted){const{leave:A,delayLeave:M}=v,F=()=>A(y,T);M?M(d.el,T,F):F()}else T()},Te=(d,h)=>{let y;for(;d!==h;)y=m(d),n(d),d=y;n(h)},xe=(d,h,y)=>{const{bum:x,scope:v,job:T,subTree:A,um:M,m:F,a:S}=d;invalidateMount(F),invalidateMount(S),x&&invokeArrayFns(x),v.stop(),T&&(T.flags|=8,ae(A,d,h,y)),M&&queuePostRenderEffect(M,h),queuePostRenderEffect(()=>{d.isUnmounted=!0},h),h&&h.pendingBranch&&!h.isUnmounted&&d.asyncDep&&!d.asyncResolved&&d.suspenseId===h.pendingId&&(h.deps--,h.deps===0&&h.resolve())},de=(d,h,y,x=!1,v=!1,T=0)=>{for(let A=T;A<d.length;A++)ae(d[A],h,y,x,v)},he=d=>{if(d.shapeFlag&6)return he(d.component.subTree);if(d.shapeFlag&128)return d.suspense.next();const h=m(d.anchor||d.el),y=h&&h[TeleportEndKey];return y?m(y):h};let me=!1;const ye=(d,h,y)=>{d==null?h._vnode&&ae(h._vnode,null,null,!0):b(h._vnode||null,d,h,null,null,null,y),h._vnode=d,me||(me=!0,flushPreFlushCbs(),flushPostFlushCbs(),me=!1)},fe={p:b,um:ae,m:re,r:ge,mt:J,mc:W,pc:V,pbc:z,n:he,o:e};let be,we;return{render:ye,hydrate:be,createApp:createAppAPI(ye,be)}}function resolveChildrenNamespace({type:e,props:t},r){return r==="svg"&&e==="foreignObject"||r==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:r}function toggleRecurse({effect:e,job:t},r){r?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function needTransition(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function traverseStaticChildren(e,t,r=!1){const s=e.children,n=t.children;if(isArray(s)&&isArray(n))for(let o=0;o<s.length;o++){const a=s[o];let l=n[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=n[o]=cloneIfMounted(n[o]),l.el=a.el),!r&&l.patchFlag!==-2&&traverseStaticChildren(a,l)),l.type===Text&&(l.el=a.el)}}function getSequence(e){const t=e.slice(),r=[0];let s,n,o,a,l;const c=e.length;for(s=0;s<c;s++){const _=e[s];if(_!==0){if(n=r[r.length-1],e[n]<_){t[s]=n,r.push(s);continue}for(o=0,a=r.length-1;o<a;)l=o+a>>1,e[r[l]]<_?o=l+1:a=l;_<e[r[o]]&&(o>0&&(t[s]=r[o-1]),r[o]=s)}}for(o=r.length,a=r[o-1];o-- >0;)r[o]=a,a=t[a];return r}function locateNonHydratedAsyncRoot(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:locateNonHydratedAsyncRoot(t)}function invalidateMount(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const ssrContextKey=Symbol.for("v-scx"),useSSRContext=()=>inject(ssrContextKey);function watch(e,t,r){return doWatch(e,t,r)}function doWatch(e,t,r=EMPTY_OBJ){const{immediate:s,deep:n,flush:o,once:a}=r,l=extend({},r),c=t&&s||!t&&o!=="post";let _;if(isInSSRComponentSetup){if(o==="sync"){const g=useSSRContext();_=g.__watcherHandles||(g.__watcherHandles=[])}else if(!c){const g=()=>{};return g.stop=NOOP,g.resume=NOOP,g.pause=NOOP,g}}const u=currentInstance;l.call=(g,w,b)=>callWithAsyncErrorHandling(g,u,w,b);let f=!1;o==="post"?l.scheduler=g=>{queuePostRenderEffect(g,u&&u.suspense)}:o!=="sync"&&(f=!0,l.scheduler=(g,w)=>{w?g():queueJob(g)}),l.augmentJob=g=>{t&&(g.flags|=4),f&&(g.flags|=2,u&&(g.id=u.uid,g.i=u))};const m=watch$1(e,t,l);return isInSSRComponentSetup&&(_?_.push(m):c&&m()),m}function instanceWatch(e,t,r){const s=this.proxy,n=isString(e)?e.includes(".")?createPathGetter(s,e):()=>s[e]:e.bind(s,s);let o;isFunction(t)?o=t:(o=t.handler,r=t);const a=setCurrentInstance(this),l=doWatch(n,o.bind(s),r);return a(),l}function createPathGetter(e,t){const r=t.split(".");return()=>{let s=e;for(let n=0;n<r.length&&s;n++)s=s[r[n]];return s}}const getModelModifiers=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${camelize(t)}Modifiers`]||e[`${hyphenate(t)}Modifiers`];function emit(e,t,...r){if(e.isUnmounted)return;const s=e.vnode.props||EMPTY_OBJ;let n=r;const o=t.startsWith("update:"),a=o&&getModelModifiers(s,t.slice(7));a&&(a.trim&&(n=r.map(u=>isString(u)?u.trim():u)),a.number&&(n=r.map(looseToNumber)));let l,c=s[l=toHandlerKey(t)]||s[l=toHandlerKey(camelize(t))];!c&&o&&(c=s[l=toHandlerKey(hyphenate(t))]),c&&callWithAsyncErrorHandling(c,e,6,n);const _=s[l+"Once"];if(_){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,callWithAsyncErrorHandling(_,e,6,n)}}function normalizeEmitsOptions(e,t,r=!1){const s=t.emitsCache,n=s.get(e);if(n!==void 0)return n;const o=e.emits;let a={},l=!1;if(!isFunction(e)){const c=_=>{const u=normalizeEmitsOptions(_,t,!0);u&&(l=!0,extend(a,u))};!r&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!o&&!l?(isObject(e)&&s.set(e,null),null):(isArray(o)?o.forEach(c=>a[c]=null):extend(a,o),isObject(e)&&s.set(e,a),a)}function isEmitListener(e,t){return!e||!isOn(t)?!1:(t=t.slice(2).replace(/Once$/,""),hasOwn(e,t[0].toLowerCase()+t.slice(1))||hasOwn(e,hyphenate(t))||hasOwn(e,t))}function markAttrsAccessed(){}function renderComponentRoot(e){const{type:t,vnode:r,proxy:s,withProxy:n,propsOptions:[o],slots:a,attrs:l,emit:c,render:_,renderCache:u,props:f,data:m,setupState:g,ctx:w,inheritAttrs:b}=e,P=setCurrentRenderingInstance(e);let I,E;try{if(r.shapeFlag&4){const R=n||s,L=R;I=normalizeVNode(_.call(L,R,u,f,g,m,w)),E=l}else{const R=t;I=normalizeVNode(R.length>1?R(f,{attrs:l,slots:a,emit:c}):R(f,null)),E=t.props?l:getFunctionalFallthrough(l)}}catch(R){handleError(R,e,1),I=createVNode(Comment)}let O=I;if(E&&b!==!1){const R=Object.keys(E),{shapeFlag:L}=O;R.length&&L&7&&(o&&R.some(isModelListener)&&(E=filterModelListeners(E,o)),O=cloneVNode(O,E,!1,!0))}return r.dirs&&(O=cloneVNode(O,null,!1,!0),O.dirs=O.dirs?O.dirs.concat(r.dirs):r.dirs),r.transition&&setTransitionHooks(O,r.transition),I=O,setCurrentRenderingInstance(P),I}const getFunctionalFallthrough=e=>{let t;for(const r in e)(r==="class"||r==="style"||isOn(r))&&((t||(t={}))[r]=e[r]);return t},filterModelListeners=(e,t)=>{const r={};for(const s in e)(!isModelListener(s)||!(s.slice(9)in t))&&(r[s]=e[s]);return r};function shouldUpdateComponent(e,t,r){const{props:s,children:n,component:o}=e,{props:a,children:l,patchFlag:c}=t,_=o.emitsOptions;if(t.dirs||t.transition)return!0;if(r&&c>=0){if(c&1024)return!0;if(c&16)return s?hasPropsChanged(s,a,_):!!a;if(c&8){const u=t.dynamicProps;for(let f=0;f<u.length;f++){const m=u[f];if(a[m]!==s[m]&&!isEmitListener(_,m))return!0}}}else return(n||l)&&(!l||!l.$stable)?!0:s===a?!1:s?a?hasPropsChanged(s,a,_):!0:!!a;return!1}function hasPropsChanged(e,t,r){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let n=0;n<s.length;n++){const o=s[n];if(t[o]!==e[o]&&!isEmitListener(r,o))return!0}return!1}function updateHOCHostEl({vnode:e,parent:t},r){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=r,t=t.parent;else break}}const isSuspense=e=>e.__isSuspense;function queueEffectWithSuspense(e,t){t&&t.pendingBranch?isArray(e)?t.effects.push(...e):t.effects.push(e):queuePostFlushCb(e)}const Fragment=Symbol.for("v-fgt"),Text=Symbol.for("v-txt"),Comment=Symbol.for("v-cmt"),Static=Symbol.for("v-stc");let currentBlock=null,isBlockTreeEnabled=1;function setBlockTracking(e){isBlockTreeEnabled+=e,e<0&&currentBlock&&(currentBlock.hasOnce=!0)}function isVNode(e){return e?e.__v_isVNode===!0:!1}function isSameVNodeType(e,t){return e.type===t.type&&e.key===t.key}const normalizeKey=({key:e})=>e??null,normalizeRef=({ref:e,ref_key:t,ref_for:r})=>(typeof e=="number"&&(e=""+e),e!=null?isString(e)||isRef(e)||isFunction(e)?{i:currentRenderingInstance,r:e,k:t,f:!!r}:e:null);function createBaseVNode(e,t=null,r=null,s=0,n=null,o=e===Fragment?0:1,a=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&normalizeKey(t),ref:t&&normalizeRef(t),scopeId:currentScopeId,slotScopeIds:null,children:r,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:n,dynamicChildren:null,appContext:null,ctx:currentRenderingInstance};return l?(normalizeChildren(c,r),o&128&&e.normalize(c)):r&&(c.shapeFlag|=isString(r)?8:16),isBlockTreeEnabled>0&&!a&&currentBlock&&(c.patchFlag>0||o&6)&&c.patchFlag!==32&&currentBlock.push(c),c}const createVNode=_createVNode;function _createVNode(e,t=null,r=null,s=0,n=null,o=!1){if((!e||e===NULL_DYNAMIC_COMPONENT)&&(e=Comment),isVNode(e)){const l=cloneVNode(e,t,!0);return r&&normalizeChildren(l,r),isBlockTreeEnabled>0&&!o&&currentBlock&&(l.shapeFlag&6?currentBlock[currentBlock.indexOf(e)]=l:currentBlock.push(l)),l.patchFlag=-2,l}if(isClassComponent(e)&&(e=e.__vccOpts),t){t=guardReactiveProps(t);let{class:l,style:c}=t;l&&!isString(l)&&(t.class=normalizeClass(l)),isObject(c)&&(isProxy(c)&&!isArray(c)&&(c=extend({},c)),t.style=normalizeStyle(c))}const a=isString(e)?1:isSuspense(e)?128:isTeleport(e)?64:isObject(e)?4:isFunction(e)?2:0;return createBaseVNode(e,t,r,s,n,a,o,!0)}function guardReactiveProps(e){return e?isProxy(e)||isInternalObject(e)?extend({},e):e:null}function cloneVNode(e,t,r=!1,s=!1){const{props:n,ref:o,patchFlag:a,children:l,transition:c}=e,_=t?mergeProps(n||{},t):n,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:_,key:_&&normalizeKey(_),ref:t&&t.ref?r&&o?isArray(o)?o.concat(normalizeRef(t)):[o,normalizeRef(t)]:normalizeRef(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Fragment?a===-1?16:a|16:a,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&cloneVNode(e.ssContent),ssFallback:e.ssFallback&&cloneVNode(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&s&&setTransitionHooks(u,c.clone(u)),u}function createTextVNode(e=" ",t=0){return createVNode(Text,null,e,t)}function normalizeVNode(e){return e==null||typeof e=="boolean"?createVNode(Comment):isArray(e)?createVNode(Fragment,null,e.slice()):isVNode(e)?cloneIfMounted(e):createVNode(Text,null,String(e))}function cloneIfMounted(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:cloneVNode(e)}function normalizeChildren(e,t){let r=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(isArray(t))r=16;else if(typeof t=="object")if(s&65){const n=t.default;n&&(n._c&&(n._d=!1),normalizeChildren(e,n()),n._c&&(n._d=!0));return}else{r=32;const n=t._;!n&&!isInternalObject(t)?t._ctx=currentRenderingInstance:n===3&&currentRenderingInstance&&(currentRenderingInstance.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else isFunction(t)?(t={default:t,_ctx:currentRenderingInstance},r=32):(t=String(t),s&64?(r=16,t=[createTextVNode(t)]):r=8);e.children=t,e.shapeFlag|=r}function mergeProps(...e){const t={};for(let r=0;r<e.length;r++){const s=e[r];for(const n in s)if(n==="class")t.class!==s.class&&(t.class=normalizeClass([t.class,s.class]));else if(n==="style")t.style=normalizeStyle([t.style,s.style]);else if(isOn(n)){const o=t[n],a=s[n];a&&o!==a&&!(isArray(o)&&o.includes(a))&&(t[n]=o?[].concat(o,a):a)}else n!==""&&(t[n]=s[n])}return t}function invokeVNodeHook(e,t,r,s=null){callWithAsyncErrorHandling(e,t,7,[r,s])}const emptyAppContext=createAppContext();let uid=0;function createComponentInstance(e,t,r){const s=e.type,n=(t?t.appContext:e.appContext)||emptyAppContext,o={uid:uid++,vnode:e,type:s,parent:t,appContext:n,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new EffectScope(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(n.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:normalizePropsOptions(s,n),emitsOptions:normalizeEmitsOptions(s,n),emit:null,emitted:null,propsDefaults:EMPTY_OBJ,inheritAttrs:s.inheritAttrs,ctx:EMPTY_OBJ,data:EMPTY_OBJ,props:EMPTY_OBJ,attrs:EMPTY_OBJ,slots:EMPTY_OBJ,refs:EMPTY_OBJ,setupState:EMPTY_OBJ,setupContext:null,suspense:r,suspenseId:r?r.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=emit.bind(null,o),e.ce&&e.ce(o),o}let currentInstance=null;const getCurrentInstance=()=>currentInstance||currentRenderingInstance;let internalSetCurrentInstance,setInSSRSetupState;{const e=getGlobalThis(),t=(r,s)=>{let n;return(n=e[r])||(n=e[r]=[]),n.push(s),o=>{n.length>1?n.forEach(a=>a(o)):n[0](o)}};internalSetCurrentInstance=t("__VUE_INSTANCE_SETTERS__",r=>currentInstance=r),setInSSRSetupState=t("__VUE_SSR_SETTERS__",r=>isInSSRComponentSetup=r)}const setCurrentInstance=e=>{const t=currentInstance;return internalSetCurrentInstance(e),e.scope.on(),()=>{e.scope.off(),internalSetCurrentInstance(t)}},unsetCurrentInstance=()=>{currentInstance&&currentInstance.scope.off(),internalSetCurrentInstance(null)};function isStatefulComponent(e){return e.vnode.shapeFlag&4}let isInSSRComponentSetup=!1;function setupComponent(e,t=!1,r=!1){t&&setInSSRSetupState(t);const{props:s,children:n}=e.vnode,o=isStatefulComponent(e);initProps(e,s,o,t),initSlots(e,n,r);const a=o?setupStatefulComponent(e,t):void 0;return t&&setInSSRSetupState(!1),a}function setupStatefulComponent(e,t){const r=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,PublicInstanceProxyHandlers);const{setup:s}=r;if(s){pauseTracking();const n=e.setupContext=s.length>1?createSetupContext(e):null,o=setCurrentInstance(e),a=callWithErrorHandling(s,e,0,[e.props,n]),l=isPromise(a);if(resetTracking(),o(),(l||e.sp)&&!isAsyncWrapper(e)&&markAsyncBoundary(e),l){if(a.then(unsetCurrentInstance,unsetCurrentInstance),t)return a.then(c=>{handleSetupResult(e,c,t)}).catch(c=>{handleError(c,e,0)});e.asyncDep=a}else handleSetupResult(e,a,t)}else finishComponentSetup(e,t)}function handleSetupResult(e,t,r){isFunction(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:isObject(t)&&(e.setupState=proxyRefs(t)),finishComponentSetup(e,r)}let compile;function finishComponentSetup(e,t,r){const s=e.type;if(!e.render){if(!t&&compile&&!s.render){const n=s.template||resolveMergedOptions(e).template;if(n){const{isCustomElement:o,compilerOptions:a}=e.appContext.config,{delimiters:l,compilerOptions:c}=s,_=extend(extend({isCustomElement:o,delimiters:l},a),c);s.render=compile(n,_)}}e.render=s.render||NOOP}{const n=setCurrentInstance(e);pauseTracking();try{applyOptions(e)}finally{resetTracking(),n()}}}const attrsProxyHandlers={get(e,t){return track(e,"get",""),e[t]}};function createSetupContext(e){const t=r=>{e.exposed=r||{}};return{attrs:new Proxy(e.attrs,attrsProxyHandlers),slots:e.slots,emit:e.emit,expose:t}}function getComponentPublicInstance(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(proxyRefs(markRaw(e.exposed)),{get(t,r){if(r in t)return t[r];if(r in publicPropertiesMap)return publicPropertiesMap[r](e)},has(t,r){return r in t||r in publicPropertiesMap}})):e.proxy}const classifyRE=/(?:^|[-_])(\w)/g,classify=e=>e.replace(classifyRE,t=>t.toUpperCase()).replace(/[-_]/g,"");function getComponentName(e,t=!0){return isFunction(e)?e.displayName||e.name:e.name||t&&e.__name}function formatComponentName(e,t,r=!1){let s=getComponentName(t);if(!s&&t.__file){const n=t.__file.match(/([^/\\]+)\.\w+$/);n&&(s=n[1])}if(!s&&e&&e.parent){const n=o=>{for(const a in o)if(o[a]===t)return a};s=n(e.components||e.parent.type.components)||n(e.appContext.components)}return s?classify(s):r?"App":"Anonymous"}function isClassComponent(e){return isFunction(e)&&"__vccOpts"in e}const computed=(e,t)=>computed$1(e,t,isInSSRComponentSetup),version="3.5.12";/**
* @vue/runtime-dom v3.5.12
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let policy;const tt=typeof window<"u"&&window.trustedTypes;if(tt)try{policy=tt.createPolicy("vue",{createHTML:e=>e})}catch{}const unsafeToTrustedHTML=policy?e=>policy.createHTML(e):e=>e,svgNS="http://www.w3.org/2000/svg",mathmlNS="http://www.w3.org/1998/Math/MathML",doc=typeof document<"u"?document:null,templateContainer=doc&&doc.createElement("template"),nodeOps={insert:(e,t,r)=>{t.insertBefore(e,r||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,r,s)=>{const n=t==="svg"?doc.createElementNS(svgNS,e):t==="mathml"?doc.createElementNS(mathmlNS,e):r?doc.createElement(e,{is:r}):doc.createElement(e);return e==="select"&&s&&s.multiple!=null&&n.setAttribute("multiple",s.multiple),n},createText:e=>doc.createTextNode(e),createComment:e=>doc.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>doc.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,r,s,n,o){const a=r?r.previousSibling:t.lastChild;if(n&&(n===o||n.nextSibling))for(;t.insertBefore(n.cloneNode(!0),r),!(n===o||!(n=n.nextSibling)););else{templateContainer.innerHTML=unsafeToTrustedHTML(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=templateContainer.content;if(s==="svg"||s==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,r)}return[a?a.nextSibling:t.firstChild,r?r.previousSibling:t.lastChild]}},vtcKey=Symbol("_vtc");function patchClass(e,t,r){const s=e[vtcKey];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):r?e.setAttribute("class",t):e.className=t}const vShowOriginalDisplay=Symbol("_vod"),vShowHidden=Symbol("_vsh"),CSS_VAR_TEXT=Symbol(""),displayRE=/(^|;)\s*display\s*:/;function patchStyle(e,t,r){const s=e.style,n=isString(r);let o=!1;if(r&&!n){if(t)if(isString(t))for(const a of t.split(";")){const l=a.slice(0,a.indexOf(":")).trim();r[l]==null&&setStyle(s,l,"")}else for(const a in t)r[a]==null&&setStyle(s,a,"");for(const a in r)a==="display"&&(o=!0),setStyle(s,a,r[a])}else if(n){if(t!==r){const a=s[CSS_VAR_TEXT];a&&(r+=";"+a),s.cssText=r,o=displayRE.test(r)}}else t&&e.removeAttribute("style");vShowOriginalDisplay in e&&(e[vShowOriginalDisplay]=o?s.display:"",e[vShowHidden]&&(s.display="none"))}const importantRE=/\s*!important$/;function setStyle(e,t,r){if(isArray(r))r.forEach(s=>setStyle(e,t,s));else if(r==null&&(r=""),t.startsWith("--"))e.setProperty(t,r);else{const s=autoPrefix(e,t);importantRE.test(r)?e.setProperty(hyphenate(s),r.replace(importantRE,""),"important"):e[s]=r}}const prefixes=["Webkit","Moz","ms"],prefixCache={};function autoPrefix(e,t){const r=prefixCache[t];if(r)return r;let s=camelize(t);if(s!=="filter"&&s in e)return prefixCache[t]=s;s=capitalize(s);for(let n=0;n<prefixes.length;n++){const o=prefixes[n]+s;if(o in e)return prefixCache[t]=o}return t}const xlinkNS="http://www.w3.org/1999/xlink";function patchAttr(e,t,r,s,n,o=isSpecialBooleanAttr(t)){s&&t.startsWith("xlink:")?r==null?e.removeAttributeNS(xlinkNS,t.slice(6,t.length)):e.setAttributeNS(xlinkNS,t,r):r==null||o&&!includeBooleanAttr(r)?e.removeAttribute(t):e.setAttribute(t,o?"":isSymbol(r)?String(r):r)}function patchDOMProp(e,t,r,s,n){if(t==="innerHTML"||t==="textContent"){r!=null&&(e[t]=t==="innerHTML"?unsafeToTrustedHTML(r):r);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,c=r==null?e.type==="checkbox"?"on":"":String(r);(l!==c||!("_value"in e))&&(e.value=c),r==null&&e.removeAttribute(t),e._value=r;return}let a=!1;if(r===""||r==null){const l=typeof e[t];l==="boolean"?r=includeBooleanAttr(r):r==null&&l==="string"?(r="",a=!0):l==="number"&&(r=0,a=!0)}try{e[t]=r}catch{}a&&e.removeAttribute(n||t)}function addEventListener(e,t,r,s){e.addEventListener(t,r,s)}function removeEventListener(e,t,r,s){e.removeEventListener(t,r,s)}const veiKey=Symbol("_vei");function patchEvent(e,t,r,s,n=null){const o=e[veiKey]||(e[veiKey]={}),a=o[t];if(s&&a)a.value=s;else{const[l,c]=parseName(t);if(s){const _=o[t]=createInvoker(s,n);addEventListener(e,l,_,c)}else a&&(removeEventListener(e,l,a,c),o[t]=void 0)}}const optionsModifierRE=/(?:Once|Passive|Capture)$/;function parseName(e){let t;if(optionsModifierRE.test(e)){t={};let s;for(;s=e.match(optionsModifierRE);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):hyphenate(e.slice(2)),t]}let cachedNow=0;const p=Promise.resolve(),getNow=()=>cachedNow||(p.then(()=>cachedNow=0),cachedNow=Date.now());function createInvoker(e,t){const r=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=r.attached)return;callWithAsyncErrorHandling(patchStopImmediatePropagation(s,r.value),t,5,[s])};return r.value=e,r.attached=getNow(),r}function patchStopImmediatePropagation(e,t){if(isArray(t)){const r=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{r.call(e),e._stopped=!0},t.map(s=>n=>!n._stopped&&s&&s(n))}else return t}const isNativeOn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,patchProp=(e,t,r,s,n,o)=>{const a=n==="svg";t==="class"?patchClass(e,s,a):t==="style"?patchStyle(e,r,s):isOn(t)?isModelListener(t)||patchEvent(e,t,r,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):shouldSetAsProp(e,t,s,a))?(patchDOMProp(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&patchAttr(e,t,s,a,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!isString(s))?patchDOMProp(e,camelize(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),patchAttr(e,t,s,a))};function shouldSetAsProp(e,t,r,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&isNativeOn(t)&&isFunction(r));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const n=e.tagName;if(n==="IMG"||n==="VIDEO"||n==="CANVAS"||n==="SOURCE")return!1}return isNativeOn(t)&&isString(r)?!1:t in e}const rendererOptions=extend({patchProp},nodeOps);let renderer;function ensureRenderer(){return renderer||(renderer=createRenderer(rendererOptions))}const createApp=(...e)=>{const t=ensureRenderer().createApp(...e),{mount:r}=t;return t.mount=s=>{const n=normalizeContainer(s);if(!n)return;const o=t._component;!isFunction(o)&&!o.render&&!o.template&&(o.template=n.innerHTML),n.nodeType===1&&(n.textContent="");const a=r(n,!1,resolveRootNamespace(n));return n instanceof Element&&(n.removeAttribute("v-cloak"),n.setAttribute("data-v-app","")),a},t};function resolveRootNamespace(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function normalizeContainer(e){return isString(e)?document.querySelector(e):e}function getDefaultExportFromCjs(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function getAugmentedNamespace(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var r=function s(){return this instanceof s?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach(function(s){var n=Object.getOwnPropertyDescriptor(e,s);Object.defineProperty(r,s,n.get?n:{enumerable:!0,get:function(){return e[s]}})}),r}var treeSitter={exports:{}};const __viteBrowserExternal={},__viteBrowserExternal$1=Object.freeze(Object.defineProperty({__proto__:null,default:__viteBrowserExternal},Symbol.toStringTag,{value:"Module"})),require$$0=getAugmentedNamespace(__viteBrowserExternal$1);(function(module,exports){var Module=typeof Module<"u"?Module:{},ENVIRONMENT_IS_WEB=typeof window=="object",ENVIRONMENT_IS_WORKER=typeof importScripts=="function",ENVIRONMENT_IS_NODE=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string",TreeSitter=function(){var initPromise,document=typeof window=="object"?{currentScript:window.document.currentScript}:null;class Parser{constructor(){this.initialize()}initialize(){throw new Error("cannot construct a Parser before calling `init()`")}static init(moduleOptions){return initPromise||(Module=Object.assign({},Module,moduleOptions),initPromise=new Promise(resolveInitPromise=>{var moduleOverrides=Object.assign({},Module),arguments_=[],thisProgram="./this.program",quit_=(e,t)=>{throw t},scriptDirectory="";function locateFile(e){return Module.locateFile?Module.locateFile(e,scriptDirectory):scriptDirectory+e}var readAsync,readBinary;if(ENVIRONMENT_IS_NODE){var fs=require$$0,nodePath=require$$0;scriptDirectory=__dirname+"/",readBinary=e=>{e=isFileURI(e)?new URL(e):nodePath.normalize(e);var t=fs.readFileSync(e);return t},readAsync=(e,t=!0)=>(e=isFileURI(e)?new URL(e):nodePath.normalize(e),new Promise((r,s)=>{fs.readFile(e,t?void 0:"utf8",(n,o)=>{n?s(n):r(t?o.buffer:o)})})),!Module.thisProgram&&process.argv.length>1&&(thisProgram=process.argv[1].replace(/\\/g,"/")),arguments_=process.argv.slice(2),module.exports=Module,quit_=(e,t)=>{throw process.exitCode=e,t}}else(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)&&(ENVIRONMENT_IS_WORKER?scriptDirectory=self.location.href:typeof document<"u"&&document.currentScript&&(scriptDirectory=document.currentScript.src),scriptDirectory.startsWith("blob:")?scriptDirectory="":scriptDirectory=scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,"").lastIndexOf("/")+1),ENVIRONMENT_IS_WORKER&&(readBinary=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),readAsync=e=>isFileURI(e)?new Promise((t,r)=>{var s=new XMLHttpRequest;s.open("GET",e,!0),s.responseType="arraybuffer",s.onload=()=>{(s.status==200||s.status==0&&s.response)&&r(s.response),t(s.status)},s.onerror=t,s.send(null)}):fetch(e,{credentials:"same-origin"}).then(t=>t.ok?t.arrayBuffer():Promise.reject(new Error(t.status+" : "+t.url))));var out=Module.print||console.log.bind(console),err=Module.printErr||console.error.bind(console);Object.assign(Module,moduleOverrides),moduleOverrides=null,Module.arguments&&(arguments_=Module.arguments),Module.thisProgram&&(thisProgram=Module.thisProgram),Module.quit&&(quit_=Module.quit);var dynamicLibraries=Module.dynamicLibraries||[],wasmBinary;Module.wasmBinary&&(wasmBinary=Module.wasmBinary);var wasmMemory,ABORT=!1,EXITSTATUS,HEAP8,HEAPU8,HEAP_DATA_VIEW;function updateMemoryViews(){var e=wasmMemory.buffer;Module.HEAP_DATA_VIEW=HEAP_DATA_VIEW=new DataView(e),Module.HEAP8=HEAP8=new Int8Array(e),Module.HEAP16=new Int16Array(e),Module.HEAPU8=HEAPU8=new Uint8Array(e),Module.HEAPU16=new Uint16Array(e),Module.HEAP32=new Int32Array(e),Module.HEAPU32=new Uint32Array(e),Module.HEAPF32=new Float32Array(e),Module.HEAPF64=new Float64Array(e)}if(Module.wasmMemory)wasmMemory=Module.wasmMemory;else{var INITIAL_MEMORY=Module.INITIAL_MEMORY||33554432;wasmMemory=new WebAssembly.Memory({initial:INITIAL_MEMORY/65536,maximum:2147483648/65536})}updateMemoryViews();var __ATPRERUN__=[],__ATINIT__=[],__ATMAIN__=[],__ATPOSTRUN__=[],__RELOC_FUNCS__=[],runtimeInitialized=!1;function preRun(){if(Module.preRun)for(typeof Module.preRun=="function"&&(Module.preRun=[Module.preRun]);Module.preRun.length;)addOnPreRun(Module.preRun.shift());callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=!0,callRuntimeCallbacks(__RELOC_FUNCS__),callRuntimeCallbacks(__ATINIT__)}function preMain(){callRuntimeCallbacks(__ATMAIN__)}function postRun(){if(Module.postRun)for(typeof Module.postRun=="function"&&(Module.postRun=[Module.postRun]);Module.postRun.length;)addOnPostRun(Module.postRun.shift());callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(e){__ATPRERUN__.unshift(e)}function addOnInit(e){__ATINIT__.unshift(e)}function addOnPostRun(e){__ATPOSTRUN__.unshift(e)}var runDependencies=0,dependenciesFulfilled=null;function getUniqueRunDependency(e){return e}function addRunDependency(e){var t;runDependencies++,(t=Module.monitorRunDependencies)==null||t.call(Module,runDependencies)}function removeRunDependency(e){var r;if(runDependencies--,(r=Module.monitorRunDependencies)==null||r.call(Module,runDependencies),runDependencies==0&&dependenciesFulfilled){var t=dependenciesFulfilled;dependenciesFulfilled=null,t()}}function abort(e){var r;(r=Module.onAbort)==null||r.call(Module,e),e="Aborted("+e+")",err(e),ABORT=!0,EXITSTATUS=1,e+=". Build with -sASSERTIONS for more info.";var t=new WebAssembly.RuntimeError(e);throw t}var dataURIPrefix="data:application/octet-stream;base64,",isDataURI=e=>e.startsWith(dataURIPrefix),isFileURI=e=>e.startsWith("file://");function findWasmBinary(){var e="tree-sitter.wasm";return isDataURI(e)?e:locateFile(e)}var wasmBinaryFile;function getBinarySync(e){if(e==wasmBinaryFile&&wasmBinary)return new Uint8Array(wasmBinary);if(readBinary)return readBinary(e);throw"both async and sync fetching of the wasm failed"}function getBinaryPromise(e){return wasmBinary?Promise.resolve().then(()=>getBinarySync(e)):readAsync(e).then(t=>new Uint8Array(t),()=>getBinarySync(e))}function instantiateArrayBuffer(e,t,r){return getBinaryPromise(e).then(s=>WebAssembly.instantiate(s,t)).then(r,s=>{err(`failed to asynchronously prepare wasm: ${s}`),abort(s)})}function instantiateAsync(e,t,r,s){return!e&&typeof WebAssembly.instantiateStreaming=="function"&&!isDataURI(t)&&!isFileURI(t)&&!ENVIRONMENT_IS_NODE&&typeof fetch=="function"?fetch(t,{credentials:"same-origin"}).then(n=>{var o=WebAssembly.instantiateStreaming(n,r);return o.then(s,function(a){return err(`wasm streaming compile failed: ${a}`),err("falling back to ArrayBuffer instantiation"),instantiateArrayBuffer(t,r,s)})}):instantiateArrayBuffer(t,r,s)}function getWasmImports(){return{env:wasmImports,wasi_snapshot_preview1:wasmImports,"GOT.mem":new Proxy(wasmImports,GOTHandler),"GOT.func":new Proxy(wasmImports,GOTHandler)}}function createWasm(){var e=getWasmImports();function t(s,n){wasmExports=s.exports,wasmExports=relocateExports(wasmExports,1024);var o=getDylinkMetadata(n);return o.neededDynlibs&&(dynamicLibraries=o.neededDynlibs.concat(dynamicLibraries)),mergeLibSymbols(wasmExports),LDSO.init(),loadDylibs(),addOnInit(wasmExports.__wasm_call_ctors),__RELOC_FUNCS__.push(wasmExports.__wasm_apply_data_relocs),removeRunDependency(),wasmExports}addRunDependency();function r(s){t(s.instance,s.module)}if(Module.instantiateWasm)try{return Module.instantiateWasm(e,t)}catch(s){return err(`Module.instantiateWasm callback failed with error: ${s}`),!1}return wasmBinaryFile||(wasmBinaryFile=findWasmBinary()),instantiateAsync(wasmBinary,wasmBinaryFile,e,r),{}}function ExitStatus(e){this.name="ExitStatus",this.message=`Program terminated with exit(${e})`,this.status=e}var GOT={},currentModuleWeakSymbols=new Set([]),GOTHandler={get(e,t){var r=GOT[t];return r||(r=GOT[t]=new WebAssembly.Global({value:"i32",mutable:!0})),currentModuleWeakSymbols.has(t)||(r.required=!0),r}},LE_HEAP_LOAD_F32=e=>HEAP_DATA_VIEW.getFloat32(e,!0),LE_HEAP_LOAD_F64=e=>HEAP_DATA_VIEW.getFloat64(e,!0),LE_HEAP_LOAD_I16=e=>HEAP_DATA_VIEW.getInt16(e,!0),LE_HEAP_LOAD_I32=e=>HEAP_DATA_VIEW.getInt32(e,!0),LE_HEAP_LOAD_U32=e=>HEAP_DATA_VIEW.getUint32(e,!0),LE_HEAP_STORE_F32=(e,t)=>HEAP_DATA_VIEW.setFloat32(e,t,!0),LE_HEAP_STORE_F64=(e,t)=>HEAP_DATA_VIEW.setFloat64(e,t,!0),LE_HEAP_STORE_I16=(e,t)=>HEAP_DATA_VIEW.setInt16(e,t,!0),LE_HEAP_STORE_I32=(e,t)=>HEAP_DATA_VIEW.setInt32(e,t,!0),LE_HEAP_STORE_U32=(e,t)=>HEAP_DATA_VIEW.setUint32(e,t,!0),callRuntimeCallbacks=e=>{for(;e.length>0;)e.shift()(Module)},UTF8Decoder=typeof TextDecoder<"u"?new TextDecoder:void 0,UTF8ArrayToString=(e,t,r)=>{for(var s=t+r,n=t;e[n]&&!(n>=s);)++n;if(n-t>16&&e.buffer&&UTF8Decoder)return UTF8Decoder.decode(e.subarray(t,n));for(var o="";t<n;){var a=e[t++];if(!(a&128)){o+=String.fromCharCode(a);continue}var l=e[t++]&63;if((a&224)==192){o+=String.fromCharCode((a&31)<<6|l);continue}var c=e[t++]&63;if((a&240)==224?a=(a&15)<<12|l<<6|c:a=(a&7)<<18|l<<12|c<<6|e[t++]&63,a<65536)o+=String.fromCharCode(a);else{var _=a-65536;o+=String.fromCharCode(55296|_>>10,56320|_&1023)}}return o},getDylinkMetadata=e=>{var t=0,r=0;function s(){return e[t++]}function n(){for(var X=0,le=1;;){var J=e[t++];if(X+=(J&127)*le,le*=128,!(J&128))break}return X}function o(){var X=n();return t+=X,UTF8ArrayToString(e,t-X,X)}function a(X,le){if(X)throw new Error(le)}var l="dylink.0";if(e instanceof WebAssembly.Module){var c=WebAssembly.Module.customSections(e,l);c.length===0&&(l="dylink",c=WebAssembly.Module.customSections(e,l)),a(c.length===0,"need dylink section"),e=new Uint8Array(c[0]),r=e.length}else{var _=new Uint32Array(new Uint8Array(e.subarray(0,24)).buffer),u=_[0]==1836278016||_[0]==6386541;a(!u,"need to see wasm magic number"),a(e[8]!==0,"need the dylink section to be first"),t=9;var f=n();r=t+f,l=o()}var m={neededDynlibs:[],tlsExports:new Set,weakImports:new Set};if(l=="dylink"){m.memorySize=n(),m.memoryAlign=n(),m.tableSize=n(),m.tableAlign=n();for(var g=n(),w=0;w<g;++w){var b=o();m.neededDynlibs.push(b)}}else{a(l!=="dylink.0");for(var P=1,I=2,E=3,O=4,R=256,L=3,H=1;t<r;){var k=s(),W=n();if(k===P)m.memorySize=n(),m.memoryAlign=n(),m.tableSize=n(),m.tableAlign=n();else if(k===I)for(var g=n(),w=0;w<g;++w)b=o(),m.neededDynlibs.push(b);else if(k===E)for(var K=n();K--;){var z=o(),te=n();te&R&&m.tlsExports.add(z)}else if(k===O)for(var K=n();K--;){o();var z=o(),te=n();(te&L)==H&&m.weakImports.add(z)}else t+=W}}return m};function getValue(e,t="i8"){switch(t.endsWith("*")&&(t="*"),t){case"i1":return HEAP8[e];case"i8":return HEAP8[e];case"i16":return LE_HEAP_LOAD_I16((e>>1)*2);case"i32":return LE_HEAP_LOAD_I32((e>>2)*4);case"i64":abort("to do getValue(i64) use WASM_BIGINT");case"float":return LE_HEAP_LOAD_F32((e>>2)*4);case"double":return LE_HEAP_LOAD_F64((e>>3)*8);case"*":return LE_HEAP_LOAD_U32((e>>2)*4);default:abort(`invalid type for getValue: ${t}`)}}var newDSO=(e,t,r)=>{var s={refcount:1/0,name:e,exports:r,global:!0};return LDSO.loadedLibsByName[e]=s,t!=null&&(LDSO.loadedLibsByHandle[t]=s),s},LDSO={loadedLibsByName:{},loadedLibsByHandle:{},init(){newDSO("__main__",0,wasmImports)}},___heap_base=78112,zeroMemory=(e,t)=>(HEAPU8.fill(0,e,e+t),e),alignMemory=(e,t)=>Math.ceil(e/t)*t,getMemory=e=>{if(runtimeInitialized)return zeroMemory(_malloc(e),e);var t=___heap_base,r=t+alignMemory(e,16);return ___heap_base=r,GOT.__heap_base.value=r,t},isInternalSym=e=>["__cpp_exception","__c_longjmp","__wasm_apply_data_relocs","__dso_handle","__tls_size","__tls_align","__set_stack_limits","_emscripten_tls_init","__wasm_init_tls","__wasm_call_ctors","__start_em_asm","__stop_em_asm","__start_em_js","__stop_em_js"].includes(e)||e.startsWith("__em_js__"),uleb128Encode=(e,t)=>{e<128?t.push(e):t.push(e%128|128,e>>7)},sigToWasmTypes=e=>{for(var t={i:"i32",j:"i64",f:"f32",d:"f64",e:"externref",p:"i32"},r={parameters:[],results:e[0]=="v"?[]:[t[e[0]]]},s=1;s<e.length;++s)r.parameters.push(t[e[s]]);return r},generateFuncType=(e,t)=>{var r=e.slice(0,1),s=e.slice(1),n={i:127,p:127,j:126,f:125,d:124,e:111};t.push(96),uleb128Encode(s.length,t);for(var o=0;o<s.length;++o)t.push(n[s[o]]);r=="v"?t.push(0):t.push(1,n[r])},convertJsFunctionToWasm=(e,t)=>{if(typeof WebAssembly.Function=="function")return new WebAssembly.Function(sigToWasmTypes(t),e);var r=[1];generateFuncType(t,r);var s=[0,97,115,109,1,0,0,0,1];uleb128Encode(r.length,s),s.push(...r),s.push(2,7,1,1,101,1,102,0,0,7,5,1,1,102,0,0);var n=new WebAssembly.Module(new Uint8Array(s)),o=new WebAssembly.Instance(n,{e:{f:e}}),a=o.exports.f;return a},wasmTableMirror=[],wasmTable=new WebAssembly.Table({initial:28,element:"anyfunc"}),getWasmTableEntry=e=>{var t=wasmTableMirror[e];return t||(e>=wasmTableMirror.length&&(wasmTableMirror.length=e+1),wasmTableMirror[e]=t=wasmTable.get(e)),t},updateTableMap=(e,t)=>{if(functionsInTableMap)for(var r=e;r<e+t;r++){var s=getWasmTableEntry(r);s&&functionsInTableMap.set(s,r)}},functionsInTableMap,getFunctionAddress=e=>(functionsInTableMap||(functionsInTableMap=new WeakMap,updateTableMap(0,wasmTable.length)),functionsInTableMap.get(e)||0),freeTableIndexes=[],getEmptyTableSlot=()=>{if(freeTableIndexes.length)return freeTableIndexes.pop();try{wasmTable.grow(1)}catch(e){throw e instanceof RangeError?"Unable to grow wasm table. Set ALLOW_TABLE_GROWTH.":e}return wasmTable.length-1},setWasmTableEntry=(e,t)=>{wasmTable.set(e,t),wasmTableMirror[e]=wasmTable.get(e)},addFunction=(e,t)=>{var r=getFunctionAddress(e);if(r)return r;var s=getEmptyTableSlot();try{setWasmTableEntry(s,e)}catch(o){if(!(o instanceof TypeError))throw o;var n=convertJsFunctionToWasm(e,t);setWasmTableEntry(s,n)}return functionsInTableMap.set(e,s),s},updateGOT=(e,t)=>{for(var r in e)if(!isInternalSym(r)){var s=e[r];r.startsWith("orig$")&&(r=r.split("$")[1],t=!0),GOT[r]||(GOT[r]=new WebAssembly.Global({value:"i32",mutable:!0})),(t||GOT[r].value==0)&&(typeof s=="function"?GOT[r].value=addFunction(s):typeof s=="number"?GOT[r].value=s:err(`unhandled export type for '${r}': ${typeof s}`))}},relocateExports=(e,t,r)=>{var s={};for(var n in e){var o=e[n];typeof o=="object"&&(o=o.value),typeof o=="number"&&(o+=t),s[n]=o}return updateGOT(s,r),s},isSymbolDefined=e=>{var t=wasmImports[e];return!(!t||t.stub)},dynCallLegacy=(e,t,r)=>{e=e.replace(/p/g,"i");var s=Module["dynCall_"+e];return s(t,...r)},dynCall=(e,t,r=[])=>{if(e.includes("j"))return dynCallLegacy(e,t,r);var s=getWasmTableEntry(t)(...r);return s},stackSave=()=>_emscripten_stack_get_current(),stackRestore=e=>__emscripten_stack_restore(e),createInvokeFunction=e=>(t,...r)=>{var s=stackSave();try{return dynCall(e,t,r)}catch(n){if(stackRestore(s),n!==n+0)throw n;_setThrew(1,0)}},resolveGlobalSymbol=(e,t=!1)=>{var r;return t&&"orig$"+e in wasmImports&&(e="orig$"+e),isSymbolDefined(e)?r=wasmImports[e]:e.startsWith("invoke_")&&(r=wasmImports[e]=createInvokeFunction(e.split("_")[1])),{sym:r,name:e}},UTF8ToString=(e,t)=>e?UTF8ArrayToString(HEAPU8,e,t):"",loadWebAssemblyModule=(binary,flags,libName,localScope,handle)=>{var metadata=getDylinkMetadata(binary);currentModuleWeakSymbols=metadata.weakImports;function loadModule(){var memAlign=Math.pow(2,metadata.memoryAlign),memoryBase=metadata.memorySize?alignMemory(getMemory(metadata.memorySize+memAlign),memAlign):0,tableBase=metadata.tableSize?wasmTable.length:0,tableGrowthNeeded=tableBase+metadata.tableSize-wasmTable.length;tableGrowthNeeded>0&&wasmTable.grow(tableGrowthNeeded);var moduleExports;function resolveSymbol(e){var t=resolveGlobalSymbol(e).sym;return!t&&localScope&&(t=localScope[e]),t||(t=moduleExports[e]),t}var proxyHandler={get(e,t){switch(t){case"__memory_base":return memoryBase;case"__table_base":return tableBase}if(t in wasmImports&&!wasmImports[t].stub)return wasmImports[t];if(!(t in e)){var r;e[t]=(...s)=>(r||(r=resolveSymbol(t)),r(...s))}return e[t]}},proxy=new Proxy({},proxyHandler),info={"GOT.mem":new Proxy({},GOTHandler),"GOT.func":new Proxy({},GOTHandler),env:proxy,wasi_snapshot_preview1:proxy};function postInstantiation(module,instance){updateTableMap(tableBase,metadata.tableSize),moduleExports=relocateExports(instance.exports,memoryBase),flags.allowUndefined||reportUndefinedSymbols();function addEmAsm(addr,body){for(var args=[],arity=0;arity<16&&body.indexOf("$"+arity)!=-1;arity++)args.push("$"+arity);args=args.join(",");var func=`(${args}) => { ${body} };`;eval(func)}if("__start_em_asm"in moduleExports)for(var start=moduleExports.__start_em_asm,stop=moduleExports.__stop_em_asm;start<stop;){var jsString=UTF8ToString(start);addEmAsm(start,jsString),start=HEAPU8.indexOf(0,start)+1}function addEmJs(name,cSig,body){var jsArgs=[];if(cSig=cSig.slice(1,-1),cSig!="void"){cSig=cSig.split(",");for(var i in cSig){var jsArg=cSig[i].split(" ").pop();jsArgs.push(jsArg.replace("*",""))}}var func=`(${jsArgs}) => ${body};`;moduleExports[name]=eval(func)}for(var name in moduleExports)if(name.startsWith("__em_js__")){var start=moduleExports[name],jsString=UTF8ToString(start),parts=jsString.split("<::>");addEmJs(name.replace("__em_js__",""),parts[0],parts[1]),delete moduleExports[name]}var applyRelocs=moduleExports.__wasm_apply_data_relocs;applyRelocs&&(runtimeInitialized?applyRelocs():__RELOC_FUNCS__.push(applyRelocs));var init=moduleExports.__wasm_call_ctors;return init&&(runtimeInitialized?init():__ATINIT__.push(init)),moduleExports}if(flags.loadAsync){if(binary instanceof WebAssembly.Module){var instance=new WebAssembly.Instance(binary,info);return Promise.resolve(postInstantiation(binary,instance))}return WebAssembly.instantiate(binary,info).then(e=>postInstantiation(e.module,e.instance))}var module=binary instanceof WebAssembly.Module?binary:new WebAssembly.Module(binary),instance=new WebAssembly.Instance(module,info);return postInstantiation(module,instance)}return flags.loadAsync?metadata.neededDynlibs.reduce((e,t)=>e.then(()=>loadDynamicLibrary(t,flags,localScope)),Promise.resolve()).then(loadModule):(metadata.neededDynlibs.forEach(e=>loadDynamicLibrary(e,flags,localScope)),loadModule())},mergeLibSymbols=(e,t)=>{for(var[r,s]of Object.entries(e)){const n=a=>{isSymbolDefined(a)||(wasmImports[a]=s)};n(r);const o="__main_argc_argv";r=="main"&&n(o),r==o&&n("main"),r.startsWith("dynCall_")&&!Module.hasOwnProperty(r)&&(Module[r]=s)}},asyncLoad=(e,t,r,s)=>{var n=`al ${e}`;readAsync(e).then(o=>{t(new Uint8Array(o)),n&&removeRunDependency()},o=>{if(r)r();else throw`Loading data file "${e}" failed.`}),n&&addRunDependency()};function loadDynamicLibrary(e,t={global:!0,nodelete:!0},r,s){var n=LDSO.loadedLibsByName[e];if(n)return t.global&&(n.global||(n.global=!0,mergeLibSymbols(n.exports))),t.nodelete&&n.refcount!==1/0&&(n.refcount=1/0),n.refcount++,t.loadAsync?Promise.resolve(!0):!0;n=newDSO(e,s,"loading"),n.refcount=t.nodelete?1/0:1,n.global=t.global;function o(){var c=locateFile(e);if(t.loadAsync)return new Promise(function(_,u){asyncLoad(c,_,u)});if(!readBinary)throw new Error(`${c}: file not found, and synchronous loading of external files is not available`);return readBinary(c)}function a(){return t.loadAsync?o().then(c=>loadWebAssemblyModule(c,t,e,r)):loadWebAssemblyModule(o(),t,e,r)}function l(c){n.global&&mergeLibSymbols(c),n.exports=c}return t.loadAsync?a().then(c=>(l(c),!0)):(l(a()),!0)}var reportUndefinedSymbols=()=>{for(var[e,t]of Object.entries(GOT))if(t.value==0){var r=resolveGlobalSymbol(e,!0).sym;if(!r&&!t.required)continue;if(typeof r=="function")t.value=addFunction(r,r.sig);else if(typeof r=="number")t.value=r;else throw new Error(`bad export type for '${e}': ${typeof r}`)}},loadDylibs=()=>{if(!dynamicLibraries.length){reportUndefinedSymbols();return}addRunDependency(),dynamicLibraries.reduce((e,t)=>e.then(()=>loadDynamicLibrary(t,{loadAsync:!0,global:!0,nodelete:!0,allowUndefined:!0})),Promise.resolve()).then(()=>{reportUndefinedSymbols(),removeRunDependency()})},noExitRuntime=Module.noExitRuntime||!0;function setValue(e,t,r="i8"){switch(r.endsWith("*")&&(r="*"),r){case"i1":HEAP8[e]=t;break;case"i8":HEAP8[e]=t;break;case"i16":LE_HEAP_STORE_I16((e>>1)*2,t);break;case"i32":LE_HEAP_STORE_I32((e>>2)*4,t);break;case"i64":abort("to do setValue(i64) use WASM_BIGINT");case"float":LE_HEAP_STORE_F32((e>>2)*4,t);break;case"double":LE_HEAP_STORE_F64((e>>3)*8,t);break;case"*":LE_HEAP_STORE_U32((e>>2)*4,t);break;default:abort(`invalid type for setValue: ${r}`)}}var ___memory_base=new WebAssembly.Global({value:"i32",mutable:!1},1024),___stack_pointer=new WebAssembly.Global({value:"i32",mutable:!0},78112),___table_base=new WebAssembly.Global({value:"i32",mutable:!1},1),__abort_js=()=>{abort("")};__abort_js.sig="v";var nowIsMonotonic=1,__emscripten_get_now_is_monotonic=()=>nowIsMonotonic;__emscripten_get_now_is_monotonic.sig="i";var __emscripten_memcpy_js=(e,t,r)=>HEAPU8.copyWithin(e,t,t+r);__emscripten_memcpy_js.sig="vppp";var _emscripten_get_now;_emscripten_get_now=()=>performance.now(),_emscripten_get_now.sig="d";var getHeapMax=()=>2147483648,growMemory=e=>{var t=wasmMemory.buffer,r=(e-t.byteLength+65535)/65536;try{return wasmMemory.grow(r),updateMemoryViews(),1}catch{}},_emscripten_resize_heap=e=>{var t=HEAPU8.length;e>>>=0;var r=getHeapMax();if(e>r)return!1;for(var s=(c,_)=>c+(_-c%_)%_,n=1;n<=4;n*=2){var o=t*(1+.2/n);o=Math.min(o,e+100663296);var a=Math.min(r,s(Math.max(e,o),65536)),l=growMemory(a);if(l)return!0}return!1};_emscripten_resize_heap.sig="ip";var _fd_close=e=>52;_fd_close.sig="ii";function _fd_seek(e,t,r,s,n){return 70}_fd_seek.sig="iiiiip";var printCharBuffers=[null,[],[]],printChar=(e,t)=>{var r=printCharBuffers[e];t===0||t===10?((e===1?out:err)(UTF8ArrayToString(r,0)),r.length=0):r.push(t)},_fd_write=(e,t,r,s)=>{for(var n=0,o=0;o<r;o++){var a=LE_HEAP_LOAD_U32((t>>2)*4),l=LE_HEAP_LOAD_U32((t+4>>2)*4);t+=8;for(var c=0;c<l;c++)printChar(e,HEAPU8[a+c]);n+=l}return LE_HEAP_STORE_U32((s>>2)*4,n),0};_fd_write.sig="iippp";function _tree_sitter_log_callback(e,t){if(currentLogCallback){const r=UTF8ToString(t);currentLogCallback(r,e!==0)}}function _tree_sitter_parse_callback(e,t,r,s,n){const a=currentParseCallback(t,{row:r,column:s});typeof a=="string"?(setValue(n,a.length,"i32"),stringToUTF16(a,e,10240)):setValue(n,0,"i32")}var runtimeKeepaliveCounter=0,keepRuntimeAlive=()=>noExitRuntime||runtimeKeepaliveCounter>0,_proc_exit=e=>{var t;EXITSTATUS=e,keepRuntimeAlive()||((t=Module.onExit)==null||t.call(Module,e),ABORT=!0),quit_(e,new ExitStatus(e))};_proc_exit.sig="vi";var exitJS=(e,t)=>{EXITSTATUS=e,_proc_exit(e)},handleException=e=>{if(e instanceof ExitStatus||e=="unwind")return EXITSTATUS;quit_(1,e)},lengthBytesUTF8=e=>{for(var t=0,r=0;r<e.length;++r){var s=e.charCodeAt(r);s<=127?t++:s<=2047?t+=2:s>=55296&&s<=57343?(t+=4,++r):t+=3}return t},stringToUTF8Array=(e,t,r,s)=>{if(!(s>0))return 0;for(var n=r,o=r+s-1,a=0;a<e.length;++a){var l=e.charCodeAt(a);if(l>=55296&&l<=57343){var c=e.charCodeAt(++a);l=65536+((l&1023)<<10)|c&1023}if(l<=127){if(r>=o)break;t[r++]=l}else if(l<=2047){if(r+1>=o)break;t[r++]=192|l>>6,t[r++]=128|l&63}else if(l<=65535){if(r+2>=o)break;t[r++]=224|l>>12,t[r++]=128|l>>6&63,t[r++]=128|l&63}else{if(r+3>=o)break;t[r++]=240|l>>18,t[r++]=128|l>>12&63,t[r++]=128|l>>6&63,t[r++]=128|l&63}}return t[r]=0,r-n},stringToUTF8=(e,t,r)=>stringToUTF8Array(e,HEAPU8,t,r),stackAlloc=e=>__emscripten_stack_alloc(e),stringToUTF8OnStack=e=>{var t=lengthBytesUTF8(e)+1,r=stackAlloc(t);return stringToUTF8(e,r,t),r},stringToUTF16=(e,t,r)=>{if(r??(r=2147483647),r<2)return 0;r-=2;for(var s=t,n=r<e.length*2?r/2:e.length,o=0;o<n;++o){var a=e.charCodeAt(o);LE_HEAP_STORE_I16((t>>1)*2,a),t+=2}return LE_HEAP_STORE_I16((t>>1)*2,0),t-s},AsciiToString=e=>{for(var t="";;){var r=HEAPU8[e++];if(!r)return t;t+=String.fromCharCode(r)}},wasmImports={__heap_base:___heap_base,__indirect_function_table:wasmTable,__memory_base:___memory_base,__stack_pointer:___stack_pointer,__table_base:___table_base,_abort_js:__abort_js,_emscripten_get_now_is_monotonic:__emscripten_get_now_is_monotonic,_emscripten_memcpy_js:__emscripten_memcpy_js,emscripten_get_now:_emscripten_get_now,emscripten_resize_heap:_emscripten_resize_heap,fd_close:_fd_close,fd_seek:_fd_seek,fd_write:_fd_write,memory:wasmMemory,tree_sitter_log_callback:_tree_sitter_log_callback,tree_sitter_parse_callback:_tree_sitter_parse_callback},wasmExports=createWasm(),_malloc=Module._malloc=e=>(_malloc=Module._malloc=wasmExports.malloc)(e);Module._calloc=(e,t)=>(Module._calloc=wasmExports.calloc)(e,t),Module._realloc=(e,t)=>(Module._realloc=wasmExports.realloc)(e,t),Module._free=e=>(Module._free=wasmExports.free)(e),Module._ts_language_symbol_count=e=>(Module._ts_language_symbol_count=wasmExports.ts_language_symbol_count)(e),Module._ts_language_state_count=e=>(Module._ts_language_state_count=wasmExports.ts_language_state_count)(e),Module._ts_language_version=e=>(Module._ts_language_version=wasmExports.ts_language_version)(e),Module._ts_language_field_count=e=>(Module._ts_language_field_count=wasmExports.ts_language_field_count)(e),Module._ts_language_next_state=(e,t,r)=>(Module._ts_language_next_state=wasmExports.ts_language_next_state)(e,t,r),Module._ts_language_symbol_name=(e,t)=>(Module._ts_language_symbol_name=wasmExports.ts_language_symbol_name)(e,t),Module._ts_language_symbol_for_name=(e,t,r,s)=>(Module._ts_language_symbol_for_name=wasmExports.ts_language_symbol_for_name)(e,t,r,s),Module._strncmp=(e,t,r)=>(Module._strncmp=wasmExports.strncmp)(e,t,r),Module._ts_language_symbol_type=(e,t)=>(Module._ts_language_symbol_type=wasmExports.ts_language_symbol_type)(e,t),Module._ts_language_field_name_for_id=(e,t)=>(Module._ts_language_field_name_for_id=wasmExports.ts_language_field_name_for_id)(e,t),Module._ts_lookahead_iterator_new=(e,t)=>(Module._ts_lookahead_iterator_new=wasmExports.ts_lookahead_iterator_new)(e,t),Module._ts_lookahead_iterator_delete=e=>(Module._ts_lookahead_iterator_delete=wasmExports.ts_lookahead_iterator_delete)(e),Module._ts_lookahead_iterator_reset_state=(e,t)=>(Module._ts_lookahead_iterator_reset_state=wasmExports.ts_lookahead_iterator_reset_state)(e,t),Module._ts_lookahead_iterator_reset=(e,t,r)=>(Module._ts_lookahead_iterator_reset=wasmExports.ts_lookahead_iterator_reset)(e,t,r),Module._ts_lookahead_iterator_next=e=>(Module._ts_lookahead_iterator_next=wasmExports.ts_lookahead_iterator_next)(e),Module._ts_lookahead_iterator_current_symbol=e=>(Module._ts_lookahead_iterator_current_symbol=wasmExports.ts_lookahead_iterator_current_symbol)(e),Module._memset=(e,t,r)=>(Module._memset=wasmExports.memset)(e,t,r),Module._memcpy=(e,t,r)=>(Module._memcpy=wasmExports.memcpy)(e,t,r),Module._ts_parser_delete=e=>(Module._ts_parser_delete=wasmExports.ts_parser_delete)(e),Module._ts_parser_reset=e=>(Module._ts_parser_reset=wasmExports.ts_parser_reset)(e),Module._ts_parser_set_language=(e,t)=>(Module._ts_parser_set_language=wasmExports.ts_parser_set_language)(e,t),Module._ts_parser_timeout_micros=e=>(Module._ts_parser_timeout_micros=wasmExports.ts_parser_timeout_micros)(e),Module._ts_parser_set_timeout_micros=(e,t,r)=>(Module._ts_parser_set_timeout_micros=wasmExports.ts_parser_set_timeout_micros)(e,t,r),Module._ts_parser_set_included_ranges=(e,t,r)=>(Module._ts_parser_set_included_ranges=wasmExports.ts_parser_set_included_ranges)(e,t,r),Module._memmove=(e,t,r)=>(Module._memmove=wasmExports.memmove)(e,t,r),Module._memcmp=(e,t,r)=>(Module._memcmp=wasmExports.memcmp)(e,t,r),Module._ts_query_new=(e,t,r,s,n)=>(Module._ts_query_new=wasmExports.ts_query_new)(e,t,r,s,n),Module._ts_query_delete=e=>(Module._ts_query_delete=wasmExports.ts_query_delete)(e),Module._iswspace=e=>(Module._iswspace=wasmExports.iswspace)(e),Module._iswalnum=e=>(Module._iswalnum=wasmExports.iswalnum)(e),Module._ts_query_pattern_count=e=>(Module._ts_query_pattern_count=wasmExports.ts_query_pattern_count)(e),Module._ts_query_capture_count=e=>(Module._ts_query_capture_count=wasmExports.ts_query_capture_count)(e),Module._ts_query_string_count=e=>(Module._ts_query_string_count=wasmExports.ts_query_string_count)(e),Module._ts_query_capture_name_for_id=(e,t,r)=>(Module._ts_query_capture_name_for_id=wasmExports.ts_query_capture_name_for_id)(e,t,r),Module._ts_query_string_value_for_id=(e,t,r)=>(Module._ts_query_string_value_for_id=wasmExports.ts_query_string_value_for_id)(e,t,r),Module._ts_query_predicates_for_pattern=(e,t,r)=>(Module._ts_query_predicates_for_pattern=wasmExports.ts_query_predicates_for_pattern)(e,t,r),Module._ts_query_disable_capture=(e,t,r)=>(Module._ts_query_disable_capture=wasmExports.ts_query_disable_capture)(e,t,r),Module._ts_tree_copy=e=>(Module._ts_tree_copy=wasmExports.ts_tree_copy)(e),Module._ts_tree_delete=e=>(Module._ts_tree_delete=wasmExports.ts_tree_delete)(e),Module._ts_init=()=>(Module._ts_init=wasmExports.ts_init)(),Module._ts_parser_new_wasm=()=>(Module._ts_parser_new_wasm=wasmExports.ts_parser_new_wasm)(),Module._ts_parser_enable_logger_wasm=(e,t)=>(Module._ts_parser_enable_logger_wasm=wasmExports.ts_parser_enable_logger_wasm)(e,t),Module._ts_parser_parse_wasm=(e,t,r,s,n)=>(Module._ts_parser_parse_wasm=wasmExports.ts_parser_parse_wasm)(e,t,r,s,n),Module._ts_parser_included_ranges_wasm=e=>(Module._ts_parser_included_ranges_wasm=wasmExports.ts_parser_included_ranges_wasm)(e),Module._ts_language_type_is_named_wasm=(e,t)=>(Module._ts_language_type_is_named_wasm=wasmExports.ts_language_type_is_named_wasm)(e,t),Module._ts_language_type_is_visible_wasm=(e,t)=>(Module._ts_language_type_is_visible_wasm=wasmExports.ts_language_type_is_visible_wasm)(e,t),Module._ts_tree_root_node_wasm=e=>(Module._ts_tree_root_node_wasm=wasmExports.ts_tree_root_node_wasm)(e),Module._ts_tree_root_node_with_offset_wasm=e=>(Module._ts_tree_root_node_with_offset_wasm=wasmExports.ts_tree_root_node_with_offset_wasm)(e),Module._ts_tree_edit_wasm=e=>(Module._ts_tree_edit_wasm=wasmExports.ts_tree_edit_wasm)(e),Module._ts_tree_included_ranges_wasm=e=>(Module._ts_tree_included_ranges_wasm=wasmExports.ts_tree_included_ranges_wasm)(e),Module._ts_tree_get_changed_ranges_wasm=(e,t)=>(Module._ts_tree_get_changed_ranges_wasm=wasmExports.ts_tree_get_changed_ranges_wasm)(e,t),Module._ts_tree_cursor_new_wasm=e=>(Module._ts_tree_cursor_new_wasm=wasmExports.ts_tree_cursor_new_wasm)(e),Module._ts_tree_cursor_delete_wasm=e=>(Module._ts_tree_cursor_delete_wasm=wasmExports.ts_tree_cursor_delete_wasm)(e),Module._ts_tree_cursor_reset_wasm=e=>(Module._ts_tree_cursor_reset_wasm=wasmExports.ts_tree_cursor_reset_wasm)(e),Module._ts_tree_cursor_reset_to_wasm=(e,t)=>(Module._ts_tree_cursor_reset_to_wasm=wasmExports.ts_tree_cursor_reset_to_wasm)(e,t),Module._ts_tree_cursor_goto_first_child_wasm=e=>(Module._ts_tree_cursor_goto_first_child_wasm=wasmExports.ts_tree_cursor_goto_first_child_wasm)(e),Module._ts_tree_cursor_goto_last_child_wasm=e=>(Module._ts_tree_cursor_goto_last_child_wasm=wasmExports.ts_tree_cursor_goto_last_child_wasm)(e),Module._ts_tree_cursor_goto_first_child_for_index_wasm=e=>(Module._ts_tree_cursor_goto_first_child_for_index_wasm=wasmExports.ts_tree_cursor_goto_first_child_for_index_wasm)(e),Module._ts_tree_cursor_goto_first_child_for_position_wasm=e=>(Module._ts_tree_cursor_goto_first_child_for_position_wasm=wasmExports.ts_tree_cursor_goto_first_child_for_position_wasm)(e),Module._ts_tree_cursor_goto_next_sibling_wasm=e=>(Module._ts_tree_cursor_goto_next_sibling_wasm=wasmExports.ts_tree_cursor_goto_next_sibling_wasm)(e),Module._ts_tree_cursor_goto_previous_sibling_wasm=e=>(Module._ts_tree_cursor_goto_previous_sibling_wasm=wasmExports.ts_tree_cursor_goto_previous_sibling_wasm)(e),Module._ts_tree_cursor_goto_descendant_wasm=(e,t)=>(Module._ts_tree_cursor_goto_descendant_wasm=wasmExports.ts_tree_cursor_goto_descendant_wasm)(e,t),Module._ts_tree_cursor_goto_parent_wasm=e=>(Module._ts_tree_cursor_goto_parent_wasm=wasmExports.ts_tree_cursor_goto_parent_wasm)(e),Module._ts_tree_cursor_current_node_type_id_wasm=e=>(Module._ts_tree_cursor_current_node_type_id_wasm=wasmExports.ts_tree_cursor_current_node_type_id_wasm)(e),Module._ts_tree_cursor_current_node_state_id_wasm=e=>(Module._ts_tree_cursor_current_node_state_id_wasm=wasmExports.ts_tree_cursor_current_node_state_id_wasm)(e),Module._ts_tree_cursor_current_node_is_named_wasm=e=>(Module._ts_tree_cursor_current_node_is_named_wasm=wasmExports.ts_tree_cursor_current_node_is_named_wasm)(e),Module._ts_tree_cursor_current_node_is_missing_wasm=e=>(Module._ts_tree_cursor_current_node_is_missing_wasm=wasmExports.ts_tree_cursor_current_node_is_missing_wasm)(e),Module._ts_tree_cursor_current_node_id_wasm=e=>(Module._ts_tree_cursor_current_node_id_wasm=wasmExports.ts_tree_cursor_current_node_id_wasm)(e),Module._ts_tree_cursor_start_position_wasm=e=>(Module._ts_tree_cursor_start_position_wasm=wasmExports.ts_tree_cursor_start_position_wasm)(e),Module._ts_tree_cursor_end_position_wasm=e=>(Module._ts_tree_cursor_end_position_wasm=wasmExports.ts_tree_cursor_end_position_wasm)(e),Module._ts_tree_cursor_start_index_wasm=e=>(Module._ts_tree_cursor_start_index_wasm=wasmExports.ts_tree_cursor_start_index_wasm)(e),Module._ts_tree_cursor_end_index_wasm=e=>(Module._ts_tree_cursor_end_index_wasm=wasmExports.ts_tree_cursor_end_index_wasm)(e),Module._ts_tree_cursor_current_field_id_wasm=e=>(Module._ts_tree_cursor_current_field_id_wasm=wasmExports.ts_tree_cursor_current_field_id_wasm)(e),Module._ts_tree_cursor_current_depth_wasm=e=>(Module._ts_tree_cursor_current_depth_wasm=wasmExports.ts_tree_cursor_current_depth_wasm)(e),Module._ts_tree_cursor_current_descendant_index_wasm=e=>(Module._ts_tree_cursor_current_descendant_index_wasm=wasmExports.ts_tree_cursor_current_descendant_index_wasm)(e),Module._ts_tree_cursor_current_node_wasm=e=>(Module._ts_tree_cursor_current_node_wasm=wasmExports.ts_tree_cursor_current_node_wasm)(e),Module._ts_node_symbol_wasm=e=>(Module._ts_node_symbol_wasm=wasmExports.ts_node_symbol_wasm)(e),Module._ts_node_field_name_for_child_wasm=(e,t)=>(Module._ts_node_field_name_for_child_wasm=wasmExports.ts_node_field_name_for_child_wasm)(e,t),Module._ts_node_children_by_field_id_wasm=(e,t)=>(Module._ts_node_children_by_field_id_wasm=wasmExports.ts_node_children_by_field_id_wasm)(e,t),Module._ts_node_first_child_for_byte_wasm=e=>(Module._ts_node_first_child_for_byte_wasm=wasmExports.ts_node_first_child_for_byte_wasm)(e),Module._ts_node_first_named_child_for_byte_wasm=e=>(Module._ts_node_first_named_child_for_byte_wasm=wasmExports.ts_node_first_named_child_for_byte_wasm)(e),Module._ts_node_grammar_symbol_wasm=e=>(Module._ts_node_grammar_symbol_wasm=wasmExports.ts_node_grammar_symbol_wasm)(e),Module._ts_node_child_count_wasm=e=>(Module._ts_node_child_count_wasm=wasmExports.ts_node_child_count_wasm)(e),Module._ts_node_named_child_count_wasm=e=>(Module._ts_node_named_child_count_wasm=wasmExports.ts_node_named_child_count_wasm)(e),Module._ts_node_child_wasm=(e,t)=>(Module._ts_node_child_wasm=wasmExports.ts_node_child_wasm)(e,t),Module._ts_node_named_child_wasm=(e,t)=>(Module._ts_node_named_child_wasm=wasmExports.ts_node_named_child_wasm)(e,t),Module._ts_node_child_by_field_id_wasm=(e,t)=>(Module._ts_node_child_by_field_id_wasm=wasmExports.ts_node_child_by_field_id_wasm)(e,t),Module._ts_node_next_sibling_wasm=e=>(Module._ts_node_next_sibling_wasm=wasmExports.ts_node_next_sibling_wasm)(e),Module._ts_node_prev_sibling_wasm=e=>(Module._ts_node_prev_sibling_wasm=wasmExports.ts_node_prev_sibling_wasm)(e),Module._ts_node_next_named_sibling_wasm=e=>(Module._ts_node_next_named_sibling_wasm=wasmExports.ts_node_next_named_sibling_wasm)(e),Module._ts_node_prev_named_sibling_wasm=e=>(Module._ts_node_prev_named_sibling_wasm=wasmExports.ts_node_prev_named_sibling_wasm)(e),Module._ts_node_descendant_count_wasm=e=>(Module._ts_node_descendant_count_wasm=wasmExports.ts_node_descendant_count_wasm)(e),Module._ts_node_parent_wasm=e=>(Module._ts_node_parent_wasm=wasmExports.ts_node_parent_wasm)(e),Module._ts_node_descendant_for_index_wasm=e=>(Module._ts_node_descendant_for_index_wasm=wasmExports.ts_node_descendant_for_index_wasm)(e),Module._ts_node_named_descendant_for_index_wasm=e=>(Module._ts_node_named_descendant_for_index_wasm=wasmExports.ts_node_named_descendant_for_index_wasm)(e),Module._ts_node_descendant_for_position_wasm=e=>(Module._ts_node_descendant_for_position_wasm=wasmExports.ts_node_descendant_for_position_wasm)(e),Module._ts_node_named_descendant_for_position_wasm=e=>(Module._ts_node_named_descendant_for_position_wasm=wasmExports.ts_node_named_descendant_for_position_wasm)(e),Module._ts_node_start_point_wasm=e=>(Module._ts_node_start_point_wasm=wasmExports.ts_node_start_point_wasm)(e),Module._ts_node_end_point_wasm=e=>(Module._ts_node_end_point_wasm=wasmExports.ts_node_end_point_wasm)(e),Module._ts_node_start_index_wasm=e=>(Module._ts_node_start_index_wasm=wasmExports.ts_node_start_index_wasm)(e),Module._ts_node_end_index_wasm=e=>(Module._ts_node_end_index_wasm=wasmExports.ts_node_end_index_wasm)(e),Module._ts_node_to_string_wasm=e=>(Module._ts_node_to_string_wasm=wasmExports.ts_node_to_string_wasm)(e),Module._ts_node_children_wasm=e=>(Module._ts_node_children_wasm=wasmExports.ts_node_children_wasm)(e),Module._ts_node_named_children_wasm=e=>(Module._ts_node_named_children_wasm=wasmExports.ts_node_named_children_wasm)(e),Module._ts_node_descendants_of_type_wasm=(e,t,r,s,n,o,a)=>(Module._ts_node_descendants_of_type_wasm=wasmExports.ts_node_descendants_of_type_wasm)(e,t,r,s,n,o,a),Module._ts_node_is_named_wasm=e=>(Module._ts_node_is_named_wasm=wasmExports.ts_node_is_named_wasm)(e),Module._ts_node_has_changes_wasm=e=>(Module._ts_node_has_changes_wasm=wasmExports.ts_node_has_changes_wasm)(e),Module._ts_node_has_error_wasm=e=>(Module._ts_node_has_error_wasm=wasmExports.ts_node_has_error_wasm)(e),Module._ts_node_is_error_wasm=e=>(Module._ts_node_is_error_wasm=wasmExports.ts_node_is_error_wasm)(e),Module._ts_node_is_missing_wasm=e=>(Module._ts_node_is_missing_wasm=wasmExports.ts_node_is_missing_wasm)(e),Module._ts_node_is_extra_wasm=e=>(Module._ts_node_is_extra_wasm=wasmExports.ts_node_is_extra_wasm)(e),Module._ts_node_parse_state_wasm=e=>(Module._ts_node_parse_state_wasm=wasmExports.ts_node_parse_state_wasm)(e),Module._ts_node_next_parse_state_wasm=e=>(Module._ts_node_next_parse_state_wasm=wasmExports.ts_node_next_parse_state_wasm)(e),Module._ts_query_matches_wasm=(e,t,r,s,n,o,a,l,c,_,u)=>(Module._ts_query_matches_wasm=wasmExports.ts_query_matches_wasm)(e,t,r,s,n,o,a,l,c,_,u),Module._ts_query_captures_wasm=(e,t,r,s,n,o,a,l,c,_,u)=>(Module._ts_query_captures_wasm=wasmExports.ts_query_captures_wasm)(e,t,r,s,n,o,a,l,c,_,u),Module._iswalpha=e=>(Module._iswalpha=wasmExports.iswalpha)(e),Module._iswblank=e=>(Module._iswblank=wasmExports.iswblank)(e),Module._iswdigit=e=>(Module._iswdigit=wasmExports.iswdigit)(e),Module._iswlower=e=>(Module._iswlower=wasmExports.iswlower)(e),Module._iswupper=e=>(Module._iswupper=wasmExports.iswupper)(e),Module._iswxdigit=e=>(Module._iswxdigit=wasmExports.iswxdigit)(e),Module._memchr=(e,t,r)=>(Module._memchr=wasmExports.memchr)(e,t,r),Module._strlen=e=>(Module._strlen=wasmExports.strlen)(e),Module._strcmp=(e,t)=>(Module._strcmp=wasmExports.strcmp)(e,t),Module._strncat=(e,t,r)=>(Module._strncat=wasmExports.strncat)(e,t,r),Module._strncpy=(e,t,r)=>(Module._strncpy=wasmExports.strncpy)(e,t,r),Module._towlower=e=>(Module._towlower=wasmExports.towlower)(e),Module._towupper=e=>(Module._towupper=wasmExports.towupper)(e);var _setThrew=(e,t)=>(_setThrew=wasmExports.setThrew)(e,t),__emscripten_stack_restore=e=>(__emscripten_stack_restore=wasmExports._emscripten_stack_restore)(e),__emscripten_stack_alloc=e=>(__emscripten_stack_alloc=wasmExports._emscripten_stack_alloc)(e),_emscripten_stack_get_current=()=>(_emscripten_stack_get_current=wasmExports.emscripten_stack_get_current)();Module.dynCall_jiji=(e,t,r,s,n)=>(Module.dynCall_jiji=wasmExports.dynCall_jiji)(e,t,r,s,n),Module._orig$ts_parser_timeout_micros=e=>(Module._orig$ts_parser_timeout_micros=wasmExports.orig$ts_parser_timeout_micros)(e),Module._orig$ts_parser_set_timeout_micros=(e,t)=>(Module._orig$ts_parser_set_timeout_micros=wasmExports.orig$ts_parser_set_timeout_micros)(e,t),Module.AsciiToString=AsciiToString,Module.stringToUTF16=stringToUTF16;var calledRun;dependenciesFulfilled=function e(){calledRun||run(),calledRun||(dependenciesFulfilled=e)};function callMain(e=[]){var t=resolveGlobalSymbol("main").sym;if(t){e.unshift(thisProgram);var r=e.length,s=stackAlloc((r+1)*4),n=s;e.forEach(a=>{LE_HEAP_STORE_U32((n>>2)*4,stringToUTF8OnStack(a)),n+=4}),LE_HEAP_STORE_U32((n>>2)*4,0);try{var o=t(r,s);return exitJS(o,!0),o}catch(a){return handleException(a)}}}function run(e=arguments_){if(runDependencies>0||(preRun(),runDependencies>0))return;function t(){var r;calledRun||(calledRun=!0,Module.calledRun=!0,!ABORT&&(initRuntime(),preMain(),(r=Module.onRuntimeInitialized)==null||r.call(Module),shouldRunNow&&callMain(e),postRun()))}Module.setStatus?(Module.setStatus("Running..."),setTimeout(function(){setTimeout(function(){Module.setStatus("")},1),t()},1)):t()}if(Module.preInit)for(typeof Module.preInit=="function"&&(Module.preInit=[Module.preInit]);Module.preInit.length>0;)Module.preInit.pop()();var shouldRunNow=!0;Module.noInitialRun&&(shouldRunNow=!1),run();const C=Module,INTERNAL={},SIZE_OF_INT=4,SIZE_OF_CURSOR=4*SIZE_OF_INT,SIZE_OF_NODE=5*SIZE_OF_INT,SIZE_OF_POINT=2*SIZE_OF_INT,SIZE_OF_RANGE=2*SIZE_OF_INT+2*SIZE_OF_POINT,ZERO_POINT={row:0,column:0},QUERY_WORD_REGEX=/[\w-.]*/g,PREDICATE_STEP_TYPE_CAPTURE=1,PREDICATE_STEP_TYPE_STRING=2,LANGUAGE_FUNCTION_REGEX=/^_?tree_sitter_\w+/;let VERSION,MIN_COMPATIBLE_VERSION,TRANSFER_BUFFER,currentParseCallback,currentLogCallback;class ParserImpl{static init(){TRANSFER_BUFFER=C._ts_init(),VERSION=getValue(TRANSFER_BUFFER,"i32"),MIN_COMPATIBLE_VERSION=getValue(TRANSFER_BUFFER+SIZE_OF_INT,"i32")}initialize(){C._ts_parser_new_wasm(),this[0]=getValue(TRANSFER_BUFFER,"i32"),this[1]=getValue(TRANSFER_BUFFER+SIZE_OF_INT,"i32")}delete(){C._ts_parser_delete(this[0]),C._free(this[1]),this[0]=0,this[1]=0}setLanguage(t){let r;if(!t)r=0,t=null;else if(t.constructor===Language){r=t[0];const s=C._ts_language_version(r);if(s<MIN_COMPATIBLE_VERSION||VERSION<s)throw new Error(`Incompatible language version ${s}. Compatibility range ${MIN_COMPATIBLE_VERSION} through ${VERSION}.`)}else throw new Error("Argument must be a Language");return this.language=t,C._ts_parser_set_language(this[0],r),this}getLanguage(){return this.language}parse(t,r,s){if(typeof t=="string")currentParseCallback=(c,_)=>t.slice(c);else if(typeof t=="function")currentParseCallback=t;else throw new Error("Argument must be a string or a function");this.logCallback?(currentLogCallback=this.logCallback,C._ts_parser_enable_logger_wasm(this[0],1)):(currentLogCallback=null,C._ts_parser_enable_logger_wasm(this[0],0));let n=0,o=0;if(s!=null&&s.includedRanges){n=s.includedRanges.length,o=C._calloc(n,SIZE_OF_RANGE);let c=o;for(let _=0;_<n;_++)marshalRange(c,s.includedRanges[_]),c+=SIZE_OF_RANGE}const a=C._ts_parser_parse_wasm(this[0],this[1],r?r[0]:0,o,n);if(!a)throw currentParseCallback=null,currentLogCallback=null,new Error("Parsing failed");const l=new Tree(INTERNAL,a,this.language,currentParseCallback);return currentParseCallback=null,currentLogCallback=null,l}reset(){C._ts_parser_reset(this[0])}getIncludedRanges(){C._ts_parser_included_ranges_wasm(this[0]);const t=getValue(TRANSFER_BUFFER,"i32"),r=getValue(TRANSFER_BUFFER+SIZE_OF_INT,"i32"),s=new Array(t);if(t>0){let n=r;for(let o=0;o<t;o++)s[o]=unmarshalRange(n),n+=SIZE_OF_RANGE;C._free(r)}return s}getTimeoutMicros(){return C._ts_parser_timeout_micros(this[0])}setTimeoutMicros(t){C._ts_parser_set_timeout_micros(this[0],t)}setLogger(t){if(!t)t=null;else if(typeof t!="function")throw new Error("Logger callback must be a function");return this.logCallback=t,this}getLogger(){return this.logCallback}}class Tree{constructor(t,r,s,n){assertInternal(t),this[0]=r,this.language=s,this.textCallback=n}copy(){const t=C._ts_tree_copy(this[0]);return new Tree(INTERNAL,t,this.language,this.textCallback)}delete(){C._ts_tree_delete(this[0]),this[0]=0}edit(t){marshalEdit(t),C._ts_tree_edit_wasm(this[0])}get rootNode(){return C._ts_tree_root_node_wasm(this[0]),unmarshalNode(this)}rootNodeWithOffset(t,r){const s=TRANSFER_BUFFER+SIZE_OF_NODE;return setValue(s,t,"i32"),marshalPoint(s+SIZE_OF_INT,r),C._ts_tree_root_node_with_offset_wasm(this[0]),unmarshalNode(this)}getLanguage(){return this.language}walk(){return this.rootNode.walk()}getChangedRanges(t){if(t.constructor!==Tree)throw new TypeError("Argument must be a Tree");C._ts_tree_get_changed_ranges_wasm(this[0],t[0]);const r=getValue(TRANSFER_BUFFER,"i32"),s=getValue(TRANSFER_BUFFER+SIZE_OF_INT,"i32"),n=new Array(r);if(r>0){let o=s;for(let a=0;a<r;a++)n[a]=unmarshalRange(o),o+=SIZE_OF_RANGE;C._free(s)}return n}getIncludedRanges(){C._ts_tree_included_ranges_wasm(this[0]);const t=getValue(TRANSFER_BUFFER,"i32"),r=getValue(TRANSFER_BUFFER+SIZE_OF_INT,"i32"),s=new Array(t);if(t>0){let n=r;for(let o=0;o<t;o++)s[o]=unmarshalRange(n),n+=SIZE_OF_RANGE;C._free(r)}return s}}class Node{constructor(t,r){assertInternal(t),this.tree=r}get typeId(){return marshalNode(this),C._ts_node_symbol_wasm(this.tree[0])}get grammarId(){return marshalNode(this),C._ts_node_grammar_symbol_wasm(this.tree[0])}get type(){return this.tree.language.types[this.typeId]||"ERROR"}get grammarType(){return this.tree.language.types[this.grammarId]||"ERROR"}get endPosition(){return marshalNode(this),C._ts_node_end_point_wasm(this.tree[0]),unmarshalPoint(TRANSFER_BUFFER)}get endIndex(){return marshalNode(this),C._ts_node_end_index_wasm(this.tree[0])}get text(){return getText(this.tree,this.startIndex,this.endIndex)}get parseState(){return marshalNode(this),C._ts_node_parse_state_wasm(this.tree[0])}get nextParseState(){return marshalNode(this),C._ts_node_next_parse_state_wasm(this.tree[0])}get isNamed(){return marshalNode(this),C._ts_node_is_named_wasm(this.tree[0])===1}get hasError(){return marshalNode(this),C._ts_node_has_error_wasm(this.tree[0])===1}get hasChanges(){return marshalNode(this),C._ts_node_has_changes_wasm(this.tree[0])===1}get isError(){return marshalNode(this),C._ts_node_is_error_wasm(this.tree[0])===1}get isMissing(){return marshalNode(this),C._ts_node_is_missing_wasm(this.tree[0])===1}get isExtra(){return marshalNode(this),C._ts_node_is_extra_wasm(this.tree[0])===1}equals(t){return this.id===t.id}child(t){return marshalNode(this),C._ts_node_child_wasm(this.tree[0],t),unmarshalNode(this.tree)}namedChild(t){return marshalNode(this),C._ts_node_named_child_wasm(this.tree[0],t),unmarshalNode(this.tree)}childForFieldId(t){return marshalNode(this),C._ts_node_child_by_field_id_wasm(this.tree[0],t),unmarshalNode(this.tree)}childForFieldName(t){const r=this.tree.language.fields.indexOf(t);return r!==-1?this.childForFieldId(r):null}fieldNameForChild(t){marshalNode(this);const r=C._ts_node_field_name_for_child_wasm(this.tree[0],t);return r?AsciiToString(r):null}childrenForFieldName(t){const r=this.tree.language.fields.indexOf(t);return r!==-1&&r!==0?this.childrenForFieldId(r):[]}childrenForFieldId(t){marshalNode(this),C._ts_node_children_by_field_id_wasm(this.tree[0],t);const r=getValue(TRANSFER_BUFFER,"i32"),s=getValue(TRANSFER_BUFFER+SIZE_OF_INT,"i32"),n=new Array(r);if(r>0){let o=s;for(let a=0;a<r;a++)n[a]=unmarshalNode(this.tree,o),o+=SIZE_OF_NODE;C._free(s)}return n}firstChildForIndex(t){marshalNode(this);const r=TRANSFER_BUFFER+SIZE_OF_NODE;return setValue(r,t,"i32"),C._ts_node_first_child_for_byte_wasm(this.tree[0]),unmarshalNode(this.tree)}firstNamedChildForIndex(t){marshalNode(this);const r=TRANSFER_BUFFER+SIZE_OF_NODE;return setValue(r,t,"i32"),C._ts_node_first_named_child_for_byte_wasm(this.tree[0]),unmarshalNode(this.tree)}get childCount(){return marshalNode(this),C._ts_node_child_count_wasm(this.tree[0])}get namedChildCount(){return marshalNode(this),C._ts_node_named_child_count_wasm(this.tree[0])}get firstChild(){return this.child(0)}get firstNamedChild(){return this.namedChild(0)}get lastChild(){return this.child(this.childCount-1)}get lastNamedChild(){return this.namedChild(this.namedChildCount-1)}get children(){if(!this._children){marshalNode(this),C._ts_node_children_wasm(this.tree[0]);const t=getValue(TRANSFER_BUFFER,"i32"),r=getValue(TRANSFER_BUFFER+SIZE_OF_INT,"i32");if(this._children=new Array(t),t>0){let s=r;for(let n=0;n<t;n++)this._children[n]=unmarshalNode(this.tree,s),s+=SIZE_OF_NODE;C._free(r)}}return this._children}get namedChildren(){if(!this._namedChildren){marshalNode(this),C._ts_node_named_children_wasm(this.tree[0]);const t=getValue(TRANSFER_BUFFER,"i32"),r=getValue(TRANSFER_BUFFER+SIZE_OF_INT,"i32");if(this._namedChildren=new Array(t),t>0){let s=r;for(let n=0;n<t;n++)this._namedChildren[n]=unmarshalNode(this.tree,s),s+=SIZE_OF_NODE;C._free(r)}}return this._namedChildren}descendantsOfType(t,r,s){Array.isArray(t)||(t=[t]),r||(r=ZERO_POINT),s||(s=ZERO_POINT);const n=[],o=this.tree.language.types;for(let u=0,f=o.length;u<f;u++)t.includes(o[u])&&n.push(u);const a=C._malloc(SIZE_OF_INT*n.length);for(let u=0,f=n.length;u<f;u++)setValue(a+u*SIZE_OF_INT,n[u],"i32");marshalNode(this),C._ts_node_descendants_of_type_wasm(this.tree[0],a,n.length,r.row,r.column,s.row,s.column);const l=getValue(TRANSFER_BUFFER,"i32"),c=getValue(TRANSFER_BUFFER+SIZE_OF_INT,"i32"),_=new Array(l);if(l>0){let u=c;for(let f=0;f<l;f++)_[f]=unmarshalNode(this.tree,u),u+=SIZE_OF_NODE}return C._free(c),C._free(a),_}get nextSibling(){return marshalNode(this),C._ts_node_next_sibling_wasm(this.tree[0]),unmarshalNode(this.tree)}get previousSibling(){return marshalNode(this),C._ts_node_prev_sibling_wasm(this.tree[0]),unmarshalNode(this.tree)}get nextNamedSibling(){return marshalNode(this),C._ts_node_next_named_sibling_wasm(this.tree[0]),unmarshalNode(this.tree)}get previousNamedSibling(){return marshalNode(this),C._ts_node_prev_named_sibling_wasm(this.tree[0]),unmarshalNode(this.tree)}get descendantCount(){return marshalNode(this),C._ts_node_descendant_count_wasm(this.tree[0])}get parent(){return marshalNode(this),C._ts_node_parent_wasm(this.tree[0]),unmarshalNode(this.tree)}descendantForIndex(t,r=t){if(typeof t!="number"||typeof r!="number")throw new Error("Arguments must be numbers");marshalNode(this);const s=TRANSFER_BUFFER+SIZE_OF_NODE;return setValue(s,t,"i32"),setValue(s+SIZE_OF_INT,r,"i32"),C._ts_node_descendant_for_index_wasm(this.tree[0]),unmarshalNode(this.tree)}namedDescendantForIndex(t,r=t){if(typeof t!="number"||typeof r!="number")throw new Error("Arguments must be numbers");marshalNode(this);const s=TRANSFER_BUFFER+SIZE_OF_NODE;return setValue(s,t,"i32"),setValue(s+SIZE_OF_INT,r,"i32"),C._ts_node_named_descendant_for_index_wasm(this.tree[0]),unmarshalNode(this.tree)}descendantForPosition(t,r=t){if(!isPoint(t)||!isPoint(r))throw new Error("Arguments must be {row, column} objects");marshalNode(this);const s=TRANSFER_BUFFER+SIZE_OF_NODE;return marshalPoint(s,t),marshalPoint(s+SIZE_OF_POINT,r),C._ts_node_descendant_for_position_wasm(this.tree[0]),unmarshalNode(this.tree)}namedDescendantForPosition(t,r=t){if(!isPoint(t)||!isPoint(r))throw new Error("Arguments must be {row, column} objects");marshalNode(this);const s=TRANSFER_BUFFER+SIZE_OF_NODE;return marshalPoint(s,t),marshalPoint(s+SIZE_OF_POINT,r),C._ts_node_named_descendant_for_position_wasm(this.tree[0]),unmarshalNode(this.tree)}walk(){return marshalNode(this),C._ts_tree_cursor_new_wasm(this.tree[0]),new TreeCursor(INTERNAL,this.tree)}toString(){marshalNode(this);const t=C._ts_node_to_string_wasm(this.tree[0]),r=AsciiToString(t);return C._free(t),r}}class TreeCursor{constructor(t,r){assertInternal(t),this.tree=r,unmarshalTreeCursor(this)}delete(){marshalTreeCursor(this),C._ts_tree_cursor_delete_wasm(this.tree[0]),this[0]=this[1]=this[2]=0}reset(t){marshalNode(t),marshalTreeCursor(this,TRANSFER_BUFFER+SIZE_OF_NODE),C._ts_tree_cursor_reset_wasm(this.tree[0]),unmarshalTreeCursor(this)}resetTo(t){marshalTreeCursor(this,TRANSFER_BUFFER),marshalTreeCursor(t,TRANSFER_BUFFER+SIZE_OF_CURSOR),C._ts_tree_cursor_reset_to_wasm(this.tree[0],t.tree[0]),unmarshalTreeCursor(this)}get nodeType(){return this.tree.language.types[this.nodeTypeId]||"ERROR"}get nodeTypeId(){return marshalTreeCursor(this),C._ts_tree_cursor_current_node_type_id_wasm(this.tree[0])}get nodeStateId(){return marshalTreeCursor(this),C._ts_tree_cursor_current_node_state_id_wasm(this.tree[0])}get nodeId(){return marshalTreeCursor(this),C._ts_tree_cursor_current_node_id_wasm(this.tree[0])}get nodeIsNamed(){return marshalTreeCursor(this),C._ts_tree_cursor_current_node_is_named_wasm(this.tree[0])===1}get nodeIsMissing(){return marshalTreeCursor(this),C._ts_tree_cursor_current_node_is_missing_wasm(this.tree[0])===1}get nodeText(){marshalTreeCursor(this);const t=C._ts_tree_cursor_start_index_wasm(this.tree[0]),r=C._ts_tree_cursor_end_index_wasm(this.tree[0]);return getText(this.tree,t,r)}get startPosition(){return marshalTreeCursor(this),C._ts_tree_cursor_start_position_wasm(this.tree[0]),unmarshalPoint(TRANSFER_BUFFER)}get endPosition(){return marshalTreeCursor(this),C._ts_tree_cursor_end_position_wasm(this.tree[0]),unmarshalPoint(TRANSFER_BUFFER)}get startIndex(){return marshalTreeCursor(this),C._ts_tree_cursor_start_index_wasm(this.tree[0])}get endIndex(){return marshalTreeCursor(this),C._ts_tree_cursor_end_index_wasm(this.tree[0])}get currentNode(){return marshalTreeCursor(this),C._ts_tree_cursor_current_node_wasm(this.tree[0]),unmarshalNode(this.tree)}get currentFieldId(){return marshalTreeCursor(this),C._ts_tree_cursor_current_field_id_wasm(this.tree[0])}get currentFieldName(){return this.tree.language.fields[this.currentFieldId]}get currentDepth(){return marshalTreeCursor(this),C._ts_tree_cursor_current_depth_wasm(this.tree[0])}get currentDescendantIndex(){return marshalTreeCursor(this),C._ts_tree_cursor_current_descendant_index_wasm(this.tree[0])}gotoFirstChild(){marshalTreeCursor(this);const t=C._ts_tree_cursor_goto_first_child_wasm(this.tree[0]);return unmarshalTreeCursor(this),t===1}gotoLastChild(){marshalTreeCursor(this);const t=C._ts_tree_cursor_goto_last_child_wasm(this.tree[0]);return unmarshalTreeCursor(this),t===1}gotoFirstChildForIndex(t){marshalTreeCursor(this),setValue(TRANSFER_BUFFER+SIZE_OF_CURSOR,t,"i32");const r=C._ts_tree_cursor_goto_first_child_for_index_wasm(this.tree[0]);return unmarshalTreeCursor(this),r===1}gotoFirstChildForPosition(t){marshalTreeCursor(this),marshalPoint(TRANSFER_BUFFER+SIZE_OF_CURSOR,t);const r=C._ts_tree_cursor_goto_first_child_for_position_wasm(this.tree[0]);return unmarshalTreeCursor(this),r===1}gotoNextSibling(){marshalTreeCursor(this);const t=C._ts_tree_cursor_goto_next_sibling_wasm(this.tree[0]);return unmarshalTreeCursor(this),t===1}gotoPreviousSibling(){marshalTreeCursor(this);const t=C._ts_tree_cursor_goto_previous_sibling_wasm(this.tree[0]);return unmarshalTreeCursor(this),t===1}gotoDescendant(t){marshalTreeCursor(this),C._ts_tree_cursor_goto_descendant_wasm(this.tree[0],t),unmarshalTreeCursor(this)}gotoParent(){marshalTreeCursor(this);const t=C._ts_tree_cursor_goto_parent_wasm(this.tree[0]);return unmarshalTreeCursor(this),t===1}}class Language{constructor(t,r){assertInternal(t),this[0]=r,this.types=new Array(C._ts_language_symbol_count(this[0]));for(let s=0,n=this.types.length;s<n;s++)C._ts_language_symbol_type(this[0],s)<2&&(this.types[s]=UTF8ToString(C._ts_language_symbol_name(this[0],s)));this.fields=new Array(C._ts_language_field_count(this[0])+1);for(let s=0,n=this.fields.length;s<n;s++){const o=C._ts_language_field_name_for_id(this[0],s);o!==0?this.fields[s]=UTF8ToString(o):this.fields[s]=null}}get version(){return C._ts_language_version(this[0])}get fieldCount(){return this.fields.length-1}get stateCount(){return C._ts_language_state_count(this[0])}fieldIdForName(t){const r=this.fields.indexOf(t);return r!==-1?r:null}fieldNameForId(t){return this.fields[t]||null}idForNodeType(t,r){const s=lengthBytesUTF8(t),n=C._malloc(s+1);stringToUTF8(t,n,s+1);const o=C._ts_language_symbol_for_name(this[0],n,s,r);return C._free(n),o||null}get nodeTypeCount(){return C._ts_language_symbol_count(this[0])}nodeTypeForId(t){const r=C._ts_language_symbol_name(this[0],t);return r?UTF8ToString(r):null}nodeTypeIsNamed(t){return!!C._ts_language_type_is_named_wasm(this[0],t)}nodeTypeIsVisible(t){return!!C._ts_language_type_is_visible_wasm(this[0],t)}nextState(t,r){return C._ts_language_next_state(this[0],t,r)}lookaheadIterator(t){const r=C._ts_lookahead_iterator_new(this[0],t);return r?new LookaheadIterable(INTERNAL,r,this):null}query(t){const r=lengthBytesUTF8(t),s=C._malloc(r+1);stringToUTF8(t,s,r+1);const n=C._ts_query_new(this[0],s,r,TRANSFER_BUFFER,TRANSFER_BUFFER+SIZE_OF_INT);if(!n){const b=getValue(TRANSFER_BUFFER+SIZE_OF_INT,"i32"),P=getValue(TRANSFER_BUFFER,"i32"),I=UTF8ToString(s,P).length,E=t.substr(I,100).split(`
`)[0];let O=E.match(QUERY_WORD_REGEX)[0],R;switch(b){case 2:R=new RangeError(`Bad node name '${O}'`);break;case 3:R=new RangeError(`Bad field name '${O}'`);break;case 4:R=new RangeError(`Bad capture name @${O}`);break;case 5:R=new TypeError(`Bad pattern structure at offset ${I}: '${E}'...`),O="";break;default:R=new SyntaxError(`Bad syntax at offset ${I}: '${E}'...`),O="";break}throw R.index=I,R.length=O.length,C._free(s),R}const o=C._ts_query_string_count(n),a=C._ts_query_capture_count(n),l=C._ts_query_pattern_count(n),c=new Array(a),_=new Array(o);for(let b=0;b<a;b++){const P=C._ts_query_capture_name_for_id(n,b,TRANSFER_BUFFER),I=getValue(TRANSFER_BUFFER,"i32");c[b]=UTF8ToString(P,I)}for(let b=0;b<o;b++){const P=C._ts_query_string_value_for_id(n,b,TRANSFER_BUFFER),I=getValue(TRANSFER_BUFFER,"i32");_[b]=UTF8ToString(P,I)}const u=new Array(l),f=new Array(l),m=new Array(l),g=new Array(l),w=new Array(l);for(let b=0;b<l;b++){const P=C._ts_query_predicates_for_pattern(n,b,TRANSFER_BUFFER),I=getValue(TRANSFER_BUFFER,"i32");g[b]=[],w[b]=[];const E=[];let O=P;for(let R=0;R<I;R++){const L=getValue(O,"i32");O+=SIZE_OF_INT;const H=getValue(O,"i32");if(O+=SIZE_OF_INT,L===PREDICATE_STEP_TYPE_CAPTURE)E.push({type:"capture",name:c[H]});else if(L===PREDICATE_STEP_TYPE_STRING)E.push({type:"string",value:_[H]});else if(E.length>0){if(E[0].type!=="string")throw new Error("Predicates must begin with a literal value");const k=E[0].value;let W=!0,K=!0,z;switch(k){case"any-not-eq?":case"not-eq?":W=!1;case"any-eq?":case"eq?":if(E.length!==3)throw new Error(`Wrong number of arguments to \`#${k}\` predicate. Expected 2, got ${E.length-1}`);if(E[1].type!=="capture")throw new Error(`First argument of \`#${k}\` predicate must be a capture. Got "${E[1].value}"`);if(K=!k.startsWith("any-"),E[2].type==="capture"){const J=E[1].name,ee=E[2].name;w[b].push(Z=>{const U=[],V=[];for(const Y of Z)Y.name===J&&U.push(Y.node),Y.name===ee&&V.push(Y.node);const se=(Y,re,ae)=>ae?Y.text===re.text:Y.text!==re.text;return K?U.every(Y=>V.some(re=>se(Y,re,W))):U.some(Y=>V.some(re=>se(Y,re,W)))})}else{z=E[1].name;const J=E[2].value,ee=U=>U.text===J,Z=U=>U.text!==J;w[b].push(U=>{const V=[];for(const Y of U)Y.name===z&&V.push(Y.node);const se=W?ee:Z;return K?V.every(se):V.some(se)})}break;case"any-not-match?":case"not-match?":W=!1;case"any-match?":case"match?":if(E.length!==3)throw new Error(`Wrong number of arguments to \`#${k}\` predicate. Expected 2, got ${E.length-1}.`);if(E[1].type!=="capture")throw new Error(`First argument of \`#${k}\` predicate must be a capture. Got "${E[1].value}".`);if(E[2].type!=="string")throw new Error(`Second argument of \`#${k}\` predicate must be a string. Got @${E[2].value}.`);z=E[1].name;const te=new RegExp(E[2].value);K=!k.startsWith("any-"),w[b].push(J=>{const ee=[];for(const U of J)U.name===z&&ee.push(U.node.text);const Z=(U,V)=>V?te.test(U):!te.test(U);return ee.length===0?!W:K?ee.every(U=>Z(U,W)):ee.some(U=>Z(U,W))});break;case"set!":if(E.length<2||E.length>3)throw new Error(`Wrong number of arguments to \`#set!\` predicate. Expected 1 or 2. Got ${E.length-1}.`);if(E.some(J=>J.type!=="string"))throw new Error('Arguments to `#set!` predicate must be a strings.".');u[b]||(u[b]={}),u[b][E[1].value]=E[2]?E[2].value:null;break;case"is?":case"is-not?":if(E.length<2||E.length>3)throw new Error(`Wrong number of arguments to \`#${k}\` predicate. Expected 1 or 2. Got ${E.length-1}.`);if(E.some(J=>J.type!=="string"))throw new Error(`Arguments to \`#${k}\` predicate must be a strings.".`);const X=k==="is?"?f:m;X[b]||(X[b]={}),X[b][E[1].value]=E[2]?E[2].value:null;break;case"not-any-of?":W=!1;case"any-of?":if(E.length<2)throw new Error(`Wrong number of arguments to \`#${k}\` predicate. Expected at least 1. Got ${E.length-1}.`);if(E[1].type!=="capture")throw new Error(`First argument of \`#${k}\` predicate must be a capture. Got "${E[1].value}".`);for(let J=2;J<E.length;J++)if(E[J].type!=="string")throw new Error(`Arguments to \`#${k}\` predicate must be a strings.".`);z=E[1].name;const le=E.slice(2).map(J=>J.value);w[b].push(J=>{const ee=[];for(const Z of J)Z.name===z&&ee.push(Z.node.text);return ee.length===0?!W:ee.every(Z=>le.includes(Z))===W});break;default:g[b].push({operator:k,operands:E.slice(1)})}E.length=0}}Object.freeze(u[b]),Object.freeze(f[b]),Object.freeze(m[b])}return C._free(s),new Query(INTERNAL,n,c,w,g,Object.freeze(u),Object.freeze(f),Object.freeze(m))}static load(t){let r;if(t instanceof Uint8Array)r=Promise.resolve(t);else{const s=t;if(typeof process<"u"&&process.versions&&process.versions.node){const n=require$$0;r=Promise.resolve(n.readFileSync(s))}else r=fetch(s).then(n=>n.arrayBuffer().then(o=>{if(n.ok)return new Uint8Array(o);{const a=new TextDecoder("utf-8").decode(o);throw new Error(`Language.load failed with status ${n.status}.

${a}`)}}))}return r.then(s=>loadWebAssemblyModule(s,{loadAsync:!0})).then(s=>{const n=Object.keys(s),o=n.find(l=>LANGUAGE_FUNCTION_REGEX.test(l)&&!l.includes("external_scanner_"));o||console.log(`Couldn't find language function in WASM file. Symbols:
${JSON.stringify(n,null,2)}`);const a=s[o]();return new Language(INTERNAL,a)})}}class LookaheadIterable{constructor(t,r,s){assertInternal(t),this[0]=r,this.language=s}get currentTypeId(){return C._ts_lookahead_iterator_current_symbol(this[0])}get currentType(){return this.language.types[this.currentTypeId]||"ERROR"}delete(){C._ts_lookahead_iterator_delete(this[0]),this[0]=0}resetState(t){return C._ts_lookahead_iterator_reset_state(this[0],t)}reset(t,r){return C._ts_lookahead_iterator_reset(this[0],t[0],r)?(this.language=t,!0):!1}[Symbol.iterator](){const t=this;return{next(){return C._ts_lookahead_iterator_next(t[0])?{done:!1,value:t.currentType}:{done:!0,value:""}}}}}class Query{constructor(t,r,s,n,o,a,l,c){assertInternal(t),this[0]=r,this.captureNames=s,this.textPredicates=n,this.predicates=o,this.setProperties=a,this.assertedProperties=l,this.refutedProperties=c,this.exceededMatchLimit=!1}delete(){C._ts_query_delete(this[0]),this[0]=0}matches(t,{startPosition:r=ZERO_POINT,endPosition:s=ZERO_POINT,startIndex:n=0,endIndex:o=0,matchLimit:a=4294967295,maxStartDepth:l=4294967295,timeoutMicros:c=0}={}){if(typeof a!="number")throw new Error("Arguments must be numbers");marshalNode(t),C._ts_query_matches_wasm(this[0],t.tree[0],r.row,r.column,s.row,s.column,n,o,a,l,c);const _=getValue(TRANSFER_BUFFER,"i32"),u=getValue(TRANSFER_BUFFER+SIZE_OF_INT,"i32"),f=getValue(TRANSFER_BUFFER+2*SIZE_OF_INT,"i32"),m=new Array(_);this.exceededMatchLimit=!!f;let g=0,w=u;for(let b=0;b<_;b++){const P=getValue(w,"i32");w+=SIZE_OF_INT;const I=getValue(w,"i32");w+=SIZE_OF_INT;const E=new Array(I);if(w=unmarshalCaptures(this,t.tree,w,E),this.textPredicates[P].every(O=>O(E))){m[g]={pattern:P,captures:E};const O=this.setProperties[P];O&&(m[g].setProperties=O);const R=this.assertedProperties[P];R&&(m[g].assertedProperties=R);const L=this.refutedProperties[P];L&&(m[g].refutedProperties=L),g++}}return m.length=g,C._free(u),m}captures(t,{startPosition:r=ZERO_POINT,endPosition:s=ZERO_POINT,startIndex:n=0,endIndex:o=0,matchLimit:a=4294967295,maxStartDepth:l=4294967295,timeoutMicros:c=0}={}){if(typeof a!="number")throw new Error("Arguments must be numbers");marshalNode(t),C._ts_query_captures_wasm(this[0],t.tree[0],r.row,r.column,s.row,s.column,n,o,a,l,c);const _=getValue(TRANSFER_BUFFER,"i32"),u=getValue(TRANSFER_BUFFER+SIZE_OF_INT,"i32"),f=getValue(TRANSFER_BUFFER+2*SIZE_OF_INT,"i32"),m=[];this.exceededMatchLimit=!!f;const g=[];let w=u;for(let b=0;b<_;b++){const P=getValue(w,"i32");w+=SIZE_OF_INT;const I=getValue(w,"i32");w+=SIZE_OF_INT;const E=getValue(w,"i32");if(w+=SIZE_OF_INT,g.length=I,w=unmarshalCaptures(this,t.tree,w,g),this.textPredicates[P].every(O=>O(g))){const O=g[E],R=this.setProperties[P];R&&(O.setProperties=R);const L=this.assertedProperties[P];L&&(O.assertedProperties=L);const H=this.refutedProperties[P];H&&(O.refutedProperties=H),m.push(O)}}return C._free(u),m}predicatesForPattern(t){return this.predicates[t]}disableCapture(t){const r=lengthBytesUTF8(t),s=C._malloc(r+1);stringToUTF8(t,s,r+1),C._ts_query_disable_capture(this[0],s,r),C._free(s)}didExceedMatchLimit(){return this.exceededMatchLimit}}function getText(e,t,r){const s=r-t;let n=e.textCallback(t,null,r);for(t+=n.length;t<r;){const o=e.textCallback(t,null,r);if(o&&o.length>0)t+=o.length,n+=o;else break}return t>r&&(n=n.slice(0,s)),n}function unmarshalCaptures(e,t,r,s){for(let n=0,o=s.length;n<o;n++){const a=getValue(r,"i32");r+=SIZE_OF_INT;const l=unmarshalNode(t,r);r+=SIZE_OF_NODE,s[n]={name:e.captureNames[a],node:l}}return r}function assertInternal(e){if(e!==INTERNAL)throw new Error("Illegal constructor")}function isPoint(e){return e&&typeof e.row=="number"&&typeof e.column=="number"}function marshalNode(e){let t=TRANSFER_BUFFER;setValue(t,e.id,"i32"),t+=SIZE_OF_INT,setValue(t,e.startIndex,"i32"),t+=SIZE_OF_INT,setValue(t,e.startPosition.row,"i32"),t+=SIZE_OF_INT,setValue(t,e.startPosition.column,"i32"),t+=SIZE_OF_INT,setValue(t,e[0],"i32")}function unmarshalNode(e,t=TRANSFER_BUFFER){const r=getValue(t,"i32");if(t+=SIZE_OF_INT,r===0)return null;const s=getValue(t,"i32");t+=SIZE_OF_INT;const n=getValue(t,"i32");t+=SIZE_OF_INT;const o=getValue(t,"i32");t+=SIZE_OF_INT;const a=getValue(t,"i32"),l=new Node(INTERNAL,e);return l.id=r,l.startIndex=s,l.startPosition={row:n,column:o},l[0]=a,l}function marshalTreeCursor(e,t=TRANSFER_BUFFER){setValue(t+0*SIZE_OF_INT,e[0],"i32"),setValue(t+1*SIZE_OF_INT,e[1],"i32"),setValue(t+2*SIZE_OF_INT,e[2],"i32"),setValue(t+3*SIZE_OF_INT,e[3],"i32")}function unmarshalTreeCursor(e){e[0]=getValue(TRANSFER_BUFFER+0*SIZE_OF_INT,"i32"),e[1]=getValue(TRANSFER_BUFFER+1*SIZE_OF_INT,"i32"),e[2]=getValue(TRANSFER_BUFFER+2*SIZE_OF_INT,"i32"),e[3]=getValue(TRANSFER_BUFFER+3*SIZE_OF_INT,"i32")}function marshalPoint(e,t){setValue(e,t.row,"i32"),setValue(e+SIZE_OF_INT,t.column,"i32")}function unmarshalPoint(e){return{row:getValue(e,"i32")>>>0,column:getValue(e+SIZE_OF_INT,"i32")>>>0}}function marshalRange(e,t){marshalPoint(e,t.startPosition),e+=SIZE_OF_POINT,marshalPoint(e,t.endPosition),e+=SIZE_OF_POINT,setValue(e,t.startIndex,"i32"),e+=SIZE_OF_INT,setValue(e,t.endIndex,"i32"),e+=SIZE_OF_INT}function unmarshalRange(e){const t={};return t.startPosition=unmarshalPoint(e),e+=SIZE_OF_POINT,t.endPosition=unmarshalPoint(e),e+=SIZE_OF_POINT,t.startIndex=getValue(e,"i32")>>>0,e+=SIZE_OF_INT,t.endIndex=getValue(e,"i32")>>>0,t}function marshalEdit(e){let t=TRANSFER_BUFFER;marshalPoint(t,e.startPosition),t+=SIZE_OF_POINT,marshalPoint(t,e.oldEndPosition),t+=SIZE_OF_POINT,marshalPoint(t,e.newEndPosition),t+=SIZE_OF_POINT,setValue(t,e.startIndex,"i32"),t+=SIZE_OF_INT,setValue(t,e.oldEndIndex,"i32"),t+=SIZE_OF_INT,setValue(t,e.newEndIndex,"i32"),t+=SIZE_OF_INT}for(const e of Object.getOwnPropertyNames(ParserImpl.prototype))Object.defineProperty(Parser.prototype,e,{value:ParserImpl.prototype[e],enumerable:!1,writable:!1});Parser.Language=Language,Module.onRuntimeInitialized=()=>{ParserImpl.init(),resolveInitPromise()}}))}}return Parser}();module.exports=TreeSitter})(treeSitter);var treeSitterExports=treeSitter.exports;const Parser=getDefaultExportFromCjs(treeSitterExports);class UpdateObject{constructor(t,r,s,n){this.packageSuffix=t,this.objectType=r,this.objectName=s,this.simpleText=n}getPackageSuffix(){return this.packageSuffix}getObjectType(){return this.objectType}getObjectName(){return this.objectName}getSimpleText(){return this.simpleText}}class GoModInfo{constructor(t,r){this._moduleDirPath=t,this._moduleName=r}get moduleDirPath(){return this._moduleDirPath}get moduleName(){return this._moduleName}}class CodeObjectIndexSystem{constructor(){_e(this,"pkgObjSet",new Set);_e(this,"goModMap",new Map)}checkObjectInPackage(t,r){console.log(`queryObjectInPackage,packagePath: ${t}, objectName: ${r}`);const s=`${t}|${r}`;return this.pkgObjSet.has(s)}checkJsFileExists(t){console.log(`checkJsFileExists,filePath: ${t}`);const r=`${t}|`;return this.pkgObjSet.has(r)}updateGoModMap(t,r){this.goModMap.set(r,t)}findBelongModule(t){let r="",s="";for(const[n,o]of this.goModMap.entries())t.startsWith(n)&&n.length>r.length&&(r=n,s=o);return s!==""&&s.length>0?new GoModInfo(r,s):null}updateByFile(t,r,s,n){if(!(!Array.isArray(n)||n.length<=0)){console.log(`updateCodeObjectIndexSystem,packagePath: ${r}, fileName: ${s}, objectsToUpdate:`+JSON.stringify(n,null,2));for(const o of n){const a=`${r}|${o.objectName}`;this.pkgObjSet.add(a)}window.treeSitterQuery({request:JSON.stringify({type:"updateCodeObjectIndexSystem",language:t,packagePath:r,fileName:s,objectsToUpdate:n}),persistent:!1,onSuccess:function(o){console.log(`updateCodeObjectIndexSystem onSuccess,response: ${o}`)},onFailure:function(o,a){console.log(`updateCodeObjectIndexSystem,error_code:${o},error_message:${a}`)}})}}queryByPackageAndObjectName(t,r,s){return console.log(`return relative result,packagePath: ${r}, objectName: ${s}`),window.treeSitterQuery({request:JSON.stringify({type:"findRelativeObjectReturned",language:t,packagePath:r,objectName:s}),persistent:!1,onSuccess:function(n){return console.log(`queryByPackageAndObjectName onSuccess,response: ${n}`),n},onFailure:function(n,o){return console.log(`queryByPackageAndObjectName,error_code:${n},error_message:${o}`),null}})}}class MessageSender{static sendErrorMessage(t){window.treeSitterQuery({request:JSON.stringify({type:"error",errorMessage:t}),persistent:!1,onSuccess:function(r){},onFailure:function(r,s){console.log(`TreeSitter Webview error,error_code:${r},error_message:${s}`)}})}}class JavaParser{constructor(t,r){_e(this,"parser");_e(this,"objectSystem");this.parser=t,this.objectSystem=r}ParseFile(t,r,s){const n=this.parseJavaCode(r,s,void 0);return n?(this.objectSystem.updateByFile(t,n.packageName,n.path,n.cacheObjects),n):(console.log("JavaParser ParseFile skip,code file parse is null."),null)}FindRelativeObject(t,r,s,n,o,a){var L;console.log(`JavaParser FindRelativeObject path:${r}`);const l=this.getRootNode(s);if(!l)return console.log("JavaParser FindRelativeObject failed, rootNode is null"),null;const c=this.parseJavaCode(r,s,l);if(!c)return console.log("JavaParser FindRelativeObject skip,code file parse is null."),null;const _=["identifier","type_identifier"],u=l.descendantsOfType(_,{row:n,column:0},{row:n,column:o});let f,m="";u.length?(f=u[u.length-1],m=f.text.trim()):f=l.descendantForPosition({row:n,column:o});const g=[];for(;f;)g.push(f),f=f.parent;if(g.length===0)return console.log("JavaParser FindRelativeObject skip,nodeList is empty."),null;const w=g[0];if(!w)return console.log("JavaParser FindRelativeObject skip,rootNode is null."),null;let b=null,P=0;for(let H=0;H<g.length;H++){const k=g[H];if(k.type==="method_declaration"||k.type==="constructor_declaration"){b=k,P=H;break}}if(!b)return console.log("JavaParser FindRelativeObject skip,cursor is not in method."),null;let I="",E="",O=null;for(let H=P;H<g.length;H++){const k=g[H];if(k.type==="class_declaration"){const W=k.childForFieldName("superclass");if(W){const z=this.firstDescendantOfType(W,"type_identifier");z&&(m=z.text.trim())}const K=this.firstDescendantOfType(k,"identifier");if(K){const z=K.text.trim();if(O=c.fullObjects.find(te=>te.objectName===z),O)break}}}if(!m)return console.log("JavaParser FindRelativeObject skip,identifier is null."),null;if(O){for(const H of O.fields)if(m===H.fieldVariable){I=H.fieldPackage,E=H.fieldType;break}}const R=[...c.importPackages,c.packageName+".*"];if(!I){const H=this.getMethodVariableDeclarations(R,b,w);H[m]&&(I=H[m].packageName,E=H[m].typeName)}return I||(I=(L=this.getObjectDeclaration(R,m))==null?void 0:L.packageName,E=m),I?this.objectSystem.queryByPackageAndObjectName(t,I,E):(console.log("JavaParser FindRelativeObject fail,targetPackageName is null."),null)}parseJavaCode(t,r,s){if(console.log(`JavaParser ParseFile path：${t}`),!s){const c=this.getRootNode(r);if(!c)return null;s=c}let n="";const o=[],a=[],l=[];for(let c=0;c<s.childCount;c++){const _=s.child(c);if(_)switch(_.type){case"package_declaration":{const u=this.firstDescendantOfType(_,["scoped_identifier","identifier"]);if(u)n=u.text;else return console.log("JavaParser ParseFile package_declaration skip,packageIdentifier is null."),null;break}case"import_declaration":{let u="";for(const f of _.children)if(f.isNamed){switch(f.type){case"identifier":case"scoped_identifier":u+=f.text;break;case"asterisk":u+=".*";break;default:u=""}if(!u)break}u&&o.push(u);break}case"class_declaration":case"interface_declaration":case"enum_declaration":{const u=this.firstDescendantOfType(_,"identifier");if(u)for(const f of[{packageSuffix:"",nodeIdentifierName:u.text,node:_},...this.descendantsOfType(_,["class_declaration","interface_declaration","enum_declaration"],u.text)]){const m=f.node,g=f.nodeIdentifierName,w=m.type,[b,P]=this.extractJavaObject(m,o),I={objectName:g,objectType:w,simpleText:b,fields:P};a.push(I),l.push(new UpdateObject(f.packageSuffix,w,g,b))}break}}}return n?{path:t,packageName:n,importPackages:o,fullObjects:a,cacheObjects:l}:(console.log("JavaParser ParseFile skip,parse packageName fail."),null)}getRootNode(t){if(!this.parser)return null;try{return this.parser.parse(t).rootNode}catch(r){throw new Error(`Parsing error: ${r}`)}}firstDescendantOfType(t,r){Array.isArray(r)||(r=[r]);for(const s of t.children)if(r.includes(s.type))return s;return null}childrenOfType(t,r){const s=[];Array.isArray(r)||(r=[r]);for(const n of t.children)r.includes(n.type)&&s.push(n);return s}descendantsOfType(t,r,s=""){const n=[];Array.isArray(r)||(r=[r]);for(const o of t.children){let a=s;if(r.includes(o.type)){const l=this.firstDescendantOfType(o,"identifier");l&&(n.push({packageSuffix:"."+s,node:o,nodeIdentifierName:l.text}),a=s+"."+l.text)}n.push(...this.descendantsOfType(o,r,a))}return n}extractJavaObject(t,r){const s=[];return[this.extractNodesSimpleText(t.children,!1,r,s),s]}extractNodesSimpleText(t,r=!1,s=[],n=[]){let o="";for(const a of t)switch(a.type){case"modifiers":case"class":case"identifier":case"superclass":case"super_interfaces":o+=a.text.trim()+" ";break;case"class_body":case"interface_body":o+=this.extractNodesSimpleText(a.children,!1,s,n);break;case"enum_body":{o+=`{
`;const l=this.childrenOfType(a,"enum_constant");for(let _=0;_<l.length;_++)_===l.length-1?o+="	"+l[_].text.trim()+`;
`:o+="	"+l[_].text.trim()+`,
`;const c=this.childrenOfType(a,"enum_body_declarations");for(const _ of c)o+=this.extractNodesSimpleText(_.children,!1,s,n);o+=`
}`;break}case"{":case"}":o+=a.type+`
`;break;case"field_declaration":this.isPrivateModifiers(a)||(n.push(this.extractFields(a,s)),o+="	"+a.text.trim()+`
`);break;case"block":case"constructor_body":o+=`{}
`;break;case"method_declaration":case"constructor_declaration":this.isPrivateModifiers(a)||(o+="	",o+=this.extractNodesSimpleText(a.children,!0));break;default:(r||!a.isNamed)&&(o+=a.text.trim()+" ")}return o}isPrivateModifiers(t){var r;return(r=this.childrenOfType(t,"modifiers")[0])==null?void 0:r.text.includes("private")}extractFields(t,r){let s="",n="",o="";const a=this.firstDescendantOfType(t,"variable_declarator");if(a){const _=this.firstDescendantOfType(a,"identifier");_&&(s=_.text.trim())}const l=this.firstDescendantOfType(t,"type_identifier");l&&(o=l.text.trim());const c=this.getObjectDeclaration(r,o);return c&&(n=c.packageName,o=c.typeName),{fieldPackage:n,fieldType:o,fieldVariable:s}}getPositionNodeList(t,r,s,n){const o=[],a=t.descendantsOfType(n,{row:r,column:0},{row:r,column:s});if(a.length){let l=a[a.length-1];for(;l;)o.push(l),l=l.parent}return o}getMethodVariableDeclarations(t,r,s){let n={};for(const o of r.descendantsOfType(["formal_parameter","local_variable_declaration","enhanced_for_statement","catch_formal_parameter"],r.startPosition,s.startPosition))n={...n,...this.getNodeVariableDeclaration(o,t)};return n}getNodeVariableDeclaration(t,r){var a,l,c,_;const s={};let n=null,o;switch(t.type){case"formal_parameter":case"local_variable_declaration":{n=t.childForFieldName("type");const u=t.childForFieldName("declarator");u?o=(a=u.childForFieldName("name"))==null?void 0:a.text:o=(l=t.childForFieldName("name"))==null?void 0:l.text;break}case"enhanced_for_statement":{n=t.childForFieldName("type"),o=(c=t.childForFieldName("name"))==null?void 0:c.text;break}case"catch_formal_parameter":{for(const u of t.children)u.type==="catch_type"&&(n=u.firstNamedChild);o=(_=t.childForFieldName("name"))==null?void 0:_.text;break}default:return s}if(n&&o){let u="";switch(n.type){case"type_identifier":case"scoped_type_identifier":u=n.text.trim();break;case"generic_type":{const f=n.descendantsOfType("type_identifier");f.length&&(u=f[0].text.trim());break}}if(u){const f=this.getObjectDeclaration(r,u);f&&(s[o]=f)}}return s}getObjectDeclaration(t,r){const s=this.getValidPackageAndObjectName(r);if(s)return s;for(const n of t){const[o,a]=this.splitLastDot(n);if(a)if(a==="*"){const l=this.getValidPackageAndObjectName(o+"."+r);if(l)return l}else{let l=r;const c=r.indexOf(".");let _=n;if(c>-1&&(l=r.slice(0,c),_=n+"."+r.slice(c+1)),a!==l)continue;const u=this.getValidPackageAndObjectName(_);if(u)return u}}return null}getValidPackageAndObjectName(t){const r=this.splitLastDot(t);return r.length>1&&this.objectSystem.checkObjectInPackage(r[0],r[1])?{packageName:r[0],typeName:r[1]}:null}splitLastDot(t){const r=t.lastIndexOf(".");return r===-1?[t]:[t.slice(0,r),t.slice(r+1)]}}class GoParser{constructor(t,r){_e(this,"parser");_e(this,"objectSystem");this.parser=t,this.objectSystem=r}ParseFile(t,r,s){const n=this.parseGoCode(r,s,void 0,void 0);return n?(this.objectSystem.updateByFile(t,n.packagePath,n.path,n.cacheObjects),n):(console.log("GoParser ParseFile skip,code file parse is null."),null)}FindRelativeObject(t,r,s,n,o,a){var b,P,I;console.log(`GoParser FindRelativeObject path:${r}`);const l=this.getRootNode(s);if(!l)return console.log("FindRelativeObject failed, rootNode is null"),null;if(!a||a.length===0)return console.warn("FindRelativeObject failed, goModMapJson is null"),null;let c,_;try{c=JSON.parse(a),_=new Map(Object.entries(c))}catch{return console.error("FindRelativeObject error, parse goModMap fail."),null}if(_===null||_.size===0)return console.warn("FindRelativeObject failed, goModMap is null"),null;const u=this.parseGoCode(r,s,l,_);if(!u)return console.log("GoParser FindRelativeObject skip,code file parse is null."),null;let f,m=null,g=null;if(f=this.findLastNodeOfType(l,"type_identifier",n,o),f){m=f.text.trim();const E=this.getPackageIdentifierBy(f);if(E)return g=((b=u.importGoPackages.find(O=>O.packageName===E||O.alias===E))==null?void 0:b.importPath)??"",g?this.objectSystem.queryByPackageAndObjectName(t,g,m):(console.log(`currFile.importGoPackages cannot find ：${E}`),null);{const O=((P=u.importGoPackages)==null?void 0:P.filter(R=>R.isDot).map(R=>R.importPath))||[];if(O.length>0){for(const R of O)if(this.objectSystem.checkObjectInPackage(R,m))return this.objectSystem.queryByPackageAndObjectName(t,R,m)}return this.objectSystem.queryByPackageAndObjectName(t,u.packagePath,m)}}const w=this.findLastNodeOfType(l,"identifier",n,o);if(w){const E=[];let O=w;for(;O;)E.push(O),O=O.parent;if(E.length===0)return console.log("GoParser FindRelativeObject skip,nodeList is empty."),null;let R=null;for(let k=0;k<E.length;k++){const W=E[k];if(W.type==="function_declaration"){R=W;break}}if(!R)return console.log("GoParser FindRelativeObject skip,cursor is not in function."),null;const[L,H]=this.getTypeDeclarationByIdentifier(w,R);if(f=H,L&&f){const k=f.text.trim();if(u.fullObjects.find(K=>K.objectName===k))return this.firstDescendantOfType(w,"field_identifier")?this.objectSystem.queryByPackageAndObjectName(t,u.packagePath,k):this.objectSystem.queryByPackageAndObjectName(t,u.packagePath,k);if(this.objectSystem.checkObjectInPackage(u.packagePath,k))return this.objectSystem.queryByPackageAndObjectName(t,u.packagePath,k);for(const K of u.importGoPackages)if(this.objectSystem.checkObjectInPackage(K.importPath,k))return this.objectSystem.queryByPackageAndObjectName(t,K.importPath,k);return null}else if(L&&!f){const k=w.text;return console.log(`isFoundRelativeId is true,relativeTypeNode is null.  relativeIdentifierNodeText：${k}`),null}if(g=((I=u.importGoPackages.find(k=>k.packageName===w.text||k.alias===w.text))==null?void 0:I.importPath)??"",g)return this.objectSystem.queryByPackageAndObjectName(t,g,"")}return null}parseGoCode(t,r,s,n){if(console.log(`GoParser ParseFile fileName：${t}`),!s){const _=this.getRootNode(r);if(!_)return null;s=_}var o=t.substring(0,t.lastIndexOf("/"));const a=[];for(let _=0;_<s.childCount;_++){const u=s.child(_);if(u&&u.type==="import_declaration"){let f=this.parsePackages(u,n);a.push(...f)}}const l=[],c=[];return l.push(...this.getObjects(s,a,o)),c.push(...this.goObjectsToUpdateObjects(l)),{path:t,packagePath:o,importGoPackages:a,fullObjects:l,cacheObjects:c}}getRootNode(t){if(!this.parser)return null;try{return this.parser.parse(t).rootNode}catch(r){throw new Error(`go parsing error: ${r}`)}}firstDescendantOfType(t,r){Array.isArray(r)||(r=[r]);for(const s of t.children){if(r.includes(s.type))return s;const n=this.firstDescendantOfType(s,r);if(n)return n}return null}childrenOfType(t,r){const s=[];Array.isArray(r)||(r=[r]);for(const n of t.children)r.includes(n.type)&&s.push(n);return s}parsePackages(t,r){if(t.type!=="import_declaration")return[];const s=[],n=new Set,o=this.firstDescendantOfType(t,"import_spec_list");if(o){for(const a of o.children)if(a.type==="import_spec"){const l=this.processImportSpec(a);l&&!n.has(l.importPath)&&(s.push(l),n.add(l.importPath))}}else{const a=this.firstDescendantOfType(t,"import_spec");if(a){const l=this.processImportSpec(a);l&&!n.has(l.importPath)&&(s.push(l),n.add(l.importPath))}}return s.length!=0&&r&&r.size>0&&s.forEach(a=>{r.forEach((l,c)=>{a.importPath=a.importPath.replace(new RegExp(`^${c}`,"g"),l)})}),s}processImportSpec(t){const r=t.descendantsOfType("interpreted_string_literal")[0];if(!r)return null;const s=r.text.slice(1,-1),n=s.split("/"),o=n[n.length-1];let a={importPath:s,packageName:o};for(const l of t.children)if(l.type==="package_identifier"){a.alias=l.text;break}else if(l.type==="dot"){a.isDot=!0;break}else if(l.type==="blank_identifier"){a.isBlank=!0;break}return a}getObjects(t,r,s){var l;const n=[],o=new Map,a=new Map;for(const c of t.children)c.type==="method_declaration"?this.collectMethod(c,o):c.type==="const_declaration"&&this.collectConst(c,a);for(const c of t.children)if(c.type==="type_declaration"){const _=this.firstDescendantOfType(c,"type_spec");if(_){const u=((l=_.childForFieldName("name"))==null?void 0:l.text)??"",f=_.childForFieldName("type");if(f){if(f.type==="struct_type"){const m=this.processStruct(u,f,o,r,s);m&&n.push(m)}else if(f.type==="interface_type"){const m=this.processInterface(u,f);m&&n.push(m)}else if(f.type==="type_identifier"){const m=this.processTypeIdentifier(u,f,a);m&&n.push(m)}}}}else c.type==="function_declaration"&&n.push(this.processFunction(c));return n}collectMethod(t,r){var a;const s=t.childForFieldName("receiver");if(!s)return;const n=this.extractReceiverType(s);r.has(n)||r.set(n,[]);const o={fieldPackage:"",fieldName:((a=t.childForFieldName("name"))==null?void 0:a.text)??"",simpleText:this.extractMethodSimpleText(t),fieldVariable:this.extractMethodSignature(t)};r.get(n).push(o)}collectConst(t,r){const s=this.childrenOfType(t,"const_spec");if(s)for(let n=0;n<s.length;n++){const o=s[n],a=this.firstDescendantOfType(o,"type_identifier");if(a){const l=a.text;if(r.has(l)||r.set(l,[]),r.get(l).push(o),this.firstDescendantOfType(o,"iota"))for(let c=n+1;c<s.length&&(!this.firstDescendantOfType(s[c],"type_identifier")&&(!s[c].childForFieldName("value")||this.firstDescendantOfType(s[c],"int_literal")));c++)r.get(l).push(s[c]),n++}}}processStruct(t,r,s,n,o){var c;const a=[];let l=`type ${t} struct {
`;if(r){const _=this.firstDescendantOfType(r,"field_declaration_list");if(_){const u=this.childrenOfType(_,"field_declaration");if(u){for(const g of u)l+=`	${g.text.trim()}
`,a.push({fieldPackage:"",fieldVariable:((c=g.childForFieldName("name"))==null?void 0:c.text)??"",fieldType:this.extractType(g.childForFieldName("type"))});l+=`}
`}const f=s.get(t)||[],m=s.get("*"+t)||[];for(const g of f)l+=(g==null?void 0:g.simpleText)+`
`;for(const g of m)l+=(g==null?void 0:g.simpleText)+`
`;return{objectName:t,objectType:"struct_type",simpleText:l,fields:a}}}}getPackagePath(t,r,s){var a;if(!t)return"";const n=this.firstDescendantOfType(t,"type_identifier");if(!n)return s;const o=this.getPackageIdentifierBy(n);if(o)return((a=r.find(l=>l.packageName===o||l.alias===o))==null?void 0:a.importPath)??"";{const l=(r==null?void 0:r.filter(c=>c.isDot).map(c=>c.importPath))||[];if(l.length>0){for(const c of l)if(this.objectSystem.checkObjectInPackage(c,n.text))return c}return s}}processInterface(t,r){if(!r)return null;let s="";const n=this.childrenOfType(r,"method_elem"),o=this.childrenOfType(r,"type_elem");s+=`type ${t} interface {
`;for(const a of n)s+=`	${a.text}
`;for(const a of o)s+=`	${a.text}
`;return s+="}",{objectName:t,objectType:"interface_declaration",simpleText:s,fields:null}}processTypeIdentifier(t,r,s){const n=[];if(r){r.text;let o="type "+t+" "+r.text+`
`;const a=s.get(t)||[];if(a.length>0){o+=`const (
`;for(const l of a)o+=`	${l.text}
`;o+=`)
`}return{objectName:t,objectType:"type_identifier",simpleText:o,fields:n}}}processFunction(t){var l,c,_;const r=((l=t.childForFieldName("name"))==null?void 0:l.text)??"",s=((c=t.childForFieldName("parameters"))==null?void 0:c.text)??"",n=((_=t.childForFieldName("result"))==null?void 0:_.text)??"",o=n===""?" ":" "+n+" ",a=`func ${r}${s}${o}{
}`;return{objectName:r,objectType:"function_declaration",simpleText:a,fields:[{fieldPackage:"",fieldType:"signature",fieldVariable:this.extractFunctionSignature(t)}]}}extractReceiverType(t){var s;const r=(s=this.firstDescendantOfType(t,"parameter_declaration"))==null?void 0:s.childForFieldName("type");return r?r.text:""}extractMethodSimpleText(t){var l,c,_,u;const r=(l=t.childForFieldName("receiver"))==null?void 0:l.text,s=(c=t.childForFieldName("name"))==null?void 0:c.text,n=(_=t.childForFieldName("parameters"))==null?void 0:_.text,o=((u=t.childForFieldName("result"))==null?void 0:u.text)??"",a=o===""?" ":" "+o+" ";return`func ${r} ${s}${n}${a}{
}`}extractMethodSignature(t){const r=t.childForFieldName("parameters"),s=t.childForFieldName("result");return`${r.text}${s?" "+s.text:""}`}extractType(t){return t?t.text:""}extractFunctionSignature(t){const r=t.childForFieldName("parameters"),s=t.childForFieldName("result");return`${r.text}${s?" "+s.text:""}`}goObjectsToUpdateObjects(t){return t.map(r=>new UpdateObject("",r.objectType,r.objectName,r.simpleText))}getPackageIdentifierBy(t){if(!t||t.type!=="type_identifier")return null;const r=t.parent;if(!r)return null;for(const s of r.children)if(s.type==="package_identifier")return s.text.trim();return null}findLastNodeOfType(t,r,s,n){if(!t)return null;const o=t.descendantsOfType(r,{row:s,column:0},{row:s,column:n});return o.length?o[o.length-1]:null}getTypeDeclarationByIdentifier(t,r){var s,n;for(const o of r.descendantsOfType(["parameter_declaration","short_var_declaration","var_declaration"],r.startPosition,t.startPosition))switch(o.type){case"parameter_declaration":const a=((s=this.firstDescendantOfType(o,"identifier"))==null?void 0:s.text)??"";if(t.text===a)return[!0,this.firstDescendantOfType(o,"type_identifier")];break;case"short_var_declaration":case"var_declaration":const l=o.descendantsOfType("identifier");for(let c=0;c<l.length;c++)if((((n=l[c])==null?void 0:n.text)??"")===t.text){const _=o.descendantsOfType("type_identifier");return _.length==0?[!0,null]:_.length==1?[!0,_[0]]:_.length==l.length?[!0,_[c]]:[!0,_[0]]}break}return[!1,null]}}class JavaScriptParser{constructor(t,r){_e(this,"parser");_e(this,"objectSystem");this.parser=t,this.objectSystem=r}FindRelativeObject(t,r,s,n,o,a){const l=this.getRootNode(s);if(!l||!this.parseJavaScriptCode(r,s,l))return null;const _=this.parseImportStatements(l,r,n,o),u=this.findIdentifierImports(l,n,o,_);return u.length>0?(console.log(`[JavaScriptParser] default import: ${u[0].source} ${u[0].name}`),this.objectSystem.queryByPackageAndObjectName(t,u[0].source,u[0].name)):null}findIdentifierImports(t,r,s,n){const o=[],a=t.descendantsOfType("identifier",{row:r,column:0},{row:r,column:s});let l,c="";if(this.parser&&a.length){l=a[a.length-1],c=l.text.trim();const _=this.findImportObject(n,c);if(_)return[_];const f=this.parser.getLanguage().query(`
      (variable_declarator 
            name: (identifier) @declarator (#eq? @declarator "${c}")
      )
      (_ parameter: (_)@param (#eq? @param "${c}")) 
      (_ parameters: (_ (identifier)@param (#eq? @param "${c}")) ) 
      (assignment_expression left: (_)@assignment (#eq? @assignment "${c}")) 
      `).matches(t,{startPosition:{row:0,column:0},endPosition:{row:r,column:s}}),m=[],g=[];let w=null;const b=this.scopeNode(l);for(const P of f)for(const I of P.captures){const E=I.node;switch(I.name){case"declarator":{const O=this.addDeclaratorInvoke(E,b,m,g,0);O&&(w=O);break}case"param":{const O=this.addDeclaratorInvoke(E,b,m,g,1);O&&(w=O);break}case"assignment":{let O=this.scopeNode(E);if(w&&this.isInvokeInScope(w,O)){for(const R of g)if(this.isInvokeInScope(R.scopeNode,O)){O=null;break}O&&m.push({node:E,scopeNode:O,type:2})}break}}}console.log(m);for(const P of m.reverse()){const I=this.findImportObjectsInIdentifierInvoke(P,t,n);o.push(...I)}}return o}findImportObjectsInIdentifierInvoke(t,r,s){var n,o;switch(t.type){case 0:return this.findImportObjectsInExpression(((n=t.node.parent)==null?void 0:n.childForFieldName("value"))||null,r,s);case 2:return this.findImportObjectsInExpression(((o=t.node.parent)==null?void 0:o.childForFieldName("right"))||null,r,s);case 1:return[]}}findImportObjectsInExpression(t,r,s){var o,a;const n=[];if(t)switch(t.type){case"new_expression":{const l=(a=(o=t.childForFieldName("constructor"))==null?void 0:o.text)==null?void 0:a.trim(),c=this.findImportObject(s,l);c&&n.push(c);break}case"identifier":return this.findIdentifierImports(r,t.endPosition.row,t.endPosition.column,s)}return n}findImportObject(t,r){return t.find(s=>s.alias===r)}addDeclaratorInvoke(t,r,s,n,o){const a=this.scopeNode(t,o===1);return this.isInvokeInScope(a,r)?(s.length=0,n.length=0,s.push({type:o,node:t,scopeNode:a}),a):(n.push({type:o,node:t,scopeNode:a}),null)}scopeNode(t,r=!1){if(r)for(;t.parent;){const s=t.childForFieldName("body");if(s&&s.type==="statement_block")return s;t=t.parent}else for(;t.type!=="statement_block"&&t.parent;)t=t.parent;return t}isInvokeInScope(t,r){for(;t.id!==r.id&&r.parent;)r=this.scopeNode(r.parent);return t.id===r.id}parseImportStatements(t,r,s,n){var l;const o=[],a=t.descendantsOfType("import_statement");for(const c of a){const _=c.childForFieldName("source");if(_){const u=this.resolveSource(r,_.text.slice(1,-1));if(u){const f=c.descendantsOfType("import_clause");for(const m of f)for(const g of m.children)switch(g.type){case"named_imports":for(const w of g.descendantsOfType("import_specifier")){const b=w.childForFieldName("name");if(b){const P=(l=w.childForFieldName("alias"))==null?void 0:l.text;o.push({name:b.text,alias:P||b.text,source:u})}}break;case"namespace_import":{const w=g.children.find(b=>b.type==="identifier");w&&o.push({name:"",alias:w.text,source:u})}break;case"identifier":o.push({name:"default",alias:g.text,source:u});break}}}}if(this.parser){const _=this.parser.getLanguage().query(`
      (variable_declarator 
        value: (call_expression
          function: (identifier) @func (#eq? @func "require")
          arguments: (arguments (string (string_fragment) @source) 
          )
        ) 
      ) @declarator
    `).matches(t,{startPosition:{row:0,column:0},endPosition:{row:s,column:n}});for(const u of _){let f="";const m=[];for(const g of u.captures){const w=g.node;switch(g.name){case"source":{f=this.resolveSource(r,w.text)||"";break}case"declarator":{const b=w.childForFieldName("name");if(b)switch(b.type){case"identifier":m.push({name:"",alias:b.text,source:f});break;case"object_pattern":for(const P of b.children)switch(P.type){case"shorthand_property_identifier_pattern":m.push({name:P.text,alias:P.text,source:f});break;case"rest_pattern":{const I=P.children.find(E=>E.type==="identifier");I&&m.push({name:"",alias:I.text,source:f});break}case"pair_pattern":{const I=P.childForFieldName("key"),E=P.childForFieldName("value");let O="",R="";(I==null?void 0:I.type)==="property_identifier"?O=I.text:(I==null?void 0:I.type)==="string"&&(O=I.text.slice(1,-1)),(E==null?void 0:E.type)==="identifier"&&(R=E.text),O&&R&&m.push({name:O,alias:R,source:f});break}}break}}}}f&&(m.forEach(g=>g.source=f),o.push(...m))}}return o}getSourcePath(t,r){const s=t.substring(0,t.lastIndexOf("/"));if(r.startsWith("./"))return`${s}/${r.slice(2)}`;if(r.startsWith("../")){let n=s,o=r;for(;o.startsWith("../");)n=n.substring(0,n.lastIndexOf("/")),o=o.slice(3);return`${n}/${o}`}return r}resolveSource(t,r){if(r.startsWith("./")||r.startsWith("../")||r.startsWith("/")){let s=this.getSourcePath(t,r);const n=[".js",".jsx",".json",".node",".mjs",".cjs"],o=n.find(a=>s.endsWith(a));o&&(s=s.slice(0,-o.length));for(const a of n){const l=`${s}${a}`;if(this.objectSystem.checkJsFileExists(l))return l}}return null}ParseFile(t,r,s){const n=this.parseJavaScriptCode(r,s,void 0);n?this.objectSystem.updateByFile(t,n.filePath,n.filePath,n.objectsToUpdate):console.log("GoParser ParseFile skip,code file parse is null.")}parseJavaScriptCode(t,r,s){if(console.log(`[JavaScriptParser] Parse JavaScript Code in file: ${t}`),console.log(`[JavaScriptParser] Content: ${r}`),!s){const _=this.getRootNode(r);if(!_)return null;console.log(`parseJavaScriptCode success, filePath:${t}`),s=_}let n=[];const o=[],a=[],l=[];for(let _=0;_<s.childCount;_++){const u=s.child(_);if(u)switch(u.type){case"export_statement":{const m=this.processExportStatement(t,u,a);n=n.concat(m);break}case"expression_statement":const f=this.processCJSExport(u,a);n=n.concat(f);break;case"class_declaration":case"function_declaration":case"generator_function_declaration":{const m=this.firstDescendantOfType(u,"identifier"),g=(m==null?void 0:m.text.trim())||"",w=u.type.replace("_declaration",""),b=this.extractNodesSimpleText(u.children),I={objectName:g,objectType:w,simpleText:b,candidateSimpleText:b};l.push(I);break}case"lexical_declaration":case"variable_declaration":{const m=u.descendantsOfType("identifier")[0];if(m){const g=m.text.trim(),w=this.firstDescendantOfType(u,"variable_declarator");if(w){const b=this.firstDescendantOfType(w,["class","function_expression","object","arrow_function","generator_function","call_expression"]);if(!b)continue;let P;b.type==="call_expression"?P=b.type:P=b.type.replace("_expression","");const I=this.extractNodesSimpleText((b==null?void 0:b.children)||[]),E=this.extractNodesSimpleText((u==null?void 0:u.children)||[]),O={objectName:g,objectType:P,simpleText:I,candidateSimpleText:E};l.push(O)}}break}}}l.forEach(_=>{a.find(u=>{if(u===_.objectName){const f=n.findIndex(m=>m.originalName===u);f!==-1&&(n[f].exportClause===!0?n[f].simpleText+=_.candidateSimpleText:n[f].simpleText+=_.simpleText,n[f].objectType=_.objectType)}})}),n=this.extractCJSExport(n);for(const _ of n)_.objectType||(_.objectType=""),o.push(new UpdateObject("",_.objectType,_.objectName,_.simpleText));const c=n.map(_=>_.simpleText).join("");return o.push(new UpdateObject("","","",c)),console.log(t,o),{filePath:t,objects:n,objectsToUpdate:o}}extractCJSExport(t){let r=0,s=[];for(let l=t.length-1;l>=0;l--){const c=t[l].moduleSystem,_=t[l].objectName;if(c==="cjs"&&_==="default"){r=l;break}}if(!r)return t;const o=t.slice(0,r).filter(l=>l.moduleSystem!=="cjs"),a=t.slice(r);return s=o.concat(a),s}processExportStatement(t,r,s){let n="",o="",a="esm",l=[];for(let c=0;c<r.childCount;c++){const _=r.child(c);if(_)switch(_.type){case"default":n=_.text.trim(),o+=_.text.trim()+" ";break;case"export":o+=_.text.trim()+" ";break;case"class_declaration":case"function_declaration":case"generator_function_declaration":{const m=this.firstDescendantOfType(_,"identifier"),g=_.type.replace("_declaration","");n=n||(m==null?void 0:m.text.trim()),o+=this.extractNodesSimpleText(_.children),l.push({objectName:n,objectType:g,simpleText:o,moduleSystem:a});break}case"class":case"function_expression":case"object":case"arrow_function":case"generator_function":case"call_expression":{let m;_.type==="call_expression"?m=_.type:m=_.type.replace("_expression",""),o+=this.extractNodesSimpleText(_.children),l.push({objectName:n,objectType:m,simpleText:o,moduleSystem:a});break}case"assignment_expression":break;case"identifier":n=_.text.trim(),s.push(n),l.push({objectName:"default",originalName:n,simpleText:o,moduleSystem:a});break;case"export_clause":l=this.parseNamedIdentifier(o,_,s);break;case"lexical_declaration":case"variable_declaration":const u=this.firstDescendantOfType(_,["const","let","var"]);o+=(u==null?void 0:u.text.trim())+" ";const f=this.firstDescendantOfType(_,"variable_declarator");if(f){const m=this.firstDescendantOfType(f,"identifier"),g=this.firstDescendantOfType(f,["class","function_expression","object","arrow_function","generator_function","call_expression"]);if(m&&g){n=m.text.trim();let w;g.type==="call_expression"?w=g.type:w=g.type.replace("_expression",""),o+=m.text.trim()+" ",o+="= ",o+=this.extractNodesSimpleText(g.children),l.push({objectName:n,objectType:w,simpleText:o,moduleSystem:a})}}default:o+=this.extractNodesSimpleText(_.children);break}}return l}processCJSExport(t,r){let s="",n="",o="",a="",l="cjs",c=[];for(let _=0;_<t.childCount;_++){const u=t.child(_);if(u)switch(u.type){case"assignment_expression":const f=u.descendantsOfType("member_expression");let m=!1;for(let g=0;g<f.length;g++){const w=f[g].firstChild,b=w==null?void 0:w.text.trim();if(w&&w.type==="identifier"&&(b==="exports"||b==="module")){m=!0;break}}if(m){const g=f[0].lastChild;s=(g==null?void 0:g.text.trim())||"",(g==null?void 0:g.type)==="property_identifier"&&s==="exports"&&(s="default");const w=u.lastChild;let b="";(w==null?void 0:w.type)==="identifier"&&(b=w==null?void 0:w.text.trim(),r.push(b));const P=this.firstDescendantOfType(u,["class","function_expression","object","arrow_function","generator_function","call_expression"]),I=this.firstDescendantOfType(u,"member_expression");o=this.extractNodesSimpleText((P==null?void 0:P.children)||[]),a=this.extractNodesSimpleText((I==null?void 0:I.children)||[]).replace(/\s+/g,""),n=a+"="+o,c.push({objectName:s,originalName:b,simpleText:n,moduleSystem:l})}break}}return c}parseNamedIdentifier(t,r,s){const n=[],o=r.descendantsOfType("export_specifier");for(const a of o){const l=a.descendantsOfType("identifier"),c=l[0].text.trim(),_=l.length>1?l[1].text.trim():null;s.push(c),n.push({objectName:_||c,originalName:c,simpleText:t,moduleSystem:"esm",exportClause:!0})}return n}getRootNode(t){if(!this.parser)return null;try{return this.parser.parse(t).rootNode}catch(r){throw new Error(`Parsing error: ${r}`)}}firstDescendantOfType(t,r){Array.isArray(r)||(r=[r]);for(const s of t.children)if(r.includes(s.type))return s;return null}extractNodesSimpleText(t){let r="";for(const s of t)switch(s.type){case"modifiers":case"class":case"identifier":case"property_identifier":case"formal_parameters":case"superclass":case"=>":r+=s.text.trim()+" ";break;case"pair":case"object":case"class_body":case"arguments":r+=this.extractNodesSimpleText(s.children);break;case":":r+=s.type+`
`;break;case"{":case"}":r+=s.type+`
`;break;case"field_declaration":break;case"block":case"statement_block":r+=`{}
`;break;case"member_expression":case"function_expression":r+=this.extractNodesSimpleText(s.children);break;case"variable_declarator":case"arrow_function":r+=this.extractNodesSimpleText(s.children);break;case"method_definition":if(!this.isPrivateModifiers(s)){const n=this.firstDescendantOfType(s,"property_identifier");r+="	",(n==null?void 0:n.text)==="constructor"?r+=s.text+`
`:r+=this.extractNodesSimpleText(s.children)}break;default:s.isNamed||(r+=s.text.trim()+" ")}return r}isPrivateModifiers(t){const r=t.child(0);return!!(r&&r.type==="private_property_identifier")}}const _sfc_main=defineComponent({__name:"App",async setup(e){let t,r;[t,r]=withAsyncContext(()=>Parser.init({locateFile(a,l){return a}})),await t,r();const s=["java","go","javascript"],n=new CodeObjectIndexSystem,o={};for(const a of s){const l=new Parser,c=([t,r]=withAsyncContext(()=>Parser.Language.load("tree-sitter-"+a+".wasm")),t=await t,r(),t);l.setLanguage(c);let _=null;switch(a){case"java":_=new JavaParser(l,n);break;case"go":_=new GoParser(l,n);break;case"javascript":_=new JavaScriptParser(l,n);break}_&&(o[a]=_)}window.addEventListener("message",a=>{if(a.data.type=="findRelativeObjectRequest")try{const l=a.data.language,c=a.data.path,_=a.data.data,u=a.data.row,f=a.data.col,m=a.data.goModMapJson;o[l].FindRelativeObject(l,c,_,u,f,m)||window.treeSitterQuery({request:JSON.stringify({type:"findRelativeObjectNull"}),persistent:!1,onSuccess:function(g){},onFailure:function(g,w){console.log(`FindRelativeObject,error_code:${g},error_message:${w}`)}})}catch{MessageSender.sendErrorMessage("Web Tree-Sitter Find Relative Object fail")}if(a.data.type=="parseCodeFileRequest")try{const l=a.data.language;o[l].ParseFile(l,a.data.path,a.data.data)}catch{MessageSender.sendErrorMessage("Web Tree-Sitter Parse Code file fail")}a.data.type=="backgroundColor"&&(a.data.color=="dark"?document.body.style.backgroundColor="rgb(51,51,51)":document.body.style.backgroundColor="rgb(209,209,209)")});try{window.treeSitterQuery({request:JSON.stringify({type:"webviewTreeSitterLoaded"}),persistent:!1,onSuccess:function(a){console.log("webviewTreeSitterLoaded,start parse all code file")},onFailure:function(a,l){console.log(`webviewTreeSitterLoaded onFailure,error_code:${a},error_message:${l}`)}})}catch{MessageSender.sendErrorMessage("Web Tree-Sitter Start fail")}return(a,l)=>null}}),_export_sfc=(e,t)=>{const r=e.__vccOpts||e;for(const[s,n]of t)r[s]=n;return r},App=_export_sfc(_sfc_main,[["__scopeId","data-v-d0e65a22"]]);createApp(App).mount("#app");
