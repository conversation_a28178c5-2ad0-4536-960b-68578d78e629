package com.srdcloud.ideplugin.remote.domain.ScanAssistant;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/10
 * @desc 扫描问题列表响应
 */
public class IssueListResponse extends ScanBaseResponse{

    /**
     * 问题总数
     */
    private int total;

    /**
     * 响应数据，需要按需反序列化解析
     */
    private List<Issue> data;

    public IssueListResponse(int code, String msg, int total, List<Issue> data) {
        this.code = code;
        this.msg = msg;
        this.total = total;
        this.data = data;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public List<Issue> getData() {
        return data;
    }

    public void setData(List<Issue> data) {
        this.data = data;
    }
}
