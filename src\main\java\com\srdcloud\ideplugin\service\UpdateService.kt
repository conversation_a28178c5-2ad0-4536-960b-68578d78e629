package com.srdcloud.ideplugin.service

import com.google.gson.Gson
import com.intellij.openapi.diagnostic.Logger
import com.jgoodies.common.base.Strings
import com.srdcloud.ideplugin.general.utils.pluginSettings
import java.io.File

import java.net.URL
import java.nio.file.Files
import java.nio.file.Paths
import java.nio.file.StandardCopyOption
import javax.net.ssl.HttpsURLConnection
import javax.net.ssl.SSLContext
import javax.net.ssl.SSLHandshakeException
import javax.net.ssl.SSLSocketFactory
import javax.net.ssl.TrustManager
import javax.net.ssl.X509TrustManager
import java.net.SocketTimeoutException
import java.security.SecureRandom
import java.security.cert.X509Certificate
import javax.net.ssl.HostnameVerifier
import java.io.FileNotFoundException
import java.net.HttpURLConnection

data class VersionCheckResponse(
    val code: Int,
    val hasUpdate: Boolean,
    val latestVersion: String,
    val downloadUrl: String? = null,
    val changelog: String? = null,
    val message: String? = null
)

class UpdateService {
    companion object {
        private val logger = Logger.getInstance(this::class.java)
        private val gson = Gson()
        private const val TIMEOUT_SECONDS = 3 // 设置3秒超时

        /**
         * 检查新版本
         */
        fun checkNewVersion(currentVersion: String, productType: String, baseUrl: String): VersionCheckResponse {
            try {
                val ide = "jetbrains"
                if (Strings.isBlank(baseUrl) || !isStandardUrl(baseUrl)) {
                    return VersionCheckResponse(
                        code = 1,
                        hasUpdate = false,
                        latestVersion = currentVersion,
                        message = "检查更新失败: 请测试与平台的连接状态"
                    )
                }

                // 例如$baseUrl/api/check_plugin_version?version=3.2.1&ide=jetbrains&productType=c10
                val url = URL("$baseUrl/api/check_plugin_version?version=$currentVersion&ide=${ide}&productType=${productType}")
                val connection = url.openConnection()

                // 设置超时
                connection.connectTimeout = TIMEOUT_SECONDS * 3000
                connection.readTimeout = TIMEOUT_SECONDS * 3000

                // 处理SSL证书问题
                if (connection is HttpsURLConnection) {
                    connection.sslSocketFactory = createTrustAllSSLSocketFactory()
                    connection.hostnameVerifier = HostnameVerifier { _, _ -> true }
                }

                try {
                    val response = connection.getInputStream().use { it.reader().readText() }
                    return gson.fromJson(response, VersionCheckResponse::class.java)
                } catch (e: FileNotFoundException) {
                    return VersionCheckResponse(
                        code = 1,
                        hasUpdate = false,
                        latestVersion = currentVersion,
                        message = "检查更新失败: 请测试与平台的连接状态"
                    )
                }
            } catch (e: Exception) {
                return VersionCheckResponse(
                    code = 1,
                    hasUpdate = false,
                    latestVersion = currentVersion,
                    message = when (e) {
                        is SSLHandshakeException -> "SSL证书验证失败"
                        is SocketTimeoutException -> "连接超时，请稍后重试"
                        else -> "检查更新失败: 请测试与平台的连接状态"
                    }
                )
            }
        }

        /**
         * 下载新版本
         */
        fun downloadNewVersion(saveDir: String, productType: String): Boolean {
            return try {
                val baseUrl = pluginSettings().address
                val url = URL("$baseUrl/api/download/jetbrains/${productType}")
                val connection = url.openConnection() as HttpURLConnection
                connection.connectTimeout = TIMEOUT_SECONDS * 3000
                connection.readTimeout = TIMEOUT_SECONDS * 30  // 下载可以给长一点的超时时间

                // 处理SSL证书问题
                if (connection is HttpsURLConnection) {
                    connection.sslSocketFactory = createTrustAllSSLSocketFactory()
                    connection.hostnameVerifier = HostnameVerifier { _, _ -> true }
                }

                // 创建保存目录
                Files.createDirectories(Paths.get(saveDir))

                // 尝试从响应头中获取文件名
                connection.requestMethod = "GET"
                connection.connect()
                var fileName = ""
                val disposition = connection.getHeaderField("content-disposition")
                if (disposition != null && disposition.contains("attachment")) {
                    val regex = Regex("filename[^;=\n]*=((['\"]).*?\\2|([^;\n]*))")
                    val matchResult = regex.find(disposition)
                    if (matchResult != null && matchResult.groupValues.size > 1) {
                        fileName = matchResult.groupValues[1].replace("['\"]".toRegex(), "") // 去除引号
                    }
                }

                // 下载文件
                val downloadFile = Paths.get(saveDir + File.separator + fileName)
                connection.inputStream.use { input ->
                    Files.copy(input, downloadFile, StandardCopyOption.REPLACE_EXISTING)
                }

                true
            } catch (e: Exception) {
                logger.error("下载新版本失败", e)
                false
            }
        }

        private fun createTrustAllSSLSocketFactory(): SSLSocketFactory {
            val trustAllCerts = arrayOf<TrustManager>(object : X509TrustManager {
                override fun getAcceptedIssuers(): Array<X509Certificate> = arrayOf()
                override fun checkClientTrusted(certs: Array<X509Certificate>, authType: String) {}
                override fun checkServerTrusted(certs: Array<X509Certificate>, authType: String) {}
            })

            return SSLContext.getInstance("SSL").apply {
                init(null, trustAllCerts, SecureRandom())
            }.socketFactory
        }


        private fun isStandardUrl(url: String): Boolean {
            val regex = Regex("^https?://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]")
            return regex.matches(url)
        }
    }
}
