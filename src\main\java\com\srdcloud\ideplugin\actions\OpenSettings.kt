package com.srdcloud.ideplugin.actions

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.options.ShowSettingsUtil
import com.srdcloud.ideplugin.settings.SecIdeaProjectSettingsConfigurable

/**
 * 打开插件设置面板的动作类
 */
class OpenSettings : AnAction() {
    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return

        // 通过ShowSettingsUtil打开指定的设置面板
        ShowSettingsUtil.getInstance().showSettingsDialog(
            project,
            SecIdeaProjectSettingsConfigurable::class.java
        )
    }
}