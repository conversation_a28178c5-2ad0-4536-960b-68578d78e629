package com.srdcloud.ideplugin.service;

import com.intellij.openapi.project.Project;
import com.intellij.openapi.project.ProjectManagerListener;
import com.srdcloud.ideplugin.agent.AgentManager;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import com.srdcloud.ideplugin.statusbar.Notify;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;
import com.srdcloud.ideplugin.webview.codechat.login.LoginCheckTask;
import com.srdcloud.ideplugin.webview.workitem.WorkItemWebview;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * 项目生命周期挂载点
 */
public class ProjectLifecycleService implements ProjectManagerListener {

    private static final Logger logger = LoggerFactory.getLogger(ProjectLifecycleService.class);

    /**
     * 项目关闭hook
     * 此处释放资源不能involater：因为容器已经销毁
     *
     * @param project
     */
    @Override
    public void projectClosing(@NotNull Project project) {
        logger.info("[cf] projectClosing:{}", project.getName());
        DebugLogUtil.println("projectClosing:" + project.getName());

        // 停止跨文件缓存更新任务
        //CodeObjectIndexSystemUpdateTask.getInstance(project).dispose();

        // 释放Treesitter Webview实例
        //WebviewTreeSitter.getInstance(project).dispose();

        // 停止登录状态检查任务
        LoginCheckTask.getInstance(project).dispose();

        // 释放CommitDialog Webview实例
        WorkItemWebview.getInstance(project).dispose();

        // 释放CodeFree Webview实例
        CodeChatWebview.getInstance(project).dispose();

        // 释放AgentManager资源
        AgentManager.getInstance(project).dispose();

        // 状态栏状态更新
        Notify.Companion.updateStatusNotify();

        logger.info("[cf] projectClosed:{}", project.getName());
        DebugLogUtil.println("projectClosed:" + project.getName());
    }
}
