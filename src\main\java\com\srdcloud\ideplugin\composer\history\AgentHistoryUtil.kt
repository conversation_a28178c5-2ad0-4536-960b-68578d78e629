package com.srdcloud.ideplugin.composer.history

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.intellij.openapi.project.Project
import com.srdcloud.ideplugin.composer.ComposerService
import com.srdcloud.ideplugin.general.utils.DebugLogUtil
import com.srdcloud.ideplugin.general.utils.FileUtil
import com.srdcloud.ideplugin.general.utils.EnvUtil
import com.srdcloud.ideplugin.webview.codechat.composer.domain.ContextInputItem
import java.io.File

/**
 * 功能：持久化agent所有的历史上下文
 * 为后面做历史消息功能预留
 */
object AgentHistoryUtil {
    private val gson = Gson()
    private val historyDir =
        "${System.getProperty("user.home")}${File.separator}${EnvUtil.isSec(".secidea", ".codefree")}${File.separator}plugin${File.separator}history"
    private val historyFile = "$historyDir${File.separator}chat_history_v1.json"

    /**
     * 单条消息体
     */
    data class Message(
        val role: String,
        val content: String,
        val displayContent: String,
        val createTime: String,
        val contextInputItems: List<ContextInputItem>
    )

    /**
     * 消息历史
     */
    data class ChatHistory(
        val messages: List<Message>,
        val sequence: Int
    )

    /**
     * 窗口历史
     */
    data class WindowHistory(
        val windowId: String,
        val histories: List<ChatHistory>
    )

    /**
     * 项目所有窗口历史
     */
    data class ProjectHistory(
        val projectPath: String,
        val windows: List<WindowHistory>
    )

    init {
        // 确保目录存在
        val dir = File(historyDir)
        if (!dir.exists()) {
            dir.mkdirs()
        }
        // 确保文件存在
        val file = File(historyFile)
        if (!file.exists()) {
            file.createNewFile()
            // 初始化为空数组
            file.writeText("[]")
        }

        // 设置历史文件读写权限
        FileUtil.setFilePermission(file.path)
    }

    /**
     * 添加新的对话记录
     * @param project 当前项目
     * @param messages 消息列表
     * @return 使用的会话ID
     */
    fun addHistory(project: Project, messages: List<Map<String, Any>>): String {
        val file = File(historyFile)
        val type = object : TypeToken<List<ProjectHistory>>() {}.type
        val allProjects = if (file.length() > 0) {
            gson.fromJson<List<ProjectHistory>>(file.readText(), type)
        } else {
            listOf()
        }.toMutableList()

        val projectPath = project.basePath ?: ""
        val windowId = AgentWindowIdUtil.getWindowId(project)

//        val newMessages = messages.map { Message(it["role"] ?: "", it["content"] ?: "") }
        fun Map<String, Any?>.toMessage() = Message(
            role = get("role") as? String ?: "",
            content = get("content") as? String ?: "",
            displayContent = get("displayContent") as? String ?: "",
            createTime = get("createTime") as? String ?: "",
            contextInputItems = (get("contextInputItems") as? List<ContextInputItem>) ?: emptyList()
        )
        val newMessages = messages.map { it.toMessage() }

        // fixme：本地开发调试用
//        DebugLogUtil.println("addHistory:$newMessages")

        // 查找或创建项目历史
        val projectIndex = allProjects.indexOfFirst { it.projectPath == projectPath }
        if (projectIndex != -1) {
            val project = allProjects[projectIndex]
            val windows = project.windows.toMutableList()

            // 查找或创建窗口历史
            val windowIndex = windows.indexOfFirst { it.windowId == windowId }
            if (windowIndex != -1) {
                val window = windows[windowIndex]
                val histories = window.histories.toMutableList()

                // 添加新的聊天记录
                val maxSequence = histories.maxOfOrNull { it.sequence } ?: 0
                histories.add(ChatHistory(newMessages, maxSequence + 1))

                windows[windowIndex] = window.copy(histories = histories)
            } else {
                // 创建新窗口历史
                windows.add(
                    WindowHistory(
                        windowId = windowId,
                        histories = listOf(ChatHistory(newMessages, 1))
                    )
                )
            }

            allProjects[projectIndex] = project.copy(windows = windows)
        } else {
            // 创建新项目历史
            allProjects.add(
                ProjectHistory(
                    projectPath = projectPath,
                    windows = listOf(
                        WindowHistory(
                            windowId = windowId,
                            histories = listOf(ChatHistory(newMessages, 1))
                        )
                    )
                )
            )
        }

        file.writeText(gson.toJson(allProjects))
        return windowId
    }

    /**
     * 获取指定会话的所有消息
     * @param projectPath 项目路径
     * @param conversationId 会话ID
     * @return 按序号排序的聊天记录列表
     */
    fun getHistoryByConversationId(project: Project, conversationId: String): List<ChatHistory>? {
        return getHistoryByWindowId(project, conversationId)
    }

    /**
     * 删除指定会话的所有消息
     * @param projectPath 项目路径
     * @param conversationId 会话ID
     * @return 无返回值
     */
    fun deleteHistoryByConversationId(project: Project, conversationId: String) {
        clearWindowIdHistory(conversationId)
    }

    /**
     * 获取当前窗口的所有消息
     * @param project 当前项目
     * @return 按序号排序的聊天记录列表
     */
    fun getCurrentWindowHistory(project: Project): List<ChatHistory>? {
        return getHistoryByWindowId(project, AgentWindowIdUtil.getWindowId(project))
    }

    /**
     * 获取指定窗口的所有消息
     * @param projectPath 项目路径
     * @param windowId 窗口ID
     * @return 按序号排序的聊天记录列表
     */
    fun getHistoryByWindowId(project: Project, windowId: String): List<ChatHistory>? {
        val projectPath = project.basePath
        if (projectPath.isNullOrEmpty()) {
            return emptyList()
        }
        val projectHistory = getProjectHistory(projectPath)
        return projectHistory?.windows?.find { it.windowId == windowId }?.histories?.sortedBy { it.sequence }
    }

    /**
     * 获取项目的所有窗口ID
     * @param projectPath 项目路径
     * @return 窗口ID列表
     */
    fun getProjectWindows(projectPath: String): List<String> {
        return getProjectHistory(projectPath)?.windows?.map { it.windowId } ?: listOf()
    }

    /**
     * 获取项目历史
     * @param projectPath 项目路径
     */
    private fun getProjectHistory(projectPath: String): ProjectHistory? {
        val file = File(historyFile)
        val type = object : TypeToken<List<ProjectHistory>>() {}.type
        val allProjects = if (file.length() > 0) {
            gson.fromJson<List<ProjectHistory>>(file.readText(), type)
        } else {
            listOf()
        }
        return allProjects.find { it.projectPath == projectPath }
    }

    /**
     * 删除指定窗口的指定序号历史记录
     * @param projectPath 项目路径
     * @param windowId 窗口ID
     * @param sequence 序号
     */
    fun removeHistoryBySequence(projectPath: String, windowId: String, sequence: Int) {
        val file = File(historyFile)
        val type = object : TypeToken<List<ProjectHistory>>() {}.type
        val allProjects = if (file.length() > 0) {
            gson.fromJson<List<ProjectHistory>>(file.readText(), type)
        } else {
            listOf()
        }.toMutableList()

        val projectIndex = allProjects.indexOfFirst { it.projectPath == projectPath }
        if (projectIndex != -1) {
            val project = allProjects[projectIndex]
            val windows = project.windows.toMutableList()

            val windowIndex = windows.indexOfFirst { it.windowId == windowId }
            if (windowIndex != -1) {
                val window = windows[windowIndex]
                val histories = window.histories.filterNot { it.sequence == sequence }

                if (histories.isEmpty()) {
                    windows.removeAt(windowIndex)
                } else {
                    windows[windowIndex] = window.copy(histories = histories)
                }

                if (windows.isEmpty()) {
                    allProjects.removeAt(projectIndex)
                } else {
                    allProjects[projectIndex] = project.copy(windows = windows)
                }
            }
        }

        file.writeText(gson.toJson(allProjects))
    }

    /**
     * 清理指定窗口的所有历史记录
     * @param windowId 窗口ID
     */
    fun clearWindowIdHistory(windowId: String) {
        val file = File(historyFile)
        val type = object : TypeToken<List<ProjectHistory>>() {}.type
        val allProjects = if (file.length() > 0) {
            gson.fromJson<List<ProjectHistory>>(file.readText(), type)
        } else {
            listOf()
        }.toMutableList()

        // 遍历所有项目，删除指定窗口
        for (i in allProjects.indices) {
            val project = allProjects[i]
            val windows = project.windows.filterNot { it.windowId == windowId }

            if (windows.isEmpty()) {
                allProjects.removeAt(i)
            } else {
                allProjects[i] = project.copy(windows = windows)
            }
        }

        file.writeText(gson.toJson(allProjects))
    }

    /**
     * 清理所有历史记录
     */
    fun clearHistory() {
        val file = File(historyFile)
        file.writeText("[]")
    }

    /**
     * 将ChatHistory转换为ChatMessage列表
     */
    fun List<ChatHistory>.toChatMessages(project: Project, userId: String): MutableList<ComposerService.ChatMessage> {
        return this.flatMap { chatHistory ->
            chatHistory.messages.map { msg ->
                ComposerService.ChatMessage(
                    role = msg.role,
                    content = msg.content,
                    userId = userId,
                    project = project.name
                )
            }
        }.toMutableList()
    }
} 