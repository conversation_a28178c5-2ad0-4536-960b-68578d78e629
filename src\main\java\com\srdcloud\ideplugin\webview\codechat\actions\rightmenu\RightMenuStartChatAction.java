package com.srdcloud.ideplugin.webview.codechat.actions.rightmenu;

import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.assistant.AssistantToolWindow;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.general.enums.ChatMessageType;
import com.srdcloud.ideplugin.general.utils.EditorUtil;
import com.srdcloud.ideplugin.general.utils.MessageBalloonNotificationUtil;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import com.intellij.openapi.project.Project;

public class RightMenuStartChatAction extends RightMenuActionBase {

    @Override
    protected int getChatMessageType() {
        return ChatMessageType.QA_RELATED_FILES.getType();
    }


    /**
     * 开始聊天  允许不选中代码，基于整个代码文件发起对话
     * 不能与RightMenuActionBase逻辑复用
     * @param e
     */
    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {
        Project project = e.getProject();
        if (project == null) return;

        // 展开编程助手对话窗
        AssistantToolWindow.toolWindowVisible(project);

        // 获取当前编辑器选中代码的所在文件的路径
        String filePath = EditorUtil.getCurrentSelectedFilePath(project);
        if (StringUtils.isBlank(filePath)) {
            MessageBalloonNotificationUtil.showBalloonNotificationByReason(project, "请先选择代码或者打开代码文件", RtnCode.RtnCode_Not_Select_Editor);
            return;
        }

        // 获取选中的代码、行号，允许为空
        Integer[] lineNumbers = EditorUtil.getCurrentSelectedFileLineNumber(project);
        String selectCode = EditorUtil.getCurrentSelectedCode(project);

        if (selectCode != null) {
            EditorUtil.getCurrentEditor(project).getSelectionModel().removeSelection();
        }


        // 发送右键菜单选中代码到CodeChatWebview进行处理
        if (lineNumbers == null || selectCode == null) {
            CodeChatWebview.getInstance(project).getActionHandler().rightSelectionAction("", getChatMessageType(), filePath, null, null);
        }else {
            CodeChatWebview.getInstance(project).getActionHandler().rightSelectionAction(selectCode, getChatMessageType(), filePath, lineNumbers[0], lineNumbers[1]);
        }
    }
}
