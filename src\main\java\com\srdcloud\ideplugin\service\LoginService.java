package com.srdcloud.ideplugin.service;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.intellij.ide.util.PropertiesComponent;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.project.ProjectManager;
import com.intellij.util.net.NetUtils;
import com.srdcloud.ideplugin.agent.AgentManager;
import com.srdcloud.ideplugin.agent.commclient.TabbyAgentClient;
import com.srdcloud.ideplugin.agent.model.AgentVersion;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.constants.AgentNameConstant;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.general.constants.RtnMessage;
import com.srdcloud.ideplugin.general.utils.*;
import com.srdcloud.ideplugin.login.LoginUtils;
import com.srdcloud.ideplugin.login.SecideaUserInfo;
import com.srdcloud.ideplugin.login.SecideaUtilsKt;
import com.srdcloud.ideplugin.remote.CodeAICommHandler;
import com.srdcloud.ideplugin.remote.UserCenterHandler;
import com.srdcloud.ideplugin.remote.client.HttpClient;
import com.srdcloud.ideplugin.remote.domain.ApiResponse;
import com.srdcloud.ideplugin.remote.domain.User.UserInfo;
import com.srdcloud.ideplugin.service.domain.apigw.ApigwWebsocketRespConfig;
import com.srdcloud.ideplugin.service.domain.apigw.ApigwWebsocketRespPayload;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.CodeChatRequestMessage;
import com.srdcloud.ideplugin.service.domain.oauth.AuthResponse;
import com.srdcloud.ideplugin.settings.SecIdeaProjectSettingsConfigurable;
import com.srdcloud.ideplugin.statusbar.Notify;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;
import com.srdcloud.ideplugin.webview.codechat.common.StatusEventType;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCode;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.jetbrains.ide.BuiltInServerManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.*;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Timer;
import java.util.TimerTask;

import static com.srdcloud.ideplugin.general.utils.PluginSettingsForSecUtilKt.pluginSettings;

/**
 * <AUTHOR> dengwy
 * @create 2023/9/6 15:20
 */
public class LoginService {

    private static final Logger logger = LoggerFactory.getLogger(LoginService.class);
    private static int loginStatus;

    private static int userStatus;

    private static Timer msgResvTimeoutTimer;

    private static final Gson gson = new Gson();

    static {
        loginStatus = Constants.LoginStatus_NOK;
        userStatus = Constants.UserStatus_OK;
    }

    // ====================================插件登录/登出逻辑=======================

    /**
     * 执行登录行为：拉起浏览器，跳转研发云Oauth登录页面
     */
    public static void Login() {
        if (EnvUtil.isSec()) {
            if(pluginSettings().getAddress() == null || Objects.requireNonNull(pluginSettings().getAddress()).isEmpty()){
                Project project = IdeUtil.findCurrentProject();
                if(project != null) {
                    MessageBalloonNotificationUtil.showSettingsNotification(
                            project, "请先配置服务器扫描地址");
                } else {
                    logger.info("请先配置服务器扫描地址");
                }
                return;
            }
            LoginUtils.INSTANCE.login();
        } else {
            String websiteURL = getLoginUrl();
            try {
                openBrowser(websiteURL);
            } catch (Exception e) {
                logger.error("打开浏览器失败", e);
            }
        }
    }

    public static boolean handleCode(String autoCode) {
        String url = getUserInfoUrl(autoCode);
        String response = SecideaUtilsKt.doGet(url);
        LoginUtils.INSTANCE.storeUser(Objects.requireNonNullElse(response, ""));
        if (response == null || response.isEmpty()) {
            logger.error("登录后获取用户信息失败");
            // 登录失败，跳转失败页，推送气泡通知，且statusbar显示服务不可用
            LoginService.onLoginEvent(Constants.LoginStatus_NOK, RtnCode.NO_CHANNEL);
            return false;
        }


        AuthResponse authResponse = new AuthResponse();
        SecideaUserInfo secideaUserInfo = LoginUtils.INSTANCE.decrypt(response);
        authResponse.setAccess_token(response);
        authResponse.setOri_session_id(response);
        authResponse.setUid(secideaUserInfo.getUserId());
        // 保存信息
        LocalStorageUtil.saveAuthProperties(authResponse);
        UserInfo userInfo = new UserInfo();
        userInfo.setName(secideaUserInfo.getUserName());
        userInfo.setUserAccount(secideaUserInfo.getUserName());
        LocalStorageUtil.saveUserInfo(userInfo);


        if(!LoginUtils.INSTANCE.isAuthorized(secideaUserInfo.getAuthCode())) {
            MessageBalloonNotificationUtil.showSettingsNotification(
                    Objects.requireNonNull(IdeUtil.findCurrentProject()), "账号未授权，请更换登录账号或联系客服");
        }

        // 3、注册socket对话通道
        try {
            // 退登并不会将之前的销毁，在登录之前，如果之前有实例，手动销毁
            if (CodeAICommHandler.instance != null) {
                CodeAICommHandler.instance.logoutDisconnect();
                CodeAICommHandler.instance = null;
            }
            boolean channelRegister = LoginService.sendAiRegisterChannel();
            if (channelRegister) {
                return true;
            }
        } catch (Exception e) {
            LoginService.onLoginEvent(Constants.LoginStatus_NOK, RtnCode.NO_CHANNEL);
                e.printStackTrace();
            }
        return false;
    }

    private static String getUserInfoUrl(String code) {
        return ConfigWrapper.getServerDomain() + "/login/oauth/getUserInfo?code=" + code;
    }


    /**
     * 根据Oauth回调code，兑换access token
     */
    public static boolean getAuthTokenByCode(String authCode) {
        logger.info("[cf] LoginService getAuthTokenByCode begin...");
        boolean authResult = true;
        String url = getAuthTokenUrl(authCode);

        ApiResponse result = HttpClient.doGet(url, null);
        if (result.getRtnCode() != RtnCode.SUCCESS) {
            logger.warn("[cf] LoginService getAuthTokenByCode fail,rtnCode:{},message:{}", result.getRtnCode(), result.getMessage());
            // token兑换失败，则登录失败，跳转失败页，推送气泡通知，且statusbar显示服务不可用
            authResult = false;
            loginStatus = Constants.LoginStatus_NOK;
            onLoginEvent(Constants.LoginStatus_NOK, RtnCode.NO_CHANNEL);
        } else {
            // token兑换成功，解析响应
            Map<String, Object> formData = FormatUtil.parseFormTypeResp(result.getMessage());
            AuthResponse authResponse = gson.fromJson(gson.toJson(formData), AuthResponse.class);
            // 异常登录回跳监控
            if (StringUtils.isBlank(authResponse.getUid()) || StringUtils.isBlank(authResponse.getAccess_token())) {
                logger.warn("[cf] LoginService getAuthTokenByCode error,authResponse:{}", result.getMessage());
            }

            if (result.getRtnCode() == RtnCode.SUCCESS) {
                // 1、持久化保存auth token信息
                LocalStorageUtil.saveAuthProperties(authResponse);

                // 2、反查用户信息
                UserInfo userInfo = UserCenterHandler.getUserInfo(authResponse.getUid());
                if (Objects.nonNull(userInfo) && StringUtils.isNotBlank(userInfo.getName())) {
                    LocalStorageUtil.saveUserInfo(userInfo);
                }

                // 3、注册socket对话通道
                try {
                    boolean channelRegister = sendAiRegisterChannel();
                    if (channelRegister) {
                        loginStatus = Constants.LoginStatus_OK;
                        userStatus = Constants.UserStatus_OK;
                        authResult = true;
                    }

                } catch (Exception e) {
                    logger.error("[cf] getAuthTokenByCode sendAiRegisterChannel error:{}", e.getMessage());
                    loginStatus = Constants.LoginStatus_NOK;
                    onLoginEvent(loginStatus, RtnCode.NO_CHANNEL);
                    e.printStackTrace();
                }
            } else {
                logger.warn("[cf] LoginService getAuthTokenByCode Auth fail.");
                loginStatus = Constants.LoginStatus_NOK;
                authResult = false;
            }
        }
        return authResult;
    }

    /**
     * 获取当前插件登录状态
     */
    public static int getLoginStatus() {
        if (EnvUtil.isSec()) {
            return (int) LoginUtils.INSTANCE.getLoginStatusForC10();
        } else {
            return loginStatus;
        }
    }

    public static int getSecideaLoginStatus() {
        return (int) LoginUtils.INSTANCE.getLoginStatusForStatusBar();
    }

    /**
     * 获取当前用户状态
     */
    public static int getUserStatus() {
        return userStatus;
    }

    /**
     * 修改当前用户状态
     */
    public static void setUserStatus(int status) {
        userStatus = status;
    }

    /**
     * 登出
     */
    public static void logout(int result) {
        if (EnvUtil.isSec()) {
            LoginUtils.INSTANCE.logout(null);
            // 退登并不会将之前的销毁，如果之前有实例，手动销毁
            if (CodeAICommHandler.instance != null) {
                CodeAICommHandler.instance.logoutDisconnect();
                CodeAICommHandler.instance = null;
            }
        }
        LocalStorageUtil.clearAuthPropertiesAndUserInfo();
        loginStatus = Constants.LoginStatus_NOK;
        userStatus = Constants.UserStatus_OK;
        onLoginEvent(loginStatus, result);
    }

    /**
     * 登出
     */
    public static void logout(String address, int result) {
        if (EnvUtil.isSec()) {
            LoginUtils.INSTANCE.logout(address);
            // 退登并不会将之前的销毁，如果之前有实例，手动销毁
            if (CodeAICommHandler.instance != null) {
                CodeAICommHandler.instance.logoutDisconnect();
                CodeAICommHandler.instance = null;
            }
        }
        LocalStorageUtil.clearAuthPropertiesAndUserInfo();
        loginStatus = Constants.LoginStatus_NOK;
        userStatus = Constants.UserStatus_OK;
        callBackLoginEvent(loginStatus, result);
    }

     private static String getAuthTokenUrl(String authCode) {

         JSONObject parasParam = new JSONObject();
         parasParam.put("grant_type", "authorization_code");
         parasParam.put("client_id", ConfigWrapper.oauthClientId);
         parasParam.put("client_secret", ConfigWrapper.oauthClientSecret);
         parasParam.put("code", authCode);
         parasParam.put("redirect_uri", ConfigWrapper.getServerUrl() + ConfigWrapper.oauthServerRedPath);

         String tokenUrl = ConfigWrapper.getServerUrl() + ConfigWrapper.oauthLoginApiUri +
                 "/access_token?" + FormatUtil.json2UrlParam(parasParam.toString(),
                 false, null);

         return tokenUrl;
     }

    /**
     * 通过浏览器打开研发云平台oauth登录页
     */
    private static void openBrowser(String url) throws IOException, URISyntaxException {
        if (Desktop.isDesktopSupported() && Desktop.getDesktop().isSupported(Desktop.Action.BROWSE)) {
            Desktop.getDesktop().browse(new URI(url));
        } else {
            logger.warn("[cf] open browser failed");
        }
    }

    public static String getLoginUrl() {
        JSONObject parasParam = new JSONObject();
        parasParam.put("response_type", "code");
        parasParam.put("client_id", ConfigWrapper.oauthClientId);
        parasParam.put("redirect_uri", ConfigWrapper.getServerUrl() + ConfigWrapper.oauthServerRedPath);
        parasParam.put("state", StringUtil.base64Encode(Objects.requireNonNull(getRedirectUrl())));

        return ConfigWrapper.getServerUrl() + ConfigWrapper.oauthLoginPageUri +
                "/authorize?" + FormatUtil.json2UrlParam(parasParam.toString(),
                false, null);
    }

    private static String getRedirectUrl() {
        String host = getHostAddress();
        if (host != null) {
            return ConfigWrapper.oauthClientRedUrl + ":" + BuiltInServerManager.getInstance().getPort() + "/api/oauth-redirect";
        }
        return null;
    }

    /**
     * 获取本机地址
     */
    @Nullable
    private static String getHostAddress() {
        try {
            String localHost = NetUtils.getLocalHostString();
            return localHost;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    // ====================================通信通道逻辑=======================

    /**
     * 请求注册对话通道
     */
    public static boolean sendAiRegisterChannel() {
        boolean registerResult = true;
        // 发送注册请求，同步获取响应
        ApiResponse sndMsgRtn = CodeChatRequestSender.SendRegisterChannel();
        // 通道注册失败，推送气泡通知
        if (sndMsgRtn.getRtnCode() != RtnCode.SUCCESS) {
            logger.warn("[cf] LoginService sendAiRegisterChannel fail,rtnCode:{}", sndMsgRtn.getRtnCode());
            registerResult = false;
            if (sndMsgRtn.getRtnCode() == RtnCode.NOT_LOGIN) {
                onLoginEvent(Constants.LoginStatus_NOK, RtnCode.NOT_LOGIN);
            } else {
                onLoginEvent(Constants.LoginStatus_NOK, RtnCode.NO_CHANNEL);
            }
        }
        // 通道注册消息发生成功，根据不同通信协议，解析响应结果
        //http通信通道：同步解析返回结果【已废弃】
        if (ConfigWrapper.ChannelType == Constants.ChannelStyle_Http) {
            CodeChatRequestMessage caiResp = gson.fromJson(sndMsgRtn.getMessage(), CodeChatRequestMessage.class);
            if (caiResp.getContext() != null && caiResp.getContext().getOptResult() == RtnCode.SUCCESS) {
                loginStatus = Constants.LoginStatus_OK;
                userStatus = Constants.UserStatus_OK;
            } else {
                loginStatus = Constants.LoginStatus_NOK;
            }
            if (caiResp.getContext() != null) {
                onLoginEvent(loginStatus, RtnCode.NO_CHANNEL);
            } else {
                onLoginEvent(loginStatus, caiResp.getContext().getOptResult());
            }


        } else if (ConfigWrapper.ChannelType == Constants.ChannelStyle_Websocket) {
            // Socket通信通道：启动定时器，间隔检测登录状态，失效则推送气泡通知
            TimerTask timerTask = new TimerTask() {
                @Override
                public void run() {
                    // loginStatus 由 CodeAIRequestReceiver.OnReceiveMessageFromCodeAI 监听socket下行消息动态更新
                    onLoginEvent(loginStatus, RtnCode.RECV_TIMEOUT);
                }
            };
            msgResvTimeoutTimer = new Timer();
            msgResvTimeoutTimer.schedule(timerTask, ConfigWrapper.AsyncMessageRecvTimeout * 1000L);
        }

        return registerResult;
    }

    /**
     * 通道注册结果 回调处理
     *
     * @param result 请求结果
     */
    public static void onRegisterResult(int result) {
        if (msgResvTimeoutTimer != null) {
            msgResvTimeoutTimer.cancel();
            msgResvTimeoutTimer = null;
        }
        //logger.debug("[cf] LoginService AiRegisterChannel return,result:<{}>", result);
        // 通道建立成功
        if (result == RtnCode.SUCCESS) {
            loginStatus = Constants.LoginStatus_OK;
            // 发送API Key查询消息
            //logger.debug("[cf] LoginService OnRegisterResult sendGetUserApiKey......");
            ApiResponse sndMsgRtn = CodeChatRequestSender.SendGetUserApiKey();
            if (sndMsgRtn.getRtnCode() != RtnCode.SUCCESS) {
                logger.warn("[cf] LoginService logout by SendGetUserApiKey,sndMsgRtn:<{}>", sndMsgRtn);
                // apiKey查询请求发送失败，执行登出行为
                logout(result);
            }
            // 执行登录后的事件，推送气泡通知 (挪到apiKey查询成功后才算登录成功）
            //OnLoginEvent(loginStatus, result);
        } else {
            logger.warn("[cf] LoginService logout by AiRegisterResult,result:<{}>", result);
            // 通道注册失败，执行登出行为
            logout(result);
        }
    }

    /**
     * 处理获取user apikey的下行结果消息
     *
     * @param result
     * @param apiKey
     */
    public static void onUserApiKeyResult(int result, String apiKey, ApigwWebsocketRespPayload respPayload) {
        DebugLogUtil.println(String.format("LoginService onUserApiKeyResult return,result:%s,respPayload:%s", result, JsonUtil.getInstance().toJson(respPayload)));
        // 获取成功
        if (result == RtnCode.SUCCESS) {
            // 处理外层下发参数
            // 下发Agent版本信息
            List<AgentVersion> agentVersions = respPayload.getAgentVersion();
            if (agentVersions == null || agentVersions.isEmpty()) {
                logger.warn("[cf] agent version is null or empty, set agentVersion local storage to empty.");
                PropertiesComponent.getInstance().setValue(Constants.AgentVersionInfoKey, "");
            } else {
                try {
                    PropertiesComponent.getInstance().setValue(Constants.AgentVersionInfoKey, new ObjectMapper().writeValueAsString(agentVersions));
                } catch (JsonProcessingException e) {
                    logger.error("[cf] onUserApiKeyResult set AgentVersions error:\n");
                    e.printStackTrace();
                }
            }
            // 当前版本ChangeNotes
            if (StringUtils.isNotBlank(respPayload.getClientVersionContent())) {
                PropertiesComponent.getInstance().setValue(Constants.CurrentVersionChange, respPayload.getClientVersionContent());
            }

            // 处理initConfig配置参数
            ApigwWebsocketRespConfig initConfig = respPayload.getConfig();
            PropertiesComponent.getInstance().setValue(Constants.ApiKeyPropKey, apiKey);
            String inputCharacterLimit = initConfig.getInputCharacterLimit();
            if (StringUtil.isNumber(inputCharacterLimit)) {
                PropertiesComponent.getInstance().setValue(Constants.InputCharacterLimit, Integer.parseInt(inputCharacterLimit), Constants.DefaultInputCharacterLimit);
            }
            String snippetsCharacterLimit = initConfig.getSnippetsCharacterLimit();
            if (StringUtil.isNumber(snippetsCharacterLimit)) {
                PropertiesComponent.getInstance().setValue(Constants.SnippetsCharacterLimit, Integer.parseInt(snippetsCharacterLimit), Constants.DefaultSnippetsCharacterLimit);
            }
            String chatCharacterLimit = initConfig.getChatCharacterLimit();
            if (StringUtil.isNumber(chatCharacterLimit)) {
                PropertiesComponent.getInstance().setValue(Constants.ChatCharacterLimit, Integer.parseInt(chatCharacterLimit), Constants.DefaultChatCharacterLimit);
            }
            String printCacheConfig = initConfig.getPrintCacheConfig();
            if (StringUtils.isNotBlank(printCacheConfig)) {
                PropertiesComponent.getInstance().setValue(Constants.ChatPrintCacheConfig, printCacheConfig);
            }
            String clientUrlSubPath = initConfig.getClientUrlSubPath();
            if (StringUtils.isNotBlank(clientUrlSubPath)) {
                PropertiesComponent.getInstance().setValue(Constants.ClientUrlSubPath, clientUrlSubPath);
            }
            String indexVersion = initConfig.getIndexVersion();
            if (StringUtils.isNotBlank(indexVersion)) {
                PropertiesComponent.getInstance().setValue(Constants.IndexVersion, indexVersion);
            }
            String ignoreList = initConfig.getIgnoreList();
            if (StringUtils.isNotBlank(ignoreList)) {
                PropertiesComponent.getInstance().setValue(Constants.IgnoreList, ignoreList);
            }
            String codeCompleteStrategy = initConfig.getCodeCompleteStrategy();
            if (StringUtils.isNotBlank(codeCompleteStrategy)) {
                PropertiesComponent.getInstance().setValue(Constants.CodeCompleteStrategy, codeCompleteStrategy);
            }

            // 登录成功
            onLoginEvent(loginStatus, result);

            // 弹出气泡通知
//            MessageBalloonNotificationUtil.showBalloonNotificationByReason(IdeUtil.findCurrentProject(), "服务登录成功！", -1);

            // 升级提醒
            if (!EnvUtil.isSec()) {
                new VersionCheckerTask(IdeUtil.findCurrentProject(), respPayload.getClientLatestVersion(), respPayload.getClientLatestVersionId(), respPayload.getClientLatestVersionDownloadUrl(), respPayload.getClientLatestVersionContent(), clientUrlSubPath).queue();
            }
        } else {
            logger.warn("[cf] LoginService logout by UserApiKeyResult,result:<{}>", result);
            // 查询失败，登出
            logout(result);
        }
    }


    /**
     * 通道会话事件回调处理
     *
     * @param eventCode 事件id
     */
    public static void onUserSessionEvent(int eventCode) {
        switch (eventCode) {
            case RtnCode.INVALID_USER:
            case RtnCode.USER_FORBIDDEN:
            case RtnCode.INVALID_SESSION_ID:
                logger.warn("[cf] LoginService logout by UserSessionEvent,eventCode:<{}>", eventCode);
                // 执行用户登出操作
                logout(eventCode);
                break;
        }
    }

    public static void onLoginEvent(int status, int reason) {
        // 获取当前打开的所有项目实例
        Project[] openProjects = ProjectManager.getInstance().getOpenProjects();

        // 根据登录状态变化而做出响应
        for (Project project : openProjects) {
            ApplicationManager.getApplication().invokeLater(() -> {
                if (status == Constants.LoginStatus_OK) {
                    onLoginSuccess(project);
                } else {
                    onLoginFailed(project, status, reason);
                }
            });
        }
        // 完成后统一更新statusbar
        Notify.Companion.updateStatusNotify();
    }

    private static void onLoginSuccess(Project project) {

        AgentManager agentManager = AgentManager.getInstance(project);
        TabbyAgentClient tabbyAgentClient = (TabbyAgentClient) agentManager.getAgentCommClient(AgentNameConstant.TABBY_AGENT);

        if (tabbyAgentClient != null && tabbyAgentClient.isEnabled()) {
                tabbyAgentClient.request("updateConfig", ["server.endpoint", pluginSettings().getAddress()], null) {
                response ->
            }
        }



            CodeChatWebview codeChatWebview = CodeChatWebview.getInstance(project);
        // 通知Webview页面变化
        // 展示首屏快捷指令按钮组、展示会话历史列表按钮、展示模版列表按钮、恢复输入框可用状态
        if (Objects.nonNull(codeChatWebview) && codeChatWebview.isLoaded()) {
            codeChatWebview.getPushStatusHandler().onStatusChanged(StatusEventType.WSSERVER_RECONNECT, WebViewRspCode.SUCCESS);
            codeChatWebview.getPushStatusHandler().onStatusChanged(StatusEventType.LOGIN_SUCCESS, WebViewRspCode.SUCCESS);
        }

        // 初始化 agents
        logger.info("[cf] Try initializing agents in LoginService...");
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            if (AgentManager.getStatus(project) == AgentManager.AgentStatus.IDLE) {
                AgentManager.tryInitialize(project);
            }
        });

    }

    private static void onLoginFailed(Project project, int status, int reason) {
        String reasonText = "";
        CodeChatWebview codeChatWebview = CodeChatWebview.getInstance(project);
        // 登录失败，弹出气泡通知显示原因
        if (!RtnMessage.containsCode(reason)) {
            reasonText = "服务端异常,错误码:" + reason;
            MessageBalloonNotificationUtil.showBalloonNotificationByReason(project, "失去登录状态。", reason);
        } else {
            reasonText = RtnMessage.getMessageByCode(reason);
            MessageBalloonNotificationUtil.showBalloonNotificationByReason(project, "失去登录状态，原因：" +
                    reasonText, reason);
        }

        // 如果是手动退出，则展示首屏登录按钮、隐藏会话历史列表按钮、隐藏模版列表按钮、收起左侧栏、禁用输入框、隐藏知识库栏
        if (Objects.nonNull(codeChatWebview) && codeChatWebview.isLoaded() && (reason == RtnCode.LOGOUT || reason == RtnCode.INVALID_SESSION_ID)) {
            codeChatWebview.getPushStatusHandler().onStatusChanged(StatusEventType.LOGOUT, WebViewRspCode.SUCCESS);
        }
        logger.warn("[cf] OnLoginEvent,loginStatus:<{}>,reasonCode:<{}>,reason:<{}>", status, reason, reasonText);
    }

   public static void callBackLoginEvent(int status, int reason) {
       // 获取当前Project实例
       Project project = IdeUtil.findCurrentProject();
       if (Objects.isNull(project)) {
           project = ProjectManager.getInstance().getDefaultProject();
       }
       final Project finalProject = project;
       CodeChatWebview codeChatWebview = CodeChatWebview.getInstance(project);

       // 根据登录状态变化而做出响应
       ApplicationManager.getApplication().invokeLater(() -> {
           String reasonText = "";
           // 登录成功
           if (status == Constants.LoginStatus_OK) {
               // 通知Webview页面变化
               // 展示首屏快捷指令按钮组、展示会话历史列表按钮、展示模版列表按钮、恢复输入框可用状态
               if (Objects.nonNull(codeChatWebview) && codeChatWebview.isLoaded()) {
                   codeChatWebview.getPushStatusHandler().onStatusChanged(StatusEventType.WSSERVER_RECONNECT, WebViewRspCode.SUCCESS);
                   codeChatWebview.getPushStatusHandler().onStatusChanged(StatusEventType.LOGIN_SUCCESS, WebViewRspCode.SUCCESS);
               }

               // 初始化 agents
               logger.info("Try initializing agents in LoginService...");
               ApplicationManager.getApplication().executeOnPooledThread(() -> {
                   AgentManager.tryInitialize(finalProject);
               });
           } else {
               // 如果是手动退出，则展示首屏登录按钮、隐藏会话历史列表按钮、隐藏模版列表按钮、收起左侧栏、禁用输入框、隐藏知识库栏
               if (Objects.nonNull(codeChatWebview) && codeChatWebview.isLoaded() && (reason == RtnCode.LOGOUT || reason == RtnCode.INVALID_SESSION_ID)) {
                   codeChatWebview.getPushStatusHandler().onStatusChanged(StatusEventType.LOGOUT, WebViewRspCode.SUCCESS);
               }
           }
           // 更新statusbar
           Notify.Companion.updateStatusNotify();

           logger.info("[cf] OnLoginEvent,loginStatus:<{}>,reasonCode:<{}>,reason:<{}>", status, reason, reasonText);
       });
   }
}
