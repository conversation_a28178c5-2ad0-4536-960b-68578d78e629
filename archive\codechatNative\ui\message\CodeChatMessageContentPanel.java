package com.srdcloud.ideplugin.assistant.codechatNative.ui.message;

import com.intellij.openapi.Disposable;
import com.intellij.openapi.project.Project;
import com.intellij.ui.JBColor;
import com.intellij.ui.components.JBPanel;
import com.intellij.ui.components.panels.VerticalLayout;
import com.intellij.util.ui.JBUI;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.ConversationMessage;
import com.srdcloud.ideplugin.assistant.codechatNative.uicomponent.CodeViewer;
import com.srdcloud.ideplugin.assistant.codechatNative.uicomponent.MessageViewer;
import com.srdcloud.ideplugin.assistant.codechatNative.uicomponent.MyScrollPane;
import com.srdcloud.ideplugin.assistant.codechatNative.uicomponent.TextViewer;
import com.srdcloud.ideplugin.general.enums.AssistantChannel;
import com.srdcloud.ideplugin.general.enums.ChatMessageType;
import com.srdcloud.ideplugin.general.enums.LanguageExt;
import com.srdcloud.ideplugin.general.enums.MessageContentType;
import com.srdcloud.ideplugin.general.utils.HtmlUtil;
import com.srdcloud.ideplugin.general.utils.IdeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yangy
 * @create 2023/6/15 9:55
 * 消息内容展示面板
 */
public class CodeChatMessageContentPanel extends JBPanel<CodeChatMessageContentPanel> implements Disposable {
    private static final Logger logger = LoggerFactory.getLogger(CodeChatMessageContentPanel.class);

    // 展示面板，可滚动
    public final MyScrollPane myScrollPane = new MyScrollPane((Component)this, 20, 31);
    public int myScrollValue = 0;

    // 消息内容
    private String myContent;

    // 内容切割：区分文本、代码
    private final Map<Integer, Object> componentMap = new HashMap<>();

    private final Project myProject;


    /**
     * 构造消息详情展示面板
     */
    public CodeChatMessageContentPanel() {
        this.myProject = IdeUtil.findCurrentProject();

        // 设置布局、透明度等
        setLayout((LayoutManager)new VerticalLayout(JBUI.scale(4)));
        setOpaque(false);
        setDoubleBuffered(true);
        setAutoscrolls(true);
        this.myScrollPane.setDoubleBuffered(true);
        this.myScrollPane.getVerticalScrollBar().setAutoscrolls(true);

        // 设置滚动条拖动事件
        this.myScrollPane.getVerticalScrollBar().addAdjustmentListener(e -> {
            SwingUtilities.invokeLater(() -> {
                int value = e.getValue();
                if ((this.myScrollValue == 0 && value > 0) || (this.myScrollValue > 0 && value == 0)) {
                    this.myScrollValue = value;
                    repaint();
                } else {
                    this.myScrollValue = value;
                }
            });

        });
    }


    // ===========逻辑函数==========
    /**
     * 渲染消息详情UI
     */
    public void paintMessage(String messageContent, ChatMessageType chatMessageType, boolean me) {
        this.myContent = messageContent;
        // 消息预处理
        messageContent = HtmlUtil.removeHtmlBodyTag(messageContent);
        // 消息分隔成不同部分
        List<MessageViewer> messageViewers = MessageViewer.extractSegments(messageContent);
        // 各个部分渲染
        for (int i = 0; i < messageViewers.size(); i++) {
            MessageViewer viewer = messageViewers.get(i);

            if (i == 0) {
                viewer.setContent(HtmlUtil.removePTag(viewer.getContent()));
            }
            int componentCount = getComponentCount();
            boolean lastLine = (i == messageViewers.size() - 1);
            if(i == 0 && viewer.getType() == MessageContentType.CODE && componentCount == 1){
                Object componentObject = this.componentMap.get(Integer.valueOf(i));
                if (componentObject instanceof TextViewer) {
                    remove(((TextViewer) componentObject).getEditorPane());
                    this.componentMap.remove(i);
                    componentCount = 0;
                }
            }

            if (i < componentCount) {
                Object componentObject = this.componentMap.get(Integer.valueOf(i));
                if (componentObject instanceof TextViewer) {
                    ((TextViewer)componentObject).setText(HtmlUtil.removeHtmlBodyTag(viewer.getContent().replace("\n", "<br>")), lastLine);
                } else if (componentObject instanceof CodeViewer) {
                    CodeViewer codeViewer = ((CodeViewer)componentObject);
                    codeViewer.updateCode(viewer.getContent().replaceAll("\\\\.", "\\."),viewer.getLanguage());
                }
            } else if (viewer.getType() == MessageContentType.TEXT) {
                // 创建文本查看器进行展示
                TextViewer textViewer = new TextViewer();
                textViewer.setText(viewer.getContent().replace("\n", "<br>"), lastLine);
                add(textViewer.getEditorPane());
                this.componentMap.put(i, textViewer);
            } else if (viewer.getType() == MessageContentType.CODE) {
                // 创建代码查看器进行展示
                CodeViewer codeViewer = new CodeViewer(this.myProject, AssistantChannel.CodeChat, viewer.getContent().replaceAll("\\\\.", "\\."), this, LanguageExt.getExtByType(viewer.getLanguage())!=null?LanguageExt.getExtByType(viewer.getLanguage()):viewer.getLanguage(),true,me,true);
                add(codeViewer.getEditorComponent());
                this.componentMap.put(i, codeViewer);
            }
        }

        // 刷新组件布局与绘制
        revalidate();
        repaint();
    }

    // 页面整体绘制
    public void paintMessage(ConversationMessage message, ChatMessageType chatMessageType, boolean me) {
        paintMessage(HtmlUtil.md2html(message.getContent()), chatMessageType, me);
    }


    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        if (this.myScrollValue > 0) {
            g.setColor(JBColor.border());
            int y = this.myScrollPane.getY() - 1;
            g.drawLine(0, y, getWidth(), y);
        }
    }


    // ===========setter/getter===
    public void dispose() {
        removeAll();
        this.myScrollPane.removeAll();
    }
    public String getText() {
        return this.myContent;
    }
}
