# SecIdea Changelog
## [3.3.0]

### Updated

- 增加智能编码功能，支持对话中进行需求描述，通过接受，拒绝实现代码的新增和变更。
- UI展示效果大幅优化。

## [3.2.1]

### Updated

- 对话功能优化
- 界面和使用体验优化
- 修复已知bug

## [3.1.0]

### Updated

- 对话窗口新增@和/命令功能
- 界面和使用体验优化
- 代码补全功能优化，并新增多种补全模式

## [3.0.0]

### Updated

- 修复已知bug
- 新增单文件或文件夹扫描功能
- 提升代码补全准确率

## [1.5.7]

### Updated

- 对话功能、扫描窗口提示优化，新增自定义指令
- 更新sbom工具
- 升级漏洞修复功能

## [1.5.6]

### Fixed

- 修复代码补全已知bug
- 功能和性能优化

## [1.5.5]

### Updated

- 更新sbom工具及其调用方式

## [1.5.4]

### Fixed

- 修复组件扫描部分大的项目无法扫描的情况

## [1.5.3]

### Fixed

- 部分已知bug修复

## [1.5.2]

### Updated

- 补全功能全面改进

## [1.5.1]

### Updated

- 功能和性能优化

## [1.5.0]

### Updated

- 补全，解释，智能修复等AI功能全面更新

## [1.4.0]

### Fixed

- 修复同一组件多个版本扫描结果仅显示一个的问题
- 添加riskLevelList和detectionState字段

## [1.3.0]

### Fixed

- 修复源码扫描结果较大时无法获取的问题

## [1.2.0]

### Updated

- 更新sbom工具

## [1.1.0]

### Fixed

- 删除用户编号长度验证
- 修复组件扫描结果详情页面参考链接无法跳转的问题

## [1.0.0]

### Added

- 开发者安全助手第一版
