package com.srdcloud.ideplugin.general.utils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.QuoteItem;

import java.lang.reflect.Type;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/19
 */
public class JsonUtil {

    private static final Gson GSON = new Gson();

    public static Gson getInstance(){
        return GSON;
    }


    //----- 列表类型Type
    public static Type quoteItemListType = new TypeToken<List<QuoteItem>>(){}.getType(); // 定义目标类型

}
