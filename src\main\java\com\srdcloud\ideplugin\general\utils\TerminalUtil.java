package com.srdcloud.ideplugin.general.utils;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.progress.ProgressIndicator;
import com.intellij.openapi.progress.Task;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowManager;
import com.intellij.terminal.JBTerminalPanel;
import com.intellij.terminal.JBTerminalWidget;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.*;
import java.awt.event.KeyEvent;
import java.util.Arrays;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.Set;

/**
 * TerminalUtil 提供与终端工具窗口交互的方法
 */
public class TerminalUtil {

    private static final Logger logger = LoggerFactory.getLogger(TerminalUtil.class);  // 日志记录器，用于记录日志信息
    private static final String TERMINAL = "Terminal";  // 终端工具窗口的名称

    /**
     * 在指定项目中打开终端并执行命令
     * @param project 项目实例
     * @param command 要执行的命令
     */
    public static void invokeTerminal(Project project, String command) {
        ToolWindowManager toolWindowManager = ToolWindowManager.getInstance(project);
        ToolWindow terminalWindow = toolWindowManager.getToolWindow(TERMINAL);

        if (terminalWindow == null) {
            logger.error("[cf] Terminal tool window not found, invoke terminal failed. ");
            return;
        }

        // activate需要invokeLater，否则会报RuntimeExceptionWithAttachments: EventQueue.isDispatchThread()=false
        ApplicationManager.getApplication().invokeLater(() -> {
            try {
                terminalWindow.activate(() ->
                        invokeWhenReady(project, terminalWindow, command)
                );
            } catch (Exception e) {
                logger.error("[cf] Error activating terminal: ", e);
                throw new RuntimeException("[cf] Error activating terminal: ", e);
            }
        });
    }

    /**
     * 在终端准备好时执行命令
     * @param project 项目实例
     * @param terminalWindow 终端工具窗口实例
     * @param command 要执行的命令
     */
    private static void invokeWhenReady(Project project, ToolWindow terminalWindow, String command) {
        new Task.Backgroundable(project, "Preparing terminal", false) {
            @Override
            public void run(@NotNull ProgressIndicator indicator) {
                // 尝试最多10次，每次等待200毫秒
                for (int attempt = 0; attempt < 10; attempt++) {
                    JBTerminalWidget terminalWidget = findTerminalComponent(terminalWindow.getComponent());
                    if (terminalWidget != null && terminalWidget.getTerminalStarter() != null) {
                        // 执行时需再次切换到EDT线程
                        ApplicationManager.getApplication().invokeLater(() -> {
                            try {
                                terminalWidget.setVisible(true);
                                // 分割命令并逐个发送
                                String[] commandList = Arrays.stream(command.split("\\n")).filter(s -> !s.trim().isEmpty()).toArray(String[]::new);
                                for (int i = 0; i < commandList.length; i++) {
                                    String singleCommand = commandList[i];
                                    // 发送命令到终端
                                    terminalWidget.getTerminalStarter().sendString(singleCommand.trim(), false);
                                    // 模拟按下回车键
                                    mockPressEnterKey(terminalWidget.getTerminalPanel());

                                    if (i != commandList.length - 1) {
                                        try {
                                            Thread.sleep(200);
                                        } catch (Exception e) {
                                            logger.warn("[cf] Thread sleep interrupted when waiting for command execution, ignored. ");
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                logger.error("[cf] Error sending command to terminal: ", e);
                                throw new RuntimeException("[cf] Error sending command to terminal: ", e);
                            }
                        });
                        return;
                    }

                    try {
                        Thread.sleep(200);
                    } catch (InterruptedException ignored) {
                        logger.warn("[cf] Thread sleep interrupted when waiting for terminal, ignored. ");
                    }
                }

                // 如果多次尝试后仍失败
                logger.error("[cf] Terminal starter not available after multiple attempts");
            }
        }.queue();
    }

    /**
     * 查找终端组件，从根组件开始遍历
     * @param rootComponent 根组件
     * @return JBTerminalWidget 终端组件，如果未找到则返回null
     */
    private static JBTerminalWidget findTerminalComponent(Component rootComponent) {
        if (rootComponent == null) {
            return null;
        }

        LinkedList<Component> queue = new LinkedList<>();
        Set<Component> visited = new HashSet<>();  // 已访问组件集合

        queue.add(rootComponent);
        visited.add(rootComponent);  // 标记根组件为已访问

        while (!queue.isEmpty()) {
            Component component = queue.poll();

            if (component instanceof JBTerminalWidget) {
                return (JBTerminalWidget) component;
            }

            if (component instanceof Container container) {
                for (Component child : container.getComponents()) {
                    if (child != null && visited.add(child)) {
                        queue.add(child);
                    }
                }
            }
        }

        return null;
    }

    /**
     * 模拟按下回车键
     * @param panel 终端面板组件
     */
    private static void mockPressEnterKey(JBTerminalPanel panel) {
        panel.handleKeyEvent(new KeyEvent(
                panel,
                KeyEvent.KEY_PRESSED,
                System.currentTimeMillis(),
                0,
                KeyEvent.VK_ENTER,
                KeyEvent.CHAR_UNDEFINED
        ));
    }
}
