package com.srdcloud.ideplugin.agent.config;

import java.nio.file.Paths;

public class AgentPath {
    private final String pluginName;
    private final String userHome;
    private final String osType;
    private final String agentName;
    protected final String agentVersion;

    public AgentPath(String pluginName, String agentName) {
        this.pluginName = pluginName;
        this.agentName = agentName;
        this.userHome = System.getProperty("user.home");
        this.osType = System.getProperty("os.name").toLowerCase();
        this.agentVersion = "v1.0.0";
    }

    public AgentPath(String pluginName, String agentName, String agentVersion) {
        this.pluginName = pluginName;
        this.agentName = agentName;
        this.userHome = System.getProperty("user.home");
        this.osType = System.getProperty("os.name").toLowerCase();
        this.agentVersion = agentVersion;
    }

    public String getAgentBasePath() {
        if (osType.contains("windows")) {
            return String.format("%s\\AppData\\Local\\.%s", userHome, pluginName);
        } else if (osType.contains("mac")) {
            return String.format("%s/.%s", userHome, pluginName);
        } else {
            return String.format("%s/.local/share/.%s", userHome, pluginName);
        }
    }

    public String getAgentFilePath() {

        // 设置os
        String osName = System.getProperty("os.name").toLowerCase();
        String os = "";
        if (osName.contains("windows")) {
            os = "win32";
        }else if (osName.contains("mac") || osName.contains("darwin")) {
            os = "darwin";
        }else if (osName.contains("nix") || osName.contains("nux") ||  osName.contains("aix")) {
            os = "linux";
        } else {
            os = "linux";
        }

        // 设置arch
        String osArch = System.getProperty("os.arch");
        String arch = "";
        if ((osArch.contains("arm") &&  osArch.contains("64")) || osArch.contains("aarch64")) {
            arch = "arm64";
        }else if (osArch.contains("amd64") || osArch.contains("x86_64") ) {
            arch = "x64";
        }else {
            arch = "x64";
        }

        String target = os + "-" + arch;
        String agentVersionFolder = agentName + "-" + agentVersion + "-" + target;
        String agentFileName = "index.js";

        return Paths.get(getAgentBasePath(), "agent", agentName, agentVersionFolder, agentFileName).toString();
    }

    public String getConfigPath() {
        return Paths.get(getAgentBasePath(), "agent", "config.json").toString();
    }
} 