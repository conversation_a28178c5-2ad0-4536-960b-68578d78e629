package com.srdcloud.ideplugin.codecomplete.handle

import com.intellij.codeInsight.CodeInsightActionHandler
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.event.DocumentEvent
import com.intellij.openapi.editor.impl.EditorImpl
import com.intellij.openapi.project.Project
import com.intellij.psi.PsiFile
import com.srdcloud.ideplugin.codecomplete.domain.CompletionType
import com.srdcloud.ideplugin.codecomplete.handle.CompletionContext.Companion.initOrGetInlineCompletionContext
import com.srdcloud.ideplugin.codecomplete.domain.CompletionRequest
import com.srdcloud.ideplugin.codecomplete.domain.CompletionState
import com.srdcloud.ideplugin.codecomplete.domain.CompletionState.Companion.getInlineCompletionState
import com.srdcloud.ideplugin.codecomplete.domain.CompletionState.Companion.initOrGetInlineCompletionState
import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.ICompletionProvider
import com.srdcloud.ideplugin.general.constants.Constants
import com.srdcloud.ideplugin.general.constants.RtnCode
import com.srdcloud.ideplugin.general.constants.RtnMessage
import com.srdcloud.ideplugin.general.enums.ActivityType
import com.srdcloud.ideplugin.general.utils.DebugLogUtil
import com.srdcloud.ideplugin.general.utils.EnvUtil
import com.srdcloud.ideplugin.service.LoginService
import com.srdcloud.ideplugin.service.UserActivityReportService
import com.srdcloud.ideplugin.statusbar.Notify
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import java.time.Duration
import java.time.LocalDateTime
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger

/**
 * 行内补全处理
 */
class CompletionHandler(private val scope: CoroutineScope) : CodeInsightActionHandler {
    private val logger = logger<CompletionHandler>()

    // CodeInsightActionHandler基类中的触发方式：如果有注册到快捷键的话
    override fun invoke(project: Project, editor: Editor, file: PsiFile) {
        val inlineState = editor.getInlineCompletionState() ?: return

        showInlineSuggestion(editor, inlineState, editor.caretModel.offset)
    }

    // 从document变动事件触发自动补全
    fun invoke(event: DocumentEvent, editor: EditorImpl, provider: ICompletionProvider) {
        val request = CompletionRequest.fromDocumentEvent(event, editor) ?: return
        doComplete(request, provider)
    }


    // 从editor实例中发起手动补全
    fun invoke(editor: EditorImpl, provider: ICompletionProvider) {
        val request = CompletionRequest.fromEditor(editor) ?: return
        doComplete(request, provider)
    }


    /**
     * 处理补全请求，执行补全逻辑
     */
    private fun doComplete(request: CompletionRequest, provider: ICompletionProvider) {
        scope.launch {
            val beginTime = LocalDateTime.now()
            if (EnvUtil.checkDebugAble()) {
                DebugLogUtil.info("CodeCompletion begin")
            }
            // 记录文档修改时间戳
            val modificationStamp = request.document.modificationStamp

            // 通过对应provider，获取补全建议
            provider.getProposals(request)

            val editor = request.editor
            val inlineState = editor.initOrGetInlineCompletionState()

            // 判断：如果当前文档最新修改时间戳 与 上文获取补全建议前记录的时间戳 一致，则说明期间没有变化，进行补全建议提示
            if (modificationStamp == request.document.modificationStamp) {
                val endTime = LocalDateTime.now()
                val duration = Duration.between(beginTime, endTime)
                val milliseconds = duration.toMillis()

                // 计算延迟时间并传递给展示函数
                showInlineSuggestion(editor, inlineState, request.endOffset, request.type == CompletionType.AUTO, milliseconds)

                if (milliseconds > Constants.Code_completion_alarm_delay) {
                logger.warn("[cf] CodeCompletion alarm, total duration: $milliseconds ms")
                }
            }
            if (EnvUtil.checkDebugAble()) {
                DebugLogUtil.info("CodeCompletion end")
            }
        }
    }

    /**
     * 展示补全提示
     */
    fun showInlineSuggestion(editor: Editor, inlineContext: CompletionState, startOffset: Int, isAuto: Boolean = false, latency: Long = 0, needReport: Boolean = true) {
        val suggestions = inlineContext.suggestions
        if (suggestions.isEmpty()) {
            return
        }

        val suggestionIndex = inlineContext.suggestionIndex
        if (suggestions.getOrNull(suggestionIndex) == null) {
            return
        }
        var offset = 0
        // 更新编辑器内提示内容
        ApplicationManager.getApplication().invokeLater {
            offset = if (startOffset > 0) startOffset else editor.caretModel.offset
            editor.initOrGetInlineCompletionContext().update(suggestions, suggestionIndex, offset)
        }
        inlineContext.lastStartOffset = offset
        inlineContext.lastModificationStamp = editor.document.modificationStamp

        // 进行指标上报，增加isAuto和latency参数
        if (needReport) UserActivityReportService.codeActivityReport(
            ActivityType.CODE_COMPLETION_GEN,
            editor.project,
            suggestions[suggestionIndex].text,
            null,
            isAuto,
            latency
        );
    }

    companion object {
        fun mute() = isMuted.set(true)
        fun unmute() = isMuted.set(false)

        fun startWorking(type: CompletionType) {
            workingType.set(type.tokenSize)
            Notify.updateStatusNotify()
        }

        fun stopWorking(errCode: Int) {
            workingType.set(CompletionType.NULL.tokenSize)
            errMsg = if (errCode == RtnCode.SUCCESS) "" else RtnMessage.getMessageByCode(errCode)
            LoginService.onUserSessionEvent(errCode)
            Notify.updateStatusNotify()
        }

        var errMsg = ""

        val workingType = AtomicInteger(CompletionType.NULL.tokenSize)
        val isMuted = AtomicBoolean(false)
    }
}