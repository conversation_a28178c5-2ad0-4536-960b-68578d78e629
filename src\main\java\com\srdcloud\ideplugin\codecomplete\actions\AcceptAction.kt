package com.srdcloud.ideplugin.codecomplete.actions

import com.intellij.codeInsight.hint.HintManagerImpl.ActionToIgnore
import com.intellij.openapi.actionSystem.DataContext
import com.intellij.openapi.editor.Caret
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.actionSystem.EditorAction
import com.intellij.openapi.editor.actionSystem.EditorWriteActionHandler
import com.srdcloud.ideplugin.codecomplete.handle.CompletionContext.Companion.getInlineCompletionContextOrNull

/**
 * <AUTHOR>
 * @date 2025/6/6
 * @desc 采纳补全建议
 */
class AcceptAction : EditorAction(InsertHandler()), ActionToIgnore, CompletionAction {
    class InsertHandler : EditorWriteActionHandler() {
        // 采纳当前补全上下文建议
        override fun executeWriteAction(editor: Editor, caret: Caret?, dataContext: DataContext) {
            editor.getInlineCompletionContextOrNull()?.insert()
        }

        // 当前光标位置，与补全上下文的起始位置一致，则本handler生效
        override fun isEnabledForCaret(editor: Editor, caret: Caret, dataContext: DataContext?): Boolean {
            return editor.getInlineCompletionContextOrNull()?.startOffset == caret.offset
        }
    }
}