package com.srdcloud.ideplugin.assistant.codechatNative.logics.domain;

import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.utils.UrlUtil;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.QuoteItem;
import org.apache.commons.lang.StringUtils;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 会话消息引用的抽象类
 * 该类定义了会话中引用消息的基本结构和行为，包括引用项的存储、获取源以及生成链接
 */
public abstract class ConversationMessageQuote {

    // 引用的项目项
    protected QuoteItem item;

    /**
     * 构造函数，初始化引用的项目项
     * @param item 要引用的项目项
     */
    public ConversationMessageQuote(QuoteItem item) {
        this.item = item;
    }

    /**
     * 获取引用的项目项
     * @return 引用的项目项
     */
    public QuoteItem getItem() {
        return item;
    }

    /**
     * 所属知识库
     * @return 所属知识库
     */
    public String getSource() {
        return item.getMetadata().getSource();
    }

    /**
     * 获取选项跳转链接：需要区分是文档预览，还是普通链接跳转
     * @return 根据引用项的类型返回相应的跳转链接
     */
    public String getLink() {
        // 获取引用源的URL
        String sourceUrl = item.getMetadata().getSource();
        // 对URL进行UTF-8编码，确保链接的正确性
        String encodeUrl = URLEncoder.encode(sourceUrl, StandardCharsets.UTF_8);
        // 获取URL对应的文件类型，用于判断是否为文档链接
        String fileType = UrlUtil.getUrlFileType(sourceUrl);

        // 默认链接为源URL
        String link = sourceUrl;
        // 如果检测到文件类型，则生成文档预览链接
        if (StringUtils.isNotBlank(fileType)) {
            link = ConfigWrapper.serverUrl + "smartassist/knowledgeBase/view/" + fileType + "/" + encodeUrl;
        }
        return link;
    }

    /**
     * 获取完整的标题
     * 该方法需在子类中实现，用于返回引用的完整标题
     * @return 引用的完整标题
     */
    public abstract String getFullTittle();

}

