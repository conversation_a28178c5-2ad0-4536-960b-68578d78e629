package com.srdcloud.ideplugin.webview.codechat.composer;

import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.composer.ComposerService;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import com.srdcloud.ideplugin.general.utils.GitUtil;
import com.srdcloud.ideplugin.general.utils.IdeUtil;
import com.srdcloud.ideplugin.general.utils.JsonUtil;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;
import com.srdcloud.ideplugin.webview.codechat.composer.request.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;


public class ComposerHandler {
    private static final Logger logger = LoggerFactory.getLogger(ComposerHandler.class);

    private final Project project;

    private final CodeChatWebview parent;

    public ComposerHandler(Project project, CodeChatWebview parent) {
        this.project = project;
        this.parent = parent;
    }

    public void processComposerRequest(String request) {
        ComposerChatRequest chatReq = JsonUtil.getInstance().fromJson(request, ComposerChatRequest.class);
        String reqType = chatReq.getData().getReqType();
        switch (reqType) {
            case ComposerRequest.REQ_TYPE_GET_CHAT_CONTEXTS:
                // todo
                break;
            case ComposerRequest.REQ_TYPE_COMPOSER_CHAT:
                DebugLogUtil.info("[cf] REQ_TYPE_COMPOSER_CHAT: " + chatReq.getData().getDisplayContent());
                ComposerService.Companion.getInstance(project).startConversation(
                        chatReq.getData().getDialogId(),
                        chatReq.getData().getInput(),
                        chatReq.getData().getDisplayContent(),
                        chatReq.getData().getCreateTime(),
                        chatReq.getData().getContextInputItems(),
                        chatReq.getData().getChatType(),
                        chatReq.getData().getTitle(),
                        StringUtils.isBlank(chatReq.getData().getModelName()) ? Constants.ComposerModelName : chatReq.getData().getModelName(),
                        GitUtil.getGitUrls(project),
                        chatReq.getData().getSelectedWorkItems()
                );
                break;
            case ComposerRequest.REQ_TYPE_STOP_COMPOSER_CHAT:
                ComposerService.Companion.getInstance(project).stopConversation(chatReq.getData().getDialogId());
                break;
            case ComposerRequest.REQ_TYPE_LOAD_HISTORY:
                logger.info("[cf] REQ_TYPE_LOAD_HISTORY: {}", chatReq.getData().getDialogId());
                ComposerService.Companion.getInstance(project).loadHistory(chatReq.getData().getDialogId());
                break;
            case ComposerRequest.REQ_TYPE_DELETE_HISTORY:
                ComposerService.Companion.getInstance(project).deleteHistory(chatReq.getData().getDialogId());
                break;
            default:
                // todo: handle exception
        }
    }
}
