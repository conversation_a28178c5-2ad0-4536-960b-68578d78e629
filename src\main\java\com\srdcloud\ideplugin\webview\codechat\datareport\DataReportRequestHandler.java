package com.srdcloud.ideplugin.webview.codechat.datareport;

import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import com.srdcloud.ideplugin.general.utils.JsonUtil;
import com.srdcloud.ideplugin.service.UserActivityReportService;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;
import com.srdcloud.ideplugin.webview.codechat.datareport.request.DataReportRequest;

/**
 * <AUTHOR>
 * 数据报告请求处理器
 */
public class DataReportRequestHandler {

    /**
     * 当前项目实例
     */
    private final Project project;

    /**
     * 父级CodeChatWebview实例
     */
    private final CodeChatWebview parent;

    /**
     * 构造函数，初始化项目和父级Webview实例
     *
     * @param project 当前项目实例
     * @param parent  父级CodeChatWebview实例
     */
    public DataReportRequestHandler(Project project, CodeChatWebview parent) {
        this.project = project;
        this.parent = parent;
    }

    /**
     * 处理数据报告请求
     *
     * @param request 请求字符串
     */
    public void processDataReportRequest(String request) {
        // 将请求字符串解析为DataReportRequest对象
        DataReportRequest dataReportRequest = JsonUtil.getInstance().fromJson(request, DataReportRequest.class);

        // 获取活动类型
        String activityType = dataReportRequest.getData().getActivityType();

        DebugLogUtil.info("[cf] data report: " + request);
        DebugLogUtil.info("[cf] data report message length: " + dataReportRequest.getData().getAnswer().length());

        // 代码相关行为上报
        UserActivityReportService.codeActivityReport(activityType, project, dataReportRequest.getData().getAnswer(), null, null, null);
    }
}
