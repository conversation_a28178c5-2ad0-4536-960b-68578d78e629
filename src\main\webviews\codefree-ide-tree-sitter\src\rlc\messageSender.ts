
// 封装消息发送
export default class MessageSender {

    // 处理error
    public static sendErrorMessage(errorMessage: string) {
        window.treeSitterQuery({
            request: JSON.stringify({
                type: "error",
                errorMessage: errorMessage
            }),
            persistent: false,
            onSuccess: function (response: any) {

            },
            onFailure: function (error_code: string, error_message: string) {
                console.log(`TreeSitter Webview error,error_code:${error_code},error_message:${error_message}`);
            }
        })
    }
}