package com.srdcloud.ideplugin.assistant.codechatNative.ui;

import com.intellij.find.SearchTextArea;
import com.srdcloud.ideplugin.general.utils.FormatUtil;
import org.jetbrains.annotations.NotNull;

import javax.swing.*;
import java.awt.*;

/**
 * <AUTHOR>
 * @date 2024/7/11
 * @desc 开发问答输入框自行封装：支持根据内容自适应高度
 */
public class CodeChatInputTextArea extends SearchTextArea {

    int minHeight;


    int maxHeight;

    public CodeChatInputTextArea(@NotNull JTextArea textArea, boolean searchMode, int minHeight, int maxHeight) {
        super(textArea, searchMode);
        this.minHeight = minHeight;
        this.maxHeight = maxHeight;
    }

    /**
     * 根据text内容计算自适应高度
     * @return
     */
    @Override
    public Dimension getPreferredSize() {
        Dimension dim = FormatUtil.getTextPreferSize(this.getTextArea());
        if (dim.height < minHeight) {
            dim.height = minHeight;
        }
        if (dim.height > maxHeight) {
            dim.height = maxHeight;
        }
        return dim;
    }

    /**
     * 预留改写页面布局接口
     */
    @Override
    public void updateLayout() {
        super.updateLayout();
    }
}
