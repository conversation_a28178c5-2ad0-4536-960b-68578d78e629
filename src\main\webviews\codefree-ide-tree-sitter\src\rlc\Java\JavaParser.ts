import Parser from "web-tree-sitter";
import CodeObjectIndexSystem from '../codeObjectIndexSystem';
import RelativeCodeObject from '../relativeCodeObject';

import {UpdateObject} from '../updateObject';
import type {IRelativeCodeFinder} from "../IRelativeCodeFinder";

// 类型别名：Node表示TreeSitter语法解析节点
type Node = Parser.SyntaxNode;

// 语法解析子节点,基于SyntaxNode的基础上，增加提取出来的节点信息
interface DescendantNode {
    // SyntaxNode节点本身
    node: Node;

    // 识别出来的信息，存到对应节点上
    packageSuffix: string;  // Java内部类需要额外拼接的包路径：一般是某个外部类名
    nodeIdentifierName: string; // 节点名
}

// 变量声明元组：存储变量声明所属的包路径、类型名
interface VariableDeclaration {
    packageName: string;
    typeName: string;
}

interface Variables {
    [name: string]: VariableDeclaration;
}

// parse解析得到的内容：一个代码文件中的主要信息，包括：所在包、import包、关联对象
interface JavaFile {
    path: string; // 代码文件路径
    packageName: string; // 文件包名
    importPackages: string[]; // 文件import部分
    fullObjects: JavaObject[]; // 文件中所有对象信息（全）
    cacheObjects: UpdateObject[]; // 代码文件的简化信息（用于关联对象缓存）
}

// 关联对象
interface JavaObject {
    // 关联对象名：类名、接口名、枚举名
    objectName: string;

    // 关联对象类型：类定义、接口定义、枚举定义
    objectType: string;

    // 关联内容简要
    simpleText: string; // 整个代码文件简化文本
    fields: JavaField[]; // 成员变量信息
}

// 成员变量信息
interface JavaField {
    // 成员变量所属类型包路径
    fieldPackage: string;

    // 成员变量所属类型
    fieldType: string;

    // 成员变量名
    fieldVariable: string;
}


export class JavaParser implements IRelativeCodeFinder {
    public parser: Parser | null;

    private objectSystem: CodeObjectIndexSystem;

    public constructor(parser: Parser, codeObjectIndexSystem: CodeObjectIndexSystem) {
        this.parser = parser;
        this.objectSystem = codeObjectIndexSystem;
    }


    // =======  三大核心业务逻辑  =========

    /**
     * 对外逻辑1：解析传入文件内容，更新关联文件缓存
     * 解释代码文件，将代码文件中的主要语法对象解释出来，并存入代码对象缓存索引系统，以提供后续跨文件对象关联使用
     * 本接口为插件启动时，以及代码工程有代码文件发生用户非输入代码更新时调用
     *
     * @param fileName        代码文件名称，为代码文件在代码工程内的路径
     * @param codeFileContent 代码文件内容
     */
    public ParseFile(languageExt: string, fileName: string, codeFileContent: string): JavaFile | null {
        // 解析文件，获取语言特性
        const currFile = this.parseJavaCode(fileName, codeFileContent, undefined);
        if (!currFile) {
            console.log(`JavaParser ParseFile skip,code file parse is null.`)
            return null;
        }

        // 更新关联对象缓存
        this.objectSystem.updateByFile(
            languageExt,
            currFile.packageName,
            currFile.path,
            currFile.cacheObjects
        );

        return currFile;
    }

    /**
     * 对外逻辑2：解析传入文件内容，返回关联对象
     */
    public FindRelativeObject(
        languageExt: string,
        path: string,
        content: string,
        row: number,
        column: number,
        goModMapJson: string
    ): RelativeCodeObject | null {
        console.log(`JavaParser FindRelativeObject path:${path}`)

        // 识别代码文件，得到AST语法树根节点
        const rootNode = this.getRootNode(content);
        if (!rootNode) {
            console.log("JavaParser FindRelativeObject failed, rootNode is null")
            return null;
        }

        // 解析文件，获取相关语言特性
        const currFile = this.parseJavaCode(path, content, rootNode);
        if (!currFile) {
            console.log(`JavaParser FindRelativeObject skip,code file parse is null.`)
            return null;
        }

        // ----- 提取用于触发关联查找对象 -----
        const typeFilter: Array<string> = ['identifier', 'type_identifier'];
        // 1、尝试提取行内变量
        const nodes = rootNode.descendantsOfType(typeFilter, {row, column: 0}, {row, column});
        let candidateNode: Node | null;
        let identifier = '';
        if (nodes.length) {
            // 如果当前行存在变量，则倒序取最近一个变量定义节点作为查找起点
            candidateNode = nodes[nodes.length - 1];
            identifier = candidateNode.text.trim();
        } else {
            // 如果当前行没有变量，则以光标停留位置所在节点作为查找起点
            candidateNode = rootNode.descendantForPosition({row, column});
        }

        // 从候选节点开始，向上查找出其父节点链路，拼接在待查找节点队列上
        const nodeList: Node[] = [];
        while (candidateNode) {
            nodeList.push(candidateNode);
            candidateNode = candidateNode.parent;
        }
        if (nodeList.length === 0) {
            console.log(`JavaParser FindRelativeObject skip,nodeList is empty.`)
            return null;
        }

        // 从第一个关联查找节点开始
        const currNode = nodeList[0];
        if (!currNode) {
            console.log(`JavaParser FindRelativeObject skip,rootNode is null.`)
            return null;
        }

        // 检查光标是否在方法中：如果不在方法中，则不支持关联查找
        let methodNode: Node | null = null;
        let methodNodePos = 0;
        for (let i = 0; i < nodeList.length; i++) {
            const node = nodeList[i];
            if (node.type === 'method_declaration' || node.type === 'constructor_declaration') {
                methodNode = node;
                methodNodePos = i;
                break;
            }
        }
        if (!methodNode) {
            console.log(`JavaParser FindRelativeObject skip,cursor is not in method.`)
            return null;
        }

        // 2、尝试提取继承关系
        let targetPackageName: string | undefined = '';
        let targetObjectName = '';

        // 往前查找各级节点：找到类声明层级,提取父类名、当前类名
        let currClass = null;
        for (let j = methodNodePos; j < nodeList.length; j++) {
            const node = nodeList[j];
            if (node.type === 'class_declaration') {
                // 提取父类
                const superNode = node.childForFieldName('superclass');
                if (superNode) {
                    const superIdentifier = this.firstDescendantOfType(superNode, 'type_identifier');
                    if (superIdentifier) {
                        // 如果有继承关系，则优先用父类作为关联对象进行覆盖
                        identifier = superIdentifier.text.trim();
                    }
                }
                // 提取当前类名
                const classNameNode = this.firstDescendantOfType(node, 'identifier');
                if (classNameNode) {
                    const className = classNameNode.text.trim();
                    currClass = currFile.fullObjects.find(jo => jo.objectName === className);
                    if (currClass) {
                        break;
                    }
                }
            }
        }
        // 如果既没有显式变量声明，也没有继承关系，则没有可进行关联的变量
        if (!identifier) {
            console.log(`JavaParser FindRelativeObject skip,identifier is null.`)
            return null;
        }

        // ----- 根据关联变量，反推关联对象路径与具体关联类 -----
        // 1、从当前类的成员变量列表尝试查找
        if (currClass) {
            for (const field of currClass.fields) {
                if (identifier === field.fieldVariable) {
                    targetPackageName = field.fieldPackage;
                    targetObjectName = field.fieldType;
                    break;
                }
            }
        }

        // 2、从函数的参数列表、局部变量列表尝试查找
        const packages = [...currFile.importPackages, currFile.packageName + '.*'];
        if (!targetPackageName) {
            const declaredVariables = this.getMethodVariableDeclarations(packages, methodNode, currNode);
            if (declaredVariables[identifier]) {
                targetPackageName = declaredVariables[identifier].packageName;
                targetObjectName = declaredVariables[identifier].typeName;
            }
        }

        // 3、仅有关联对象名，通过关联对象类从缓存系统反查其路径
        if (!targetPackageName) {
            targetPackageName = this.getObjectDeclaration(packages, identifier)?.packageName;
            targetObjectName = identifier;
        }

        if (!targetPackageName) {
            console.log(`JavaParser FindRelativeObject fail,targetPackageName is null.`)
            return null;
        }
        return this.objectSystem.queryByPackageAndObjectName(languageExt, targetPackageName, targetObjectName);
    }


    /**
     * 核心逻辑：提前文件语言特性信息并返回
     */
    private parseJavaCode(path: string, content: string, rootNode?: Node): JavaFile | null {
        console.log(`JavaParser ParseFile path：${path}`)

        if (!rootNode) {
            const newNode = this.getRootNode(content);
            if (!newNode) {
                return null;
            }
            rootNode = newNode;
        }

        let packageName = '';
        const importPackages: string[] = [];
        const objects: JavaObject[] = [];
        const objectsToUpdate: UpdateObject[] = [];

        // 遍历代码文件的所有节点，提取出package, import, class, enum, interface等信息
        for (let i = 0; i < rootNode.childCount; i++) {
            const node = rootNode.child(i);
            if (!node) {
                continue;
            }
            switch (node.type) {
                // package 声明部分
                case 'package_declaration': {
                    const packageIdentifier = this.firstDescendantOfType(node, [
                        'scoped_identifier',
                        'identifier',
                    ]);
                    if (packageIdentifier) {
                        packageName = packageIdentifier.text;
                    } else {
                        console.log(`JavaParser ParseFile package_declaration skip,packageIdentifier is null.`)
                        return null;
                    }
                    break;
                }

                // import 部分
                case 'import_declaration': {
                    let importIdentifier = '';
                    for (const child of node.children) {
                        if (!child.isNamed) {
                            continue;
                        }
                        switch (child.type) {
                            case 'identifier':
                            case 'scoped_identifier':
                                importIdentifier += child.text;
                                break;
                            case 'asterisk':
                                importIdentifier += '.*';
                                break;
                            default:
                                importIdentifier = '';
                        }
                        if (!importIdentifier) {
                            break;
                        }
                    }

                    // 每识别出一个，则加入import数组
                    if (importIdentifier) {
                        importPackages.push(importIdentifier);
                    }
                    break;
                }

                // class, enum, interface 等声明部分
                case 'class_declaration':
                case 'interface_declaration':
                case 'enum_declaration': {
                    const objectNameNode = this.firstDescendantOfType(node, 'identifier');
                    if (objectNameNode) {
                        for (const descendantNode of [
                            {packageSuffix: '', nodeIdentifierName: objectNameNode.text, node},
                            ...this.descendantsOfType(
                                node,
                                ['class_declaration', 'interface_declaration', 'enum_declaration'],
                                objectNameNode.text
                            ),
                        ]) {
                            // 提取出对象的基本信息，如名称、类型、简单文本、字段等
                            const node = descendantNode.node;
                            const objectName = descendantNode.nodeIdentifierName;
                            const objectType = node.type;
                            const [simpleText, fields] = this.extractJavaObject(node, importPackages);

                            // 创建关联对象
                            const javaObject: JavaObject = {objectName, objectType, simpleText, fields};
                            objects.push(javaObject);
                            objectsToUpdate.push(new UpdateObject(descendantNode.packageSuffix, objectType, objectName, simpleText));
                        }
                    }
                    break;
                }
            }
        }

        if (!packageName) {
            console.log(`JavaParser ParseFile skip,parse packageName fail.`)
            return null;
        }

        return {path, packageName, importPackages, fullObjects: objects, cacheObjects: objectsToUpdate};
    }


    // =======  以下用于解析AST语法树，提前目标语言特性  =========
    /**
     * 调用TreeSitter，解析代码文件，获取AST语法树根节点
     */
    private getRootNode(codeFileContent: string) {
        if (!this.parser) {
            return null;
        }
        try {
            return this.parser.parse(codeFileContent).rootNode;
        } catch (error) {
            throw new Error(`Parsing error: ${error}`);
        }
    }

    /**
     * 查找当前节点的子节点中，符合查找类型要求的第一个子节点
     */
    private firstDescendantOfType(node: Node, types: string | Array<string>): Node | null {
        // 如果传入是单个类型，则将其包装为数组。
        if (!Array.isArray(types)) {
            types = [types];
        }
        // 遍历节点的所有子节点，找到第一个符合类型查找要求的子节点
        for (const child of node.children) {
            if (types.includes(child.type)) {
                return child;
            }
        }
        return null;
    }

    /**
     * 查找当前节点的子节点中，符合查找类型要求的所有子节点
     */
    private childrenOfType(node: Node, types: string | Array<string>): Array<Node> {
        const children: Array<Node> = [];
        if (!Array.isArray(types)) {
            types = [types];
        }
        for (const child of node.children) {
            if (types.includes(child.type)) {
                children.push(child);
            }
        }
        return children;
    }

    /**
     * 递归查找当前节点的所有子孙节点中，符合查找类型要求的所有子孙节点
     * 目前用于递归查找内部类定义
     */
    private descendantsOfType(
        node: Node,
        types: string | Array<string>,
        parentNodeName = ''
    ): Array<DescendantNode> {
        const descendants: Array<DescendantNode> = [];
        if (!Array.isArray(types)) {
            types = [types];
        }
        for (const child of node.children) {
            let parentName = parentNodeName;
            if (types.includes(child.type)) {
                const childName = this.firstDescendantOfType(child, 'identifier');
                if (childName) {
                    descendants.push({
                        packageSuffix: '.' + parentNodeName,  // Java内部类对应的外部类路径前缀补充
                        node: child,
                        nodeIdentifierName: childName.text,
                    });
                    parentName = parentNodeName + '.' + childName.text;
                }
            }
            descendants.push(...this.descendantsOfType(child, types, parentName));
        }
        return descendants;
    }

    /**
     * 提炼出Java关联对象的基本信息，如：成员变量、简略文本表示等
     */
    private extractJavaObject(node: Node, importPackages: string[]): [string, JavaField[]] {
        const fields: JavaField[] = [];
        const simpleText = this.extractNodesSimpleText(node.children, false, importPackages, fields);
        return [simpleText, fields];
    }

    /**
     * 识别并提取节点中的简略文本表示，并提取出成员变量信息
     */
    private extractNodesSimpleText(
        nodes: Node[],
        all = false,
        importPackages: string[] = [],
        fields: JavaField[] = []
    ): string {
        let simpleText = '';
        for (const node of nodes) {
            switch (node.type) {
                case 'modifiers':
                case 'class':
                case 'identifier':
                case 'superclass':
                case 'super_interfaces':
                    simpleText += node.text.trim() + ' ';
                    break;
                case 'class_body':
                case 'interface_body':
                    simpleText += this.extractNodesSimpleText(node.children, false, importPackages, fields);
                    break;
                case 'enum_body': {
                    simpleText += '{\n';
                    const enums = this.childrenOfType(node, 'enum_constant');
                    for (let i = 0; i < enums.length; i++) {
                        if (i === enums.length - 1) {
                            simpleText += '\t' + enums[i].text.trim() + ';\n';
                        } else {
                            simpleText += '\t' + enums[i].text.trim() + ',\n';
                        }
                    }
                    const declarations = this.childrenOfType(node, 'enum_body_declarations');
                    for (const declaration of declarations) {
                        simpleText += this.extractNodesSimpleText(
                            declaration.children,
                            false,
                            importPackages,
                            fields
                        );
                    }
                    simpleText += '\n}';
                    break;
                }
                case '{':
                case '}':
                    simpleText += node.type + '\n';
                    break;
                case 'field_declaration':
                    // 私有方法不纳入参考
                    if (!this.isPrivateModifiers(node)) {
                        fields.push(this.extractFields(node, importPackages));
                        simpleText += '\t' + node.text.trim() + '\n';
                    }
                    break;
                case 'block':
                case 'constructor_body':
                    simpleText += '{}\n';
                    break;
                case 'method_declaration':
                case 'constructor_declaration':
                    if (!this.isPrivateModifiers(node)) {
                        simpleText += '\t';
                        simpleText += this.extractNodesSimpleText(node.children, true);
                    }
                    break;
                default:
                    if (all || !node.isNamed) {
                        simpleText += node.text.trim() + ' ';
                    }
            }
        }
        return simpleText;
    }

    /**
     * 判断是否为私有方法
     */
    private isPrivateModifiers(node: Node) {
        return this.childrenOfType(node, 'modifiers')[0]?.text.includes('private');
    }

    /**
     * 提取类成员字段
     */
    private extractFields(node: Node, importPackages: string[]): JavaField {
        let fieldVariable = '', fieldPackage = '', fieldType = '';

        // 1、提取类成员变量名
        const variableDeclarator = this.firstDescendantOfType(node, 'variable_declarator');
        if (variableDeclarator) {
            const identifier = this.firstDescendantOfType(variableDeclarator, 'identifier');
            if (identifier) {
                fieldVariable = identifier.text.trim();
            }
        }

        // 2、提取成员字段类型
        const typeIdentifier = this.firstDescendantOfType(node, 'type_identifier');
        if (typeIdentifier) {
            fieldType = typeIdentifier.text.trim();
        }

        // 3、根据成员类，从缓存系统反查包路径
        const objectDeclaration = this.getObjectDeclaration(importPackages, fieldType);
        if (objectDeclaration) {
            fieldPackage = objectDeclaration.packageName;
            fieldType = objectDeclaration.typeName;
        }

        return {
            fieldPackage,
            fieldType,
            fieldVariable,
        };
    }

    private getPositionNodeList(
        rootNode: Node,
        row: number,
        column: number,
        filter: Array<string>
    ): Node[] {
        const nodeList: Node[] = [];
        const nodes = rootNode.descendantsOfType(filter, {row, column: 0}, {row, column});
        if (nodes.length) {
            let candidateNode: Node | null = nodes[nodes.length - 1];
            while (candidateNode) {
                nodeList.push(candidateNode);
                candidateNode = candidateNode.parent;
            }
        }
        return nodeList;
    }

    /**
     * 提取方法的参数列表、局部变量列表，提取出变量声明信息
     */
    private getMethodVariableDeclarations(
        importPackages: string[],
        methodNode: Node,
        identifierNode: Node
    ): Variables {
        let variables: Variables = {};
        // 获取本地变量声明节点
        for (const node of methodNode.descendantsOfType(
            [
                'formal_parameter',
                'local_variable_declaration',
                'enhanced_for_statement',
                'catch_formal_parameter',
            ],
            methodNode.startPosition,
            identifierNode.startPosition
        )) {
            variables = {...variables, ...this.getNodeVariableDeclaration(node, importPackages)};
        }

        return variables;
    }

    private getNodeVariableDeclaration(node: Node, importPackages: string[]): Variables {
        const variable: Variables = {};
        let typeNode: Node | null = null;
        let variableName: string | undefined;
        switch (node.type) {
            case 'formal_parameter':
            case 'local_variable_declaration': {
                typeNode = node.childForFieldName('type');
                const declaratorNode = node.childForFieldName('declarator');
                if (declaratorNode) {
                    variableName = declaratorNode.childForFieldName('name')?.text;
                } else {
                    variableName = node.childForFieldName('name')?.text;
                }
                break;
            }
            case 'enhanced_for_statement': {
                typeNode = node.childForFieldName('type');
                variableName = node.childForFieldName('name')?.text;
                break;
            }
            case 'catch_formal_parameter': {
                for (const child of node.children) {
                    if (child.type === 'catch_type') {
                        typeNode = child.firstNamedChild;
                    }
                }
                variableName = node.childForFieldName('name')?.text;
                break;
            }
            default:
                return variable;
        }
        // 获取变量类型
        if (typeNode && variableName) {
            let typeName = '';
            switch (typeNode.type) {
                case 'type_identifier':
                case 'scoped_type_identifier':
                    typeName = typeNode.text.trim();
                    break;
                case 'generic_type': {
                    const typeNodes = typeNode.descendantsOfType('type_identifier');
                    if (typeNodes.length) {
                        typeName = typeNodes[0].text.trim();
                    }
                    break;
                }
            }
            if (typeName) {
                const objectDeclaration = this.getObjectDeclaration(importPackages, typeName);
                if (objectDeclaration) {
                    variable[variableName] = objectDeclaration;
                }
            }
        }
        return variable;
    }


    /**
     * 反查目标对象的包路径与对象名
     */
    private getObjectDeclaration(
        importPackages: string[],
        objectName: string
    ): VariableDeclaration | null {
        // 1、尝试提取 完整路径.类 形式的关联对象的包路径、类名，如：com.codecomplete.rlcc.domain.CacheObject obj = new com.codecomplete.rlcc.domain.CacheObject() 形式
        const relativeDeclaration = this.getValidPackageAndObjectName(objectName);
        if (relativeDeclaration) {
            return relativeDeclaration;
        }

        // 2、尝试基于import导入，进行逐个反查
        for (const importPackage of importPackages) {
            // 以 "." 为分隔符，拆分导入路径，得到 路径前缀、导入对象
            const [importPackageName, importObject] = this.splitLastDot(importPackage);
            if (!importObject) {
                continue;
            }

            // 如果是导入某包下的*，则以该包路径为前缀，拼接关联对象，尝试反查缓存系统中是否存在
            if (importObject === '*') {
                const relativeDeclaration = this.getValidPackageAndObjectName(importPackageName + '.' + objectName);
                if (relativeDeclaration) {
                    return relativeDeclaration;
                }
            } else {
                // 如果是导入某个包路径下具体类的，则进行拼接比较
                let firstObject = objectName;
                const lastDotIndexObject = objectName.indexOf('.');
                let path = importPackage;
                if (lastDotIndexObject > -1) {
                    firstObject = objectName.slice(0, lastDotIndexObject);
                    path = importPackage + '.' + objectName.slice(lastDotIndexObject + 1);
                }
                // 如果存在某条import的对象与当前要查找的关联对象一致，则反查缓存系统检查是否存在
                if (importObject !== firstObject) {
                    continue;
                }
                const relativeDeclaration = this.getValidPackageAndObjectName(path);
                if (relativeDeclaration) {
                    return relativeDeclaration;
                }
            }
        }
        return null;
    }

    /**
     * 获取有效的关联类及其包路径：存在与缓存系统中的
     */
    private getValidPackageAndObjectName(objectPath: string): VariableDeclaration | null {
        const splits = this.splitLastDot(objectPath);
        if (splits.length > 1 && this.objectSystem.checkObjectInPackage(splits[0], splits[1])) {
            return {packageName: splits[0], typeName: splits[1]};
        }
        return null;
    }

    private splitLastDot(objectPath: string): string[] {
        const lastDotIndexObject = objectPath.lastIndexOf('.');
        if (lastDotIndexObject === -1) {
            return [objectPath];
        }
        return [objectPath.slice(0, lastDotIndexObject), objectPath.slice(lastDotIndexObject + 1)];
    }
}