package com.srdcloud.ideplugin.actions

import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.project.DumbAware
import com.srdcloud.ideplugin.common.icons.MyIcons
import com.srdcloud.ideplugin.general.config.ConfigWrapper
import com.srdcloud.ideplugin.general.utils.BrowseUtil
import com.srdcloud.ideplugin.general.utils.UIUtil

class HelpAction : AnAction(), DumbAware {
    override fun actionPerformed(e: AnActionEvent) {
        BrowseUtil.browse("${ConfigWrapper.HelpDocsPageUrl}")
    }

    override fun update(e: AnActionEvent) {
        var helpIcon = MyIcons.helptips
        if (UIUtil.judgeBackgroudDarkTheme()) {
            helpIcon = MyIcons.helptipsDark
        }
        e.presentation.icon = helpIcon
    }

    override fun getActionUpdateThread(): ActionUpdateThread {
        return ActionUpdateThread.BGT
    }
}