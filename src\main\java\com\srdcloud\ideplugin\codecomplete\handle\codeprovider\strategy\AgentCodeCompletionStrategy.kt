package com.srdcloud.ideplugin.codecomplete.handle.codeprovider.strategy

import com.intellij.ide.util.PropertiesComponent
import com.intellij.openapi.application.ReadAction
import com.srdcloud.ideplugin.agent.AgentManager
import com.srdcloud.ideplugin.codecomplete.domain.CompletionElement
import com.srdcloud.ideplugin.codecomplete.domain.CompletionRequest
import com.srdcloud.ideplugin.codecomplete.domain.CompletionState.Companion.getInlineCompletionState
import com.srdcloud.ideplugin.codecomplete.domain.CompletionType
import com.srdcloud.ideplugin.codecomplete.handle.CompletionHandler
import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.prompt.EditorPromptProvider.Companion.getCompletionPrompt
import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.prompt.Prompt
import com.srdcloud.ideplugin.general.constants.AgentNameConstant
import com.srdcloud.ideplugin.general.constants.Constants
import com.srdcloud.ideplugin.general.constants.RtnCode
import com.srdcloud.ideplugin.general.enums.Language
import com.srdcloud.ideplugin.general.utils.DebugLogUtil
import com.srdcloud.ideplugin.general.utils.GitUtil
import com.srdcloud.ideplugin.general.utils.IdeUtil
import com.srdcloud.ideplugin.general.utils.JsonUtil
import kotlinx.coroutines.runBlocking
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import java.time.Duration
import java.time.LocalDateTime
import java.util.*
import kotlin.coroutines.Continuation
import kotlin.coroutines.resume


/**
 * 用于启动agent的补全策略
 */
class AgentCodeCompletionStrategy public constructor() : CodeCompletionStrategy() {

    // 获取日志记录器实例
    private var logger = LoggerFactory.getLogger(AgentCodeCompletionStrategy::class.java)

    // 表示该策略是否启用
    private var isActive = true

    // 检查策略是否启用
    override fun isEnabled(): Boolean {
        // 查找当前项目
        val project = IdeUtil.findCurrentProject()

        // 如果项目为空或默认项目，禁用策略
        if (project == null || project.isDefault) {
            logger.info("AgentCodeCompletionStrategy is disabled: project is null")
            return false
        }

        // 获取Tabby Agent客户端
        val client = AgentManager.getInstance(project).getAgentCommClient(AgentNameConstant.TABBY_AGENT)
        // 如果客户端存在且启用，并且策略本身也启用，则返回true
        if (client != null && client.isEnabled && isActive) {
            return true
        }

        // 否则，禁用策略并记录日志
        logger.info("AgentCodeCompletionStrategy is disabled: client is null or client is not running")
        return false
    }

    // 获取代码补全结果
    override fun getCodeCompletion(request: CompletionRequest, continuation: Continuation<Unit>?): String {
        // 获取当前文档中推理上下文：前缀、后缀、stopWords
        val prompt = ReadAction.compute<Prompt, Throwable> {
            request.editor.getCompletionPrompt(request.type)
        }

        // 空文档编辑，不进行补全
        if (prompt.prefix.isEmpty()) {
            logger.warn("[cf] getCodeCompletion skip,prompt.prefix.isEmpty()");
            return ""
        }

        val inputCharacterLimit =
            PropertiesComponent.getInstance()
                .getInt(Constants.InputCharacterLimit, Constants.DefaultInputCharacterLimit)
        val snippetSizeLimit =
            PropertiesComponent.getInstance()
                .getInt(Constants.SnippetsCharacterLimit, Constants.DefaultSnippetsCharacterLimit)

        // 默认仅启用rlcc策略
        var codeCompleteStrategy = arrayOf("rlcc");
        // 如果后端有下发，则优先采用后端控制激活的策略
        val codeCompleteStrategyStr = PropertiesComponent.getInstance().getValue(Constants.CodeCompleteStrategy, "");
        if (StringUtils.isNotBlank(codeCompleteStrategyStr)) {
            codeCompleteStrategy = JsonUtil.getInstance().fromJson(codeCompleteStrategyStr, Array<String>::class.java);
        }

        // 打印触发agent补全的日志
        DebugLogUtil.println("[cf] getCodeCompletion by AgentCodeCompletionStrategy")
        var beginTime: LocalDateTime?
        var endTime: LocalDateTime?
        // 使用协程运行异步任务
        runBlocking {
            request.let {
                // 查找当前项目
                // fixme:检查非项目中的editor‘s project
                val project = it.editor.project

                // 获取当前编辑器的补全状态
                val state = it.editor.getInlineCompletionState()
                // 获取文件路径
                val filepath = it.file.virtualFile.path
                // 检测文件语言
                val language = Language.detectLanguageName(it.file.virtualFile.name)
                // 构建补全文本段
                val text = prompt.prefix + prompt.suffix
                // 获取补全位置
                val position = prompt.prefix.length

                // 获取Tabby Agent客户端
                val client = AgentManager.getInstance(project).getAgentCommClient(AgentNameConstant.TABBY_AGENT)

                val stopWords =  arrayOf(prompt.stopWords).toMutableList();

                // todo:预埋，借助IDE的语义补全能力，获取多个候选关联对象


                // 构建tabby补全请求
                val id = UUID.randomUUID().toString()
                val requestData = listOf(
                    TabbyData(
                        id = id,
                        filepath = filepath,
                        language = language,
                        text = text,
                        position = position,
                        line = prompt.line,
                        col = prompt.col,
                        stopWords = stopWords,
                        gitUrls = GitUtil.getGitUrls(project),
                        inputCharacterLimit = inputCharacterLimit,
                        snippetSizeLimit = snippetSizeLimit,
                        codeCompleteStrategy = codeCompleteStrategy
                    ), TabbySignal()
                )

                // 测试日志埋点：拿到reqId之后，通过 [cf tabby][cpl-${reqId}] 来查看补全链路日志
                DebugLogUtil.println("[cf] AgentCodeCompletionStrategy CodeCompletion start, reqId:${id},filepath:${filepath}")
                beginTime = LocalDateTime.now()
                client.request("provideCompletions", requestData, null) {
                    // 响应回调处理
                        response ->
                    run {
                        // 补全通信性能监控埋点
                        endTime = LocalDateTime.now()
                        val duration = Duration.between(beginTime, endTime)
                        val milliseconds = duration.toMillis()
                        if (milliseconds > Constants.Code_completion_alarm_delay) {
                            logger.warn("[cf] AgentCodeCompletionStrategy CodeCompletion alarm, file:${prompt.fileName}, duration: $milliseconds ms")
                        }

                        if (state != null) {
                            // 记录agent补全内容回复
                            DebugLogUtil.println("AgentCodeCompletionStrategy provideCompletions file:${prompt.fileName},\n completionAnswer:$response")
                            try {
                                // 解析响应
                                val result =
                                    JsonUtil.getInstance().fromJson(response.toString(), TabbyResult::class.java)

                                // 如果没有补全结果，则添加空补全元素
                                if (result.choices.isEmpty()) {
                                    state.suggestions += CompletionElement("", request.type == CompletionType.AUTO)
                                } else {
                                    // 有补全结果，则添加
                                    state.suggestions += CompletionElement(result.choices[0].text, request.type == CompletionType.AUTO)
                                }
                                // 完成本次补全任务
                                CompletionHandler.stopWorking(RtnCode.SUCCESS)
                            } catch (e: Exception) {
                                // 记录补全失败
                                logger.warn("[cf] AgentCodeCompletionStrategy error: ${e.message}")
                                CompletionHandler.stopWorking(RtnCode.CANCEL)
                                isActive = false
                            }

                            // 恢复协程
                            continuation?.resume(Unit)
                        }
                    }
                }
            }
        }

        // 状态栏开始转圈
        CompletionHandler.startWorking(request.type)

        // 返回空字符串
        return ""
    }

    // 取消补全操作
    override fun cancel() {
        CompletionHandler.stopWorking(RtnCode.SUCCESS)
    }
}

// 定义TabbyData数据类
data class TabbyData(
    // 无用id，必传函数
    val id: String = UUID.randomUUID().toString(),
    val filepath: String,
    val language: String,
    val text: String,
    val position: Int, // 前后缀内容分割位置
    val line: Int, // 光标所在行
    val col: Int, // 光标所在列
    val stopWords:List<String?>,
    val indentation: String = " ",
    val clipboard: String = "",
    val manually: Boolean = true,
    val content: List<String> = ArrayList(),
    val gitUrls: List<String>,
    val inputCharacterLimit: Int,
    val snippetSizeLimit: Int,
    val codeCompleteStrategy: Array<String> = arrayOf("rlcc", "bm25")
)

// 定义TabbySignal数据类
data class TabbySignal(
    val signal: Boolean = true
)

// 定义TabbyResult数据类
data class TabbyResult(
    val id: String,
    val choices: Array<CompletionResult>,
)

// 定义CompletionResult数据类
data class CompletionResult(
    var index: Int,
    var text: String,
)
