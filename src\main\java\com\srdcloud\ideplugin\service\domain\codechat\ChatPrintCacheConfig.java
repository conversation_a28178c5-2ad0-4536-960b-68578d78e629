package com.srdcloud.ideplugin.service.domain.codechat;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/5
 * @desc webview打字机效果配置参数
 */
public class ChatPrintCacheConfig implements Serializable {
    private int printInterval = 30 ; // 打字机打印分片时间间隔（初始值30ms）

    private int chunkSize = 1; // 分片大小（初始值：1）

    private int preCacheChunkCount = 0; // 预缓存分片个数（0个）

    private boolean enableConfig = true; // 是否启用打字机相应配置（初始值：是）

    public ChatPrintCacheConfig() {
    }

    public ChatPrintCacheConfig(int printInterval, int chunkSize, int preCacheChunkCount, boolean enableConfig) {
        this.printInterval = printInterval;
        this.chunkSize = chunkSize;
        this.preCacheChunkCount = preCacheChunkCount;
        this.enableConfig = enableConfig;
    }

    public int getPrintInterval() {
        return printInterval;
    }

    public void setPrintInterval(int printInterval) {
        this.printInterval = printInterval;
    }

    public int getChunkSize() {
        return chunkSize;
    }

    public void setChunkSize(int chunkSize) {
        this.chunkSize = chunkSize;
    }

    public int getPreCacheChunkCount() {
        return preCacheChunkCount;
    }

    public void setPreCacheChunkCount(int preCacheChunkCount) {
        this.preCacheChunkCount = preCacheChunkCount;
    }

    public boolean isEnableConfig() {
        return enableConfig;
    }

    public void setEnableConfig(boolean enableConfig) {
        this.enableConfig = enableConfig;
    }
}
