package com.srdcloud.ideplugin.service.domain.apigw.useractivity;

import com.srdcloud.ideplugin.service.domain.apigw.ApigwWebsocketRequestContext;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/4/25
 * @desc 用户行为埋点上报请求报文
 */
public class UserActivityNotifyMessage implements Serializable {
    /**
     * 消息业务名称
     */
    private String messageName;

    /**
     * 通信上下文
     */
    private ApigwWebsocketRequestContext context;

    /**
     * 消息内容
     */
    private UserActivityNotifyPayload payload;

    public UserActivityNotifyMessage() {
    }

    public UserActivityNotifyMessage(String messageName, ApigwWebsocketRequestContext context, UserActivityNotifyPayload payload) {
        this.messageName = messageName;
        this.context = context;
        this.payload = payload;
    }

    public String getMessageName() {
        return messageName;
    }

    public void setMessageName(String messageName) {
        this.messageName = messageName;
    }

    public ApigwWebsocketRequestContext getContext() {
        return context;
    }

    public void setContext(ApigwWebsocketRequestContext context) {
        this.context = context;
    }

    public UserActivityNotifyPayload getPayload() {
        return payload;
    }

    public void setPayload(UserActivityNotifyPayload payload) {
        this.payload = payload;
    }
}
