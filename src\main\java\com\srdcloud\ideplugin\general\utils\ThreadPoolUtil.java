package com.srdcloud.ideplugin.general.utils;

import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/7/25
 * @desc 线程池工具类，专门用来执行异步操作
 */
public class ThreadPoolUtil {
    private static final ThreadPoolExecutor TASK_SUBMIT_POOL = new ThreadPoolExecutor(8, 16, 10L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    public static Future<?> submit(Runnable task) {
        return TASK_SUBMIT_POOL.submit(task);
    }

    public static void execute(Runnable task) {
        TASK_SUBMIT_POOL.execute(task);
    }

}
