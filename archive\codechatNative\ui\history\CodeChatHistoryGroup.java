package com.srdcloud.ideplugin.assistant.codechatNative.ui.history;

import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.VerticalFlowLayout;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.CodeChatCompleteEngin;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.ConversationManagerByAeBackend;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.Conversation;
import com.srdcloud.ideplugin.assistant.codechatNative.ui.CodeChatMainPanel;
import com.srdcloud.ideplugin.assistant.codechatNative.uicomponent.ListViewer;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.utils.HtmlUtil;
import com.srdcloud.ideplugin.general.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.List;

/**
 * @author: 蔡一新
 * @date: 2024/6/19
 * @Desc 历史对话列表组，集合历史对话的显示控件，用于显示历史对话的信息和时间以及控制效果
 */
public class CodeChatHistoryGroup extends ListViewer<Conversation> {
    private static final Logger logger = LoggerFactory.getLogger(CodeChatHistoryGroup.class);


    private CodeChatMainPanel codeChatMainPanel;
    private Project project;

    public CodeChatHistoryGroup(CodeChatMainPanel codeChatMainPanel, Project project) {
        super();
        this.codeChatMainPanel = codeChatMainPanel;
        this.project = project;
    }


    // 针对单个Cell的UI渲染逻辑和组件样式
    @Override
    public JComponent createCellView(List<Conversation> list, int index) {
        Conversation conversation = list.get(index);

        CodeChatHistoryContentPanel contentPanel = new CodeChatHistoryContentPanel(conversation);

        contentPanel.setToolTipText(HtmlUtil.wrapHtmlTooltip(conversation.getTitle()));

        contentPanel.setBackground(getBackground());

        contentPanel.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));

        // 设置鼠标效果
        contentPanel.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                if (getSelectedIndex() != index) {
                    contentPanel.onFocus();
                }
            }

            @Override
            public void mouseExited(MouseEvent e) {
                if (getSelectedIndex() != index) {
                    contentPanel.lostFocus();
                }
            }

            @Override
            public void mouseClicked(MouseEvent e) {
                setSelectedIndex(index);
            }
        });

        if (index == getSelectedIndex()) {
            contentPanel.onClick();
        }

        return contentPanel;
    }

    // 当列表中的选中项发生变化时，会触发此方法，将当前对话的信息打到main panel中
    @Override
    public void changeSelection() {
        if (CodeChatCompleteEngin.onAnswerStatus) {
            codeChatMainPanel.stopAnswerButton.doClick();
        }

        if (getSelectedIndex() >= 0) {
            Conversation conv = getSelectedItem();
            if (conv == null || StringUtil.isEmpty(conv.getId())) {
                conv = Conversation.defaultConversationWithTitle(Constants.NEW_CONVERSATION_NAME, true);
                // 新建会话，切换到首屏卡片
                codeChatMainPanel.messageAreaPanel.applyConversationToCurrent(project, conv);
                codeChatMainPanel.changeRightPartCenter(CodeChatMainPanel.firstScreenCardName);
            } else {
                // 非本地新建的会话，从后台服务器查询会话消息，填充进conv
                if (!conv.checkIsNewConversation()) {
                    ConversationManagerByAeBackend.setConversationByDialog(conv);
                }

                // 应用该会话到当前问答场景
                codeChatMainPanel.messageAreaPanel.applyConversationToCurrent(project, conv);

                // 切换UI
                if (conv.checkIsNewConversation()) {
                    // 点击新建会话，切换到首屏卡片
                    codeChatMainPanel.changeRightPartCenter(CodeChatMainPanel.firstScreenCardName);
                } else {
                    // 点击历史会话，切换到消息卡片
                    codeChatMainPanel.changeRightPartCenter(CodeChatMainPanel.codeChatMessageGroupCardName);
                }

                conv.updateKbId();
                codeChatMainPanel.changeKnowledgeButton();
            }
        }
    }

    // 无信息时的UI
    @Override
    public JComponent emptyView() {
        JLabel label = new JLabel("");
        return label;
    }

    // 改变当前列表的选中元素
    @Override
    public void setSelectedIndex(int selectedIndex) {

        try {
            if (getSelectedIndex() >= 0) {
                CodeChatHistoryContentPanel before = (CodeChatHistoryContentPanel) getComponent(getSelectedIndex());
                before.lostOnClick();
                before.repaint();
            }

            if (selectedIndex >= 0) {
                CodeChatHistoryContentPanel after = (CodeChatHistoryContentPanel) getComponent(selectedIndex);
                after.onClick();
                after.repaint();
            }
        } catch (Exception e) {
            logger.warn("[cf] CodeChatHistoryGroup setSelectedIndex error:{}", e.getMessage());
        }

        super.setSelectedIndex(selectedIndex);
    }

    @Override
    public void addItem(Conversation item) {
        super.addItem(item, 0);
        add(createCellView(getList(), 0), VerticalFlowLayout.TOP);
    }
}

