package com.srdcloud.ideplugin.assistant.codechatNative.logics.domain;

import com.srdcloud.ideplugin.general.enums.MessageNodeType;


/**
 * <AUTHOR> 蔡一新
 * @since  : 2024/8/23
 * 会话消息节点类，继承自TreeNodeConnector，用于管理会话消息对象的父子关系和兄弟关系
 */
public class ConversationMessageNode extends TreeNodeConnector<ConversationMessage> {
    // 父节点消息对象，初始化为null
    private ConversationMessage parent = null;

    /**
     * 默认构造函数
     */
    public ConversationMessageNode() {
        super();
    }

    /**
     * 给当前节点添加一个子节点，并设置子节点的父节点为当前节点的一个具体节点
     * @param child 待添加的子节点
     * @param cur   设置为子节点的父节点
     */
    public void addChild(ConversationMessage child, ConversationMessage cur) {
        // 调用父类的addChild方法添加子节点
        super.addChild(child);

        // 设置子节点的父节点
        child.getTreeNode().setParent(cur);

        // 设置当前节点的selectedIndex为最后一个子节点的索引
        setSelectedIndex(getChildren().size() - 1);
    }

    /**
     * 添加一个兄弟节点，即向当前节点的父节点添加一个子节点
     * @param brother 待添加的兄弟节点
     */
    public void addBrother(ConversationMessage brother) {
        // 检查当前节点是否有父节点
        if(getParent() != null) {
            // 向父节点添加兄弟节点
            getParent().getTreeNode().addChild(brother, getParent());
        }
    }

    /**
     * 获取当前节点的父节点
     * @return 当前节点的父节点
     */
    public ConversationMessage getParent() {
        return parent;
    }

    /**
     * 设置当前节点的父节点
     * @param parent 新的父节点
     */
    public void setParent(ConversationMessage parent) {
        this.parent = parent;
    }

    /**
     * 移除当前节点的一个子节点
     * @param message 待移除的子节点
     */
    public void removeChild(ConversationMessage message) {
        // 移除子节点
        getChildren().remove(message);

        // 更新当前节点的selectedIndex
        setSelectedIndex(getSelectedIndex() - 1);
    }

    /**
     * 重置当前节点的selectedIndex，用于消除无效节点
     * @param selectedIndex 目标selectedIndex
     */
    public void resetSelectedIndex(int selectedIndex) {
        if (getSelectedChildren() != null && getSelectedChildren().getNodeType() == MessageNodeType.INVALID_ERROR) {
            ConversationMessage message = getSelectedChildren();
            if (message.getNodeType() == MessageNodeType.INVALID_ERROR) {
                // 移除无效节点
                removeChild(message);
            }
        }
        // 设置新的selectedIndex
        setSelectedIndex(selectedIndex);
    }
}
