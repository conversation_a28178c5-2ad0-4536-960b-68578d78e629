package com.srdcloud.ideplugin.agent.process;

import com.srdcloud.ideplugin.agent.AgentManager;
import com.srdcloud.ideplugin.agent.config.AgentConfig;
import com.srdcloud.ideplugin.agent.config.AgentPath;
import com.srdcloud.ideplugin.agent.model.AgentVersion;
import com.srdcloud.ideplugin.general.constants.AgentNameConstant;
import com.srdcloud.ideplugin.general.utils.EnvUtil;
import org.apache.commons.lang.StringUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.attribute.PosixFilePermission;
import java.util.HashSet;
import java.util.Set;

public class TabbyAgentProcess extends AgentProcess {

    public static String AGENT_NAME = AgentNameConstant.TABBY_AGENT;

    public TabbyAgentProcess(AgentConfig agentConfig, AgentManager agent<PERSON>anager, AgentVersion tabbyAgentVersion) {
        super(AGENT_NAME, agentConfig, agentManager, tabbyAgentVersion);
    }

    @Override
    public ProcessBuilder buildProcess() {
        return new ProcessBuilder(getAgentManager().getNodePath().getAgentFilePath(),
                getAgentPath().getAgentFilePath()
        ).directory(new File(getAgentPath().getAgentFilePath()).getParentFile());
    }

    @Override
    public String getAgentName() {
        return AGENT_NAME;
    }

    @Override
    protected void setUpEnvironment(AgentConfig agentConfig, ProcessBuilder processBuilder) {

        // 添加环境变量，启动参数
        processBuilder.environment().put("apiKey", agentConfig.getApiKey());
        processBuilder.environment().put("invokerId", agentConfig.getInvokerId());
        processBuilder.environment().put("pluginType", agentConfig.getPluginType());
        processBuilder.environment().put("pluginVersion", agentConfig.getPluginVersion());
        processBuilder.environment().put("clientType", agentConfig.getClientType());
        processBuilder.environment().put("clientVersion", agentConfig.getClientVersion());
        processBuilder.environment().put("serverType", agentConfig.getServerType());
        processBuilder.environment().put("serverBaseUrl", EnvUtil.isSec(StringUtils.removeEnd(agentConfig.getServerBaseUrl(), "/"), agentConfig.getServerBaseUrl() + agentConfig.getTabbyApi()));
        processBuilder.environment().put("env", agentConfig.getEnv());
    }
}
