package com.srdcloud.ideplugin.assistant.codechatNative.ui.message;

import com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions.KnowledgeBaseAction;
import com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions.SendAction;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.CodeChatCompleteEngin;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.Conversation;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.ConversationMessage;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.ConversationMessageQuoteInterface;
import com.srdcloud.ideplugin.assistant.codechatNative.ui.CodeChatMainPanel;
import com.srdcloud.ideplugin.assistant.codechatNative.uicomponent.ListViewer;
import com.srdcloud.ideplugin.common.icons.MyIcons;
import com.srdcloud.ideplugin.general.enums.QuestionType;
import com.srdcloud.ideplugin.general.utils.BrowseUtil;
import com.srdcloud.ideplugin.general.utils.UIUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.List;

/**
 * 代码聊天消息引用接口面板，用于显示和管理消息引用
 */
public class CodeChatMessageQuoteInterfacePanel extends ListViewer<ConversationMessageQuoteInterface> {

    // 标识是否已经被点击过
    private boolean isClicked = false;

    // 主页面
    private CodeChatMainPanel codeChatMainPanel;

    // 引用消息
    private ConversationMessage message;

    /**
     * 构造函数，初始化面板
     *
     * @param list 消息引用接口列表
     */
    public CodeChatMessageQuoteInterfacePanel(List<ConversationMessageQuoteInterface> list, CodeChatMainPanel codeChatMainPanel, ConversationMessage message) {
        super(list);
        this.codeChatMainPanel = codeChatMainPanel;
        this.message = message;
        this.isClicked = message.isQuoteBanned();
        setOpaque(false);
        setSelectedIndex(-1);
    }

    /**
     * 根据提供的消息列表和索引，创建一个单元格视图组件
     *
     * @param list  消息列表，包含实现ConversationMessageQuoteInterface的所有消息对象
     * @param index 指定要显示的消息对象在列表中的索引
     * @return 返回一个JComponent组件，该组件代表列表中指定索引的消息对象的视图
     */
    @Override
    public JComponent createCellView(List<ConversationMessageQuoteInterface> list, int index) {
        // 从列表中获取指定索引的消息对象
        ConversationMessageQuoteInterface item = list.get(index);

        // 创建一个主面板，使用BorderLayout布局
        JPanel mainPanel = new JPanel(new BorderLayout());
        // 创建一个内容面板，用于放置图标和文本
        JPanel contentPanel = new JPanel();

        // 创建一个标签，用于显示选择图标
        JLabel pickIcon = new JLabel(MyIcons.Unpicked_Circle);

        // 创建一个标签，用于显示消息对象的完整标题
        JLabel interfaceText = new JLabel(item.getFullTittle());

        // 根据当前状态设置图标和行为
        if (!isClicked) {
            // 设置手型光标，增加鼠标点击事件来选择项目
            pickIcon.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));
            pickIcon.addMouseListener(new MouseAdapter() {
                @Override
                public void mouseClicked(MouseEvent e) {
                    // 获取当前对话
                    Conversation conversation = CodeChatCompleteEngin.getCurrentConversation(codeChatMainPanel);
                    // 该message内容不再可点击
                    isClicked = true;
                    message.setQuoteBanned(true);

                    // 为对话消息添加当前提示语
                    message.setQuoteItem(item.getItem());

                    // 更新UI状态
                    setSelectedIndex(index);

                    // 调用知识库问答请求
                    if (conversation.getKbId() == null) {
                        // 无选中知识库，选择大模型回复
                        SendAction sendAction = codeChatMainPanel.getProject().getService(SendAction.class);
                        sendAction.doActionPerformed(codeChatMainPanel, item.getFullTittle(), null, QuestionType.NEW_ASK, item.getItem());
                    } else {
                        // 根据选中知识库进行提问
                        KnowledgeBaseAction knowledgeBaseAction = codeChatMainPanel.getProject().getService(KnowledgeBaseAction.class);
                        knowledgeBaseAction.doActionPerformed(codeChatMainPanel, item.getFullTittle(), conversation.getKbId(), QuestionType.NEW_ASK, item.getItem());
                    }
                }
            });
        } else {
            // 当已选择或其他状态时，禁用文本颜色并调整图标
            interfaceText.setForeground(UIUtil.disableTextColor);
            pickIcon = new JLabel(index == getSelectedIndex() ? MyIcons.Disable_Picked_Circle : MyIcons.Disable_Unpicked_Circle);
        }

        // 创建一个标签，用于显示链接图标
        JLabel linkIcon = new JLabel(MyIcons.Hyperlink_Icon);
        // 设置手型光标，增加鼠标点击事件来打开链接
        linkIcon.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));
        linkIcon.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                BrowseUtil.Companion.browse(item.getLink());
            }
        });

        // 将图标和文本添加到内容面板
        contentPanel.add(pickIcon);
        contentPanel.add(interfaceText);
        contentPanel.add(linkIcon);

        // 设置面板和内容面板为透明，将内容面板添加到主面板的西侧
        mainPanel.setOpaque(false);
        contentPanel.setOpaque(false);
        mainPanel.add(contentPanel, BorderLayout.WEST);
        // 返回主面板作为单元格视图
        return mainPanel;
    }


    /**
     * 当选择发生变化时调用此方法，以刷新当前视图
     */
    @Override
    public void changeSelection() {
        refresh();
    }

    /**
     * 返回空视图，未使用
     *
     * @return null
     */
    @Override
    public JComponent emptyView() {
        return null;
    }

    /**
     * 设置有效性状态
     *
     * @param clicked 有效状态
     */
    public void setClicked(boolean clicked) {
        isClicked = clicked;
    }
}