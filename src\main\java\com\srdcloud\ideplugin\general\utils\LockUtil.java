package com.srdcloud.ideplugin.general.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.nio.channels.FileChannel;
import java.nio.channels.FileLock;
import java.nio.file.StandardOpenOption;
import java.time.Duration;
import java.time.Instant;
import java.util.Objects;
import java.util.function.Supplier;

/**
 * 文件锁工具类，用于处理跨进程同步问题
 * 例如，多IDE进程同时尝试访问同一文件的场景
 */
public class LockUtil {

    private static final Logger logger = LoggerFactory.getLogger(LockUtil.class);

    // 默认锁过期时间为30分钟（用于应对进程崩溃文件锁未及时释放的情况）
    private static final long DEFAULT_LOCK_EXPIRE_MINUTES = 30;
    
    // 默认最长等待时间为2分钟
    private static final long DEFAULT_MAX_WAIT_SECONDS = 120;
    
    // 重试间隔（毫秒）
    private static final long RETRY_INTERVAL_MS = 500;

    /**
     * 使用文件锁执行任务，包含锁文件过期检查
     * @param lockFilePath 锁文件路径
     * @param task 要执行的任务
     * @param <T> 任务返回值类型
     * @return 任务执行结果
     */
    public static <T> T executeWithFileLock(String lockFilePath, Supplier<T> task) {
        return executeWithFileLock(lockFilePath, task, DEFAULT_MAX_WAIT_SECONDS);
    }
    
    /**
     * 使用文件锁执行任务，包含锁文件过期检查和最长等待时间
     * @param lockFilePath 锁文件路径
     * @param task 要执行的任务
     * @param maxWaitSeconds 最长等待时间（秒）
     * @param <T> 任务返回值类型
     * @return 任务执行结果
     * @throws RuntimeException 如果获取锁超时或执行任务失败
     */
    public static <T> T executeWithFileLock(String lockFilePath, Supplier<T> task, long maxWaitSeconds) {
        File lockFile = new File(lockFilePath);
        // 确保目录存在
        if (lockFile.getParentFile() != null) {
            lockFile.getParentFile().mkdirs();
        }
        
        // 检查锁文件是否可能已过期（进程崩溃未释放）
        checkAndCleanStaleLock(lockFile);
        
        FileChannel channel = null;
        FileLock lock = null;
        
        Instant startTime = Instant.now();
        Exception lastException = null;
        
        try {
            // 尝试获取锁，直到超时
            while (Duration.between(startTime, Instant.now()).getSeconds() < maxWaitSeconds) {
                try {
                    channel = FileChannel.open(lockFile.toPath(), 
                            StandardOpenOption.CREATE, StandardOpenOption.WRITE);
                    
                    // 尝试立即获取锁，不阻塞
                    lock = channel.tryLock();
                    
                    if (lock != null) {
                        // 成功获取锁
                        DebugLogUtil.info("Success acquire file lock: " + lockFilePath);

                        // 更新锁文件的最后修改时间
                        updateLockFileTimestamp(lockFile);
                        
                        // 执行任务
                        T result = task.get();
                        DebugLogUtil.info("Execute task finish. Ready to release file lock: " + lockFilePath);
                        return result;
                    } else {
                        // 锁被其他进程持有，关闭通道后稍后重试
                        closeQuietly(channel);
                        channel = null;
                        
                        // 等待一段时间后重试
                        Thread.sleep(RETRY_INTERVAL_MS);
                    }
                } catch (Exception e) {
                    lastException = e;
                    closeQuietly(channel);
                    channel = null;
                    
                    // 等待一段时间后重试
                    Thread.sleep(RETRY_INTERVAL_MS);
                }
            }
            
            // 超过最长等待时间仍未获取到锁
            String errorMsg = "[cf] Wait " + maxWaitSeconds + "s, can't acquire lock: " + lockFilePath;
            logger.error(errorMsg);
            if (lastException != null) {
                throw new RuntimeException(errorMsg, lastException);
            } else {
                throw new RuntimeException(errorMsg + " (Time out)");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Interrupted when acquiring file lock: " + lockFilePath, e);
        } finally {
            // 确保释放锁和关闭通道
            if (lock != null) {
                try {
                    lock.release();
                } catch (Exception e) {
                    logger.error("[cf] Error releasing file lock: {}", e.getMessage());
                }
            }
            
            closeQuietly(channel);
            // 不手动删除锁文件
        }
    }

    /**
     * 安静地关闭通道，不抛出异常
     */
    private static void closeQuietly(FileChannel channel) {
        if (channel != null) {
            try {
                channel.close();
            } catch (Exception e) {
                logger.warn("[cf] Error closing file channel: {}", e.getMessage());
            }
        }
    }

    /**
     * 使用文件锁执行无返回值的任务
     * @param lockFilePath 锁文件路径
     * @param task 要执行的任务
     */
    public static void executeWithFileLock(String lockFilePath, Runnable task) {
        executeWithFileLock(lockFilePath, () -> {
            task.run();
            return null;
        });
    }
    
    /**
     * 使用文件锁执行无返回值的任务，带有最长等待时间
     * @param lockFilePath 锁文件路径
     * @param task 要执行的任务
     * @param maxWaitSeconds 最长等待时间（秒）
     */
    public static void executeWithFileLock(String lockFilePath, Runnable task, long maxWaitSeconds) {
        executeWithFileLock(lockFilePath, () -> {
            task.run();
            return null;
        }, maxWaitSeconds);
    }
    
    /**
     * 检查锁文件是否已过期（可能是由于进程崩溃未正常释放）
     * 如果过期则删除锁文件
     * @param lockFile 锁文件
     */
    private static void checkAndCleanStaleLock(File lockFile) {
        if (lockFile.exists()) {
            try {
                Instant lastModified = Instant.ofEpochMilli(lockFile.lastModified());
                Instant now = Instant.now();
                Duration duration = Duration.between(lastModified, now);
                
                if (duration.toMinutes() > DEFAULT_LOCK_EXPIRE_MINUTES) {
                    DebugLogUtil.warn("Stale lock file detected: " + lockFile.getPath() +
                          " (Age: " + duration.toMinutes() + " minutes). Deleting...");
                    if (!lockFile.delete()) {
                        DebugLogUtil.warn("Failed to delete stale lock file: " + lockFile.getPath());
                    }
                }
            } catch (Exception e) {
                DebugLogUtil.warn("Error checking lock file staleness: " + e.getMessage());
            }
        }
    }
    
    /**
     * 更新锁文件的时间戳
     * @param lockFile 锁文件
     */
    private static void updateLockFileTimestamp(File lockFile) {
        try {
            // 更新文件的最后修改时间为当前时间
            lockFile.setLastModified(System.currentTimeMillis());
        } catch (Exception e) {
            DebugLogUtil.warn("Error updating lock file timestamp: " + e.getMessage());
        }
    }
} 