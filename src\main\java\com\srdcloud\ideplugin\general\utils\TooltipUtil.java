package com.srdcloud.ideplugin.general.utils;

import com.intellij.ide.IdeTooltip;
import com.intellij.ide.IdeTooltipManager;
import com.intellij.openapi.ui.popup.Balloon.Position;
import com.intellij.ui.components.JBLabel;
import com.intellij.ui.components.panels.Wrapper;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseEvent;

/**
 * @author: yangy
 * @date: 2023/7/3 10:24
 * @Desc
 */
public class TooltipUtil {

    private final Timer timer;
    private final Icon myNewIcon;

    public TooltipUtil(final JLabel label, final Icon originalIcon, Icon newIcon) {
        this.myNewIcon = newIcon;
        this.timer = new Timer(3000, new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                label.setIcon(originalIcon);
                TooltipUtil.this.timer.stop();
            }
        });
    }

    /**
     * 浮现"已复制"toolTip
     * @param event
     */
    public void showCopiedToolTip(MouseEvent event) {
        Point point = event.getPoint();
        if (IdeTooltipManager.getInstance().hasCurrent()) {
            IdeTooltipManager.getInstance().hideCurrent(event);
        }

        ((JLabel)event.getComponent()).setIcon(this.myNewIcon);
        Wrapper wrapper = new Wrapper(new JBLabel("已复制！"));
        IdeTooltip tooltip = (new IdeTooltip(event.getComponent(), point, wrapper, new Object[0])).setPreferredPosition(Position.above);
        this.timer.start();
        IdeTooltipManager.getInstance().show(tooltip, true, true);
    }
}
