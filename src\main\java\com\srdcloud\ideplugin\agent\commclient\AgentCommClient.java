package com.srdcloud.ideplugin.agent.commclient;

import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.composer.ComposerService;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.*;
import java.net.Socket;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

/**
 * 抽象通信客户端基类，负责与外部进程/服务建立双向通信通道。
 *
 * <p>核心功能：
 * <p>1. 支持通过进程标准IO或TCP套接字建立连接
 * <p>2. 自动重连机制和消息队列管理
 * <p>3. 线程安全的资源管理和优雅关闭
 *
 * <p>设计模式：
 * <p>- 模板方法模式：handleMessage()和request()由子类实现具体协议处理
 * <p>- 观察者模式：通过responseListeners实现异步回调
 *
 * @see AgentMessageReceiver 消息接收器接口
 * @see com.srdcloud.ideplugin.agent.AgentManager Agent管理器
 */
public abstract class AgentCommClient implements Closeable {
    // 持有进程
    private final Process process;
    private Writer writer;
    private BufferedReader reader;

    private final static Logger logger = LoggerFactory.getLogger(AgentCommClient.class);

    private final boolean useTcp = false;
    private volatile boolean isRunning = true;
    private final long reconnectDelay = 5000L;
    private static final long SHUTDOWN_TIMEOUT = 5000L;
    private volatile boolean isConnected = false;

    private final List<String> messageQueue = new ArrayList<>(); // 消息队列
    private final Object queueLock = new Object(); // 用于消息队列同步

    // 捆绑项目
    protected final Project project;
    protected final AgentMessageReceiver messageReceiver;
    protected final Map<String, Consumer<Object>> responseListeners = new ConcurrentHashMap<>();

    /**
     * 初始化通信客户端
     * @param process 要通信的外部进程对象
     * @param messageReceiver 消息接收处理器
     * @throws IllegalStateException 如果进程不可用
     *
     * @implSpec 构造函数会立即尝试建立连接并注册JVM关闭钩子
     */
    public AgentCommClient(Project project,Process process, AgentMessageReceiver messageReceiver) {
        this.project = project;
        this.process = process;
        this.messageReceiver = messageReceiver;
        
        Runtime.getRuntime().addShutdownHook(new Thread(this::shutdown));
        initializeConnection();
    }

    /**
     * 写入消息到通信通道（线程安全）
     * @param message 要发送的原始消息字符串
     *
     * <p>执行流程：
     * <p>1. 如果连接正常则直接发送
     * <p>2. 连接异常时进入消息队列
     * <p>3. 触发自动重连机制
     *
     * @throws NullPointerException 如果message为null
     */
    public void write(String message) {
        synchronized (queueLock) {
            if (isConnected && writer != null) {
                try {
                    writer.write(message + "\r\n");
                    writer.flush();
                } catch (Exception e) {
                    logger.warn("Error writing to Continue core: " + e);
                    messageQueue.add(message);
                    handleConnectionFailure(e);
                }
            } else {
                messageQueue.add(message);
                if (!isConnected) {
                    initializeConnection(); // 如果未连接，尝试重新连接
                }
            }
        }
    }

    /**
     * 处理连接失败事件（内部方法）
     * @param error 导致失败的异常对象
     *
     * <p>副作用：
     * <p>1. 标记连接状态为断开
     * <p>2. 清理现有资源
     * <p>3. 启动后台重连线程
     */
    private void handleConnectionFailure(Exception error) {
        synchronized (queueLock) {
            isConnected = false;
            closeResources();
            // 在新线程中进行重连，避免阻塞当前线程
            new Thread(this::initializeConnection).start();
        }
    }


    private void processMessageQueue() {
        synchronized (queueLock) {
            if (isConnected && writer != null) {
                Iterator<String> iterator = messageQueue.iterator();
                while (iterator.hasNext()) {
                    String message = iterator.next();
                    try {
                        writer.write(message + "\r\n");
                        writer.flush();
                        iterator.remove();
                    } catch (Exception e) {
                        logger.error("Failed to process queued message: " + e.getMessage());
                        handleConnectionFailure(e);
                        break;
                    }
                }
            }
        }
    }

    /**
     * 初始化通信连接（阻塞式）
     *
     * <p>连接策略：
     * <p>1. 根据useTcp选择TCP或进程IO连接
     * <p>2. 成功后会启动消息读取线程
     * <p>3. 自动处理积压消息队列
     *
     * @throws IOException 当所有连接尝试都失败时抛出
     */
    private void initializeConnection() {
        while (isRunning && !isConnected) {
            try {
                logger.info("Attempting to establish connection...");
                if (useTcp && this.messageReceiver instanceof ComposerService) {
                    initTcpConnection();
                } else {
                    initProcessConnection();
                }

                isConnected = true;
                logger.info("Connection established successfully");

                // 启动读取线程
                startReading();

                // 处理队列中的消息
                processMessageQueue();

                break; // 连接成功，退出循环
            } catch (Exception e) {
                logger.error("Connection attempt failed: " + e.getMessage());
                try {
                    Thread.sleep(reconnectDelay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                }
            }
        }
    }

    /**
     * 初始化TCP通信连接（测试用）
     */
    private void initTcpConnection() throws IOException {
        Socket socket = new Socket("127.0.0.1", 1983);
        writer = new PrintWriter(socket.getOutputStream(), true);
        reader = new BufferedReader(new InputStreamReader(socket.getInputStream()));
    }

    /**
     * 初始化IO进程通信连接（上线用）
     */
    private void initProcessConnection() throws IOException {
        try {
            OutputStream outputStream = process.getOutputStream();
            InputStream inputStream = process.getInputStream();

            writer = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);
            reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));

            // 启动错误流监控
            monitorProcessErrors();

            // 监控进程退出
            process.onExit().thenAccept(proc -> {
                int exitCode = proc.exitValue();
                logger.warn("[cf] Process exited with code: " + exitCode);
                handleConnectionFailure(new IOException("Process exited with code: " + exitCode));
            });

        } catch (Exception e) {
            logger.error("[cf] Failed to initialize process: " + e.getMessage());
            throw e;
        }
    }

    private void monitorProcessErrors() {
        new Thread(() -> {
            try {
                BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
                String line;
                while (isRunning && (line = errorReader.readLine()) != null) {
                    DebugLogUtil.warn("[cf] Process error: " + line);
                }
            } catch (Exception e) {
                // Ignore error monitoring exceptions
            }
        }).start();
    }

    private void closeResources() {
        synchronized (queueLock) {
            try {
                // 先标记停止运行
                isRunning = false;
                isConnected = false;

                // 使用 CompletableFuture 来处理异步关闭
                CompletableFuture<Void> shutdownFuture = CompletableFuture.runAsync(() -> {
                    try {
                        // 先关闭写入器
                        if (writer != null) {
                            try {
                                writer.flush();
                                writer.close();
                            } catch (IOException e) {
                                logger.warn("Error closing writer", e);
                            }
                        }

                        // 再关闭读取器
                        if (reader != null) {
                            try {
                                reader.close();
                            } catch (IOException e) {
                                logger.warn("Error closing reader", e);
                            }
                        }

                        // 最后处理进程
                        if (process != null) {
                            try {
                                // 先尝试正常终止
                                if (process.isAlive()) {
                                    process.destroy();
                                    // 等待一小段时间
                                    if (!process.waitFor(2, TimeUnit.SECONDS)) {
                                        // 如果还活着，强制终止
                                        process.destroyForcibly();
                                    }
                                }
                            } catch (Exception e) {
                                logger.error("Error destroying process", e);
                            }
                        }
                    } finally {
                        writer = null;
                        reader = null;
                    }
                });

                // 添加超时控制
                try {
                    shutdownFuture.get(SHUTDOWN_TIMEOUT, TimeUnit.MILLISECONDS);
                } catch (TimeoutException e) {
                    logger.error("Shutdown timed out", e);
                    // 强制结束
                    if (process != null) {
                        process.destroyForcibly();
                    }
                    shutdownFuture.cancel(true);
                } catch (Exception e) {
                    logger.error("Error during shutdown", e);
                }

            } catch (Exception e) {
                logger.error("Error during resource cleanup", e);
            }
        }
    }

    @Override
    public void close() {
        try {
            // 先停止所有活动
            isRunning = false;

            // 清理消息队列
            synchronized (queueLock) {
                messageQueue.clear();
                responseListeners.clear();
            }

            // 关闭资源
            closeResources();

            // 确保进程被终止
            if (process != null && process.isAlive()) {
                process.destroyForcibly();
            }
        } catch (Exception e) {
            logger.error("Error during close", e);
        }
    }

    private void startReading() {
        new Thread(() -> {
            try {
                while (isRunning && isConnected) {
                    String line = reader != null ? reader.readLine() : null;
                    if (line != null && !line.isEmpty()) {
                        try {
                            handleMessage(line);
                        } catch (Exception e) {
                            logger.error("Error handling message: " + line, e);
                        }
                    } else if (line == null) {
                        // null 表示流已关闭
                        handleConnectionFailure(new IOException("Stream closed"));
                        break;
                    } else {
                        try {
                            Thread.sleep(100);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("Error in read loop: " + e.getMessage());
                handleConnectionFailure(e);
            }
        }).start();
    }

    public void shutdown() {
        try {
            close();
        } catch (Exception e) {
            logger.error("Error during shutdown", e);
            // 确保进程被强制终止
            if (process != null) {
                process.destroyForcibly();
            }
        }
    }

    /**
     * 处理接收到的消息（抽象方法）
     * @param json 原始消息JSON字符串
     *
     * @implNote 子类需实现具体协议解析逻辑
     */
    protected abstract void handleMessage(String json);

    /**
     * 发送请求并通过responseListeners响应回调
     * @param messageType 消息类型标识符
     * @param data 请求数据对象
     * @param messageId 唯一消息ID
     * @param onResponse 响应回调函数
     *
     */
    public abstract void request(String messageType, Object data, String messageId, Consumer<Object> onResponse);

    public boolean isRunning() {
        return isRunning;
    }

    public boolean isConnected() {
        return isConnected;
    }

    public boolean isEnabled() {
        return isConnected && isRunning;
    }

    protected static String uuid() {
        return UUID.randomUUID().toString();
    }

    public Project getProject() {
        return project;
    }
}