package com.srdcloud.ideplugin.general.utils;

import com.intellij.openapi.ui.Messages;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2024/12/25
 * @desc 对话通知工具类
 */
public class MessageDialogUtil {
    private static final Logger logger = LoggerFactory.getLogger(MessageDialogUtil.class);

    /**
     * 显示只有 确认 按钮的对话框
     */
    public static void showConfirmDialog(final String message, final String title) {
        Messages.showMessageDialog("该账号已经被禁用，请联系管理员解禁后继续使用。", "账户被禁用通知", Messages.getInformationIcon());
    }

    /**
     * 显示用户禁用对话框
     */
    public static void showUserBanDialog() {
        showConfirmDialog("该账号已经被禁用，请联系管理员解禁后继续使用。", "账户被禁用通知");
    }
}
