package com.srdcloud.ideplugin.codecomplete.actions

import com.intellij.ide.util.PropertiesComponent
import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.project.Project
import com.srdcloud.ideplugin.codecomplete.listener.CompletionDocumentListener.Companion.completionEnabled
import com.srdcloud.ideplugin.common.icons.MyIcons
import com.srdcloud.ideplugin.general.constants.Constants
import com.srdcloud.ideplugin.general.utils.UIUtil
import com.srdcloud.ideplugin.service.LoginService
import com.srdcloud.ideplugin.statusbar.Notify

/**
 * <AUTHOR>
 * @date 2025/6/6
 * @desc 自动补全禁用/启用
 */
class ToggleCompletionEnabledAction : AnAction(), DumbAware {
    override fun actionPerformed(e: AnActionEvent) {
        PropertiesComponent.getInstance().completionEnabled = !PropertiesComponent.getInstance().completionEnabled
        Notify.updateStatusNotify()
    }

    override fun update(e: AnActionEvent) {
        var enableIcon = MyIcons.codeenabling
        var disableIcon = MyIcons.codedisable
        if (UIUtil.judgeBackgroudDarkTheme()) {
            enableIcon = MyIcons.codeenablingDark
            disableIcon = MyIcons.codedisableDark
        }

        if (PropertiesComponent.getInstance().completionEnabled) {
            e.presentation.icon = disableIcon
            e.presentation.text = "禁用代码自动补全"
        } else {
            e.presentation.icon = enableIcon
            e.presentation.text = "启用代码自动补全"
        }
        if (LoginService.getLoginStatus() == Constants.LoginStatus_NOK) {
            e.presentation.isEnabled = false
        }
    }

    private fun getEditor(project: Project?): Editor? = project?.let {
        FileEditorManager.getInstance(it).selectedTextEditor
    }

    override fun getActionUpdateThread(): ActionUpdateThread {
        return ActionUpdateThread.BGT
    }
}