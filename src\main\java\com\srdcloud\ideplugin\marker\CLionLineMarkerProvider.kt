package com.srdcloud.ideplugin.marker

import com.intellij.psi.PsiElement

class CLionLineMarkerProvider : BaseLineMarkerProvider() {
    companion object {
        private const val ALLOW_LANGUAGE = "C/C++"
        val ALLOW_LANGUAGE_SUFFIX = listOf("c", "cpp", "cc", "c++", "hpp", "h", "cxx")
        val ALLOW_IDE = listOf("CLion")
    }

    override fun isApplicable(element: PsiElement): Boolean {
        val fileSuffix = element.containingFile.name.substringAfterLast('.', "")
        return ALLOW_IDE.contains(getIdeType()) && ALLOW_LANGUAGE_SUFFIX.contains(fileSuffix) &&
                element.node.elementType.toString().equals("DECLARATOR", ignoreCase = true) &&
                element.parent?.node?.elementType.toString().equals("FUNCTION_DEFINITION", ignoreCase = true)
    }

    override fun filterFunction(element: PsiElement, bean: LineMarkerFunctionBean) {
        bean.apply {
            isUtValid = isValidForUnitTest(element)
            isAnnotateValid = true
            isCodeExplain = true
            isOptimization = true

            val elementInfo = LineMarkerFunctionBean.getFunctionLinesCount(element)
            elementInfo?.let {
                val lineCount = it.endLine - it.startLine
                val charCount = it.endOffset - it.startOffset
                isCodeSplit = lineCount >= 20 && charCount <= 4500
                isLineAnotateValid = charCount <= 4500
            }
        }
    }

    private fun isValidForUnitTest(element: PsiElement): Boolean {
        // Implement C/C++-specific unit test validation logic
        return !SmartUTUtil.isCTestFile(element)
    }

    private fun getIdeType(): String {
        // Implement IDE type detection logic
        return "CLion"
    }
}
