package com.srdcloud.ideplugin.service.domain.apigw.codechat.history;

/**
 * @author: yangy
 * @date: 2024/5/13 16:40
 * @Desc
 */
public class DialogReq {


    /**
     * 产生本对话的用户ID
     */
    private String userId;

    /**
     * 问答对所属的对话ID
     */
    private String dialogId;

    /**
     * 本对话的AI原子能力标识
     */
    private String subService;

    /**
     * 提问类型，取值为：
     * newAsk - 对话下新的提问
     * reAsk - 对话下原有问题重新提问
     */
    private String questionType;

    /**
     * 当questionType=newAsk时：
     *    1）如果本问答对是所属对话中的首轮问答，此参数无需携带；
     *    2）如果本问答对并非所属对话中首轮问答，此必须携带此参数，值为本问答对之前的回答reqId，用于寻址本问答对插入的位置。
     * 当questionType=reAsk时：
     *    取值为原有提问的reqId。
     */
    private String parentReqId;

    /**
     * 本对话的system prompt对象，其中role=system。
     * 如果本问答对是一个对话的首轮问答，且对话携带了system prompt的话，就携带此参数。
     */
    private ChatMessage systemPrompt;

    /**
     * 当questionType=newAsk时，为问答对的提问对象。ChatMessage结构定义参见“2.数据设计”一章；
     * 当questionType=reAsk时，无需携带此参数，通过parentReqId来关联提问对象。
     */
    private ChatMessage question;

    /**
     * 问答对产生的回答对象。
     */
    private ChatMessage answer;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSubService() {
        return subService;
    }

    public void setSubService(String subService) {
        this.subService = subService;
    }

    public String getDialogId() {
        return dialogId;
    }

    public void setDialogId(String dialogId) {
        this.dialogId = dialogId;
    }

    public String getQuestionType() {
        return questionType;
    }

    public void setQuestionType(String questionType) {
        this.questionType = questionType;
    }

    public String getParentReqId() {
        return parentReqId;
    }

    public void setParentReqId(String parentReqId) {
        this.parentReqId = parentReqId;
    }

    public ChatMessage getSystemPrompt() {
        return systemPrompt;
    }

    public void setSystemPrompt(ChatMessage systemPrompt) {
        this.systemPrompt = systemPrompt;
    }

    public ChatMessage getQuestion() {
        return question;
    }

    public void setQuestion(ChatMessage question) {
        this.question = question;
    }

    public ChatMessage getAnswer() {
        return answer;
    }

    public void setAnswer(ChatMessage answer) {
        this.answer = answer;
    }
}
