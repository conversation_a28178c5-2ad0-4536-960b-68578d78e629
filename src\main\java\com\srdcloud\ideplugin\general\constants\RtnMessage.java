package com.srdcloud.ideplugin.general.constants;

/**
 * <AUTHOR>
 * @date 2025/1/13
 */
public enum RtnMessage {
    SUCCESS(RtnCode.SUCCESS, "操作成功"),//RtnCode_Success
    NO_CHANNEL(RtnCode.NO_CHANNEL, "服务端不可达"),//RtnCode_No_Channel
    NOT_LOGIN(RtnCode.NOT_LOGIN, "未登录"),//RtnCode_Not_Login
    INVALID_USER(RtnCode.INVALID_USER, "服务未开通"),//RtnCode_InvalidUser
    SEND_ERROR(RtnCode.SEND_ERROR, "发送消息失败"),//RtnCode_SendError
    RECV_TIMEOUT(RtnCode.RECV_TIMEOUT, "接收消息超时"),//RtnCode_RecvTimeout
    USER_FORBIDDEN(RtnCode.USER_FORBIDDEN, "服务被禁用"),//RtnCode_UserForbidden
    INSUFFICIENT_RESOURCE(RtnCode.INSUFFICIENT_RESOURCE, "资源不足"),//RtnCode_INSUFFICENT_RESOURCE
    MODEL_ERROR(RtnCode.MODEL_ERROR, "请求处理错误"),//RtnCode_MODEL_ERROR
    SERVER_DOWN(RtnCode.SERVER_DOWN, "服务异常"),//RtnCode_SERVER_DOWN
    CANCEL(RtnCode.CANCEL, "用户取消请求"),//RtnCode_CANCLE
    INVALID_SESSION_ID(RtnCode.INVALID_SESSION_ID, "登录过期"),//RtnCode_Invalid_SessionId
    LOGOUT(RtnCode.LOGOUT, "用户登出"),//RtnCode_Logout
    INVALID_QUESTION(RtnCode.INVALID_QUESTION, "无效提问"),//RtnCode_Invalid_Question
    INVALID_ANSWER(RtnCode.INVALID_ANSWER, "无效回答，请重新提问"),//RtnCode_Invalid_Answer
    KNOWLEDGE_BASE_DELETED(RtnCode.KNOWLEDGE_BASE_DELETED, "找不到知识库，请切换后重新提问"),

    // vscode客户端自定义errMsg
    OAUTH2_ERROR(RtnCode.OAUTH2_ERROR, "Oauth2认证失败，请检查网络设置"),
    CONNECTED_ERROR(RtnCode.CONNECTED_ERROR, "Websocket连接异常，请检查网络设置"),
    UPLOAD_FAIL(RtnCode.UPLOAD_FAIL, "上传失败"),
    HTTP_CLIENT_ERROR(RtnCode.HTTP_CLIENT_ERROR, "客户端错误"),
    HTTP_REQUEST_ERROR(RtnCode.HTTP_REQUEST_ERROR, "请求错误"),
    INSERT_ERROR(RtnCode.INSERT_ERROR, "异常回答，该分支不支持继续提问，请切换分支提问"),//RtnCode_Insert_Error
    STOP_ANSWER(RtnCode.STOP_ANSWER, "停止回答"),
    NO_CHANNEL_CHAT(RtnCode.NO_CHANNEL_CHAT, "请检查网络"),

    // jetbrains客户端自定义errMsg
    NOT_SELECT_TEXT(RtnCode.RtnCode_Not_Select_Text, "请选择代码"),
    RIGHT_CHAT_RUNNING(RtnCode.RtnCode_Right_Chat_RUNNING, "正在回答中，请稍后或点击停止按钮"),
    MAIN_PANEL_IS_NOT_LOADED(RtnCode.RtnCode_Main_Panel_Is_Not_Loaded, "插件组件加载中，请稍后"),

    ;
    public int code;
    public String message;

    RtnMessage(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static boolean containsCode(int code) {
        for (RtnMessage rtnMessage : RtnMessage.values()) {
            if (rtnMessage.code == code) {
                return true;
            }
        }
        return false;
    }

    public static String getMessageByCode(int code) {
        for (RtnMessage rtnMessage : RtnMessage.values()) {
            if (rtnMessage.code == code) {
                return rtnMessage.message;
            }
        }
        return "";
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
