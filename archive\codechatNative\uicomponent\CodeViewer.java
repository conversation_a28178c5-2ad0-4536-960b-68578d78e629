package com.srdcloud.ideplugin.assistant.codechatNative.uicomponent;

import com.intellij.icons.AllIcons.Actions;
import com.intellij.openapi.Disposable;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.command.WriteCommandAction;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.EditorFactory;
import com.intellij.openapi.editor.EditorSettings;
import com.intellij.openapi.editor.colors.EditorColorsManager;
import com.intellij.openapi.editor.colors.EditorColorsScheme;
import com.intellij.openapi.editor.ex.EditorEx;
import com.intellij.openapi.editor.highlighter.EditorHighlighter;
import com.intellij.openapi.editor.highlighter.EditorHighlighterFactory;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.fileTypes.FileType;
import com.intellij.openapi.fileTypes.FileTypeManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Disposer;
import com.intellij.openapi.vfs.VfsUtil;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.testFramework.LightVirtualFile;
import com.intellij.ui.components.JBLabel;
import com.srdcloud.ideplugin.general.constants.PathConstant;
import com.srdcloud.ideplugin.general.enums.AssistantChannel;
import com.srdcloud.ideplugin.general.enums.UserActivityType;
import com.srdcloud.ideplugin.general.icons.GPTIcons;
import com.srdcloud.ideplugin.general.utils.TooltipUtil;
import com.srdcloud.ideplugin.general.utils.UIUtil;
import com.srdcloud.ideplugin.service.UserActivityReportService;
import org.apache.commons.lang.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;
import java.awt.datatransfer.Clipboard;
import java.awt.datatransfer.ClipboardOwner;
import java.awt.datatransfer.StringSelection;
import java.awt.datatransfer.Transferable;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.File;
import java.util.UUID;

/**
 * @author: yangy
 * @date: 2023/7/3 10:04
 * @Desc 代码内容查看器
 */
public class CodeViewer extends MessageViewer implements Disposable {
    private static final Logger logger = LoggerFactory.getLogger(CodeViewer.class);
    /**
     * 代码编辑器控件：保持原生样式呈现代码
     */
    private EditorEx myEditor;

    /**
     * 消息绑定的项目实例：点击 插入 按钮时通过项目实例进行插入
     */
    private final Project myProject;

    /**
     * 那个对话渠道下展示的代码
     */
    private final AssistantChannel assistantChannel;

    /**
     * 代码部分内容
     */
    private String myCode;

    /**
     * 基于代码新建文件功能：代码语言对应文件的扩展名
     */
    private String newFileExt;

    private JPanel leftPanelOld = new JPanel();

    public CodeViewer(Project project, AssistantChannel assistantChannel, String content, Disposable parent, String ext, boolean supportNewFile, boolean me, boolean containHeadOps) {
        String fileExt = ext;
        if (ext == null || ext.isEmpty()) {
            ext = "";
            fileExt = "java";
        }
        FileType fileType = FileTypeManager.getInstance().getFileTypeByExtension(ext);
        // 内容需要先反转义，还原原始字符
        content = StringEscapeUtils.unescapeHtml(content);

        this.myProject = project;
        this.assistantChannel = assistantChannel;
        this.myCode = content;
        this.newFileExt = ext;
        EditorFactory editorFactory = EditorFactory.getInstance();
        EditorColorsManager colorsManager = EditorColorsManager.getInstance();
        EditorColorsScheme colorsScheme = colorsManager.getSchemeForCurrentUITheme();
        myEditor = (EditorEx) editorFactory.createViewer(editorFactory.createDocument(content), project);
        myEditor.setColorsScheme(colorsScheme);
        EditorHighlighter editorHighlighter = EditorHighlighterFactory.getInstance().createEditorHighlighter(new LightVirtualFile(PathConstant.TEMP_PATH + File.separator + UUID.randomUUID().toString() + "." + fileExt, fileType, content), colorsScheme, project);
        myEditor.setHighlighter(editorHighlighter);
        myEditor.setCaretVisible(true);
        myEditor.setCaretEnabled(false);
        myEditor.setViewer(true);

        myEditor.getComponent().setOpaque(false);
        EditorSettings editorSettings = myEditor.getSettings();
        editorSettings.setFoldingOutlineShown(false);
        editorSettings.setWhitespacesShown(false);
        editorSettings.setUseSoftWraps(false);

        // 是否包含头部操作按钮：插入、复制、新建文件等
        if (containHeadOps) {
            myEditor.setHeaderComponent(this.headerPanel(project, ext, supportNewFile, me));
            editorSettings.setLineNumbersShown(true);
        } else {
            editorSettings.setLineNumbersShown(false);
        }
        editorSettings.setAdditionalLinesCount(0);
        editorSettings.setAdditionalColumnsCount(0);
        if (parent != null) {
            Disposer.register(parent, this);
        }
        this.myEditor.getComponent().setOpaque(false);
    }

    private JPanel headerPanel(final Project project, String title, boolean supportNewFile, boolean me) {
        boolean isDark = UIUtil.judgeBackgroudDarkTheme();
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setOpaque(true);
        if (isDark) {
            headerPanel.setBackground(Color.decode("#3D4349"));
        } else {
            headerPanel.setBackground(Color.decode("#E5E9ED"));
        }

        JPanel centerPanel = new JPanel();
        JPanel rightPanel = new JPanel(new FlowLayout(0));
        //拷贝代码
        Icon copyIcon = null;
        if (isDark) {
            copyIcon = GPTIcons.COPY_DARK;
        } else {
            copyIcon = GPTIcons.COPY;
        }
        final JBLabel copyLabel = new JBLabel(copyIcon);
        copyLabel.setToolTipText("复制代码");
        copyLabel.setCursor(new Cursor(12));
        Icon finalCopyIcon = copyIcon;
        copyLabel.addMouseListener(new MouseAdapter() {
            public void mouseClicked(MouseEvent e) {
                Clipboard clipboard = Toolkit.getDefaultToolkit().getSystemClipboard();
                Transferable transferable = new StringSelection(CodeViewer.this.myCode);
                clipboard.setContents(transferable, (ClipboardOwner) null);
                (new TooltipUtil(copyLabel, finalCopyIcon, Actions.Commit)).showCopiedToolTip(e);
                // 进行代码采纳上报
                codeChatActivityReport();
            }
        });

        //插入代码
        Icon inertIcon = null;
        if (isDark) {
            inertIcon = GPTIcons.INSERT_DARK;
        } else {
            inertIcon = GPTIcons.INSERT;
        }
        final JBLabel insertLabel = new JBLabel(inertIcon);
        insertLabel.setToolTipText("插入代码");
        insertLabel.setCursor(new Cursor(12));
        insertLabel.addMouseListener(new MouseAdapter() {
            public void mouseClicked(MouseEvent e) {
                // 执行编辑器写入
                WriteCommandAction.runWriteCommandAction(myProject, () -> {
                    Document currentDocument = FileEditorManager.getInstance(project).getSelectedTextEditor().getDocument();
                    Editor[] editors = EditorFactory.getInstance().getEditors(currentDocument);
                    // 通常索引为0的编辑器就是当前获得焦点的
                    Editor currentEditor = editors[0];
                    int selectionStart = currentEditor.getSelectionModel().getSelectionStart();
                    int selectionEnd = currentEditor.getSelectionModel().getSelectionEnd();
                    boolean hasSelection = selectionStart > 0 && selectionStart != selectionEnd;

                    if (hasSelection) {
                        currentEditor.getDocument().replaceString(selectionStart, selectionEnd, myCode);
                    } else {
                        int caretOffset = currentEditor.getCaretModel().getOffset();
                        currentEditor.getDocument().insertString(caretOffset, myCode);
                    }
                });
                // 进行代码采纳上报
                codeChatActivityReport();
            }
        });

        //新建文件
        Icon newFileIcon = null;
        if (isDark) {
            newFileIcon = GPTIcons.NEW_FILE_DARK;
        } else {
            newFileIcon = GPTIcons.NEW_FILE;
        }
        final JBLabel newFileLabel = new JBLabel(newFileIcon);
        newFileLabel.setToolTipText("新建文件");
        newFileLabel.setCursor(new Cursor(12));
        newFileLabel.addMouseListener(new MouseAdapter() {
            public void mouseClicked(MouseEvent e) {
                // 执行文件写入
                WriteCommandAction.runWriteCommandAction(myProject, () -> {
                    try {
                        Document currentDocument = FileEditorManager.getInstance(project).getSelectedTextEditor().getDocument();
                        VirtualFile virtualFile = FileDocumentManager.getInstance().getFile(currentDocument);
                        String extName = newFileExt;
                        if (extName != null && !extName.isEmpty() && !extName.equals("Unknown")) {
                            extName = "." + newFileExt.toLowerCase();
                        } else {
                            extName = "";
                        }
                        // 构建新文件名
                        String fileName = "unittest-" + System.currentTimeMillis() + extName;
                        // 获取当前文件所在虚拟目录
                        VirtualFile dir = virtualFile.getParent();
                        // 在目录下创建新文件
                        VirtualFile newFile = dir.createChildData(this, fileName);
                        // 在新文件中插入代码
                        VfsUtil.saveText(newFile, myCode);

                        ApplicationManager.getApplication().invokeLater(() -> {
                            // 打开新创建的文件
                            FileEditorManager.getInstance(project).openFile(newFile, true);
                        });
                    } catch (Exception ex) {
                        logger.error("[cf] Create new file error!", ex);
                    }
                });
                // 进行代码采纳上报
                codeChatActivityReport();
            }
        });


        if (title != null && !title.isEmpty()) {
            JPanel leftPanel = new JPanel();
            JBLabel titleLabel = new JBLabel(title);
            leftPanel.add(titleLabel);
            leftPanelOld = leftPanel;
            headerPanel.add(leftPanelOld, "West");
        }
        if (supportNewFile && !me) {
            rightPanel.add(newFileLabel);
        }
        if (!me) {
            rightPanel.add(insertLabel);
        }
        rightPanel.add(copyLabel);
        headerPanel.add(centerPanel, "Center");
        headerPanel.add(rightPanel, "East");
        return headerPanel;
    }

    public void updateCode(String code, String title) {
        String finalCode = StringEscapeUtils.unescapeHtml(code);
        this.myCode = finalCode;
        Runnable runnable = () -> {
            this.myEditor.getDocument().setText(finalCode);
            if (title != null && !title.isEmpty()) {
                JPanel leftPanel = new JPanel();
                JBLabel titleLabel = new JBLabel(title);
                leftPanel.add(titleLabel);
                leftPanel.setComponentZOrder(titleLabel, 0);
                this.myEditor.getHeaderComponent().remove(leftPanelOld);
                leftPanelOld = leftPanel;
                this.myEditor.getHeaderComponent().add(leftPanelOld, "West");
            }
        };
        WriteCommandAction.runWriteCommandAction(this.myEditor.getProject(), runnable);
    }

    private void codeChatActivityReport() {
        // 是否CodeChat回答的消息，是才上报采纳
        if (AssistantChannel.CodeChat != this.assistantChannel) {
            return;
        }
        // 进行上报
        UserActivityReportService.codeActivityReport(UserActivityType.chat_accepted_code, this.myCode, null);
    }

    public JComponent getEditorComponent() {
        return this.myEditor.getComponent();
    }

    public Editor getEditor() {
        return this.myEditor;
    }

    @Override
    public void dispose() {
        if (myEditor != null) {
            EditorFactory.getInstance().releaseEditor(myEditor);
            myEditor = null;
        }
    }
}
