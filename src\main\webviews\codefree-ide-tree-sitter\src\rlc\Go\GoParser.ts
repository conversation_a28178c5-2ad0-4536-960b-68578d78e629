import type Parser from "web-tree-sitter";
import type CodeObjectIndexSystem from "../codeObjectIndexSystem";
import RelativeCodeObject from '../relativeCodeObject';

import {UpdateObject} from '../updateObject';
import type {IRelativeCodeFinder} from "../IRelativeCodeFinder";
import GoModInfo from "@/rlc/goModInfo";

// 类型别名：Node表示TreeSitter语法解析节点
type Node = Parser.SyntaxNode;


// parse解析得到的内容：一个代码文件中的主要信息，包括：所在包、import包、关联对象
interface GoFile {
    path: string; // 代码文件路径
    packagePath: string; // 文件所在包名
    importGoPackages: GoPackage[];
    fullObjects: GoObject[]; // 文件中所有对象信息（全）
    cacheObjects: UpdateObject[]; // 代码文件的简化信息（用于关联对象缓存）
}

interface GoObject {
    // 关联对象名：类名、接口名、枚举名
    objectName: string;

    // 关联对象类型：类定义、接口定义、枚举定义
    objectType: string;

    // 关联内容简要
    simpleText: string; // 整个代码文件简化文本
    fields: GoField[] | null; // 成员变量信息
}

// 成员变量信息
interface GoField {
    // 成员变量所属类型包路径
    fieldPackage: string;

    // 成员变量所属类型
    fieldType: string;

    // 成员变量名
    fieldVariable: string;
}


interface GoPackage {
    importPath: string;     // 完整导入路径，如 "github.com/user/proj/pkg/database/mysql"
    packageName: string;    // 包名，如 "mysql_driver"或"fmt"
    alias?: string;         // 可选的别名，如 f "fmt" 中的 "f"
    isDot?: boolean;        // 是否是点导入，如 . "fmt"
    isBlank?: boolean;      // 是否是空白导入，如 _ "database/sql"
}


export class GoParser implements IRelativeCodeFinder {
    public parser: Parser | null;

    private objectSystem: CodeObjectIndexSystem;

    public constructor(parser: Parser, codeObjectIndexSystem: CodeObjectIndexSystem) {
        this.parser = parser;
        this.objectSystem = codeObjectIndexSystem;
    }

    // =======  三大核心业务逻辑  =========

    /**
     * 对外逻辑1：解析传入文件内容，更新关联文件缓存
     * 解释代码文件，将代码文件中的主要语法对象解释出来，并存入代码对象缓存索引系统，以提供后续跨文件对象关联使用
     * 本接口为插件启动时，以及代码工程有代码文件发生用户非输入代码更新时调用
     *
     * @param jsonExt         json扩展字段，存额外的必要信息，go中存的是gomodmap
     * @param languageExt     代码的语言扩展名，如 ".go"
     * @param fileName        代码文件名称，为代码文件在代码工程内的路径
     * @param codeFileContent 代码文件内容
     */
    public ParseFile(languageExt: string, fileName: string, codeFileContent: string): GoFile | null {
        // 解析文件，获取语言特性
        const currFile = this.parseGoCode(fileName, codeFileContent, undefined, undefined);
        if (!currFile) {
            console.log(`GoParser ParseFile skip,code file parse is null.`)
            return null;
        }

        // 更新关联对象缓存
        this.objectSystem.updateByFile(
            languageExt,
            currFile.packagePath,
            currFile.path,
            currFile.cacheObjects
        );

        return currFile;
    }


    /**
     * 对外逻辑2：解析传入文件内容，返回关联对象
     */
    public FindRelativeObject(
        languageExt: string,
        path: string,
        content: string,
        row: number,
        column: number,
        goModMapJson: string
    ): RelativeCodeObject | null {

        console.log(`GoParser FindRelativeObject path:${path}`)

        // 识别代码文件，得到AST语法树根节点
        const rootNode = this.getRootNode(content);
        if (!rootNode) {
            console.log("FindRelativeObject failed, rootNode is null")
            return null;
        }

        //没有moduleMapping映射传入，则无法正确识别go相关路径，不支持跨文件关联
        if (!goModMapJson || goModMapJson.length === 0) {
            console.warn("FindRelativeObject failed, goModMapJson is null")
            return null;
        }
        let goModObject;
        let goModMap;
        try {
            goModObject = JSON.parse(goModMapJson);
            goModMap = new Map(Object.entries(goModObject));
        } catch (e) {
            console.error("FindRelativeObject error, parse goModMap fail.")
            return null;
        }
        if (goModMap === null || goModMap.size === 0) {
            console.warn("FindRelativeObject failed, goModMap is null")
            return null;
        }

        // 解析文件，获取相关语言特性
        const currFile = this.parseGoCode(path, content, rootNode, goModMap);
        if (!currFile) {
            console.log(`GoParser FindRelativeObject skip,code file parse is null.`)
            return null;
        }


        //全新的代码，寻找关联对象
        let relativeTypeNode: Node | null;
        let relativeObjectName: string | null = null;
        let relativePackagePath: string | null = null;

        //1、查找当前行是否有type_identifier，若有，检查是否为项目定义的类型，是则直接返回关联对象
        relativeTypeNode = this.findLastNodeOfType(rootNode, "type_identifier", row, column);
        if (relativeTypeNode) {
            // 当前行有,取最后一个符号
            relativeObjectName = relativeTypeNode.text.trim();

            // 查找这个类型的前面是否有包名信息，有则去找import pkg，没有则说明这个类型定义在当前包或者“.”导入的包下面
            const packageIdentifier = this.getPackageIdentifierBy(relativeTypeNode);
            if (packageIdentifier) {
                // 通过包id（名字或者别名）查找包路径
                relativePackagePath = currFile.importGoPackages.find(pkg => pkg.packageName === packageIdentifier || pkg.alias === packageIdentifier)?.importPath ?? "";
                if (relativePackagePath) {
                    return this.objectSystem.queryByPackageAndObjectName(languageExt, relativePackagePath, relativeObjectName);
                } else {
                    console.log(`currFile.importGoPackages cannot find ：${packageIdentifier}`)
                    return null;
                }
            } else {
                // 注意.导入，直接变成本包了
                const dotImportPaths: string[] = currFile.importGoPackages?.filter(pkg => pkg.isDot).map(pkg => pkg.importPath) || [];
                // 如果有.导入的包，则需要在这些包下查找,没有直接确认为本包下定义的类型
                if (dotImportPaths.length > 0) {
                    for (const packagePath of dotImportPaths) {
                        if (this.objectSystem.checkObjectInPackage(packagePath, relativeObjectName)) {
                            // 如果找到，立即返回
                            return this.objectSystem.queryByPackageAndObjectName(languageExt, packagePath, relativeObjectName);
                        }
                    }
                }
                // 如果没有.导入的包，则直接确认为本包下定义的类型,直接返回
                return this.objectSystem.queryByPackageAndObjectName(languageExt, currFile.packagePath, relativeObjectName);
            }
        }

        //2、当前行没有type_identifier时，查找当前行是否有identifier，若有，检查是否在function_declaration、method_declaration中，是的话，才继续
        // 查找这个identifier的变量定义或函数入参，找到他的类型，
        // 若没有对应的变量定义或函数入参，看看是不是包名
        const relativeIdentifierNode = this.findLastNodeOfType(rootNode, "identifier", row, column);
        if (relativeIdentifierNode) {
            //查看是否在方法或者函数内

            // 从本节点开始，向上查找出其父节点链路，拼接在待查找节点队列上
            const reverseNodeList: Node[] = [];
            let candidateNode: Node | null = relativeIdentifierNode;
            while (candidateNode) {
                reverseNodeList.push(candidateNode);
                candidateNode = candidateNode.parent;
            }
            if (reverseNodeList.length === 0) {
                console.log(`GoParser FindRelativeObject skip,nodeList is empty.`)
                return null;
            }

            // 检查光标是否在方法中：如果不在方法中，则不支持关联查找
            let funcNode: Node | null = null;
            let funcNodePos = 0;
            for (let i = 0; i < reverseNodeList.length; i++) {
                const node = reverseNodeList[i];
                if (node.type === 'function_declaration') {  //条件比java的少
                    funcNode = node;
                    funcNodePos = i;
                    break;
                }
            }
            if (!funcNode) {
                console.log(`GoParser FindRelativeObject skip,cursor is not in function.`)
                return null;
            }

            //从函数中找parameter_declaration（method的struct定义和方法的参数列表都通过这个取，区别在于前面的parameter_list一个是receiver，一个是parameters） short_var_declaration  var_declaration的变量节点
            //从上面的节点中找到identifier节点，注意go中一次定义可以定义多个identifier，注意每个节点中取的是identifier的list，（注意可能0个或者多个）
            //对比identifier中的text，和当前的relativeIdentifierNode的text，是的那个取出来，（如果list里面有多个，还要注意下标）
            //获取这个identifier 一起的type，可能是同级，也可能是子级，也可能（如果list里面有多个，还要注意下标，如果没有（各种奇怪的赋值），直接null）
            const [isFoundRelativeId, foundTypeNode] = this.getTypeDeclarationByIdentifier(relativeIdentifierNode, funcNode)
            relativeTypeNode = foundTypeNode
            // 返回结构[boolean，node],true+node非空的时候，处理typenode；true+node为空，返回空；false时对比importpkg
            if (isFoundRelativeId && relativeTypeNode) {
                const relativeTypeNodeText = relativeTypeNode.text.trim();
                const currGoObject = currFile.fullObjects.find(obj => obj.objectName === relativeTypeNodeText);
                if (currGoObject) {
                    //留个口子，对比本地的结构，存在则看下 field_identifier？？
                    const currField = this.firstDescendantOfType(relativeIdentifierNode, 'field_identifier')
                    if (currField) {
                        // 留个口子，后续做更详细的处理?  1、链式的时候是返回object的method，2、是substruct的时候是否检查substruct并返回，检查substruct不存在的时候怎么办
                        return this.objectSystem.queryByPackageAndObjectName(languageExt, currFile.packagePath, relativeTypeNodeText);
                    } else {
                        return this.objectSystem.queryByPackageAndObjectName(languageExt, currFile.packagePath, relativeTypeNodeText);
                    }
                } else {
                    //2、无对应的本文件结构时，直接将类型和本包、importpkglist分别查看是否存在缓存中，存在则返回结果
                    if (this.objectSystem.checkObjectInPackage(currFile.packagePath, relativeTypeNodeText)) {
                        return this.objectSystem.queryByPackageAndObjectName(languageExt, currFile.packagePath, relativeTypeNodeText);
                    }

                    for (const goPackages of currFile.importGoPackages) {
                        const result = this.objectSystem.checkObjectInPackage(goPackages.importPath, relativeTypeNodeText)
                        if (result) {
                            // 如果找到，立即返回
                            return this.objectSystem.queryByPackageAndObjectName(languageExt, goPackages.importPath, relativeTypeNodeText);
                        }
                    }
                }

                return null;
            } else if (isFoundRelativeId && !relativeTypeNode) {
                const relativeIdentifierNodeText = relativeIdentifierNode.text;
                console.log(`isFoundRelativeId is true,relativeTypeNode is null.  relativeIdentifierNodeText：${relativeIdentifierNodeText}`)
                return null;
            }

            //identifier一直找不到type的话，直接和importpkg的包名，别名对比，存在返回包，不存在返回null
            relativePackagePath = currFile.importGoPackages
                .find(pkg => pkg.packageName === relativeIdentifierNode.text || pkg.alias === relativeIdentifierNode.text)
                ?.importPath ?? "";
            if (relativePackagePath) {
                return this.objectSystem.queryByPackageAndObjectName(languageExt, relativePackagePath, '');
            }
        }

        //3、无结果直接返回null
        return null;
    }

    /**
     * 核心逻辑：提前文件语言特性信息并返回
     */
    private parseGoCode(fileName: string, content: string, rootNode?: Node, goModMap?: Map<String, any>): GoFile | null {
        console.log(`GoParser ParseFile fileName：${fileName}`)

        if (!rootNode) {
            const newNode = this.getRootNode(content);
            if (!newNode) {
                return null;
            }
            rootNode = newNode;
        }

        // 基于文件路径，截取所在包路径
        var packagePath = fileName.substring(0, fileName.lastIndexOf("/"));

        // 提取import pkg 列表
        const importGoPackages: GoPackage[] = [];
        for (let i = 0; i < rootNode.childCount; i++) {
            const node = rootNode.child(i);
            if (!node) {
                continue;
            }
            if (node.type === 'import_declaration') {
                let subImportPackage = this.parsePackages(node, goModMap);
                importGoPackages.push(...subImportPackage);
            }
        }

        const objects: GoObject[] = [];
        const objectsToUpdate: UpdateObject[] = [];
        //取object  提取declarations,获得文件中新定义的objects(结构体、函数和枚举常量）
        objects.push(...this.getObjects(rootNode, importGoPackages, packagePath))
        objectsToUpdate.push(...this.goObjectsToUpdateObjects(objects))

        return {
            path: fileName,
            packagePath: packagePath,
            importGoPackages,
            fullObjects: objects,
            cacheObjects: objectsToUpdate
        };
    }

    // =======  以下用于解析AST语法树，提前目标语言特性  =========
    /**
     * 调用TreeSitter，解析代码文件，获取AST语法树根节点
     */
    private getRootNode(codeFileContent: string) {
        if (!this.parser) {
            return null;
        }
        try {
            return this.parser.parse(codeFileContent).rootNode;
        } catch (error) {
            throw new Error(`go parsing error: ${error}`);
        }
    }

    /**
     * 查找当前节点的子节点中，符合查找类型要求的第一个子节点
     */
    private firstDescendantOfType(node: Node, types: string | Array<string>): Node | null {
        if (!Array.isArray(types)) {
            types = [types];
        }
        for (const child of node.children) {
            if (types.includes(child.type)) {
                return child;
            }
            const descendant = this.firstDescendantOfType(child, types);
            if (descendant) {
                return descendant;
            }
        }
        return null;
    }

    /**
     * 查找当前节点的子节点中，符合查找类型要求的所有子节点
     */
    private childrenOfType(node: Node, types: string | Array<string>): Array<Node> {
        const children: Array<Node> = [];
        if (!Array.isArray(types)) {
            types = [types];
        }
        for (const child of node.children) {
            if (types.includes(child.type)) {
                children.push(child);
            }
        }
        return children;
    }


    /**
     * 转换包路径：go特有，需要将import的 moduleName/xxx 格式路径，转换为 绝对路径/xxx 格式，才能用于反查缓存系统中的数据
     */
    private parsePackages(node: Node, goModMap?: Map<String, any>): GoPackage[] {
        if (node.type !== 'import_declaration') {
            return [];
        }

        const packages: GoPackage[] = [];
        const processedImports = new Set<string>();

        // 查找import_spec_list节点判断是否是多行导入
        const importSpecList = this.firstDescendantOfType(node, 'import_spec_list');

        if (importSpecList) {
            // 多行导入
            for (const child of importSpecList.children) {
                if (child.type === 'import_spec') {
                    const pkg = this.processImportSpec(child);
                    if (pkg && !processedImports.has(pkg.importPath)) {
                        packages.push(pkg);
                        processedImports.add(pkg.importPath);
                    }
                }
            }
        } else {
            // 单行导入
            const importSpec = this.firstDescendantOfType(node, 'import_spec');
            if (importSpec) {
                const pkg = this.processImportSpec(importSpec);
                if (pkg && !processedImports.has(pkg.importPath)) {
                    packages.push(pkg);
                    processedImports.add(pkg.importPath);
                }
            }
        }

        // 包路径替换
        if (packages.length != 0 && goModMap && goModMap.size > 0) {
            packages.forEach(pkg => {
                goModMap.forEach((value, key) => {
                    // 使用正则替换路径中的 key 为对应的 value
                    pkg.importPath = pkg.importPath.replace(new RegExp(`^${key}`, 'g'), value);
                });
            });
        }

        return packages;
    }

    //自动生成，把pkgPath分割了，其实不需要
    private processImportSpec(node: Node): GoPackage | null {
        // 获取导入路径字符串
        const pathLiteral = node.descendantsOfType('interpreted_string_literal')[0];
        if (!pathLiteral) {
            return null;
        }

        // 移除字符串的引号
        const importPath = pathLiteral.text.slice(1, -1);

        // 提取包名（路径的最后一部分）
        const pathParts = importPath.split('/');
        const packageName = pathParts[pathParts.length - 1];

        let pkg: GoPackage = {
            importPath,
            packageName
        };

        // 检查前面的标识符
        for (const child of node.children) {
            if (child.type === 'package_identifier') {
                pkg.alias = child.text;
                break;
            } else if (child.type === 'dot') {
                pkg.isDot = true;
                break;
            } else if (child.type === "blank_identifier") {
                pkg.isBlank = true;
                break
            }
        }

        return pkg;
    }


    /**
     * 提取文件中的Go对象信息，如：结构体、函数和枚举常量等
     */
    getObjects(rootNode: Node, importGoPackages: GoPackage[], filePackagePath: string): GoObject[] {
        const objects: GoObject[] = [];
        const methodMap = new Map();
        const constMap = new Map();

        // 第一遍：收集所有struct的method定义，所有的const_spec带type_identifier的const定义
        for (const node of rootNode.children) {
            if (node.type === 'method_declaration') {
                this.collectMethod(node, methodMap);
            } else if (node.type === 'const_declaration') {
                this.collectConst(node, constMap);
            }
        }

        // 第二遍：收集结构体、函数和枚举   还有接口类型
        for (const node of rootNode.children) {
            if (node.type === 'type_declaration') {
                const typeSpec = this.firstDescendantOfType(node, 'type_spec');
                //获取到type_spec的时候才处理，若获取不到，可能只有”type_alias“，为struct别名定义，暂时不处理
                if (typeSpec) {
                    const name = typeSpec.childForFieldName('name')?.text ?? "";
                    const typeNode = typeSpec.childForFieldName('type');

                    if (typeNode) {
                        // 处理结构体类型定义
                        if (typeNode.type === 'struct_type') {
                            const structObject = this.processStruct(name, typeNode, methodMap, importGoPackages, filePackagePath);
                            structObject && objects.push(structObject);
                            // 处理接口类型定义
                        } else if (typeNode.type === 'interface_type') {
                            const interfaceObject = this.processInterface(name, typeNode);
                            interfaceObject && objects.push(interfaceObject);
                        } else if (typeNode.type === 'type_identifier') {
                            // 处理的是枚举类型定义  例如：type Color int
                            const typeIdentifierObject = this.processTypeIdentifier(name, typeNode, constMap);
                            typeIdentifierObject && objects.push(typeIdentifierObject);
                        }
                    }

                }
            } else if (node.type === 'function_declaration') {
                objects.push(this.processFunction(node));
            }
        }

        return objects;
    }


    private collectMethod(node: Parser.SyntaxNode, methodMap: Map<any, any>) {
        const receiverNode = node.childForFieldName('receiver');
        if (!receiverNode) return;

        const structName = this.extractReceiverType(receiverNode);
        if (!methodMap.has(structName)) {
            methodMap.set(structName, []);
        }

        const methodField = {
            fieldPackage: '',
            fieldName: node.childForFieldName('name')?.text ?? "",
            simpleText: this.extractMethodSimpleText(node),
            fieldVariable: this.extractMethodSignature(node)
        };

        methodMap.get(structName).push(methodField);
    }

    // 只获取带有type_identifier（做作为某个基本类型的枚举量）的const定义，无type_identifier的时候，说明是本包的常量或枚举，目前不处理包级别的跨文件
    // 注意，iota需要特殊处理，因为iota会自动递增
    private collectConst(node: Parser.SyntaxNode, constMap: Map<any, any>) {
        const constSpecNodeList = this.childrenOfType(node, 'const_spec');
        if (constSpecNodeList) {
            for (let i = 0; i < constSpecNodeList.length; i++) {
                const constSpecNode = constSpecNodeList[i];
                const typeIdentifierNode = this.firstDescendantOfType(constSpecNode, 'type_identifier');
                if (typeIdentifierNode) {
                    const typeName = typeIdentifierNode.text
                    if (!constMap.has(typeName)) {
                        constMap.set(typeName, []);
                    }
                    constMap.get(typeName)!.push(constSpecNode);

                    // 处理iota,识别是否有iota，有的话对constSpecNodeList后面的常量进行检查，
                    // 1、没有type_identifier，也没有赋值的时候，加进constMap.get(typeName)中
                    // 2、没有type_identifier，有赋值的时候，有int_literal，加进constMap.get(typeName)中
                    if (this.firstDescendantOfType(constSpecNode, 'iota')) {
                        for (let j = i + 1; j < constSpecNodeList.length; j++) {

                            if (!this.firstDescendantOfType(constSpecNodeList[j], 'type_identifier')
                                && (!constSpecNodeList[j].childForFieldName('value')
                                    || this.firstDescendantOfType(constSpecNodeList[j], 'int_literal'))) {
                                constMap.get(typeName)!.push(constSpecNodeList[j]);
                                i++;
                            } else {
                                // 不满足时直接退出
                                break;
                            }
                        }
                    }

                }
            }
        }
    }


    processStruct(name: string, structNode: Parser.SyntaxNode | null, methodMap: Map<any, any>, importGoPackages: GoPackage[], filePackagePath: string): GoObject | undefined {
        const fields: GoField[] = [];
        let simpleText = `type ${name} struct {\n`
        if (structNode) {
            const node = this.firstDescendantOfType(structNode, 'field_declaration_list')
            if (node) {
                const fieldList = this.childrenOfType(node, 'field_declaration');
                if (fieldList) {
                    for (const fieldNode of fieldList) {
                        simpleText += `\t${fieldNode.text.trim()}\n`;
                        fields.push({
                            // fieldPackage: this.getPackagePath(fieldNode,importGoPackages,filePackagePath), // 这里获取也没啥用，以后再用
                            fieldPackage: '', // 这里获取也没啥用，以后再用
                            fieldVariable: fieldNode.childForFieldName('name')?.text ?? "",
                            fieldType: this.extractType(fieldNode.childForFieldName('type'))
                        });
                    }
                    simpleText += `}\n`;
                }

                // 添加该结构体的方法
                const methods = methodMap.get(name) || [];
                const ptrMethods = methodMap.get('*' + name) || [];
                for (const method of methods) {
                    simpleText += method?.simpleText + "\n";
                }
                for (const ptrMethod of ptrMethods) {
                    simpleText += ptrMethod?.simpleText + "\n";
                }

                return {
                    objectName: name,
                    objectType: 'struct_type',
                    simpleText: simpleText,
                    fields
                };
            }
        }

    }


    getPackagePath(node: Node | null, importGoPackages: GoPackage[], filePackagePath: string): string {
        if (!node) {
            return ""
        }

        const typeIdentifierNode = this.firstDescendantOfType(node, 'type_identifier');
        if (!typeIdentifierNode) return filePackagePath

        const packageIdentifier = this.getPackageIdentifierBy(typeIdentifierNode);
        if (packageIdentifier) {
            // 通过包id（名字或者别名）查找包路径
            return importGoPackages.find(pkg => pkg.packageName === packageIdentifier || pkg.alias === packageIdentifier)?.importPath ?? "";

        } else {
            // 注意.导入，直接变成本包了
            const dotImportPaths: string[] = importGoPackages?.filter(pkg => pkg.isDot).map(pkg => pkg.importPath) || [];
            // 如果有.导入的包，则需要在这些包下查找,没有直接确认为本包下定义的类型
            if (dotImportPaths.length > 0) {
                for (const packagePath of dotImportPaths) {
                    if (this.objectSystem.checkObjectInPackage(packagePath, typeIdentifierNode.text)) {
                        // 如果找到，立即返回
                        return packagePath;
                    }
                }
            }
            // 如果没有.导入的包，则直接确认为本包下定义的类型,直接返回
            return filePackagePath;
        }

    }


    processInterface(name: string, interfaceNode: Parser.SyntaxNode | null): GoObject | null {
        if (!interfaceNode) return null;
        let simpleText = ""; // 初始化为空字符串
        const methodList = this.childrenOfType(interfaceNode, 'method_elem');
        const subInterface: Node[] = this.childrenOfType(interfaceNode, 'type_elem');

        simpleText += `type ${name} interface {\n`;

        //遍历每个method
        for (const method of methodList) {
            simpleText += `\t${method.text}\n`;
        }

        //遍历每个subInterface
        for (const sub of subInterface) {
            simpleText += `\t${sub.text}\n`;
        }

        // 添加接口的结束括号
        simpleText += '}';

        return {
            objectName: name,
            objectType: 'interface_declaration',
            simpleText: simpleText,
            fields: null
        };
    }


    // const CONST_IDENTIFIER string = "AAAAA"这种是否处理呢
    processTypeIdentifier(name: string, oldTypeNode: Parser.SyntaxNode | null, constMap: Map<any, Parser.SyntaxNode[]>) {
        const fields: never[] = [];
        if (oldTypeNode) {
            // 获取的是" type Color int"中的int
            const oldTypeName = oldTypeNode.text;
            let simpleText = "type " + name + " " + oldTypeNode.text + "\n";
            const constDeclaration = constMap.get(name) || [];
            if (constDeclaration.length > 0) {
                simpleText += `const (\n`
                for (const constNode of constDeclaration) {
                    simpleText += `\t${constNode.text}\n`;
                }
                simpleText += `)\n`;
            }

            return {
                objectName: name,
                objectType: 'type_identifier',
                simpleText: simpleText,
                fields
            };
        }

    }


    processFunction(node: Parser.SyntaxNode): GoObject {
        const objectName = node.childForFieldName('name')?.text ?? ""
        const parameters = node.childForFieldName('parameters')?.text ?? ""
        const resultParam = node.childForFieldName('result')?.text ?? ""
        const result = resultParam === "" ? " " : " " + resultParam + " "
        const simpleText = `func ${objectName}${parameters}${result}{\n}`
        return {
            objectName: objectName,
            objectType: 'function_declaration',
            simpleText: simpleText,
            fields: [{
                fieldPackage: '',
                fieldType: 'signature',
                fieldVariable: this.extractFunctionSignature(node)
            }]
        };
    }


    extractReceiverType(receiverNode: Parser.SyntaxNode) {
        //method的接收者要区分有没有*
        const typeNode = this.firstDescendantOfType(receiverNode, 'parameter_declaration')?.childForFieldName('type');
        return typeNode ? typeNode.text : '';
    }

    extractMethodSimpleText(node: { childForFieldName: (arg0: string) => any; }) {
        const receiver = node.childForFieldName('receiver')?.text;
        const methodName = node.childForFieldName('name')?.text;
        const params = node.childForFieldName('parameters')?.text;
        const resultParam = node.childForFieldName('result')?.text ?? ""
        const result = resultParam === "" ? " " : " " + resultParam + " "
        return `func ${receiver} ${methodName}${params}${result}{\n}`;
    }

    extractMethodSignature(node: { childForFieldName: (arg0: string) => any; }) {
        const params = node.childForFieldName('parameters');
        const result = node.childForFieldName('result');
        return `${params.text}${result ? ' ' + result.text : ''}`;
    }

    extractType(typeNode: Parser.SyntaxNode | null) {
        return typeNode ? typeNode.text : '';
    }

    extractFunctionSignature(node: { childForFieldName: (arg0: string) => any; }) {
        const params = node.childForFieldName('parameters');
        const result = node.childForFieldName('result');
        return `${params.text}${result ? ' ' + result.text : ''}`;
    }

    goObjectsToUpdateObjects(goObjects: GoObject[]): UpdateObject[] {
        return goObjects.map(goObject => {
            // go没有内部类，packageSuffix传""即可
            return new UpdateObject("", goObject.objectType, goObject.objectName, goObject.simpleText);
        });
    }

    // 从 type_identifier 获取关联的 package_identifier
    getPackageIdentifierBy(typeIdentifierNode: Node | null): String | null {
        if (!typeIdentifierNode) {
            return null;
        }
        // 确保当前节点是 type_identifier
        if (typeIdentifierNode.type !== 'type_identifier') {
            return null;
        }

        // 获取父节点（qualified_type）
        const parent = typeIdentifierNode.parent;
        if (!parent) {
            return null;
        }

        // 在同级子节点中寻找 package_identifier
        for (const sibling of parent.children) {
            if (sibling.type === 'package_identifier') {
                return sibling.text.trim();
            }
        }

        return null;
    }

    findLastNodeOfType(rootNode: Node, types: String | Array<String>, row: number, column: number): Node | null {
        if (!rootNode) {
            return null;
        }
        const TypeNodes = rootNode.descendantsOfType(types, {row, column: 0}, {row, column});
        if (TypeNodes.length) {
            // 当前行有,取最后一个符号
            return TypeNodes[TypeNodes.length - 1];
        }
        return null;
    }

    getTypeDeclarationByIdentifier(identifierNode: Node, funcNode: Node): [boolean, Node | null] {
        for (const node of funcNode.descendantsOfType(
            [
                'parameter_declaration',
                'short_var_declaration',
                'var_declaration',
            ],
            funcNode.startPosition,
            identifierNode.startPosition
        )) {
            switch (node.type) {
                case 'parameter_declaration':
                    const varIdName = this.firstDescendantOfType(node, 'identifier')?.text ?? '';
                    if (identifierNode.text === varIdName) {
                        return [true, this.firstDescendantOfType(node, 'type_identifier')]
                    }
                    break
                case 'short_var_declaration':
                case 'var_declaration' :
                    const varIds = node.descendantsOfType('identifier');
                    for (let i = 0; i < varIds.length; i++) {
                        if ((varIds[i]?.text ?? '') === identifierNode.text) {
                            const typeIdentifierNodes = node.descendantsOfType('type_identifier')
                            if (typeIdentifierNodes.length == 0) {
                                return [true, null]
                            } else if (typeIdentifierNodes.length == 1) {
                                return [true, typeIdentifierNodes[0]]
                            } else if (typeIdentifierNodes.length == varIds.length) {
                                return [true, typeIdentifierNodes[i]]
                            } else {
                                //typeIdentifierNodes.length大于1，且不与varIds.length相等。目前考虑只有一下情况
                                // 	person := otherpackage.Person2{
                                // 		Name2: "Alice",
                                // 		Address2: &otherpackage.Address2{
                                // 			Road9: "Main Road",
                                // 		},
                                // 	}
                                // console.log(`typeIdentifierNodes.length ${typeIdentifierNodes.length} 不匹配 varIds.length ${varIds.length}`)
                                return [true, typeIdentifierNodes[0]];
                            }
                        }
                    }
                    break
            }
        }
        return [false, null];
    }


}
