package com.srdcloud.ideplugin.codecomplete.handle.codeprovider.strategy

import com.intellij.openapi.application.ReadAction
import com.intellij.openapi.application.invokeLater
import com.srdcloud.ideplugin.codecomplete.domain.CompletionType
import com.srdcloud.ideplugin.codecomplete.domain.CompletionElement
import com.srdcloud.ideplugin.codecomplete.domain.CompletionRequest
import com.srdcloud.ideplugin.codecomplete.domain.CompletionState.Companion.getInlineCompletionState
import com.srdcloud.ideplugin.codecomplete.handle.CompletionHandler
import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.prompt.EditorPromptProvider.Companion.getCompletionPrompt
import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.prompt.Prompt
import com.srdcloud.ideplugin.general.constants.Constants
import com.srdcloud.ideplugin.general.constants.RtnCode
import com.srdcloud.ideplugin.general.enums.AnswerMode
import com.srdcloud.ideplugin.general.utils.DebugLogUtil
import com.srdcloud.ideplugin.general.utils.GitUtil
import com.srdcloud.ideplugin.service.QuestionTask
import com.srdcloud.ideplugin.service.domain.apigw.ApigwWebsocketRespPayload
import com.srdcloud.ideplugin.service.domain.codechat.AskQuestionParams
import com.srdcloud.ideplugin.service.interfaces.IQuestionTaskEventHandler
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory
import java.time.Duration
import java.time.LocalDateTime
import java.util.*
import kotlin.coroutines.Continuation
import kotlin.coroutines.resume

/**
 * 本地补全：仅使用编辑文档的前文、后文进行补全请求
 */
class DefaultCodeCompletionStrategy public constructor() : CodeCompletionStrategy() {

    // 初始化一个日志记录器，用于记录日志信息
    private var logger = LoggerFactory.getLogger(DefaultCodeCompletionStrategy::class.java)

    // 定义一个可选的 QuestionTask 对象，用于处理代码补全任务
    private var questionTask: QuestionTask? = null

    // 重写 getCodeCompletion 方法，该方法接受一个 InlineCompletionRequest 请求和一个 Continuation 单元
    override fun getCodeCompletion(request: CompletionRequest, continuation: Continuation<Unit>?): String {
        // 获取当前文档中推理上下文：前缀、后缀、stopWords
        val prompt = ReadAction.compute<Prompt, Throwable> {
            request.editor.getCompletionPrompt(request.type)
        }

        // 空文档编辑，不进行补全
        if (prompt.prefix.isEmpty()) {
            logger.warn("[cf] getCodeCompletion skip,prompt.prefix.isEmpty()");
            return ""
        }

        // 定义ws提问任务
        var beginTime: LocalDateTime? = null
        var endTime: LocalDateTime?
        questionTask = QuestionTask(object :
            IQuestionTaskEventHandler {
            //收到下行的代码补全结果
            override fun onAnswer(
                regId: String?,
                isEnd: Int,
                accumulateAnswer: String?,
                segmentAnswer: String?,
                seqNo: Int,
                payload: ApigwWebsocketRespPayload?
            ) {
                // 补全效果评测日志
                DebugLogUtil.println("[cf debug] DefaultCodeCompletionStrategy CodeCompletion reqId: $regId,\n file:${prompt.fileName},\n prefix:${prompt.prefix},\n completionAnswer:$accumulateAnswer")

                // 补全通信性能监控埋点
                endTime = LocalDateTime.now()
                val duration = Duration.between(beginTime, endTime)
                val milliseconds = duration.toMillis()
                if (milliseconds > Constants.Code_completion_alarm_delay) {
                    logger.warn("[cf] DefaultCodeCompletionStrategy CodeCompletion alarm, askQuestion regId: $regId, duration: $milliseconds ms")
                }
                runBlocking {
                    request.let {
                        val state = it.editor.getInlineCompletionState()
                        if (regId == questionTask?.GetReqId() && state != null && accumulateAnswer != null) {
                            if (accumulateAnswer.isNotEmpty()) {
                                var ret = ""
                                if (it.type == CompletionType.AUTO) {
                                    val strings = accumulateAnswer.split("\n")
                                    for (i in strings) {
                                        ret += i
                                        if (i.isNotBlank()) break
                                        ret += "\n"
                                    }
                                } else {
                                    ret = accumulateAnswer
                                }
                                val answerHandle = ret.replace("(\n)+".toRegex(), "\n")
                                state.suggestions += CompletionElement(answerHandle, request.type == CompletionType.AUTO)
                            }
                            if (isEnd == Constants.IS_ANSWER_END_TRUE) {
                                continuation?.resume(Unit)
                                cancel()
                            }
                        }
                    }
                }
            }

            override fun onTaskError(regId: String?, eventId: Int, payload: ApigwWebsocketRespPayload?) {
                logger.warn("[cf] DefaultCodeCompletionStrategy CodeCompletion askQuestion OnTaskError: $eventId, reqId: $regId")
                if (regId == questionTask?.GetReqId()) {
                    continuation?.resume(Unit)
                    cancel()
                }
            }
        }, AnswerMode.SYNC.value)

        // 发起提问
        val questionType =
            if (request.type == CompletionType.AUTO)
                Constants.QUESTION_TASK_TYPE_CODEGEN else Constants.QUESTION_TASK_TYPE_CODEGEN_MANUAL
        DebugLogUtil.info("[cf]DefaultCodeCompletionStrategy CodeCompletion AskQuestion begin")
        val result = questionTask?.AskQuestion(
            AskQuestionParams(
                UUID.randomUUID().toString(),
                null,
                null,
                questionType,
                prompt.fileName,
                prompt.prefix,
                prompt.suffix,
                null,
                arrayOf(prompt.stopWords).toMutableList(),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                prompt.importSnippets,
                null,
                GitUtil.getGitUrls(request.editor.project),
                null,
                null
            )
        )
        if (result == RtnCode.NOT_LOGIN) {
            logger.warn("[cf] DefaultCodeCompletionStrategy CodeCompletion askQuestion failed by not login.")
            cancel()
        } else if (result == RtnCode.NO_CHANNEL) {
            logger.warn("[cf] DefaultCodeCompletionStrategy CodeCompletion askQuestion failed by no channel.")
            cancel()
        } else {
            beginTime = LocalDateTime.now()
            DebugLogUtil.info("[cf] DefaultCodeCompletionStrategy CodeCompletion askQuestion startWorking,ReqId: ${questionTask?.GetReqId()}")
            CompletionHandler.startWorking(request.type)
        }

        // 返回空字符串
        return ""
    }

    // 重写 cancel 方法，用于取消当前任务
    override fun cancel() {
        questionTask?.let {
            // 记录日志，显示取消任务
            logger.info("[cf] InlineCompletion cancel questionTask: ${it.GetReqId()}")
            questionTask = null
            CompletionHandler.stopWorking(RtnCode.SUCCESS)
        }
    }

    // 重写 isEnabled 方法，返回 true 表示可用，原生补全策略默认可用
    override fun isEnabled(): Boolean {
        return true
    }
}
