package com.srdcloud.ideplugin.diff.diffreport

import com.intellij.openapi.editor.event.DocumentEvent
import com.intellij.openapi.editor.event.DocumentListener
import com.srdcloud.ideplugin.diff.diffreport.DiffEditorCreateListener.Companion.DIFF_CONTENT_KEY
import com.srdcloud.ideplugin.diff.diffreport.DiffEditorCreateListener.Companion.DIFF_CLICK_TIMESTAMP
import com.srdcloud.ideplugin.diff.diffreport.DiffEditorCreateListener.Companion.DIFF_TIME_KEY
import com.srdcloud.ideplugin.general.enums.ActivityType
import com.srdcloud.ideplugin.general.utils.IdeUtil
import com.srdcloud.ideplugin.service.UserActivityReportService.codeActivityReport
import com.sun.istack.NotNull

class DiffDocumentListener: DocumentListener {

    override fun documentChanged(@NotNull event: DocumentEvent) {

        // 获取变更后的内容
        val oldText = event.oldFragment.toString() // 被替换/删除的旧文本
        val newText = event.newFragment.toString() // 插入的新文本
        val document = event.document

        // 判断变更类型并输出和记录
        val finalText = newText.ifEmpty { oldText }

        if (document.getUserData(DIFF_CLICK_TIMESTAMP) != null) {
            codeActivityReport(
                ActivityType.COMPOSER_ACCEPTED_CODE,
                IdeUtil.findCurrentProject(),
                finalText,
                ""
            )
            document.putUserData(DIFF_CLICK_TIMESTAMP, null)
        }else {
            // 更新时间戳
            document.putUserData(DIFF_CONTENT_KEY, finalText)
            document.putUserData(DIFF_TIME_KEY, System.currentTimeMillis());
        }
    }
}