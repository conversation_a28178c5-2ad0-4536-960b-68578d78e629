package com.srdcloud.ideplugin.webview.codechat.terminal;

import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.general.utils.JsonUtil;
import com.srdcloud.ideplugin.general.utils.TerminalUtil;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;
import com.srdcloud.ideplugin.webview.codechat.common.InvokeTerminalReqType;
import com.srdcloud.ideplugin.webview.codechat.terminal.request.InvokeTerminalRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class InvokeTerminalHandler {

    private static final Logger logger = LoggerFactory.getLogger(InvokeTerminalHandler.class);

    private final Project project;

    private final CodeChatWebview parent;

    public InvokeTerminalHandler(Project project, CodeChatWebview parent) {
        this.project = project;
        this.parent = parent;
    }

    public void processInvokeTerminal(String request) {
        InvokeTerminalRequest invokeTerminalRequest = JsonUtil.getInstance().fromJson(request, InvokeTerminalRequest.class);
        String reqType = invokeTerminalRequest.getData().getReqType();
        if (reqType.equals(InvokeTerminalReqType.EXECUTE_COMMAND)) {
            String command = invokeTerminalRequest.getData().getCommand();
            TerminalUtil.invokeTerminal(project, command);
            // todo 回传webview
        } else {
            logger.error("Unknown reqType: {}", reqType);
        }
    }
}
