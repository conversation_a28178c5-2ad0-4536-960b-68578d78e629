package com.srdcloud.ideplugin.diff.diffreport

import com.intellij.diff.DiffContext
import com.intellij.diff.DiffExtension
import com.intellij.diff.FrameDiffTool
import com.intellij.diff.requests.DiffRequest
import com.intellij.diff.requests.SimpleDiffRequest
import com.intellij.diff.tools.fragmented.UnifiedDiffViewer
import com.intellij.diff.tools.util.side.TwosideTextDiffViewer
import com.intellij.diff.util.Side
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.editor.Document
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.ex.EditorGutterComponentEx
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Key
import com.intellij.openapi.vfs.LocalFileSystem
import com.srdcloud.ideplugin.diff.DiffFile
import com.srdcloud.ideplugin.diff.DiffService
import com.srdcloud.ideplugin.general.enums.ActivityType
import com.srdcloud.ideplugin.general.utils.DebugLogUtil
import com.srdcloud.ideplugin.service.UserActivityReportService.codeActivityReport
import java.awt.BorderLayout
import java.awt.Color
import java.awt.Cursor
import java.awt.event.MouseAdapter
import java.awt.event.MouseEvent
import javax.swing.JButton
import javax.swing.JPanel

// 监听diff界面的创建事件，用于绑定在gutter和diff界面底部按钮组的点击事件
class DiffEditorCreateListener : DiffExtension() {

    private val logger = Logger.getInstance(this::class.java)

    companion object {
        // 用于存储diff时间和内容的key，获取diff时间戳
        val DIFF_TIME_KEY = Key.create<Long>("CODEFREE.DIFF.EDITOR_CHANGE_TIMESTAMP")
        val DIFF_CLICK_TIMESTAMP = Key.create<Long>("CODEFREE.DIFF.DIFF_CLICK_TIMESTAMP")

        // 用于存储diff内容的key，获取diff内容
        val DIFF_CONTENT_KEY = Key.create<String>("CODEFREE.DIFF.DIFF_CONTENT")

        // 用于监测当前的data hold是否是插件生成的diff界面
        // todo：value需要抽成类
        var DIFF_KEY: Key<String> = Key.create("CODEFREE.DIFF")

        var DIFF_FILE_KEY: Key<DiffFile> = Key.create("CODEFREE.DIFF.DIFF_FILE")

        //用于检测diff是否accept的最大判定间隔，单位为毫秒
        const val MAX_DIFF_TIME = 50
    }


    // 当diff viewer创建时，调用此方法
    override fun onViewerCreated(viewer: FrameDiffTool.DiffViewer, context: DiffContext, request: DiffRequest) {
        // 根据key判定是否为codefree生成的
        if (request.getUserData(DIFF_KEY) != null) {

            // 根据key判定是否为composer生成的diff界面
            // 目前仅针对composer生成的diff界面，后续可扩展其他工具
            if (request.getUserData(DIFF_KEY).equals("composer") && request is SimpleDiffRequest) {

                // 获取diff file信息
                val diffFile = request.getUserData(DIFF_FILE_KEY)
                if (diffFile == null) {
                    logger.error("[cf] diffFile file null, skip create decision panel and listeners.")
                    return
                }
                // 只有undecided状态的diff file才需要进行事件监听
                if (diffFile.status != DiffService.STATUS_UNDECIDED) {
                    return
                }

                // 绑定在diff界面的源文件的文档监听
                val simpleRequest = request

                // 对于新生成的文件，curFilePath通过diffFile获取
                val curFilePath = if(simpleRequest.contentTitles[0] == null) {
                    diffFile.path
                } else {
                    getName(simpleRequest.contentTitles[0])
                }
                val suggestPath = getName(simpleRequest.contentTitles[1])

                // 获取源文件的文档，用于监听采纳事件
                val virtualFile = LocalFileSystem.getInstance().findFileByPath(curFilePath)
                val modifiedFile = LocalFileSystem.getInstance().findFileByPath(suggestPath)

                // 根据源文件的文档，获取编辑器
                val curDocument: Document? = FileDocumentManager.getInstance().getDocument(virtualFile!!)
                if (curDocument != null) {

                    // 绑定在DIFF界面的源文件的文档监听
                    curDocument.addDocumentListener(DiffDocumentListener())

                    // 设定在DIFF界面的底部按钮组，用于接受或拒绝代码更改
                    viewer.component.add(BorderLayout.SOUTH, createDecisionPanel(context.project, curDocument, diffFile))

                    // 处理不同类型的差异视图
                    when (viewer) {
                        is UnifiedDiffViewer -> {
                            val unifiedViewer = viewer  // 智能转换为具体类型
                            val editor = unifiedViewer.editor as Editor

                            // 处理统一差异视图逻辑...
                            report("UnifiedDiffView", editor, curDocument, request)
                        }

                        is TwosideTextDiffViewer -> {
                            val twosideViewer = viewer  // 智能转换为具体类型
                            // 处理两侧视图逻辑（如当前文件的gutter操作）
                            val leftEditor = twosideViewer.getEditor(Side.LEFT)

                            // 处理统一差异视图逻辑...
                            report("Two Side TextDiffViewer:LEFT", leftEditor, curDocument, request)

                            val rightEditor = twosideViewer.getEditor(Side.RIGHT)
                            rightEditor.document.setReadOnly(true)
                            // 处理统一差异视图逻辑...
                            report("Two Side TextDiffViewer:RIGHT", rightEditor, curDocument, request)
                        }

                        else -> {
                            logger.warn("[cf] Unknown diff viewer type: ${viewer::class.simpleName}")
                            return
                        }
                    }


                } else {
                    logger.warn("[cf] 比对用源文件不存在！")
                }
            } else if (request.getUserData(DIFF_KEY).equals("codechat")) {
                // 预埋：code chat生成diff单独处理
            }
        }
    }

    fun report(type: String, editor: Editor, curDocument: Document?, request: SimpleDiffRequest) {
        val gutter = editor.gutter
        if (gutter is EditorGutterComponentEx) {
            // 绑定在gutter的点击事件，用于判定是否接受diff界面
            gutter.addMouseListener(object : MouseAdapter() {
                override fun mouseClicked(e: MouseEvent?) {
                    if (e != null) {
                        DebugLogUtil.info("[cf] $type was clicked")
                        // 如果点击了gutter，则根据源文件变更状态来判定是否接受diff界面
                        if (curDocument != null) {
                            if (curDocument.getUserData(DIFF_TIME_KEY) != null && curDocument.getUserData(
                                    DIFF_CONTENT_KEY
                                ) != null
                            ) {
                                val currentTime = System.currentTimeMillis()
                                if ((currentTime - curDocument.getUserData(DIFF_TIME_KEY)!!) < MAX_DIFF_TIME) {
                                    // 如果在最大判定间隔内，则报告代码活动并清除diff时间戳和内容
                                    codeActivityReport(
                                        ActivityType.COMPOSER_ACCEPTED_CODE,
                                        editor.project,
                                        curDocument.getUserData(DIFF_CONTENT_KEY),
                                        ""
                                    )
                                    curDocument.putUserData(DIFF_TIME_KEY, null)
                                    curDocument.putUserData(DIFF_CONTENT_KEY, null)

                                    DebugLogUtil.info("[cf] report code activity success")
                                    DebugLogUtil.println("report code activity success")

                                    // 检查分块采纳逻辑
                                    try {
                                        val curFilePath = getName(request.contentTitles[0])
                                        val suggestPath = getName(request.contentTitles[1])

                                        val curFile = LocalFileSystem.getInstance().findFileByPath(curFilePath)
                                        val modifiedFile = LocalFileSystem.getInstance().findFileByPath(suggestPath)

                                        val curFileDocument = FileDocumentManager.getInstance().getDocument(curFile!!)
                                        val suggestDocument = FileDocumentManager.getInstance().getDocument(modifiedFile!!)

                                        ApplicationManager.getApplication().invokeLater {
                                            FileDocumentManager.getInstance().saveDocument(curFileDocument!!)
                                        }

                                        if (curFileDocument?.text.equals(suggestDocument?.text)) {
                                            editor.project?.let {
                                                ApplicationManager.getApplication().invokeLater {
                                                    DiffService.getInstance(it)?.onAcceptFile(curFile.path, curFile)
                                                }
                                            }
                                        }
                                    }catch (e: Exception) {
                                        logger.warn("[cf] diff accept error")
                                    }
                                }
                            }
                        }
                    }
                }
            })
        }
    }


    /**
     * 根据request的content title内容拼接文件路径，用于监听对于文件的变化
     */
    private fun getName(str: String): String {

        //根据request的content title内容拼接文件路径，用于监听对于文件的变化
        val startIndex = str.indexOf('(') + 1
        val endIndex = str.lastIndexOf(')')
        val result1 = str.substring(0, startIndex - 2)
        val result2 = str.substring(startIndex, endIndex)

        return result2 + "\\" + result1
    }

    private fun createDecisionPanel(project: Project?, curDocument: Document, diffFile: DiffFile?): JPanel {

        // 设置接受按钮，用于接受diff界面中的代码更改
        val acceptButton = JButton("接受")
        acceptButton.foreground = Color.GREEN
        acceptButton.cursor = Cursor.getPredefinedCursor(Cursor.HAND_CURSOR)

        // 设置拒绝按钮，用于拒绝diff界面中的代码更改
        val rejectButton = JButton("拒绝")
        rejectButton.cursor = Cursor.getPredefinedCursor(Cursor.HAND_CURSOR)
        rejectButton.foreground = Color.GRAY

        // 将按钮添加到diff界面底部，用于接受或拒绝代码更改
        var panel = JPanel()
        panel.add(rejectButton)
        panel.add(acceptButton)
        panel.isOpaque = false

        // 接受按钮监听
        acceptButton.addMouseListener(object : MouseAdapter() {
            override fun mouseClicked(e: MouseEvent?) {
                if (project != null && diffFile != null) {
                    curDocument.putUserData(DIFF_CLICK_TIMESTAMP, System.currentTimeMillis())
                    DiffService.getInstance(project)?.acceptFile(diffFile.path)
                } else {
                    logger.error("[cf] project or diffFile is null, skip accept file")
                }
            }

            override fun mouseEntered(e: MouseEvent?) {
                acceptButton.background = Color.GREEN
            }

            override fun mouseExited(e: MouseEvent?) {
                acceptButton.background = null
            }
        })

        // 拒绝按钮监听
        rejectButton.addMouseListener(object : MouseAdapter() {
            override fun mouseClicked(e: MouseEvent?) {
                if (project != null && diffFile != null) {
                    DiffService.getInstance(project)?.rejectFile(diffFile.path)
                } else {
                    logger.error("[cf] project or diffFile is null, skip reject file")
                }
            }

            override fun mouseEntered(e: MouseEvent?) {
                rejectButton.background = Color.GRAY
            }

            override fun mouseExited(e: MouseEvent?) {
                rejectButton.background = null
            }
        })

        return panel
    }

}

