package com.srdcloud.ideplugin.webview.codechat.common;

/**
 * <AUTHOR>
 * @date 2025/1/13
 */
public class StatusEventType {
    public static final int LOGIN = 1;
    public static final int BROWSER_OPENED = 2;
    public static final int CODE_RECEIVED = 3;
    public static final int ACCESS_TOKEN_RECEIVED = 4;
    public static final int USER_INFO_RECEIVED = 5;
    public static final int CHANNEL_REGISTERED = 6;
    public static final int LOGIN_SUCCESS = 7;
    public static final int LOGIN_CANCELED = 8;
    public static final int LOGIN_EXPIRED = 9;
    public static final int LOGIN_FAILED = 10;
    public static final int LOGOUT = 11;
    public static final int WSSERVER_ERROR = 12;
    public static final int QUESTION_NOT_LOGIN = 13;
    public static final int WSSERVER_RECONNECT = 14;
    public static final int THEME_CHANGED = 15;
}
