package com.srdcloud.ideplugin.agent.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.srdcloud.ideplugin.agent.model.AgentConfigFile;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import com.srdcloud.ideplugin.general.utils.OsUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;

public class ConfigFileManager {

    private static final Logger logger = LoggerFactory.getLogger(ConfigFileManager.class);

    private final Path configPath;
    private final ObjectMapper objectMapper;

    public ConfigFileManager(Path configPath) {
        this.configPath = configPath;
        this.objectMapper = new ObjectMapper();
    }

    public AgentConfigFile readConfig() throws IOException {
        if (!Files.exists(configPath)) {
            return createDefaultConfig();
        }
        return objectMapper.readValue(configPath.toFile(), AgentConfigFile.class);
    }

    public void writeConfig(AgentConfigFile config) throws IOException {
        objectMapper.writeValue(configPath.toFile(), config);
    }

    private AgentConfigFile createDefaultConfig() {
        AgentConfigFile config = new AgentConfigFile();
        config.setAgent(new HashMap<>());
        return config;
    }

    /**
     * 更新或生成索引文件
     */
    @Deprecated
    public static void updateConfigFileOld(String pluginType, String agentName, String version) throws IOException {
        String configFilePath = new AgentPath(pluginType, agentName).getConfigPath();
        String platform = OsUtil.getOs() + "-" + OsUtil.getArch();
        String versionFolderPath = agentName + "-" + version + "-" + platform;
        String path = Paths.get(new AgentPath(pluginType, agentName).getAgentBasePath(),"agent", agentName, versionFolderPath).toString();

        ObjectMapper objectMapper = new ObjectMapper();
        ObjectNode rootNode;

        File configFile = new File(configFilePath);

        synchronized (ConfigFileManager.class){
            // 确保父目录存在
            File parentDir = configFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs(); // 创建父目录
            }

            // 如果索引文件存在，读取文件内容；否则创建一个新的 JSON 对象
            if (configFile.exists()) {
                rootNode = (ObjectNode) objectMapper.readTree(configFile);
            } else {
                rootNode = objectMapper.createObjectNode();
                rootNode.putObject("agent");
            }

            // "agent" -> "core/tabby/node" -> "os-arch"
            ObjectNode platformNode = getOrCreateNestedNode(rootNode, "agent", agentName, platform);

            // 更新 path 和 version
            platformNode.put("path", path);
            platformNode.put("version", StringUtils.removeStart(version,"v"));

            // 将更新后的 JSON 写回文件
            objectMapper.writerWithDefaultPrettyPrinter().writeValue(configFile, rootNode);
        }
    }

    /**
     * 递归获取或创建嵌套节点
     *
     * @param parentNode 父节点
     * @param keys       嵌套路径
     * @return 最深层的节点
     */
    private static ObjectNode getOrCreateNestedNode(ObjectNode parentNode, String... keys) {
        ObjectNode currentNode = parentNode;
        for (String key : keys) {
            ObjectNode childNode = (ObjectNode) currentNode.get(key);
            if (childNode == null) {
                childNode = currentNode.putObject(key);
            }
            currentNode = childNode;
        }
        return currentNode;
    }

    /**
     * 更新配置文件，支持版本数组格式
     * 将新版本信息添加到现有版本列表中，并按照语义化版本排序
     * 配置结构示例: {"agent": {"core": {"win32-x64": [{"path": "...", "version": "1.0.1"}, ...]}}}
     * 
     * @param pluginType 插件类型
     * @param agentName 代理名称
     * @param version 版本号
     * @throws IOException 如果读写配置文件失败
     */
    public static void updateConfigFile(String pluginType, String agentName, String version) throws IOException {
        // todo 重构此处代码
        DebugLogUtil.info("[ConfigFileManager] 开始更新配置文件, pluginType=" + pluginType + ", agentName=" + agentName + ", version=" + version);
        String configFilePath = new AgentPath(pluginType, agentName).getConfigPath();
        String platform = OsUtil.getOs() + "-" + OsUtil.getArch();
        String versionFolderPath = agentName + "-" + version + "-" + platform;
        String path = Paths.get(new AgentPath(pluginType, agentName).getAgentBasePath(), "agent", agentName, versionFolderPath).toString();

        logger.info("[cf] Start updating config file. path={}...", path);

        // 标准化版本号（移除可能的v前缀）

        ObjectMapper objectMapper = new ObjectMapper();
        File configFile = new File(configFilePath);

        try {
            synchronized (ConfigFileManager.class) {
                // 确保父目录存在
                File parentDir = configFile.getParentFile();
                if (parentDir != null && !parentDir.exists()) {
                    DebugLogUtil.info("[ConfigFileManager] 创建父目录: " + parentDir.getAbsolutePath());
                    parentDir.mkdirs();
                }

                // 读取或创建配置根节点
                com.fasterxml.jackson.databind.JsonNode rootNode;
                ObjectNode rootObjectNode;

                if (configFile.exists()) {
                    DebugLogUtil.info("[ConfigFileManager] 配置文件已存在，读取现有内容");
                    rootNode = objectMapper.readTree(configFile);
                    rootObjectNode = (ObjectNode) rootNode;
                } else {
                    DebugLogUtil.info("[ConfigFileManager] 配置文件不存在，创建新配置");
                    rootObjectNode = objectMapper.createObjectNode();
                    rootObjectNode.putObject("agent");
                }
                ObjectNode agentNameNode = getOrCreateNestedNode(rootObjectNode, "agent", agentName);
                DebugLogUtil.info("[ConfigFileManager] 已获取或创建代理节点: " + agentName);

                // 处理平台节点，这应该是一个数组
                com.fasterxml.jackson.databind.JsonNode platformNode = agentNameNode.get(platform);
                com.fasterxml.jackson.databind.node.ArrayNode versionsArray;

                if (platformNode != null && platformNode.isArray()) {
                    DebugLogUtil.info("[ConfigFileManager] 平台节点已存在: " + platform);
                    versionsArray = (com.fasterxml.jackson.databind.node.ArrayNode) platformNode;
                } else {
                    DebugLogUtil.info("[ConfigFileManager] 创建新的平台节点数组: " + platform);
                    // 创建新的版本数组
                    versionsArray = agentNameNode.putArray(platform);
                }

                // 检查版本是否已存在
                boolean versionExists = false;
                DebugLogUtil.info("[ConfigFileManager] 开始检查版本是否已存在，当前版本数组大小: " + versionsArray.size());

                for (int i = 0; i < versionsArray.size(); i++) {
                    com.fasterxml.jackson.databind.JsonNode versionNode = versionsArray.get(i);
                    String existingVersion = versionNode.get("version").asText();

                    if (existingVersion.equals(version)) {
                        // 版本已存在，更新path
                        DebugLogUtil.info("[ConfigFileManager] 版本已存在，更新路径");
                        ObjectNode existingVersionNode = (ObjectNode) versionNode;
                        existingVersionNode.put("path", path);
                        versionExists = true;
                        break;
                    }
                }

                if (!versionExists) {
                    DebugLogUtil.info("[ConfigFileManager] 版本不存在，创建新版本节点");
                    // 创建新版本节点
                    ObjectNode newVersionNode = objectMapper.createObjectNode();
                    newVersionNode.put("path", path);
                    newVersionNode.put("version", version);

                    // 直接添加到数组末尾
                    DebugLogUtil.info("[ConfigFileManager] 添加新版本到数组末尾");
                    versionsArray.add(newVersionNode);
                }

                // 将更新后的配置写回文件
                objectMapper.writerWithDefaultPrettyPrinter().writeValue(configFile, rootObjectNode);
                DebugLogUtil.info("[ConfigFileManager] 配置文件更新完成");
            }
        } catch (IOException e) {
            logger.error("[cf] Failed to update config file. IO Exception:", e);
            DebugLogUtil.error("[ConfigFileManager] 更新配置文件时发生IO异常: " + e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("[cf] Failed to update config file.", e);
            throw e;
        }
    }
}