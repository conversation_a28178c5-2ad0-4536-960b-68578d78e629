package com.srdcloud.ideplugin.agent.commclient;

import com.google.gson.*;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import org.jetbrains.annotations.NotNull;

import java.util.Objects;
import java.util.logging.Logger;

/**
 * Tabby Agent 消息接收器
 * 处理来自 Tabby Agent 的消息
 */
public class TabbyAgentMessageReceiver implements AgentMessageReceiver {
    private static final Logger logger = Logger.getLogger(TabbyAgentMessageReceiver.class.getName());
    private static final Gson gson = new Gson();

    private final String serviceId;

    // 可以注入依赖服务
    public TabbyAgentMessageReceiver() {
        this(null);
    }

    public TabbyAgentMessageReceiver(String serviceId) {
        this.serviceId = serviceId;
    }

    /**
     * 处理 Agent 消息
     * @param text 消息内容 JSON 字符串
     */
    @Override
    public void onAgentMessageHandler(@NotNull String text) {
        DebugLogUtil.info("[cf] TabbyAgentMessageReceiver.onAgentMessageHandler: " + text);

        if (text.trim().isEmpty()) {
            return;
        }
        text = text.trim();

        try {
            // 处理旧格式消息 [id, data], 目前都是这种格式
            if (text.startsWith("[") && text.endsWith("]")) {
                handleLegacyFormatMessage(text);
                return;
            }

            // 处理新格式消息 {messageId, messageType, data} ，暂时write和read都是[xxx]行，而不是{xx}, 所以这个暂时没启用
            if (text.startsWith("{") && text.endsWith("}")) {
                handleNewFormatMessage(text);
                return;
            }

            // 不支持的格式，后续调整
//            logger.warning("[TabbyAgent] 不支持的消息格式: " + text);
        } catch (Exception error) {
//            logger.severe("[TabbyAgent] 处理消息时出错: " + error.getMessage());
        }
    }

    /**
     * 处理旧格式消息 [id, data]
     */
    private void handleLegacyFormatMessage(String text) {
        JsonArray parsed = gson.fromJson(text, JsonArray.class);
        int id = parsed.get(0).getAsInt();
        JsonElement dataElement = parsed.get(1);

        // 处理事件消息
        if (id == 0) {
            handleEventMessage(dataElement);
        } else {
            // 处理响应消息
            logger.fine("[TabbyAgent] 收到响应消息 ID: " + id + ", 数据: " + gson.toJson(dataElement));
        }
    }

    /**
     * 处理新格式消息 {messageId, messageType, data}
     */
    private void handleNewFormatMessage(String text) {
        JsonObject message = gson.fromJson(text, JsonObject.class);
        String messageId = message.has("messageId") ? message.get("messageId").getAsString() : null;
        String messageType = message.has("messageType") ? message.get("messageType").getAsString() : null;
        JsonElement data = message.get("data");

        // 记录消息信息
        logger.info("[TabbyAgent] 消息类型: " + messageType + ", 数据: " + gson.toJson(data));

        // 根据消息类型处理不同的消息
        switch (Objects.requireNonNull(messageType)) {
            case "completion" -> handleCompletionMessage(data);
            case "statusChanged" -> handleStatusChangedMessage(data);
            case "configUpdated" -> handleConfigUpdatedMessage(data);
            case "issuesUpdated" -> handleIssuesUpdatedMessage(data);
            default ->
                // 处理其他类型的消息
                    handleGenericMessage(messageType, data);
        }
    }

    /**
     * 处理事件消息（旧格式）
     */
    private void handleEventMessage(JsonElement dataElement) {
        if (dataElement.isJsonObject()) {
            JsonObject data = dataElement.getAsJsonObject();
            if (data.has("event")) {
                String event = data.get("event").getAsString();
                switch (event) {
                    case "statusChanged" -> logger.info("[TabbyAgent] 状态变更: " + gson.toJson(data));
                    case "configUpdated" -> logger.info("[TabbyAgent] 配置更新: " + gson.toJson(data));
                    case "authRequired" -> logger.info("[TabbyAgent] 需要认证");
                    case "issuesUpdated" -> logger.info("[TabbyAgent] 问题更新: " + gson.toJson(data));
                }
            }
        } else if (dataElement.isJsonPrimitive()) {
            String data = dataElement.getAsString();
            if ("notInitialized".equals(data)) {
                logger.info("[TabbyAgent] 未初始化");
            } else if ("ready".equals(data)) {
                logger.info("[TabbyAgent] 就绪");
            }
        }
    }

    /**
     * 处理补全消息
     */
    private void handleCompletionMessage(JsonElement data) {
        // 实现补全消息的处理逻辑
        logger.fine("[TabbyAgent] 处理补全消息: " + gson.toJson(data));
    }

    /**
     * 处理状态变更消息
     */
    private void handleStatusChangedMessage(JsonElement data) {
        // 实现状态变更消息的处理逻辑
        logger.info("[TabbyAgent] 状态变更: " + gson.toJson(data));
    }

    /**
     * 处理配置更新消息
     */
    private void handleConfigUpdatedMessage(JsonElement data) {
        // 实现配置更新消息的处理逻辑
        logger.info("[TabbyAgent] 配置更新: " + gson.toJson(data));
    }

    /**
     * 处理问题更新消息
     */
    private void handleIssuesUpdatedMessage(JsonElement data) {
        // 实现问题更新消息的处理逻辑
        logger.info("[TabbyAgent] 问题更新: " + gson.toJson(data));
    }

    /**
     * 处理通用消息
     */
    private void handleGenericMessage(String messageType, JsonElement data) {
        // 处理其他类型的消息
        logger.fine("[TabbyAgent] 收到消息类型: " + messageType + ", 数据: " + gson.toJson(data));
    }
}