package com.srdcloud.ideplugin.webview.codechat.composer.request;

import com.srdcloud.ideplugin.remote.domain.WorkItem.WorkItemInfo;
import com.srdcloud.ideplugin.webview.base.domain.WebViewCommand;
import com.srdcloud.ideplugin.webview.codechat.composer.domain.ContextInputItem;

import java.util.List;

public class ComposerChatRequestData extends WebViewCommand {
    private String reqType;
    private String dialogId;
    private String input;
    private String displayContent; //chat展示的内容
    private String createTime; //请求时间
    private ContextInputItem[] contextInputItems;
    private String chatType;
    private String title;
    private String modelName;
    private List<WorkItemInfo> selectedWorkItems;

    public ComposerChatRequestData(String reqType, String dialogId, String input, String displayContent, String createTime, ContextInputItem[] contextInputItems, String chatType, String title, String modelName, List<WorkItemInfo> selectedWorkItems) {
        this.reqType = reqType;
        this.dialogId = dialogId;
        this.input = input;
        this.displayContent = displayContent;
        this.createTime = createTime;
        this.contextInputItems = contextInputItems;
        this.chatType = chatType;
        this.title = title;
        this.modelName = modelName;
        this.selectedWorkItems = selectedWorkItems;
    }

    public String getReqType() {
        return reqType;
    }

    public void setReqType(String reqType) {
        this.reqType = reqType;
    }

    public String getDialogId() {
        return dialogId;
    }

    public void setDialogId(String dialogId) {
        this.dialogId = dialogId;
    }

    public String getInput() {
        return input;
    }

    public void setInput(String input) {
        this.input = input;
    }

    public String getDisplayContent() {
        return displayContent;
    }

    public void setDisplayContent(String displayContent) {
        this.displayContent = displayContent;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public ContextInputItem[] getContextInputItems() {
        return contextInputItems;
    }

    public void setContextInputItems(ContextInputItem[] contextInputItems) {
        this.contextInputItems = contextInputItems;
    }

    public String getChatType() {
        return chatType;
    }

    public void setChatType(String chatType) {
        this.chatType = chatType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public List<WorkItemInfo> getSelectedWorkItems() {
        return selectedWorkItems;
    }

    public void setSelectedWorkItems(List<WorkItemInfo> selectedWorkItems) {
        this.selectedWorkItems = selectedWorkItems;
    }
}
