package com.srdcloud.ideplugin.assistant.codechatNative.ui.prompttemplate;

import com.intellij.find.SearchTextArea;
import com.intellij.icons.AllIcons;
import com.intellij.openapi.Disposable;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.ComboBox;
import com.intellij.openapi.ui.VerticalFlowLayout;
import com.intellij.ui.JBColor;
import com.intellij.ui.components.JBLabel;
import com.intellij.ui.components.JBLoadingPanel;
import com.intellij.ui.components.JBScrollPane;
import com.intellij.ui.components.JBTextArea;
import com.intellij.ui.components.panels.VerticalLayout;
import com.intellij.util.ui.JBUI;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.CodeChatCompleteEngin;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.PromptTemplateLoader;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.PromptTemplate;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.PromptTemplateCategory;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.PromptTemplateScope;
import com.srdcloud.ideplugin.assistant.codechatNative.ui.CodeChatMainPanel;
import com.srdcloud.ideplugin.common.icons.MyIcons;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.enums.PromptOperationType;
import com.srdcloud.ideplugin.general.enums.PromptScopeType;
import com.srdcloud.ideplugin.general.utils.*;
import com.srdcloud.ideplugin.remote.domain.PromptManage.PromptTemplateListResponse;
import com.srdcloud.ideplugin.service.UserActivityReportService;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import javax.swing.*;
import java.awt.*;
import java.awt.event.*;
import java.util.ArrayList;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/13
 * @desc 指令模板tab面板组合面板
 */
public class CodeChatPromptTemplateGroupComponent extends JBLoadingPanel {

    // 窗体头部
    JPanel headerPanel;

    // 窗体内容
    JPanel contentPanel;

    JPanel promptTemplateFilterPanel;

    JPanel promptScopePanel;

    JPanel promptSearchPanel;

    JPanel opsPanel;

    // 列表窗口，垂直布局、可滚动
    JPanel promptListPanel = new JPanel(new VerticalLayout(JBUI.scale(0)));
    // -- 将列表包装成可滚动
    JBScrollPane promptListScrollPane = new JBScrollPane(promptListPanel, ScrollPaneConstants.VERTICAL_SCROLLBAR_AS_NEEDED,
            ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);

    // -- 滚动条位置值：0为起点，100为终点
    public int currentPromptListScrollValue = 0;
    // -- 滚动条拖动事件响应器
    PromptListAdjustmentListener scrollListener = new PromptListAdjustmentListener();

    JPanel pagePanel;

    JLabel pageText = new JLabel();

    private Project project;

    private CodeChatMainPanel codeChatMainPanel;

    // 线程池
    private ExecutorService executorService;

    // ======== 模板列表相关变量
    // -- 模板查询关键字
    public static String selectedPromptKeyword;

    // -- 模板查询范围
    ArrayList<PromptTemplateScope> promptScopeList;
    public static String selectedPromptScope = "all";

    // -- 模板分类
    JPanel promptCategoryPanel;
    DefaultComboBoxModel<PromptTemplateCategory> promptCategoryModel = new DefaultComboBoxModel<>();
    ComboBox<PromptTemplateCategory> promptCategoryBox = new ComboBox<>(promptCategoryModel);
    ItemListener promptCategoryBoxItemListener;
    ArrayList<PromptTemplateCategory> promptCategoryList;

    // 当前点选的模板
    public static PromptTemplateCategory selectedPromptCategory;

    // 模板列表-分页大小
    public static int promptTemplatePageSize = 30;

    // 模板列表-current页
    public static int promptTemplateScrollCurrentPage = 1;

    // 模板列表-total页数
    public static int promptTemplateScrollTotalPages = 1;

    public static boolean lastPage = false;


    public CodeChatPromptTemplateGroupComponent(@NotNull Project project, final CodeChatMainPanel codeChatMainPanel, @Nullable LayoutManager manager, @NotNull Disposable parent, int startDelayMs) {
        super(manager, parent, startDelayMs);
        this.project = project;
        this.codeChatMainPanel = codeChatMainPanel;
        boolean isDark = UIUtil.judgeBackgroudDarkTheme();
        this.setBorder(JBUI.Borders.empty(0, 6, 0, 6));
        UIUtil.setBackground(this);

        // 窗体header栏位:标题、操作按钮
        headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBorder(JBUI.Borders.empty());
        headerPanel.setVisible(true);
        UIUtil.setBackground(headerPanel);

        // 窗体头部-左侧区域:标题
        JPanel headerPanelLeft = new JPanel();
        headerPanelLeft.setOpaque(false);
        UIUtil.setBackground(headerPanelLeft);
        headerPanelLeft.setBorder(JBUI.Borders.empty(6, 3, 0, 0));
        headerPanelLeft.setLayout(new FlowLayout(FlowLayout.LEFT, 0, 0));

        JPanel titlePanel = new JPanel(new BorderLayout());
        titlePanel.setBorder(JBUI.Borders.empty());
        titlePanel.setOpaque(false);
        UIUtil.setBackground(titlePanel);
        titlePanel.setBorder(BorderFactory.createEmptyBorder());

        JLabel titleLabel = new JLabel(CodeChatMainPanel.promptTemplateCardName);
        Font defaultFont = titleLabel.getFont();
        Font customFont = defaultFont.deriveFont(Font.PLAIN, 13);
        titleLabel.setFont(customFont);
        titleLabel.setVisible(true);
        titleLabel.setVerticalAlignment(JLabel.CENTER); // 设置垂直居中对齐
        titlePanel.add(titleLabel, BorderLayout.CENTER);

        headerPanelLeft.add(titlePanel);
        headerPanelLeft.add(Box.createHorizontalStrut(16));//分隔

        // 窗体头部-右侧区域
        JPanel headerPanelRight = new JPanel();
        headerPanelRight.setOpaque(false);
        UIUtil.setBackground(headerPanelRight);
        headerPanelRight.setBorder(JBUI.Borders.empty(3, 0, 1, 0));
        headerPanelRight.setLayout(new FlowLayout(FlowLayout.RIGHT, 0, 0));

        // 模版中心跳转
        Icon promptCenterIcon = MyIcons.PromptCenter;
        if (isDark) {
            promptCenterIcon = MyIcons.PromptCenterDark;
        }
        JBLabel promptCenterSwitch = new JBLabel(promptCenterIcon);
        promptCenterSwitch.setOpaque(false);
        promptCenterSwitch.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                BrowseUtil.Companion.browse(ConfigWrapper.PromptCenterPageUrl);
            }

            @Override
            public void mouseEntered(MouseEvent e) {
                promptCenterSwitch.setCursor(new Cursor(Cursor.HAND_CURSOR));
                promptCenterSwitch.setToolTipText("模板中心");
            }
        });

        // -- 侧边栏收起按钮
        Icon closeIcon = null;
        if (isDark) {
            closeIcon = MyIcons.CloseDark;
        } else {
            closeIcon = MyIcons.Close;
        }
        JBLabel closeSwitch = new JBLabel(closeIcon);
        closeSwitch.setOpaque(false);
        closeSwitch.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                // 收起左侧展开
                codeChatMainPanel.closeLeftWindow();
            }

            @Override
            public void mouseEntered(MouseEvent e) {
                closeSwitch.setCursor(new Cursor(Cursor.HAND_CURSOR));
                closeSwitch.setToolTipText("关闭");
            }
        });
        // 右侧填充:flow布局，预埋更多logo位置
        headerPanelRight.add(promptCenterSwitch);
        headerPanelRight.add(Box.createHorizontalStrut(5));//分隔
        headerPanelRight.add(closeSwitch);

        headerPanel.add(headerPanelLeft, BorderLayout.WEST);
        headerPanel.add(headerPanelRight, BorderLayout.EAST);
        this.add(headerPanel, BorderLayout.NORTH);


        // 窗体内容
        contentPanel = new JPanel(new BorderLayout());
        contentPanel.setVisible(true);
        contentPanel.setOpaque(false);
        UIUtil.setBackground(contentPanel);
        this.add(contentPanel, BorderLayout.CENTER);


        //1、内容的North区域：垂直布局的筛选项
        promptTemplateFilterPanel = new JPanel(new VerticalLayout(JBUI.scale(3)));
        promptTemplateFilterPanel.setVisible(true);
        UIUtil.setBackground(promptTemplateFilterPanel);
        //-- 关键字搜索筛选
        promptSearchPanel = new JPanel(new BorderLayout());
        promptSearchPanel.setVisible(true);
        UIUtil.setBackground(promptSearchPanel);
        promptSearchPanel.setBorder(JBUI.Borders.empty(0));//边框保持一致样式

        SearchTextArea promptSearchInputArea = new SearchTextArea(new JBTextArea(), true);
        UIUtil.setBackground(promptSearchInputArea);
        UIUtil.setBackground(promptSearchInputArea.getTextArea());
        promptSearchInputArea.setMultilineEnabled(false);
        promptSearchInputArea.setBorder(promptCategoryBox.getBorder());// 边框样式统一
        promptSearchInputArea.getTextArea().setLineWrap(true);
        promptSearchInputArea.getTextArea().setWrapStyleWord(false);
        promptSearchInputArea.setToolTipText("请输入关键字");
        setPlaceholder("搜索指令模板", promptSearchInputArea, isDark);
        promptSearchInputArea.getTextArea().addKeyListener(new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent e) {
                e.consume();
                if (e.getKeyCode() == KeyEvent.VK_ENTER) {
                    selectedPromptKeyword = promptSearchInputArea.getTextArea().getText();
                    // 重置分页
                    promptTemplateScrollCurrentPage = 1;
                    promptTemplateScrollTotalPages = 1;
                    lastPage = false;
                    // 当Enter键被按下时，拉取最新模板进行渲染
                    refreshPromptTemplateList();
                }
            }
        });

        promptSearchPanel.add(promptSearchInputArea, BorderLayout.CENTER);
        promptTemplateFilterPanel.add(promptSearchPanel);


        //-- 范围条件下拉筛选
        promptScopePanel = new JPanel(new BorderLayout());
        promptScopePanel.setVisible(true);
        UIUtil.setBackground(promptScopePanel);
        DefaultComboBoxModel<PromptTemplateScope> promptScopeModel = new DefaultComboBoxModel<>();
        promptScopeList = PromptScopeType.getPromptScopeList();
        promptScopeList.forEach(promptScopeModel::addElement);
        ComboBox<PromptTemplateScope> promptScopeBox = new ComboBox<>(promptScopeModel);
        UIUtil.setBackground(promptScopeBox);
        promptScopeBox.setSelectedIndex(3);//默认选中“全部”，需要与下面的 selectedPromptScope 对应上
        selectedPromptScope = "all";
        promptScopeBox.addItemListener(e -> {
            if (e.getStateChange() == ItemEvent.SELECTED) {
                PromptTemplateScope selectedScopeItem = (PromptTemplateScope) e.getItem();
                selectedPromptScope = selectedScopeItem.getKey();

                // 重置分类数据
                refreshPromptCategory();

                // 重置分页
                promptTemplateScrollCurrentPage = 1;
                promptTemplateScrollTotalPages = 1;
                lastPage = false;

                // 拉取最新模板进行渲染
                refreshPromptTemplateList();
            }
        });
        promptScopePanel.add(promptScopeBox, BorderLayout.CENTER);
        promptTemplateFilterPanel.add(promptScopePanel);

        //-- 分类条件筛选
        promptCategoryPanel = new JPanel(new BorderLayout());
        promptCategoryPanel.setVisible(true);
        UIUtil.setBackground(promptCategoryPanel);
        // 初始化下拉项选择监听器
        initPromptCategoryBoxItemListener();
        // 加载分类数据:628版本，改为懒加载模式，点击展开侧边栏才加载
        //refreshPromptCategory();
        promptCategoryPanel.add(promptCategoryBox, BorderLayout.CENTER);
        promptTemplateFilterPanel.add(promptCategoryPanel);

        //-- 操作按钮区域：预埋，后续UI优化或扩增时使用
        //opsPanel = new JPanel(new BorderLayout());
        //opsPanel.setVisible(true);


        // -- 北部筛选区域
        contentPanel.add(promptTemplateFilterPanel, BorderLayout.NORTH);

        // -- center：展示模版列表
        promptListPanel = new JPanel();
        promptListPanel.setLayout(new VerticalFlowLayout(0, 4));
        promptListPanel.setOpaque(false);
        UIUtil.setBackground(promptListPanel);

        promptListScrollPane = new JBScrollPane(promptListPanel);
        promptListScrollPane.setOpaque(false);
        UIUtil.setBackground(promptListScrollPane);
        promptListScrollPane.setBorder(BorderFactory.createEmptyBorder(0, 0, 0, 0));
        promptListScrollPane.getVerticalScrollBar().setUnitIncrement(30);
        promptListScrollPane.setHorizontalScrollBarPolicy(31);
        promptListScrollPane.setVerticalScrollBarPolicy(20);
        promptListScrollPane.getVerticalScrollBar().addAdjustmentListener(scrollListener);

        // 加载列表面板内容:628版本，改为懒加载模式，点击展开侧边栏才加载
        //refreshPromptTemplateList();
        contentPanel.add(promptListScrollPane, BorderLayout.CENTER);

        // -- 模板列表底部分页栏
        pagePanel = new JPanel(new BorderLayout());
        pagePanel.setVisible(true);
        UIUtil.setBackground(pagePanel);
        contentPanel.add(pagePanel, BorderLayout.SOUTH);
        pagePanel.setBorder(JBUI.Borders.empty());

        // 刷新分页展示信息
        refreshPageInfo();

        JLabel left = new JLabel();
        left.setIcon(AllIcons.General.ArrowLeft);
        left.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                super.mouseClicked(e);
                pagePromptTemplateList(false);
            }

            @Override
            public void mouseEntered(MouseEvent e) {
                left.setCursor(new Cursor(Cursor.HAND_CURSOR));
                left.setToolTipText("查看上一页");
            }
        });

        JLabel right = new JLabel();
        right.setIcon(AllIcons.General.ArrowRight);
        right.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                super.mouseClicked(e);
                pagePromptTemplateList(true);
            }

            @Override
            public void mouseEntered(MouseEvent e) {
                right.setCursor(new Cursor(Cursor.HAND_CURSOR));
                right.setToolTipText("查看下一页");
            }
        });

        pagePanel.add(left, BorderLayout.WEST);
        pagePanel.add(pageText, BorderLayout.CENTER);
        pagePanel.add(right, BorderLayout.EAST);
    }

    public void setPlaceholder(String placeholder, SearchTextArea searchInputArea, boolean isDark) {
        searchInputArea.getTextArea().setText(placeholder);
        searchInputArea.getTextArea().setForeground(UIUtil.getTextPlaceHolderColor());
        searchInputArea.getTextArea().addFocusListener(new FocusListener() {
            @Override
            public void focusGained(FocusEvent e) {
                if (searchInputArea.getTextArea().getText().equals(placeholder)) {
                    searchInputArea.getTextArea().setText(null);
                    searchInputArea.setForeground(null);
                    searchInputArea.getTextArea().setForeground(null);
                }
            }

            @Override
            public void focusLost(FocusEvent e) {
                if (searchInputArea.getTextArea().getText().isEmpty()) {
                    searchInputArea.getTextArea().setText(placeholder);
                    searchInputArea.setForeground(UIUtil.getTextPlaceHolderColor());
                    searchInputArea.getTextArea().setForeground(UIUtil.getTextPlaceHolderColor());
                }
            }
        });
    }

    /**
     * 初始化分类下拉监听器
     */
    private void initPromptCategoryBoxItemListener() {
        promptCategoryBoxItemListener = new ItemListener() {
            @Override
            public void itemStateChanged(ItemEvent e) {
                if (e.getStateChange() == ItemEvent.SELECTED) {
                    selectedPromptCategory = (PromptTemplateCategory) e.getItem();
                    // 重置分页
                    promptTemplateScrollCurrentPage = 1;
                    promptTemplateScrollTotalPages = 1;
                    lastPage = false;
                    // 拉取最新模板进行渲染
                    refreshPromptTemplateList();
                }
            }
        };
    }

    public void refreshPageInfo() {
        // 0517:特殊处理：lastUsed查询只查询显示1页
        if (PromptScopeType.LAST_USED.getType().equalsIgnoreCase(selectedPromptScope)) {
            lastPage = true;
            promptTemplateScrollTotalPages = 1;
        }

        pageText.setFont(IdeUtil.getIDELabelFont());
        pageText.setHorizontalAlignment(JLabel.CENTER);
        pageText.setVerticalAlignment(JLabel.CENTER);
        pageText.setText(promptTemplateScrollCurrentPage + "/" + promptTemplateScrollTotalPages);
    }

    /**
     * 加载模板分类
     */
    public void refreshPromptCategory() {
        // 移除数据与监听器
        promptCategoryModel.removeAllElements();
        promptCategoryBox.removeItemListener(promptCategoryBoxItemListener);
        // 重新加载数据
        ThreadPoolUtil.submit(() -> {
            promptCategoryList = PromptTemplateLoader.loadPromptCategoryList();

            if (CollectionUtils.isNotEmpty(promptCategoryList)) {
                // 0614版本：二开专区分类特殊处理，仅提取包含“二开”关键字的分类
                if (PromptScopeType.SECONDARY.getType().equalsIgnoreCase(selectedPromptScope)) {
                    promptCategoryList = promptCategoryList.stream().filter(t -> t.getName().contains("二开")).collect(Collectors.toCollection(ArrayList::new));
                }
                promptCategoryList.forEach(promptCategoryModel::addElement);
            }
            promptCategoryBox.setModel(promptCategoryModel);
            UIUtil.setBackground(promptCategoryBox);
            if (CollectionUtils.isNotEmpty(promptCategoryList)) {
                //默认选中第一个分类
                promptCategoryBox.setSelectedIndex(0);
                PromptTemplateCategory selectedItem = (PromptTemplateCategory) promptCategoryBox.getSelectedItem();
                if (Objects.nonNull(selectedItem)) {
                    selectedPromptCategory = selectedItem;
                } else {
                    selectedPromptCategory = null;
                }
            }

            // 重新设置数据
            promptCategoryBox.addItemListener(promptCategoryBoxItemListener);
        });
    }

    /**
     * 模板列表选中事件监听处理
     */
    public void handlePromptTemplateSelection(final PromptTemplate promptTemplate) {
        SwingUtilities.invokeLater(() -> {
            if (CodeChatCompleteEngin.onAnswerStatus) {
                this.codeChatMainPanel.getStopAnswerButton().doClick();
            }
            // 设置开发问答输入框
            this.codeChatMainPanel.getInputTextArea().getTextArea().setText(promptTemplate.getContent());

            // 暂存当前所选模板Id
            CodeChatCompleteEngin.currUsePromptTemplateId = promptTemplate.getId();

            // 模板指标点选使用行为上报
            UserActivityReportService.promptOpsReport(promptTemplate, PromptOperationType.USE);
        });
    }


    /**
     * 翻页：传参true则向下翻，否则向上翻
     */
    private void pagePromptTemplateList(final Boolean down) {
        // 0517:特殊处理：lastUsed查询只查询前30条，不允许翻页
        if (PromptScopeType.LAST_USED.getType().equalsIgnoreCase(selectedPromptScope)) {
            lastPage = true;
        }
        if (down) {
            if (lastPage) {
                return;
            }
            promptTemplateScrollCurrentPage = promptTemplateScrollCurrentPage + 1;
        } else {
            if (promptTemplateScrollCurrentPage <= 1) {
                promptTemplateScrollCurrentPage = 1;
                return;
            }
            promptTemplateScrollCurrentPage = promptTemplateScrollCurrentPage - 1;
        }

        // 翻页拉取数据
        refreshPromptTemplateList();
    }

    /**
     * 重新加载并渲染 模板 列表
     */
    public void refreshPromptTemplateList() {
        this.startLoading();

        ThreadPoolUtil.submit(() -> {
            // 重新加载数据
            ArrayList<PromptTemplate> promptTemplateListData = new ArrayList<>();
            // 未登录
            if (!LocalStorageUtil.checkIsLogin()) {
                promptTemplateScrollCurrentPage = 1;
                promptTemplateScrollTotalPages = 1;
                lastPage = true;
            } else {
                PromptTemplateListResponse listResponse = PromptTemplateLoader.loadCodeChatPromptTemplates(selectedPromptScope, selectedPromptCategory, selectedPromptKeyword, promptTemplateScrollCurrentPage, promptTemplatePageSize);
                if (Objects.isNull(listResponse) || CollectionUtils.isEmpty(listResponse.getData())) {
                    promptTemplateScrollCurrentPage = 1;
                    promptTemplateScrollTotalPages = 1;
                    lastPage = true;
                } else {
                    // 分页结果计算
                    promptTemplateScrollTotalPages = (listResponse.getTotalCount() + promptTemplatePageSize - 1) / promptTemplatePageSize;
                    lastPage = promptTemplateScrollCurrentPage == promptTemplateScrollTotalPages;

                    // 数据提取,创建列表元素
                    promptTemplateListData = listResponse.getData();

                    // 0614版本特殊处理：“我的创建”选择范围的模板不支持收藏/取消收藏
                    if (CollectionUtils.isNotEmpty(promptTemplateListData) && PromptScopeType.MINE.getType().equalsIgnoreCase(selectedPromptScope)) {
                        promptTemplateListData.forEach(t -> t.setForbiddenFavorite(true));
                    }
                }
            }

            ArrayList<PromptTemplate> finalPromptTemplateListData = promptTemplateListData;
            SwingUtilities.invokeLater(() -> {
                this.stopLoading();

                // 清空模板列表窗口
                promptListPanel.removeAll();
                for (final PromptTemplate template : finalPromptTemplateListData) {
                    promptListPanel.add(new CodeChatPromptTemplateContentPanel(project, codeChatMainPanel, this, template));
                }

                //刷新分页信息
                refreshPageInfo();

                // 刷新UI
                promptListPanel.revalidate();
                promptListPanel.repaint();
                pageText.revalidate();
                pageText.repaint();
            });
        });
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        if (currentPromptListScrollValue > 0) {
            g.setColor(JBColor.border());
            int y = promptListScrollPane.getY() - 1;
            g.drawLine(0, y, getWidth(), y);
        }
    }

    class PromptListAdjustmentListener implements AdjustmentListener {

        @Override
        public void adjustmentValueChanged(AdjustmentEvent e) {
            JScrollBar source = (JScrollBar) e.getSource();
            if (!source.getValueIsAdjusting()) {
                currentPromptListScrollValue = source.getValue();
                promptListScrollPane.getVerticalScrollBar().setValue(source.getValue());
            }

            SwingUtilities.invokeLater(() -> {
                int value = e.getValue();// 获取滚动条值
                if (currentPromptListScrollValue == 0 && value > 0 || currentPromptListScrollValue > 0 && value == 0) {
                    currentPromptListScrollValue = value;
                    promptListScrollPane.getVerticalScrollBar().setValue(source.getValue());
                    repaint();//重新渲染聊天窗口
                } else {
                    currentPromptListScrollValue = value;
                    promptListScrollPane.getVerticalScrollBar().setValue(source.getValue());
                }
            });
        }
    }
}
