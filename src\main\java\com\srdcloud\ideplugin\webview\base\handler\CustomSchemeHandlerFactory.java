package com.srdcloud.ideplugin.webview.base.handler;

import org.cef.browser.CefBrowser;
import org.cef.browser.CefFrame;
import org.cef.callback.CefSchemeHandlerFactory;
import org.cef.handler.CefResourceHandler;
import org.cef.network.CefRequest;

public class CustomSchemeHandlerFactory implements CefSchemeHandlerFactory {

    @Override
    public CefResourceHandler create(<PERSON>f<PERSON><PERSON><PERSON> cefBrowser, CefFrame cefFrame, String s, CefRequest cefRequest){
        return new CustomResourceHandler();
    }
}
