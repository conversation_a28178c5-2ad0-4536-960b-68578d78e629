package com.srdcloud.ideplugin.general.utils;

import com.vladsch.flexmark.ast.FencedCodeBlock;
import com.vladsch.flexmark.html.HtmlWriter;
import com.vladsch.flexmark.html.renderer.NodeRenderer;
import com.vladsch.flexmark.html.renderer.NodeRendererContext;
import com.vladsch.flexmark.html.renderer.NodeRendererFactory;
import com.vladsch.flexmark.html.renderer.NodeRenderingHandler;
import com.vladsch.flexmark.util.data.DataHolder;
import org.jetbrains.annotations.NotNull;

import java.util.Set;

/**
 * md转html 代码块渲染
 */
public class CodeBlockNodeRenderer implements NodeRenderer {

  @Override
  public Set<NodeRenderingHandler<?>> getNodeRenderingHandlers() {
    return Set.of(new NodeRenderingHandler<>(FencedCodeBlock.class, this::render));
  }

  private void render(FencedCodeBlock node, NodeRendererContext context, HtmlWriter html) {
    var code = String.join("", node.getContentLines());

    context.delegateRender();
  }

  public static class Factory implements NodeRendererFactory {

    @NotNull
    @Override
    public NodeRenderer apply(@NotNull DataHolder options) {
      return new CodeBlockNodeRenderer();
    }
  }
}
