package com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc;

import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc.domain.RelativeCodeObject;
import com.srdcloud.ideplugin.webview.treesitter.WebviewTreeSitter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2024/12/5
 * 跨文件关联处理器
 * 1. 针对文件级别进行抽象语法树解析，解析文件内所有该语言的主要语法对象，如java的class等，并将结果存入全局缓存索引中
 * 2. 针对代码文件中光标位置进行语法树遍历，查找并返回光标所在位置的所有跨文件关联对象信息
 */
public class RlccHandler {

    private static final Logger logger = LoggerFactory.getLogger(RlccHandler.class);

    private final Project myProject;

    public RlccHandler(Project project) {
        this.myProject = project;
    }

    /**
     * 建立go mod路径映射
     * 废弃：存在插件本地
     */
    @Deprecated
    public void buildGoModMapping(String moduleName, String moduleDirPath) {
        WebviewTreeSitter.getInstance(myProject).sendGoModuleMappingMessageToWebview(moduleName, moduleDirPath);
    }

    /**
     * 解析指定代码文件中的代码对象信息，并存入全局缓存索引中
     */
    public void ParseFile(String filePath, String codeFileContent, String languageExt) {
        WebviewTreeSitter.getInstance(myProject).ParseFile(filePath, codeFileContent, languageExt);
    }


    /**
     * 按指定的行列号，所搜当前位置代码在整个代码项目中的相关代码对象
     *
     * @param codeFilePath    当前代码文件在代码项目中的路径
     * @param codeFileContent 当前最新代码文件内容
     * @return 搜索到的关联代码对象
     */
    public RelativeCodeObject FindRelativeObject(String codeFilePath, String codeFileContent,
                                                 int row, int column, String languageExt, String goModMapJson) {
        return WebviewTreeSitter.getInstance(myProject).findRelativeObject(codeFilePath, codeFileContent, row, column, languageExt,goModMapJson);
    }


}
