package com.srdcloud.ideplugin.service;

import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.general.enums.PromptOperationType;
import com.srdcloud.ideplugin.general.utils.CodeUtil;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import com.srdcloud.ideplugin.general.utils.GitUtil;
import com.srdcloud.ideplugin.general.utils.IdeUtil;
import com.srdcloud.ideplugin.remote.PromptManageCommHandler;
import com.srdcloud.ideplugin.remote.domain.ApiResponse;
import com.srdcloud.ideplugin.service.domain.template.PromptTemplate;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/4/25
 * @desc 用户行为埋点上报服务
 */
public class UserActivityReportService {
    private static final Logger logger = LoggerFactory.getLogger(UserActivityReportService.class);

    // 异步上报：1次尝试原则，线程满则丢弃
    private static final ThreadPoolExecutor ONE_TRY_TASK_SUBMIT_POOL = new ThreadPoolExecutor(1, 2, 10L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.AbortPolicy());

    /**
     * 模板操作行为上报(异步)
     */
    @Deprecated
    public static void promptOpsReport(final PromptTemplate template, final PromptOperationType operationType) {
        try {
            ONE_TRY_TASK_SUBMIT_POOL.execute(() -> {
                if (Objects.nonNull(template.getId())) {
                    PromptManageCommHandler.promptOps(template.getId(), operationType.getType(), IdeUtil.getProjectName(IdeUtil.findCurrentProject()), GitUtil.getGitRepositoryUrl(IdeUtil.findCurrentProject()));
                }
            });
        } catch (final Exception e) {
            // 上报仅实现一次尝试原则，不重试，允许丢失
            logger.error("[cf] promptOpsReport error:{}", e.getMessage());
            e.printStackTrace();
        }
    }


    /**
     * 代码相关行为上报（异步）
     * code：纯代码内容
     * firstLineExclude：第一行中需要剔除不纳入计算的前缀输入部分
     * isAuto：是否为自动补全
     * latency：延迟时间（毫秒）
     */
    public static void codeActivityReport(final String activityType, Project project, final String code, final String firstLineExclude, final Boolean isAuto, final Long latency) {
        // 根据传入的project优先，否则使用当前project项目
        final Project currentProject = project == null ? IdeUtil.findCurrentProject() : project;

        try {
            ONE_TRY_TASK_SUBMIT_POOL.execute(() -> {
                Double lines = null;
                Integer count = null;
                // 传入代码非空，则上报代码行数
                if (StringUtils.isNotBlank(code)) {
                    lines = CodeUtil.calGenCodeLines(code, firstLineExclude);
                }
                if (Objects.isNull(lines)) {
                    logger.debug("[cf] codeActivityReport skip,code is null.");
                    return;
                }
                // 有代码才上报行数，并且次数+1
                count = 1;
                DebugLogUtil.info("[cf] codeActivityReport type:" +activityType);
                DebugLogUtil.info("[cf] codeActivityReport count:" +lines);
                final ApiResponse response = CodeChatRequestSender.SendUserActivityReport(activityType, lines, count, IdeUtil.getProjectName(currentProject), GitUtil.getGitRepositoryUrl(currentProject), GitUtil.getGitUrls(currentProject), isAuto, latency);
                if (response.getRtnCode() != RtnCode.SUCCESS) {
                    logger.error("[cf] codeActivityReport fail,rtnCode:<{}>,msg:<{}>", response.getRtnCode(), response.getMessage());
                }else {
                    logger.info("[cf] codeActivityReport success");
                }
            });
        } catch (final Exception e) {
            // 埋点上报仅实现一次尝试原则，不重试，允许丢失
            logger.error("[cf] codeActivityReport error:{}", e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 代码相关行为上报（异步）- 兼容旧接口
     * code：纯代码内容
     * firstLineExclude：第一行中需要剔除不纳入计算的前缀输入部分
     */
    public static void codeActivityReport(final String activityType, Project project, final String code, final String firstLineExclude) {
        codeActivityReport(activityType, project, code, firstLineExclude, null, null);
    }
}
