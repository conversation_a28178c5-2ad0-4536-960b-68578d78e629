package com.srdcloud.ideplugin.ui

import StrictThrottler
import com.intellij.ide.plugins.PluginManagerCore
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.ModalityState
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.extensions.PluginId
import com.intellij.openapi.fileChooser.FileChooser
import com.intellij.openapi.fileChooser.FileChooserDescriptor
import com.intellij.openapi.keymap.impl.ui.KeymapPanel
import com.intellij.openapi.options.ShowSettingsUtil
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.ComboBox
import com.intellij.openapi.util.text.StringUtil
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.ui.IdeBorderFactory
import com.intellij.ui.JBColor
import com.intellij.ui.TableUtil
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBPasswordField
import com.intellij.ui.components.JBScrollPane
import com.intellij.ui.table.JBTable
import com.intellij.uiDesigner.core.Spacer
import com.intellij.util.ui.FormBuilder
import com.intellij.util.ui.JBUI
import com.intellij.util.ui.UIUtil
import com.srdcloud.ideplugin.general.constants.Constants
import com.srdcloud.ideplugin.general.constants.RtnCode
import com.srdcloud.ideplugin.general.utils.ChatColorUtil.SUCCESS_COLOR
import com.srdcloud.ideplugin.general.utils.ConnectStatusForSecUtil
import com.srdcloud.ideplugin.general.utils.pluginSettings
import com.srdcloud.ideplugin.login.LoginStatus
import com.srdcloud.ideplugin.login.LoginUtils
import com.srdcloud.ideplugin.login.SecIdeaSettingsListener
import com.srdcloud.ideplugin.service.LoginService
import com.srdcloud.ideplugin.service.SecIdeaApplicationSettingsStateService
import com.srdcloud.ideplugin.service.UpdateService
import com.srdcloud.ideplugin.service.VersionCheckResponse
import com.srdcloud.ideplugin.settings.KeymapSettings
import com.srdcloud.ideplugin.agent.AgentManager
import com.srdcloud.ideplugin.general.constants.AgentNameConstant
import gnu.trove.TIntArrayList
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.awt.*
import java.awt.event.ItemEvent
import java.io.File
import java.util.Objects.nonNull
import javax.swing.*
import javax.swing.border.LineBorder
import javax.swing.event.DocumentEvent
import javax.swing.event.DocumentListener
import javax.swing.table.AbstractTableModel
import javax.swing.table.TableCellRenderer
import com.intellij.uiDesigner.core.GridConstraints as UIGridConstraints
import com.intellij.uiDesigner.core.GridLayoutManager as UIGridLayoutManager
import com.srdcloud.ideplugin.settings.SecIdeaProjectSettingsConfigurable

class SecIdeaSettingsDialog(
    private val project: Project,
    applicationSettings: SecIdeaApplicationSettingsStateService
) {
    private fun FormBuilder.addCopyableTooltip(text: String): FormBuilder {
        return this.addComponentToRightColumn(
            JBLabel("<html>例如：我是一名软件开发者，当我问安全问题时，如果回复内容包含代码，请解释这些代码；<br/>但如果仅需要提供功能代码时，只提供代码。</html>")
                .apply {
                    font = JBUI.Fonts.label().deriveFont(Font.ITALIC)
                    foreground = UIUtil.getContextHelpForeground()
                }
        )
    }

    private val rootPanel = object : JPanel(), Disposable {
        override fun dispose() = Unit
    }

    val connectionLabel = JLabel()
    private val connectionTextButton = JButton("测试与平台的连接状态")
    private val loginTextButton = JButton()
    private val userNameText = JLabel()
    private val selectionSavePathButton = JButton("选择存放位置")
    private val newPackageSaveDirTextField = JTextField()

    private val addressTextField = JTextField()
    private val myModel: MyModel = MyModel("")
    private val myTable = JBTable(myModel)
    private val secretKeyTextField = JBPasswordField()

    private val logger = Logger.getInstance(this::class.java)
    var isTableModify = false

    // 新增的组件
    private val completionModeButtonGroup = ButtonGroup()
    private val precisionFirstRadioButton = JRadioButton("精准优先")
    private val balancedModeRadioButton = JRadioButton("平衡模式")
    private val speedFirstRadioButton = JRadioButton("速度优先")
    private val languageTable = JTable()
    private val customInstructionTextArea = JTextArea()
    private val keymapStyleDefaultRadioButton = JRadioButton("默认")
    private val keymapStyleCustomRadioButton = JRadioButton("自定义...")
    private val keymapStyleButtonGroup = ButtonGroup()

    // Add new UI components
    private val autoDownloadCheckBox = JCheckBox("自动下载新版本")
    private val notificationCheckBox = JCheckBox("更新提醒")
    private val notificationTimeComboBox = ComboBox(arrayOf("1h", "4h", "12h", "24h"))
    private val versionLabel = JLabel()  // 用于显示当前版本号
    private val checkUpdateButton = JButton("检查更新")

    // 添加 versionPanel 作为类属性
    private val versionPanel = JPanel(FlowLayout(FlowLayout.LEFT, 0, 0))
    var preStoreAddress = false

    init {

        //初始化LoginUtils
        LoginUtils.initProject(project)
        initializeUiComponents()
        bindVersionManagementEvents()

        connectionTextButton.addActionListener {
            connectionLabel.text = "正在检测..."
            connectionLabel.foreground = Color.GRAY

            if (applicationSettings.address != addressTextField.text) {
                preStoreAddress = true
            }

            // 保存服务器地址：
            applicationSettings.address = addressTextField.text

            ApplicationManager.getApplication().invokeLater {
                try {
                    checkConnectionStatus()
                } catch (e: Exception) {
                    logger.error("加载设置失败", e)
                }
            }
        }
        LoginUtils.updateLoginButton(loginTextButton)
        LoginUtils.updateUserName(userNameText)
        loginTextButton.addActionListener {
            StrictThrottler.throttle(key = "login", runOnEDT = true, task = {
                if (applicationSettings.address != addressTextField.text) {
                    preStoreAddress = true
                }
                applicationSettings.address = addressTextField.text
                val loginStatus = LoginUtils.getLoginStatus()
                if (loginStatus == LoginStatus.NOT_LOGGED_IN) {
                    try {
                        LoginService.Login()
                    } catch (e: Exception) {
                        logger.error("登录失败", e)
                    }
                } else {
                    loginTextButton.text = "退登中"
                    try {
                        LoginService.logout(RtnCode.LOGOUT)
                    } catch (e: Exception) {
                        logger.error("登录失败", e)
                    }
                }
            })
        }

        project.messageBus.connect()
            .subscribe(SecIdeaSettingsListener.SECIDEA_SETTINGS_TOPIC, object : SecIdeaSettingsListener {
                override fun settingsChanged() {
                    SwingUtilities.invokeLater {
                        LoginUtils.updateLoginButton(loginTextButton)
                        LoginUtils.updateUserName(userNameText)
                        addressTextField.text = applicationSettings.address
                        if (LoginUtils.getLoginStatusForC10() == Constants.LoginStatus_OK) {
                            LoginService.onLoginEvent(Constants.LoginStatus_OK, RtnCode.SUCCESS)
                        } else {
                            LoginService.onLoginEvent(Constants.LoginStatus_NOK, RtnCode.LOGOUT)
                        }
                    }
                }
            })

        if (nonNull(applicationSettings)) {
            addressTextField.text = applicationSettings.address
            secretKeyTextField.text = applicationSettings.secretKey
            isTableModify = false
            newPackageSaveDirTextField.text = applicationSettings.newPackageSaveDir
            autoDownloadCheckBox.isSelected = applicationSettings.isAutoDownloadNewPackage
            notificationCheckBox.isSelected = applicationSettings.hasUpdateTip
            notificationTimeComboBox.item = applicationSettings.updateTipTimeInterval
        }
        // 设置补全模式的默认值
        setCompletionMode(applicationSettings.completionMode ?: "精准优先")
        // 设置补全模式的默认值
        setShortcutMode(applicationSettings.shortcutMode ?: "默认")
    }

    fun getRootPanel(): JComponent = rootPanel

    private fun initializeUiComponents() {
        /** -------layout-------- **/
        rootPanel.layout = UIGridLayoutManager(18, 1, Insets(0, 0, 0, 0), -1, -1)

        /** -------通用设置------- **/
        initializeGeneralSettingsPanel()

        /** ------ 版本信息 -------**/
        initializeVersionManagementPanel()

        /** -------高级安全配置------- **/
        initializeAdvancedSecurityPanel()

        /** -------补全模式------- **/
        initializeCompletionModePanel()

        /** -------补全快捷键配置------- **/
        initializeKeymapStylePanel()

        /** -------禁用补全语言------- **/
        initializedDisabledLanguagesPanel()

        /** -------自定义指令------- **/
        initializedCustomInstructionPanel()
    }

    private fun initializedCustomInstructionPanel() {
        val customInstructionPanel = JPanel(UIGridLayoutManager(4, 1, Insets(0, 0, 0, 0), -1, -1))
        customInstructionPanel.border = IdeBorderFactory.createTitledBorder("自定义指令")

        rootPanel.add(
            customInstructionPanel,
            baseGridConstraints(
                row = 12,
                anchor = UIGridConstraints.ANCHOR_NORTHWEST,
                fill = UIGridConstraints.FILL_BOTH,
                hSizePolicy = UIGridConstraints.SIZEPOLICY_CAN_SHRINK or UIGridConstraints.SIZEPOLICY_CAN_GROW,
                vSizePolicy = UIGridConstraints.SIZEPOLICY_WANT_GROW,
                indent = 0
            )
        )

        val instructionMessage = JBLabel("需要海云智码了解哪些信息或如何进行回答：")
        instructionMessage.font = instructionMessage.font.deriveFont(Font.ITALIC)
        instructionMessage.foreground = Color.GRAY
        customInstructionPanel.add(
            instructionMessage,
            baseGridConstraints(row = 0, column = 0, anchor = UIGridConstraints.ANCHOR_WEST)
        )

        val instructionCountLabel = JBLabel("${pluginSettings().customInstruction.length}/1500")
        val instructionTextArea = JTextArea().apply {
            rows = 5
            columns = 5
            lineWrap = true
            wrapStyleWord = true
            text = pluginSettings().customInstruction
            border = LineBorder(JBColor.LIGHT_GRAY, 1)
            preferredSize = Dimension(600, 200)
        }
        instructionTextArea.document.addDocumentListener(object : DocumentListener {
            override fun insertUpdate(e: DocumentEvent?) {
                enforceCharacterLimit()
                updateCharacterCount()
            }

            override fun removeUpdate(e: DocumentEvent?) {
                enforceCharacterLimit()
                updateCharacterCount()
            }

            override fun changedUpdate(e: DocumentEvent?) {
            }

            private fun enforceCharacterLimit() {
                if (instructionTextArea.text.length > 1500) {
                    // 不能在document listener内部直接修改值
                    object : Thread() {
                        override fun run() {
                            instructionTextArea.text = instructionTextArea.text.substring(0, 1500)
                        }
                    }.start()
                }
                pluginSettings().customInstruction = instructionTextArea.text
            }

            private fun updateCharacterCount() {
                instructionCountLabel.text = "${instructionTextArea.text.length}/1500"
            }
        })
        customInstructionPanel.add(
            instructionTextArea,
            baseGridConstraints(row = 1, column = 0, anchor = UIGridConstraints.ANCHOR_WEST)
        )
        customInstructionPanel.add(
            instructionCountLabel,
            baseGridConstraints(row = 2, column = 0, anchor = UIGridConstraints.ANCHOR_WEST)
        )

        val instructionHelpLabel =
            JLabel("<html>例如：我是一名软件开发者，当我问安全问题时，如果回复内容包含代码，请解释这些代码；<br/>但如果仅需要提供功能代码时，只提供代码。</html>")
        instructionHelpLabel.font = instructionHelpLabel.font.deriveFont(Font.ITALIC)
        instructionHelpLabel.foreground = Color.GRAY
        customInstructionPanel.add(
            instructionHelpLabel,
            baseGridConstraints(row = 3, column = 0, anchor = UIGridConstraints.ANCHOR_WEST)
        )
    }

    private fun initializedDisabledLanguagesPanel() {
        val disabledLanguagesPanel = JPanel(UIGridLayoutManager(2, 1, Insets(0, 0, 0, 0), -1, -1))
        disabledLanguagesPanel.border = IdeBorderFactory.createTitledBorder("禁用补全语言")

        rootPanel.add(
            disabledLanguagesPanel,
            baseGridConstraints(
                row = 11,
                anchor = UIGridConstraints.ANCHOR_NORTHWEST,
                fill = UIGridConstraints.FILL_BOTH,
                hSizePolicy = UIGridConstraints.SIZEPOLICY_CAN_SHRINK or UIGridConstraints.SIZEPOLICY_CAN_GROW,
                vSizePolicy = UIGridConstraints.SIZEPOLICY_FIXED,
                indent = 0
            )
        )

        val languageTableModel = object : AbstractTableModel() {
            val columns = arrayOf("语言", "尾缀名", "是否用")
            val data: List<SecIdeaApplicationSettingsStateService.LanguageInfo> =
                SecIdeaApplicationSettingsStateService.languageMap.values.map { it.copy() }

            override fun getRowCount() = data.size
            override fun getColumnCount() = columns.size
            override fun getColumnName(column: Int) = columns[column]
            override fun getValueAt(rowIndex: Int, columnIndex: Int): Any = when (columnIndex) {
                0 -> data[rowIndex].name
                1 -> data[rowIndex].extension
                2 -> data[rowIndex].isDisabled
                else -> throw IndexOutOfBoundsException("Invalid column index")
            }

            override fun getColumnClass(columnIndex: Int) = when (columnIndex) {
                2 -> Boolean::class.java
                else -> String::class.java
            }

            override fun isCellEditable(rowIndex: Int, columnIndex: Int) = columnIndex == 2
            override fun setValueAt(aValue: Any, rowIndex: Int, columnIndex: Int) {
                if (columnIndex == 2 && aValue is Boolean) {
                    data[rowIndex].isDisabled = aValue
                    fireTableCellUpdated(rowIndex, columnIndex)
                }
            }
        }

        languageTable.model = languageTableModel
        languageTable.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION)
        val scrollPane = JBScrollPane(languageTable)
        disabledLanguagesPanel.add(scrollPane, baseGridConstraints(row = 0, fill = UIGridConstraints.FILL_BOTH))

        // 设置复选框渲染器和编辑器
        with(languageTable.columnModel.getColumn(2)) {
            cellRenderer = TableCellRenderer { table, value, isSelected, hasFocus, row, column ->
                JCheckBox().apply {
                    this.isSelected = value as Boolean
                    horizontalAlignment = JCheckBox.CENTER
                    background = if (isSelected) table.selectionBackground else table.background
                }
            }
            cellEditor = DefaultCellEditor(JCheckBox().apply {
                horizontalAlignment = JCheckBox.CENTER
            })
        }
    }

    private fun initializeKeymapStylePanel() {
        val keymapPanel = JPanel(UIGridLayoutManager(2, 2, Insets(0, 0, 0, 0), -1, -1))
        keymapPanel.border = IdeBorderFactory.createTitledBorder("补全快捷键配置")

        rootPanel.add(
            keymapPanel,
            baseGridConstraints(
                row = 9,
                anchor = UIGridConstraints.ANCHOR_NORTHWEST,
                fill = UIGridConstraints.FILL_HORIZONTAL,
                hSizePolicy = UIGridConstraints.SIZEPOLICY_CAN_SHRINK or UIGridConstraints.SIZEPOLICY_CAN_GROW,
                vSizePolicy = UIGridConstraints.SIZEPOLICY_FIXED,
                indent = 0
            )
        )

        keymapStyleButtonGroup.add(keymapStyleDefaultRadioButton)
        keymapStyleButtonGroup.add(keymapStyleCustomRadioButton)

        keymapPanel.add(keymapStyleDefaultRadioButton, baseGridConstraintsAnchorWest(row = 0, column = 0))

        val keymapHelpLabel =
            JLabel("默认使用 Ctrl+\\ 键显示补全信息，使用 Tab 键接受完整补全，使用 Ctrl+Alt+/ 键接受下一行。")
        keymapHelpLabel.font = keymapHelpLabel.font.deriveFont(Font.ITALIC)
        keymapHelpLabel.foreground = Color.GRAY
        keymapPanel.add(
            keymapHelpLabel,
            baseGridConstraints(row = 0, column = 1, anchor = UIGridConstraints.ANCHOR_WEST)
        )

        keymapPanel.add(keymapStyleCustomRadioButton, baseGridConstraintsAnchorWest(row = 1, column = 0))

        // 设置默认选项
        keymapStyleDefaultRadioButton.isSelected = true

        // 恢复自定义功能
        keymapStyleCustomRadioButton.addActionListener {
            ShowSettingsUtil.getInstance().showSettingsDialog(null, KeymapPanel::class.java) { panel ->
                CoroutineScope(Dispatchers.IO).launch {
                    delay(500) // 等待 KeymapPanel 准备就绪
                    invokeLater(ModalityState.stateForComponent(panel)) {
                        panel.showOption("补全")
                    }
                }
            }
        }
    }

    private fun initializeCompletionModePanel() {
        val completionModePanel = JPanel(UIGridLayoutManager(1, 4, Insets(0, 0, 0, 0), -1, -1))
        completionModePanel.border = IdeBorderFactory.createTitledBorder("补全模式")

        rootPanel.add(
            completionModePanel,
            baseGridConstraints(
                row = 8,
                anchor = UIGridConstraints.ANCHOR_NORTHWEST,
                fill = UIGridConstraints.FILL_HORIZONTAL,
                hSizePolicy = UIGridConstraints.SIZEPOLICY_CAN_SHRINK or UIGridConstraints.SIZEPOLICY_CAN_GROW,
                vSizePolicy = UIGridConstraints.SIZEPOLICY_FIXED,
                indent = 0
            )
        )

        completionModeButtonGroup.add(precisionFirstRadioButton)
        completionModeButtonGroup.add(balancedModeRadioButton)
        completionModeButtonGroup.add(speedFirstRadioButton)

        completionModePanel.add(precisionFirstRadioButton, baseGridConstraintsAnchorWest(row = 0, column = 0))
        completionModePanel.add(balancedModeRadioButton, baseGridConstraintsAnchorWest(row = 0, column = 1))
        completionModePanel.add(speedFirstRadioButton, baseGridConstraintsAnchorWest(row = 0, column = 2))

        // 使用Spacer来保证靠左对齐
        completionModePanel.add(
            Spacer(), baseGridConstraints(
                row = 0,
                column = 3,
                fill = UIGridConstraints.FILL_HORIZONTAL,
                hSizePolicy = UIGridConstraints.SIZEPOLICY_WANT_GROW
            )
        )
    }

    private fun initializeVersionManagementPanel() {
        val versionManagementPanel = JPanel(UIGridLayoutManager(4, 3, Insets(0, 0, 0, 0), -1, -1))
        versionManagementPanel.border = IdeBorderFactory.createTitledBorder("版本信息")

        // Version ID row - 使用类属性 versionPanel
        versionPanel.border = JBUI.Borders.empty()  // 移除面板的边距

        val versionIdLabel = JLabel("插件版本信息")
        versionPanel.add(versionIdLabel)

        // 给版本号标签添加右边距，使其与检查更新按钮有间距
        versionLabel.border = JBUI.Borders.empty(0, 20)
        versionPanel.add(versionLabel)
        versionPanel.add(checkUpdateButton)

        versionManagementPanel.add(
            versionPanel, baseGridConstraints(
                row = 0,
                column = 0,
                colSpan = 3,
                fill = UIGridConstraints.FILL_HORIZONTAL
            )
        )

        // Save path row
        val savePathLabel = JLabel("插件更新包存放目录")
        versionManagementPanel.add(savePathLabel, baseGridConstraintsAnchorWest(row = 1, column = 0))
        versionManagementPanel.add(
            newPackageSaveDirTextField,
            baseGridConstraints(row = 1, column = 1, fill = UIGridConstraints.FILL_HORIZONTAL)
        )
        versionManagementPanel.add(selectionSavePathButton, baseGridConstraintsAnchorWest(row = 1, column = 2))

        // Auto download and notification settings in one row using FlowLayout
        val downloadSettingsPanel = JPanel(FlowLayout(FlowLayout.LEFT, 0, 0))
        downloadSettingsPanel.add(autoDownloadCheckBox, baseGridConstraintsAnchorWest(row = 0, column = 0))
        val downloadHelpTextField = JLabel("  IDE启动时或者检测更新时，自动下载新版本更新包")
        downloadHelpTextField.foreground = JBColor.GRAY
        downloadSettingsPanel.add(downloadHelpTextField, baseGridConstraintsAnchorWest(row = 0, column = 3))
        versionManagementPanel.add(
            downloadSettingsPanel, baseGridConstraints(
                row = 2,
                column = 0,
                colSpan = 3,
                fill = UIGridConstraints.FILL_HORIZONTAL
            )
        )

        val notificationSettingsPanel = JPanel(FlowLayout(FlowLayout.LEFT, 0, 0))
        notificationSettingsPanel.add(notificationCheckBox, baseGridConstraintsAnchorWest(row = 0, column = 0))
        notificationSettingsPanel.add(notificationTimeComboBox, baseGridConstraintsAnchorWest(row = 0, column = 1))
        val notificationTextField = JLabel("  IDE启动试或者每隔固定时间自动检测是否有新版本")
        notificationTextField.foreground = JBColor.GRAY
        notificationSettingsPanel.add(notificationTextField, baseGridConstraintsAnchorWest(row = 0, column = 2))

        versionManagementPanel.add(
            notificationSettingsPanel, baseGridConstraints(
                row = 3,
                column = 0,
                colSpan = 3,
                fill = UIGridConstraints.FILL_HORIZONTAL
            )
        )

        rootPanel.add(
            versionManagementPanel,
            baseGridConstraints(
                row = 1,
                anchor = UIGridConstraints.ANCHOR_NORTHWEST,
                fill = UIGridConstraints.FILL_HORIZONTAL,
                hSizePolicy = UIGridConstraints.SIZEPOLICY_CAN_SHRINK or UIGridConstraints.SIZEPOLICY_CAN_GROW,
                vSizePolicy = UIGridConstraints.SIZEPOLICY_FIXED,
                indent = 0
            )
        )
    }

    private fun initializeGeneralSettingsPanel() {
        val generalSettingsPanel = JPanel(UIGridLayoutManager(5, 4, Insets(0, 0, 0, 0), -1, -1))
        generalSettingsPanel.border = IdeBorderFactory.createTitledBorder("通用设置")

        rootPanel.add(
            generalSettingsPanel,
            baseGridConstraints(
                row = 0,
                anchor = UIGridConstraints.ANCHOR_NORTHWEST,
                fill = UIGridConstraints.FILL_HORIZONTAL,
                hSizePolicy = UIGridConstraints.SIZEPOLICY_CAN_SHRINK or UIGridConstraints.SIZEPOLICY_CAN_GROW,
                vSizePolicy = UIGridConstraints.SIZEPOLICY_FIXED,
                indent = 0
            )
        )

        val addressLabel = JLabel("服务器地址")
        generalSettingsPanel.add(addressLabel, baseGridConstraintsAnchorWest(row = 0, column = 0))
        generalSettingsPanel.add(
            addressTextField,
            baseGridConstraints(row = 0, column = 1, colSpan = 3, fill = UIGridConstraints.FILL_HORIZONTAL)
        )

        val addressHelpLabel = JLabel("协议://IP地址:端口/项目名/  如 http://***********:3000/oscap/ ")
        addressHelpLabel.font = addressHelpLabel.font.deriveFont(Font.ITALIC)
        addressHelpLabel.foreground = Color.GRAY
        generalSettingsPanel.add(
            addressHelpLabel,
            baseGridConstraints(row = 1, column = 1, colSpan = 3, anchor = UIGridConstraints.ANCHOR_WEST)
        )

        generalSettingsPanel.add(connectionTextButton, baseGridConstraintsAnchorWest(row = 2, column = 1))
        generalSettingsPanel.add(
            connectionLabel,
            baseGridConstraints(row = 2, column = 2, colSpan = 2, fill = UIGridConstraints.FILL_HORIZONTAL)
        )

        // 账号登录相关
        val userNameLabel = JLabel("账号")
        userNameText.foreground = JBColor.GRAY
        generalSettingsPanel.add(
            userNameLabel,
            baseGridConstraints(
                row = 3,
                column = 0,
                anchor = UIGridConstraints.ANCHOR_CENTER,
                hSizePolicy = UIGridConstraints.SIZEPOLICY_FIXED
            )
        )
        generalSettingsPanel.add(
            userNameText,
            baseGridConstraints(
                row = 3,
                column = 1,
                anchor = UIGridConstraints.ANCHOR_CENTER,
                hSizePolicy = UIGridConstraints.SIZEPOLICY_FIXED
            )
        )
        generalSettingsPanel.add(loginTextButton, baseGridConstraintsAnchorWest(row = 3, column = 2))

    }

    private fun initializeAdvancedSecurityPanel() {
        val advancedSecurityPanel = JPanel(UIGridLayoutManager(3, 3, Insets(0, 0, 0, 0), -1, -1))
        advancedSecurityPanel.border = IdeBorderFactory.createTitledBorder("高级安全配置")

        val secretKeyLabel = JLabel("密钥配置")
        advancedSecurityPanel.add(secretKeyLabel, baseGridConstraintsAnchorWest(row = 0, column = 0))
        advancedSecurityPanel.add(
            secretKeyTextField,
            baseGridConstraints(row = 0, column = 1, colSpan = 2, fill = UIGridConstraints.FILL_HORIZONTAL)
        )

        val secretKeyHelpLabel = JLabel("此密钥为平台随机生成，需要钥请向安全管理员索取 ")
        secretKeyHelpLabel.font = secretKeyHelpLabel.font.deriveFont(Font.ITALIC)
        secretKeyHelpLabel.foreground = Color.GRAY
        advancedSecurityPanel.add(
            secretKeyHelpLabel,
            baseGridConstraints(row = 1, column = 1, colSpan = 2, anchor = UIGridConstraints.ANCHOR_WEST)
        )

        rootPanel.add(
            advancedSecurityPanel,
            baseGridConstraints(
                row = 2,
                anchor = UIGridConstraints.ANCHOR_NORTHWEST,
                fill = UIGridConstraints.FILL_HORIZONTAL,
                hSizePolicy = UIGridConstraints.SIZEPOLICY_CAN_SHRINK or UIGridConstraints.SIZEPOLICY_CAN_GROW,
                vSizePolicy = UIGridConstraints.SIZEPOLICY_FIXED,
                indent = 0
            )
        )
    }

    private fun baseGridConstraints(
        row: Int,
        column: Int = 0,
        colSpan: Int = 1,
        rowSpan: Int = 1,
        anchor: Int = UIGridConstraints.ANCHOR_CENTER,
        fill: Int = UIGridConstraints.FILL_NONE,
        hSizePolicy: Int = UIGridConstraints.SIZEPOLICY_CAN_GROW or UIGridConstraints.SIZEPOLICY_CAN_SHRINK,
        vSizePolicy: Int = UIGridConstraints.SIZEPOLICY_CAN_GROW or UIGridConstraints.SIZEPOLICY_CAN_SHRINK,
        indent: Int = 0
    ): UIGridConstraints {
        return UIGridConstraints(
            row, column, rowSpan, colSpan,
            anchor, fill,
            hSizePolicy, vSizePolicy,
            null, null, null, indent
        )
    }

    private fun baseGridConstraintsAnchorWest(
        row: Int,
        column: Int = 0,
        colSpan: Int = 1,
        rowSpan: Int = 1,
        indent: Int = 0,
        hSizePolicy: Int = UIGridConstraints.SIZEPOLICY_FIXED
    ): UIGridConstraints {
        return baseGridConstraints(
            row = row,
            column = column,
            colSpan = colSpan,
            rowSpan = rowSpan,
            anchor = UIGridConstraints.ANCHOR_WEST,
            hSizePolicy = hSizePolicy,
            indent = indent
        )
    }

    private fun panelGridConstraints(
        row: Int,
        column: Int = 0,
        colSpan: Int = 1,
        rowSpan: Int = 1,
        fill: Int = UIGridConstraints.FILL_VERTICAL,
        anchor: Int = UIGridConstraints.ANCHOR_CENTER
    ): UIGridConstraints {
        return UIGridConstraints(
            row, column, rowSpan, colSpan,
            anchor, fill,
            UIGridConstraints.SIZEPOLICY_WANT_GROW, UIGridConstraints.SIZEPOLICY_WANT_GROW,
            null, null, null, 0
        )
    }

    private fun doAddAction() {
        val descriptor = FileChooserDescriptor(true, true, true, true, true, false)
        val files = FileChooser.chooseFiles(descriptor, project, null)
        if (files.first() == null) return
        doAddFile(files.first())
    }

    private fun doAddFile(file: VirtualFile) {
        val chosen: Set<VirtualFile> = HashSet(listOf(file))
        if (chosen.isEmpty()) return
        val allData = myModel.data
        for (data in allData) {
            if (data == file.path) return
        }
        myModel.data.add(file.path)
        myModel.fireTableDataChanged()
        isTableModify = true
        val rowList = TIntArrayList()
        var i = 0
        val size: Int = myModel.data.size
        while (i < size) {
            if (chosen.contains(myModel.data[i])) rowList.add(i)
            i++
        }
        selectRow(rowList.toNativeArray(), true)
    }

    private fun selectRow(rows: IntArray, convertModelRowsToView: Boolean) {
        val viewRows = if (convertModelRowsToView) IntArray(rows.size) else rows
        if (convertModelRowsToView) {
            for (i in rows.indices) {
                viewRows[i] = myTable.convertRowIndexToView(rows[i])
            }
        }
        TableUtil.selectRows(myTable, viewRows)
        TableUtil.scrollSelectionToVisible(myTable)
    }

    private fun doRemoveAction() {
        val row = myTable.selectedRow
        myModel.data.removeAt(row)
        myModel.fireTableDataChanged()
        isTableModify = true
    }

    private class MyModel(names: String) : AbstractTableModel() {
        val columnName: String = names
        var data = mutableListOf<Any>()

        override fun isCellEditable(rowIndex: Int, columnIndex: Int) = true
        override fun getRowCount() = data.size
        override fun getColumnName(column: Int) = columnName
        override fun getColumnCount() = 1
        override fun getValueAt(rowIndex: Int, columnIndex: Int) = data[rowIndex]
        override fun setValueAt(aValue: Any, rowIndex: Int, columnIndex: Int) {
            data[rowIndex] = aValue
            fireTableRowsUpdated(rowIndex, rowIndex)
        }

        override fun fireTableDataChanged() {
            data.sortWith { o1, o2 -> StringUtil.naturalCompare(o1 as String?, o2 as String?) }
            super.fireTableDataChanged()
        }
    }

    private fun checkConnectionStatus() {
        val currentAddress = addressTextField.text
        ConnectStatusForSecUtil.testConnection(currentAddress, this)
        Thread.sleep(1000)
    }

    fun getAddress(): String = addressTextField.text
    fun getSecretKey(): String = secretKeyTextField.text
    fun getNewPackageSaveDir(): String = newPackageSaveDirTextField.text
    fun getIsAutoDownloadNewPackage(): Boolean = autoDownloadCheckBox.isSelected
    fun getHasUpdateTip(): Boolean = notificationCheckBox.isSelected
    fun getUpdateTipTimeInterval(): String = notificationTimeComboBox.item
    fun getFilterFiles() = myModel.data

    // 新增的方法
    fun getCompletionMode(): String = when {
        precisionFirstRadioButton.isSelected -> "精准优先"
        balancedModeRadioButton.isSelected -> "平衡模式"
        speedFirstRadioButton.isSelected -> "速度优先"
        else -> "精准优先" // 默认值
    }

    fun getKeymapStyle(): KeymapSettings.KeymapStyle {
        return when {
            keymapStyleDefaultRadioButton.isSelected -> KeymapSettings.KeymapStyle.DEFAULT
            keymapStyleCustomRadioButton.isSelected -> KeymapSettings.KeymapStyle.CUSTOMIZE
            else -> KeymapSettings.KeymapStyle.DEFAULT // fallback to default if somehow nothing is selected
        }
    }

    fun getShortcutMode(): String = when {
        keymapStyleDefaultRadioButton.isSelected -> "默认"
        keymapStyleCustomRadioButton.isSelected -> "自定义..."
        else -> "默认" // 默认值
    }

    fun getDisabledLanguages(): List<String> {
        val model = languageTable.model as AbstractTableModel
        return (0 until model.rowCount)
            .filter { model.getValueAt(it, 2) as Boolean }
            .map { model.getValueAt(it, 1) as String }
    }

    fun getCustomInstruction(): String = customInstructionTextArea.text

    fun setCompletionMode(mode: String) {
        when (mode) {
            "精准优先" -> precisionFirstRadioButton.isSelected = true
            "平衡模式" -> balancedModeRadioButton.isSelected = true
            "速度优先" -> speedFirstRadioButton.isSelected = true
        }
    }

    fun setShortcutMode(mode: String) {
        when (mode) {
            "默认" -> keymapStyleDefaultRadioButton.isSelected = true
            "自定义..." -> keymapStyleCustomRadioButton.isSelected = true
        }
    }

    fun setDisabledLanguages(languages: List<String>) {
        val model = languageTable.model as AbstractTableModel
        for (i in 0 until model.rowCount) {
            val identifier = model.getValueAt(i, 1) as String
            model.setValueAt(identifier in languages, i, 2)
        }
    }

    fun setCustomInstruction(instruction: String) {
        customInstructionTextArea.text = instruction
    }

    private fun downloadNewVersion(productType: String, updateStatusLabel: JLabel, response: VersionCheckResponse) {
        val saveDir = newPackageSaveDirTextField.text

        if (UpdateService.downloadNewVersion(saveDir, productType)) {
            JOptionPane.showMessageDialog(
                versionPanel,
                "插件更新包已下载到: $saveDir，请手动安装更新包",
                "下载完成",
                JOptionPane.INFORMATION_MESSAGE
            )
            updateStatusLabel.text = "发现新版本 ${response.latestVersion}，已下载至存放目录，请手动安装更新包"
        } else {
            JOptionPane.showMessageDialog(
                versionPanel,
                "插件更新包下载失败",
                "错误",
                JOptionPane.WARNING_MESSAGE
            )
            updateStatusLabel.text = "新版本更新包下载失败"
        }
    }

    // Add methods to handle the new components
    private fun bindVersionManagementEvents() {
        // 初始化时禁用时间选择下拉框
        notificationTimeComboBox.isEnabled = false
        val updateStatusLabel = JLabel()

        // 检查更新按钮事件
        checkUpdateButton.addActionListener {
            updateStatusLabel.border = JBUI.Borders.empty(0, 20)
            versionPanel.add(updateStatusLabel)
            versionPanel.revalidate()
            versionPanel.repaint()

            updateStatusLabel.text = "检查更新中..."
            updateStatusLabel.foreground = Color.GRAY
            checkUpdateButton.isEnabled = false

            ApplicationManager.getApplication().executeOnPooledThread {
                try {
                    // PluginID和puglin.xml中的id保持一致
                    val pluginVersionWithProductType =
                        PluginManagerCore.getPlugin(PluginId.getId(Constants.PLUGIN_ID))?.version
                    val version = pluginVersionWithProductType?.split("-")?.get(0) ?: "0.0.0" // 默认是0.0.0, 如果不存在
                    val productType = pluginVersionWithProductType?.split("-")?.get(1) ?: "c10" // 默认是c10, 如果不存在

                    val response = UpdateService.checkNewVersion(version, productType, addressTextField.text)

                    checkUpdateButton.isEnabled = true

                    when {
                        response.code != 0 -> {
                            updateStatusLabel.text = response.message ?: "检查更新失败"
                            updateStatusLabel.foreground = JBColor.RED
                        }

                        response.hasUpdate -> {
                            updateStatusLabel.text = "发现新版本 ${response.latestVersion}"
                            updateStatusLabel.foreground = JBColor.ORANGE

                            // 如果启用了自动下载, 则直接下载
                            if (autoDownloadCheckBox.isSelected) {
                                updateStatusLabel.text = "发现新版本 ${response.latestVersion}, 正在下载..."
                                updateStatusLabel.foreground = JBColor.ORANGE
                                checkUpdateButton.isEnabled = false
                                downloadNewVersion(productType, updateStatusLabel, response)
                                checkUpdateButton.isEnabled = true
                            } else {
                                updateStatusLabel.text = "发现新版本 ${response.latestVersion}"
                                updateStatusLabel.foreground = JBColor.ORANGE
                                // 如果没有启用自动下载, 则弹出提示
                                val result = JOptionPane.showConfirmDialog(
                                    versionPanel,
                                    "发现新版本 ${response.latestVersion}，是否现在下载?",
                                    "新版本提示",
                                    JOptionPane.YES_NO_OPTION,
                                    JOptionPane.QUESTION_MESSAGE
                                )

                                if (result == JOptionPane.YES_OPTION) {
                                    updateDownloadStatus(
                                        "发现新版本 ${response.latestVersion}, 正在下载...",
                                        updateStatusLabel,
                                        JBColor.ORANGE
                                    )
                                    checkUpdateButton.isEnabled = false
                                    downloadNewVersion(productType, updateStatusLabel, response)
                                    checkUpdateButton.isEnabled = true
                                }
                            }
                        }

                        else -> {
                            updateStatusLabel.text = "已是最新版本"
                            updateStatusLabel.foreground = SUCCESS_COLOR
                        }
                    }
                } catch (e: Exception) {
                    logger.error("检查更新失败", e)
                    ApplicationManager.getApplication().invokeLater {
                        checkUpdateButton.isEnabled = true
                        updateStatusLabel.text = "检查更新失败: ${e.message}"
                        updateStatusLabel.foreground = JBColor.RED
                    }
                }
            }
        }

        selectionSavePathButton.addActionListener {
            val fileChooser = JFileChooser().apply {
                fileSelectionMode = JFileChooser.DIRECTORIES_ONLY
                dialogTitle = "选择更新包存放目录"
                isMultiSelectionEnabled = false
                currentDirectory = File(newPackageSaveDirTextField.text)  // 设置当前目录
            }

            // 在父窗口的中心显示对话框
            val parent = SwingUtilities.getWindowAncestor(rootPanel)
            val result = fileChooser.showDialog(parent, "选择")
            if (result == JFileChooser.APPROVE_OPTION) {
                newPackageSaveDirTextField.text = fileChooser.selectedFile.absolutePath
            }
        }

        notificationCheckBox.addItemListener { e ->
            notificationTimeComboBox.isEnabled = e.stateChange == ItemEvent.SELECTED
        }

        // 置文本框为只读
        newPackageSaveDirTextField.isEditable = false
    }

    private fun updateDownloadStatus(message: String, updateStatusLabel: JLabel, color: JBColor) {
        updateStatusLabel.text = message
        updateStatusLabel.foreground = color
    }
}
