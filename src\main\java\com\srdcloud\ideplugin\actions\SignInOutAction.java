package com.srdcloud.ideplugin.actions;


import com.intellij.openapi.actionSystem.ActionUpdateThread;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.Presentation;
import com.intellij.openapi.project.DumbAware;
import com.srdcloud.ideplugin.common.icons.MyIcons;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.utils.EnvUtil;
import com.srdcloud.ideplugin.general.utils.LocalStorageUtil;
import com.srdcloud.ideplugin.general.utils.UIUtil;
import com.srdcloud.ideplugin.service.LoginService;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;

import static com.srdcloud.ideplugin.general.constants.RtnCode.LOGOUT;

/**
 * <AUTHOR>
 */
public class SignInOutAction extends AnAction implements DumbAware {
    private static final Logger logger = LoggerFactory.getLogger(SignInOutAction.class);
    KeyboardFocusManager keyboardFocusManager;


    public SignInOutAction() {
        keyboardFocusManager = KeyboardFocusManager.getCurrentKeyboardFocusManager();
    }

    @Override
    public void update(@NotNull AnActionEvent e) {
        Presentation presentation = e.getPresentation();
        Icon loginIcon = MyIcons.login;
        Icon logoutIcon = MyIcons.logout;
        if(UIUtil.judgeBackgroudDarkTheme()){
            loginIcon = MyIcons.loginDark;
            logoutIcon = MyIcons.logoutDark;
        }

        if (LoginService.getSecideaLoginStatus() == Constants.LoginStatus_NOK) {
            presentation.setText(EnvUtil.isSec("登录","登录研发云"));
            presentation.setIcon(loginIcon);
        } else {
            String logoutText = EnvUtil.isSec("登出","登录研发云");
            String userName = LocalStorageUtil.getUserAccount();
            if (StringUtils.isNotBlank(userName)) {
                logoutText = logoutText + "(" + userName + ")";
            }
            presentation.setText(logoutText);
            presentation.setIcon(logoutIcon);
        }
        super.update(e);
    }

    @Override
    public void actionPerformed(@NotNull AnActionEvent event) {
        // 如果当前处于未登录状态，发起登录请求
        if (LoginService.getSecideaLoginStatus() == Constants.LoginStatus_NOK) {
            LoginService.Login();
        } else {
            // 如果当前处于登录状态，发起登出请求
            LoginService.logout(LOGOUT);
        }
    }

    @Override
    public @NotNull ActionUpdateThread getActionUpdateThread() {
        return ActionUpdateThread.BGT;
    }
}
