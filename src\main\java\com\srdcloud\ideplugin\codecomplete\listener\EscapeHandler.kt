package com.srdcloud.ideplugin.codecomplete.listener

import com.intellij.openapi.actionSystem.DataContext
import com.intellij.openapi.editor.Caret
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.actionSystem.EditorActionHandler
import com.srdcloud.ideplugin.codecomplete.handle.CompletionContext.Companion.getInlineCompletionContextOrNull
import com.srdcloud.ideplugin.codecomplete.handle.CompletionContext.Companion.resetInlineCompletionContext

/**
 * <AUTHOR>
 * @date 2025/6/6
 * @desc 编辑器esc事件处理
 */
class EscapeHandler(private val originalHandler: EditorActionHandler) : EditorActionHandler() {
    public override fun doExecute(editor: Editor, caret: Caret?, dataContext: DataContext) {
        // 如果编辑器内按esc，则重置补全提示
        editor.resetInlineCompletionContext()

        // 继续走原来的esc事件处理器链条
        if (originalHandler.isEnabled(editor, caret, dataContext)) {
            originalHandler.execute(editor, caret, dataContext)
        }
    }

    override fun isEnabledForCaret(editor: Editor, caret: Caret, dataContext: DataContext): Boolean {
        // 当前具备补全提示上下文，则本处理器在光标处生效
        if (editor.getInlineCompletionContextOrNull() != null) {
            return true
        }

        // 继续走原来的esc事件处理器判断链条
        return originalHandler.isEnabled(editor, caret, dataContext)
    }
}