package com.srdcloud.ideplugin.service.domain.codechat;

public class CodeChatAnswerData {

    private String MessageName;

    private String API_KEY;

    private int RegisterResult;

    private String ReqId;

    private int RetCode;

    private int IsEnd;

    private String Answer;

    private int SeqNo;

    public String getMessageName() {
        return MessageName;
    }

    public void setMessageName(String messageName) {
        MessageName = messageName;
    }

    public String getAPI_KEY() {
        return API_KEY;
    }

    public void setAPI_KEY(String API_KEY) {
        this.API_KEY = API_KEY;
    }

    public int getRegisterResult() {
        return RegisterResult;
    }

    public void setRegisterResult(int registerResult) {
        RegisterResult = registerResult;
    }

    public String getReqId() {
        return ReqId;
    }

    public void setReqId(String reqId) {
        ReqId = reqId;
    }

    public int getRetCode() {
        return RetCode;
    }

    public void setRetCode(int retCode) {
        RetCode = retCode;
    }

    public int getIsEnd() {
        return IsEnd;
    }

    public void setIsEnd(int isEnd) {
        IsEnd = isEnd;
    }

    public String getAnswer() {
        return Answer;
    }

    public void setAnswer(String answer) {
        Answer = answer;
    }

    public int getSeqNo() {
        return SeqNo;
    }

    public void setSeqNo(int seqNo) {
        SeqNo = seqNo;
    }

    @Override
    public String toString() {
        return "CodeAIResponse{" +
                "MessageName='" + MessageName + '\'' +
                ", API_KEY='" + API_KEY + '\'' +
                ", RegisterResult=" + RegisterResult +
                ", ReqId='" + ReqId + '\'' +
                ", RetCode=" + RetCode +
                ", IsEnd=" + IsEnd +
                ", Answer='" + Answer + '\'' +
                '}';
    }
}
