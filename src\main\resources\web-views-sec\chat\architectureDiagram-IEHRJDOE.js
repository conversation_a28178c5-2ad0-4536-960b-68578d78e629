import{ax as me,F as Ze,_ as nt,bq as ke,br as qe,o as Je,p as Qe,s as Ke,g as je,c as _e,b as tr,d as fe,l as Ie,j as er,v as rr,aE as ir,bb as ar,bg as Te,bs as pe}from"./index.js";import{p as nr}from"./chunk-4BMEZGHF.js";import{I as or}from"./chunk-XZIHB7SX.js";import{p as sr}from"./mermaid-parser.core.js";import{c as Re}from"./cytoscape.esm.js";import"./_baseUniq.js";import"./_basePickBy.js";import"./clone.js";var Se={exports:{}},Fe={exports:{}},be={exports:{}};(function(C,G){(function(H,L){C.exports=L()})(me,function(){return function(A){var H={};function L(v){if(H[v])return H[v].exports;var h=H[v]={i:v,l:!1,exports:{}};return A[v].call(h.exports,h,h.exports,L),h.l=!0,h.exports}return L.m=A,L.c=H,L.i=function(v){return v},L.d=function(v,h,a){L.o(v,h)||Object.defineProperty(v,h,{configurable:!1,enumerable:!0,get:a})},L.n=function(v){var h=v&&v.__esModule?function(){return v.default}:function(){return v};return L.d(h,"a",h),h},L.o=function(v,h){return Object.prototype.hasOwnProperty.call(v,h)},L.p="",L(L.s=28)}([function(A,H,L){function v(){}v.QUALITY=1,v.DEFAULT_CREATE_BENDS_AS_NEEDED=!1,v.DEFAULT_INCREMENTAL=!1,v.DEFAULT_ANIMATION_ON_LAYOUT=!0,v.DEFAULT_ANIMATION_DURING_LAYOUT=!1,v.DEFAULT_ANIMATION_PERIOD=50,v.DEFAULT_UNIFORM_LEAF_NODE_SIZES=!1,v.DEFAULT_GRAPH_MARGIN=15,v.NODE_DIMENSIONS_INCLUDE_LABELS=!1,v.SIMPLE_NODE_SIZE=40,v.SIMPLE_NODE_HALF_SIZE=v.SIMPLE_NODE_SIZE/2,v.EMPTY_COMPOUND_NODE_SIZE=40,v.MIN_EDGE_LENGTH=1,v.WORLD_BOUNDARY=1e6,v.INITIAL_WORLD_BOUNDARY=v.WORLD_BOUNDARY/1e3,v.WORLD_CENTER_X=1200,v.WORLD_CENTER_Y=900,A.exports=v},function(A,H,L){var v=L(2),h=L(8),a=L(9);function r(f,i,g){v.call(this,g),this.isOverlapingSourceAndTarget=!1,this.vGraphObject=g,this.bendpoints=[],this.source=f,this.target=i}r.prototype=Object.create(v.prototype);for(var e in v)r[e]=v[e];r.prototype.getSource=function(){return this.source},r.prototype.getTarget=function(){return this.target},r.prototype.isInterGraph=function(){return this.isInterGraph},r.prototype.getLength=function(){return this.length},r.prototype.isOverlapingSourceAndTarget=function(){return this.isOverlapingSourceAndTarget},r.prototype.getBendpoints=function(){return this.bendpoints},r.prototype.getLca=function(){return this.lca},r.prototype.getSourceInLca=function(){return this.sourceInLca},r.prototype.getTargetInLca=function(){return this.targetInLca},r.prototype.getOtherEnd=function(f){if(this.source===f)return this.target;if(this.target===f)return this.source;throw"Node is not incident with this edge"},r.prototype.getOtherEndInGraph=function(f,i){for(var g=this.getOtherEnd(f),t=i.getGraphManager().getRoot();;){if(g.getOwner()==i)return g;if(g.getOwner()==t)break;g=g.getOwner().getParent()}return null},r.prototype.updateLength=function(){var f=new Array(4);this.isOverlapingSourceAndTarget=h.getIntersection(this.target.getRect(),this.source.getRect(),f),this.isOverlapingSourceAndTarget||(this.lengthX=f[0]-f[2],this.lengthY=f[1]-f[3],Math.abs(this.lengthX)<1&&(this.lengthX=a.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=a.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY))},r.prototype.updateLengthSimple=function(){this.lengthX=this.target.getCenterX()-this.source.getCenterX(),this.lengthY=this.target.getCenterY()-this.source.getCenterY(),Math.abs(this.lengthX)<1&&(this.lengthX=a.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=a.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY)},A.exports=r},function(A,H,L){function v(h){this.vGraphObject=h}A.exports=v},function(A,H,L){var v=L(2),h=L(10),a=L(13),r=L(0),e=L(16),f=L(5);function i(t,s,o,c){o==null&&c==null&&(c=s),v.call(this,c),t.graphManager!=null&&(t=t.graphManager),this.estimatedSize=h.MIN_VALUE,this.inclusionTreeDepth=h.MAX_VALUE,this.vGraphObject=c,this.edges=[],this.graphManager=t,o!=null&&s!=null?this.rect=new a(s.x,s.y,o.width,o.height):this.rect=new a}i.prototype=Object.create(v.prototype);for(var g in v)i[g]=v[g];i.prototype.getEdges=function(){return this.edges},i.prototype.getChild=function(){return this.child},i.prototype.getOwner=function(){return this.owner},i.prototype.getWidth=function(){return this.rect.width},i.prototype.setWidth=function(t){this.rect.width=t},i.prototype.getHeight=function(){return this.rect.height},i.prototype.setHeight=function(t){this.rect.height=t},i.prototype.getCenterX=function(){return this.rect.x+this.rect.width/2},i.prototype.getCenterY=function(){return this.rect.y+this.rect.height/2},i.prototype.getCenter=function(){return new f(this.rect.x+this.rect.width/2,this.rect.y+this.rect.height/2)},i.prototype.getLocation=function(){return new f(this.rect.x,this.rect.y)},i.prototype.getRect=function(){return this.rect},i.prototype.getDiagonal=function(){return Math.sqrt(this.rect.width*this.rect.width+this.rect.height*this.rect.height)},i.prototype.getHalfTheDiagonal=function(){return Math.sqrt(this.rect.height*this.rect.height+this.rect.width*this.rect.width)/2},i.prototype.setRect=function(t,s){this.rect.x=t.x,this.rect.y=t.y,this.rect.width=s.width,this.rect.height=s.height},i.prototype.setCenter=function(t,s){this.rect.x=t-this.rect.width/2,this.rect.y=s-this.rect.height/2},i.prototype.setLocation=function(t,s){this.rect.x=t,this.rect.y=s},i.prototype.moveBy=function(t,s){this.rect.x+=t,this.rect.y+=s},i.prototype.getEdgeListToNode=function(t){var s=[],o=this;return o.edges.forEach(function(c){if(c.target==t){if(c.source!=o)throw"Incorrect edge source!";s.push(c)}}),s},i.prototype.getEdgesBetween=function(t){var s=[],o=this;return o.edges.forEach(function(c){if(!(c.source==o||c.target==o))throw"Incorrect edge source and/or target";(c.target==t||c.source==t)&&s.push(c)}),s},i.prototype.getNeighborsList=function(){var t=new Set,s=this;return s.edges.forEach(function(o){if(o.source==s)t.add(o.target);else{if(o.target!=s)throw"Incorrect incidency!";t.add(o.source)}}),t},i.prototype.withChildren=function(){var t=new Set,s,o;if(t.add(this),this.child!=null)for(var c=this.child.getNodes(),l=0;l<c.length;l++)s=c[l],o=s.withChildren(),o.forEach(function(T){t.add(T)});return t},i.prototype.getNoOfChildren=function(){var t=0,s;if(this.child==null)t=1;else for(var o=this.child.getNodes(),c=0;c<o.length;c++)s=o[c],t+=s.getNoOfChildren();return t==0&&(t=1),t},i.prototype.getEstimatedSize=function(){if(this.estimatedSize==h.MIN_VALUE)throw"assert failed";return this.estimatedSize},i.prototype.calcEstimatedSize=function(){return this.child==null?this.estimatedSize=(this.rect.width+this.rect.height)/2:(this.estimatedSize=this.child.calcEstimatedSize(),this.rect.width=this.estimatedSize,this.rect.height=this.estimatedSize,this.estimatedSize)},i.prototype.scatter=function(){var t,s,o=-r.INITIAL_WORLD_BOUNDARY,c=r.INITIAL_WORLD_BOUNDARY;t=r.WORLD_CENTER_X+e.nextDouble()*(c-o)+o;var l=-r.INITIAL_WORLD_BOUNDARY,T=r.INITIAL_WORLD_BOUNDARY;s=r.WORLD_CENTER_Y+e.nextDouble()*(T-l)+l,this.rect.x=t,this.rect.y=s},i.prototype.updateBounds=function(){if(this.getChild()==null)throw"assert failed";if(this.getChild().getNodes().length!=0){var t=this.getChild();if(t.updateBounds(!0),this.rect.x=t.getLeft(),this.rect.y=t.getTop(),this.setWidth(t.getRight()-t.getLeft()),this.setHeight(t.getBottom()-t.getTop()),r.NODE_DIMENSIONS_INCLUDE_LABELS){var s=t.getRight()-t.getLeft(),o=t.getBottom()-t.getTop();this.labelWidth&&(this.labelPosHorizontal=="left"?(this.rect.x-=this.labelWidth,this.setWidth(s+this.labelWidth)):this.labelPosHorizontal=="center"&&this.labelWidth>s?(this.rect.x-=(this.labelWidth-s)/2,this.setWidth(this.labelWidth)):this.labelPosHorizontal=="right"&&this.setWidth(s+this.labelWidth)),this.labelHeight&&(this.labelPosVertical=="top"?(this.rect.y-=this.labelHeight,this.setHeight(o+this.labelHeight)):this.labelPosVertical=="center"&&this.labelHeight>o?(this.rect.y-=(this.labelHeight-o)/2,this.setHeight(this.labelHeight)):this.labelPosVertical=="bottom"&&this.setHeight(o+this.labelHeight))}}},i.prototype.getInclusionTreeDepth=function(){if(this.inclusionTreeDepth==h.MAX_VALUE)throw"assert failed";return this.inclusionTreeDepth},i.prototype.transform=function(t){var s=this.rect.x;s>r.WORLD_BOUNDARY?s=r.WORLD_BOUNDARY:s<-r.WORLD_BOUNDARY&&(s=-r.WORLD_BOUNDARY);var o=this.rect.y;o>r.WORLD_BOUNDARY?o=r.WORLD_BOUNDARY:o<-r.WORLD_BOUNDARY&&(o=-r.WORLD_BOUNDARY);var c=new f(s,o),l=t.inverseTransformPoint(c);this.setLocation(l.x,l.y)},i.prototype.getLeft=function(){return this.rect.x},i.prototype.getRight=function(){return this.rect.x+this.rect.width},i.prototype.getTop=function(){return this.rect.y},i.prototype.getBottom=function(){return this.rect.y+this.rect.height},i.prototype.getParent=function(){return this.owner==null?null:this.owner.getParent()},A.exports=i},function(A,H,L){var v=L(0);function h(){}for(var a in v)h[a]=v[a];h.MAX_ITERATIONS=2500,h.DEFAULT_EDGE_LENGTH=50,h.DEFAULT_SPRING_STRENGTH=.45,h.DEFAULT_REPULSION_STRENGTH=4500,h.DEFAULT_GRAVITY_STRENGTH=.4,h.DEFAULT_COMPOUND_GRAVITY_STRENGTH=1,h.DEFAULT_GRAVITY_RANGE_FACTOR=3.8,h.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=1.5,h.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION=!0,h.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION=!0,h.DEFAULT_COOLING_FACTOR_INCREMENTAL=.3,h.COOLING_ADAPTATION_FACTOR=.33,h.ADAPTATION_LOWER_NODE_LIMIT=1e3,h.ADAPTATION_UPPER_NODE_LIMIT=5e3,h.MAX_NODE_DISPLACEMENT_INCREMENTAL=100,h.MAX_NODE_DISPLACEMENT=h.MAX_NODE_DISPLACEMENT_INCREMENTAL*3,h.MIN_REPULSION_DIST=h.DEFAULT_EDGE_LENGTH/10,h.CONVERGENCE_CHECK_PERIOD=100,h.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=.1,h.MIN_EDGE_LENGTH=1,h.GRID_CALCULATION_CHECK_PERIOD=10,A.exports=h},function(A,H,L){function v(h,a){h==null&&a==null?(this.x=0,this.y=0):(this.x=h,this.y=a)}v.prototype.getX=function(){return this.x},v.prototype.getY=function(){return this.y},v.prototype.setX=function(h){this.x=h},v.prototype.setY=function(h){this.y=h},v.prototype.getDifference=function(h){return new DimensionD(this.x-h.x,this.y-h.y)},v.prototype.getCopy=function(){return new v(this.x,this.y)},v.prototype.translate=function(h){return this.x+=h.width,this.y+=h.height,this},A.exports=v},function(A,H,L){var v=L(2),h=L(10),a=L(0),r=L(7),e=L(3),f=L(1),i=L(13),g=L(12),t=L(11);function s(c,l,T){v.call(this,T),this.estimatedSize=h.MIN_VALUE,this.margin=a.DEFAULT_GRAPH_MARGIN,this.edges=[],this.nodes=[],this.isConnected=!1,this.parent=c,l!=null&&l instanceof r?this.graphManager=l:l!=null&&l instanceof Layout&&(this.graphManager=l.graphManager)}s.prototype=Object.create(v.prototype);for(var o in v)s[o]=v[o];s.prototype.getNodes=function(){return this.nodes},s.prototype.getEdges=function(){return this.edges},s.prototype.getGraphManager=function(){return this.graphManager},s.prototype.getParent=function(){return this.parent},s.prototype.getLeft=function(){return this.left},s.prototype.getRight=function(){return this.right},s.prototype.getTop=function(){return this.top},s.prototype.getBottom=function(){return this.bottom},s.prototype.isConnected=function(){return this.isConnected},s.prototype.add=function(c,l,T){if(l==null&&T==null){var d=c;if(this.graphManager==null)throw"Graph has no graph mgr!";if(this.getNodes().indexOf(d)>-1)throw"Node already in graph!";return d.owner=this,this.getNodes().push(d),d}else{var u=c;if(!(this.getNodes().indexOf(l)>-1&&this.getNodes().indexOf(T)>-1))throw"Source or target not in graph!";if(!(l.owner==T.owner&&l.owner==this))throw"Both owners must be this graph!";return l.owner!=T.owner?null:(u.source=l,u.target=T,u.isInterGraph=!1,this.getEdges().push(u),l.edges.push(u),T!=l&&T.edges.push(u),u)}},s.prototype.remove=function(c){var l=c;if(c instanceof e){if(l==null)throw"Node is null!";if(!(l.owner!=null&&l.owner==this))throw"Owner graph is invalid!";if(this.graphManager==null)throw"Owner graph manager is invalid!";for(var T=l.edges.slice(),d,u=T.length,N=0;N<u;N++)d=T[N],d.isInterGraph?this.graphManager.remove(d):d.source.owner.remove(d);var R=this.nodes.indexOf(l);if(R==-1)throw"Node not in owner node list!";this.nodes.splice(R,1)}else if(c instanceof f){var d=c;if(d==null)throw"Edge is null!";if(!(d.source!=null&&d.target!=null))throw"Source and/or target is null!";if(!(d.source.owner!=null&&d.target.owner!=null&&d.source.owner==this&&d.target.owner==this))throw"Source and/or target owner is invalid!";var M=d.source.edges.indexOf(d),b=d.target.edges.indexOf(d);if(!(M>-1&&b>-1))throw"Source and/or target doesn't know this edge!";d.source.edges.splice(M,1),d.target!=d.source&&d.target.edges.splice(b,1);var R=d.source.owner.getEdges().indexOf(d);if(R==-1)throw"Not in owner's edge list!";d.source.owner.getEdges().splice(R,1)}},s.prototype.updateLeftTop=function(){for(var c=h.MAX_VALUE,l=h.MAX_VALUE,T,d,u,N=this.getNodes(),R=N.length,M=0;M<R;M++){var b=N[M];T=b.getTop(),d=b.getLeft(),c>T&&(c=T),l>d&&(l=d)}return c==h.MAX_VALUE?null:(N[0].getParent().paddingLeft!=null?u=N[0].getParent().paddingLeft:u=this.margin,this.left=l-u,this.top=c-u,new g(this.left,this.top))},s.prototype.updateBounds=function(c){for(var l=h.MAX_VALUE,T=-h.MAX_VALUE,d=h.MAX_VALUE,u=-h.MAX_VALUE,N,R,M,b,Q,Y=this.nodes,k=Y.length,D=0;D<k;D++){var rt=Y[D];c&&rt.child!=null&&rt.updateBounds(),N=rt.getLeft(),R=rt.getRight(),M=rt.getTop(),b=rt.getBottom(),l>N&&(l=N),T<R&&(T=R),d>M&&(d=M),u<b&&(u=b)}var n=new i(l,d,T-l,u-d);l==h.MAX_VALUE&&(this.left=this.parent.getLeft(),this.right=this.parent.getRight(),this.top=this.parent.getTop(),this.bottom=this.parent.getBottom()),Y[0].getParent().paddingLeft!=null?Q=Y[0].getParent().paddingLeft:Q=this.margin,this.left=n.x-Q,this.right=n.x+n.width+Q,this.top=n.y-Q,this.bottom=n.y+n.height+Q},s.calculateBounds=function(c){for(var l=h.MAX_VALUE,T=-h.MAX_VALUE,d=h.MAX_VALUE,u=-h.MAX_VALUE,N,R,M,b,Q=c.length,Y=0;Y<Q;Y++){var k=c[Y];N=k.getLeft(),R=k.getRight(),M=k.getTop(),b=k.getBottom(),l>N&&(l=N),T<R&&(T=R),d>M&&(d=M),u<b&&(u=b)}var D=new i(l,d,T-l,u-d);return D},s.prototype.getInclusionTreeDepth=function(){return this==this.graphManager.getRoot()?1:this.parent.getInclusionTreeDepth()},s.prototype.getEstimatedSize=function(){if(this.estimatedSize==h.MIN_VALUE)throw"assert failed";return this.estimatedSize},s.prototype.calcEstimatedSize=function(){for(var c=0,l=this.nodes,T=l.length,d=0;d<T;d++){var u=l[d];c+=u.calcEstimatedSize()}return c==0?this.estimatedSize=a.EMPTY_COMPOUND_NODE_SIZE:this.estimatedSize=c/Math.sqrt(this.nodes.length),this.estimatedSize},s.prototype.updateConnected=function(){var c=this;if(this.nodes.length==0){this.isConnected=!0;return}var l=new t,T=new Set,d=this.nodes[0],u,N,R=d.withChildren();for(R.forEach(function(D){l.push(D),T.add(D)});l.length!==0;){d=l.shift(),u=d.getEdges();for(var M=u.length,b=0;b<M;b++){var Q=u[b];if(N=Q.getOtherEndInGraph(d,this),N!=null&&!T.has(N)){var Y=N.withChildren();Y.forEach(function(D){l.push(D),T.add(D)})}}}if(this.isConnected=!1,T.size>=this.nodes.length){var k=0;T.forEach(function(D){D.owner==c&&k++}),k==this.nodes.length&&(this.isConnected=!0)}},A.exports=s},function(A,H,L){var v,h=L(1);function a(r){v=L(6),this.layout=r,this.graphs=[],this.edges=[]}a.prototype.addRoot=function(){var r=this.layout.newGraph(),e=this.layout.newNode(null),f=this.add(r,e);return this.setRootGraph(f),this.rootGraph},a.prototype.add=function(r,e,f,i,g){if(f==null&&i==null&&g==null){if(r==null)throw"Graph is null!";if(e==null)throw"Parent node is null!";if(this.graphs.indexOf(r)>-1)throw"Graph already in this graph mgr!";if(this.graphs.push(r),r.parent!=null)throw"Already has a parent!";if(e.child!=null)throw"Already has a child!";return r.parent=e,e.child=r,r}else{g=f,i=e,f=r;var t=i.getOwner(),s=g.getOwner();if(!(t!=null&&t.getGraphManager()==this))throw"Source not in this graph mgr!";if(!(s!=null&&s.getGraphManager()==this))throw"Target not in this graph mgr!";if(t==s)return f.isInterGraph=!1,t.add(f,i,g);if(f.isInterGraph=!0,f.source=i,f.target=g,this.edges.indexOf(f)>-1)throw"Edge already in inter-graph edge list!";if(this.edges.push(f),!(f.source!=null&&f.target!=null))throw"Edge source and/or target is null!";if(!(f.source.edges.indexOf(f)==-1&&f.target.edges.indexOf(f)==-1))throw"Edge already in source and/or target incidency list!";return f.source.edges.push(f),f.target.edges.push(f),f}},a.prototype.remove=function(r){if(r instanceof v){var e=r;if(e.getGraphManager()!=this)throw"Graph not in this graph mgr";if(!(e==this.rootGraph||e.parent!=null&&e.parent.graphManager==this))throw"Invalid parent node!";var f=[];f=f.concat(e.getEdges());for(var i,g=f.length,t=0;t<g;t++)i=f[t],e.remove(i);var s=[];s=s.concat(e.getNodes());var o;g=s.length;for(var t=0;t<g;t++)o=s[t],e.remove(o);e==this.rootGraph&&this.setRootGraph(null);var c=this.graphs.indexOf(e);this.graphs.splice(c,1),e.parent=null}else if(r instanceof h){if(i=r,i==null)throw"Edge is null!";if(!i.isInterGraph)throw"Not an inter-graph edge!";if(!(i.source!=null&&i.target!=null))throw"Source and/or target is null!";if(!(i.source.edges.indexOf(i)!=-1&&i.target.edges.indexOf(i)!=-1))throw"Source and/or target doesn't know this edge!";var c=i.source.edges.indexOf(i);if(i.source.edges.splice(c,1),c=i.target.edges.indexOf(i),i.target.edges.splice(c,1),!(i.source.owner!=null&&i.source.owner.getGraphManager()!=null))throw"Edge owner graph or owner graph manager is null!";if(i.source.owner.getGraphManager().edges.indexOf(i)==-1)throw"Not in owner graph manager's edge list!";var c=i.source.owner.getGraphManager().edges.indexOf(i);i.source.owner.getGraphManager().edges.splice(c,1)}},a.prototype.updateBounds=function(){this.rootGraph.updateBounds(!0)},a.prototype.getGraphs=function(){return this.graphs},a.prototype.getAllNodes=function(){if(this.allNodes==null){for(var r=[],e=this.getGraphs(),f=e.length,i=0;i<f;i++)r=r.concat(e[i].getNodes());this.allNodes=r}return this.allNodes},a.prototype.resetAllNodes=function(){this.allNodes=null},a.prototype.resetAllEdges=function(){this.allEdges=null},a.prototype.resetAllNodesToApplyGravitation=function(){this.allNodesToApplyGravitation=null},a.prototype.getAllEdges=function(){if(this.allEdges==null){var r=[],e=this.getGraphs();e.length;for(var f=0;f<e.length;f++)r=r.concat(e[f].getEdges());r=r.concat(this.edges),this.allEdges=r}return this.allEdges},a.prototype.getAllNodesToApplyGravitation=function(){return this.allNodesToApplyGravitation},a.prototype.setAllNodesToApplyGravitation=function(r){if(this.allNodesToApplyGravitation!=null)throw"assert failed";this.allNodesToApplyGravitation=r},a.prototype.getRoot=function(){return this.rootGraph},a.prototype.setRootGraph=function(r){if(r.getGraphManager()!=this)throw"Root not in this graph mgr!";this.rootGraph=r,r.parent==null&&(r.parent=this.layout.newNode("Root node"))},a.prototype.getLayout=function(){return this.layout},a.prototype.isOneAncestorOfOther=function(r,e){if(!(r!=null&&e!=null))throw"assert failed";if(r==e)return!0;var f=r.getOwner(),i;do{if(i=f.getParent(),i==null)break;if(i==e)return!0;if(f=i.getOwner(),f==null)break}while(!0);f=e.getOwner();do{if(i=f.getParent(),i==null)break;if(i==r)return!0;if(f=i.getOwner(),f==null)break}while(!0);return!1},a.prototype.calcLowestCommonAncestors=function(){for(var r,e,f,i,g,t=this.getAllEdges(),s=t.length,o=0;o<s;o++){if(r=t[o],e=r.source,f=r.target,r.lca=null,r.sourceInLca=e,r.targetInLca=f,e==f){r.lca=e.getOwner();continue}for(i=e.getOwner();r.lca==null;){for(r.targetInLca=f,g=f.getOwner();r.lca==null;){if(g==i){r.lca=g;break}if(g==this.rootGraph)break;if(r.lca!=null)throw"assert failed";r.targetInLca=g.getParent(),g=r.targetInLca.getOwner()}if(i==this.rootGraph)break;r.lca==null&&(r.sourceInLca=i.getParent(),i=r.sourceInLca.getOwner())}if(r.lca==null)throw"assert failed"}},a.prototype.calcLowestCommonAncestor=function(r,e){if(r==e)return r.getOwner();var f=r.getOwner();do{if(f==null)break;var i=e.getOwner();do{if(i==null)break;if(i==f)return i;i=i.getParent().getOwner()}while(!0);f=f.getParent().getOwner()}while(!0);return f},a.prototype.calcInclusionTreeDepths=function(r,e){r==null&&e==null&&(r=this.rootGraph,e=1);for(var f,i=r.getNodes(),g=i.length,t=0;t<g;t++)f=i[t],f.inclusionTreeDepth=e,f.child!=null&&this.calcInclusionTreeDepths(f.child,e+1)},a.prototype.includesInvalidEdge=function(){for(var r,e=[],f=this.edges.length,i=0;i<f;i++)r=this.edges[i],this.isOneAncestorOfOther(r.source,r.target)&&e.push(r);for(var i=0;i<e.length;i++)this.remove(e[i]);return!1},A.exports=a},function(A,H,L){var v=L(12);function h(){}h.calcSeparationAmount=function(a,r,e,f){if(!a.intersects(r))throw"assert failed";var i=new Array(2);this.decideDirectionsForOverlappingNodes(a,r,i),e[0]=Math.min(a.getRight(),r.getRight())-Math.max(a.x,r.x),e[1]=Math.min(a.getBottom(),r.getBottom())-Math.max(a.y,r.y),a.getX()<=r.getX()&&a.getRight()>=r.getRight()?e[0]+=Math.min(r.getX()-a.getX(),a.getRight()-r.getRight()):r.getX()<=a.getX()&&r.getRight()>=a.getRight()&&(e[0]+=Math.min(a.getX()-r.getX(),r.getRight()-a.getRight())),a.getY()<=r.getY()&&a.getBottom()>=r.getBottom()?e[1]+=Math.min(r.getY()-a.getY(),a.getBottom()-r.getBottom()):r.getY()<=a.getY()&&r.getBottom()>=a.getBottom()&&(e[1]+=Math.min(a.getY()-r.getY(),r.getBottom()-a.getBottom()));var g=Math.abs((r.getCenterY()-a.getCenterY())/(r.getCenterX()-a.getCenterX()));r.getCenterY()===a.getCenterY()&&r.getCenterX()===a.getCenterX()&&(g=1);var t=g*e[0],s=e[1]/g;e[0]<s?s=e[0]:t=e[1],e[0]=-1*i[0]*(s/2+f),e[1]=-1*i[1]*(t/2+f)},h.decideDirectionsForOverlappingNodes=function(a,r,e){a.getCenterX()<r.getCenterX()?e[0]=-1:e[0]=1,a.getCenterY()<r.getCenterY()?e[1]=-1:e[1]=1},h.getIntersection2=function(a,r,e){var f=a.getCenterX(),i=a.getCenterY(),g=r.getCenterX(),t=r.getCenterY();if(a.intersects(r))return e[0]=f,e[1]=i,e[2]=g,e[3]=t,!0;var s=a.getX(),o=a.getY(),c=a.getRight(),l=a.getX(),T=a.getBottom(),d=a.getRight(),u=a.getWidthHalf(),N=a.getHeightHalf(),R=r.getX(),M=r.getY(),b=r.getRight(),Q=r.getX(),Y=r.getBottom(),k=r.getRight(),D=r.getWidthHalf(),rt=r.getHeightHalf(),n=!1,m=!1;if(f===g){if(i>t)return e[0]=f,e[1]=o,e[2]=g,e[3]=Y,!1;if(i<t)return e[0]=f,e[1]=T,e[2]=g,e[3]=M,!1}else if(i===t){if(f>g)return e[0]=s,e[1]=i,e[2]=b,e[3]=t,!1;if(f<g)return e[0]=c,e[1]=i,e[2]=R,e[3]=t,!1}else{var p=a.height/a.width,E=r.height/r.width,y=(t-i)/(g-f),I=void 0,w=void 0,S=void 0,W=void 0,x=void 0,q=void 0;if(-p===y?f>g?(e[0]=l,e[1]=T,n=!0):(e[0]=c,e[1]=o,n=!0):p===y&&(f>g?(e[0]=s,e[1]=o,n=!0):(e[0]=d,e[1]=T,n=!0)),-E===y?g>f?(e[2]=Q,e[3]=Y,m=!0):(e[2]=b,e[3]=M,m=!0):E===y&&(g>f?(e[2]=R,e[3]=M,m=!0):(e[2]=k,e[3]=Y,m=!0)),n&&m)return!1;if(f>g?i>t?(I=this.getCardinalDirection(p,y,4),w=this.getCardinalDirection(E,y,2)):(I=this.getCardinalDirection(-p,y,3),w=this.getCardinalDirection(-E,y,1)):i>t?(I=this.getCardinalDirection(-p,y,1),w=this.getCardinalDirection(-E,y,3)):(I=this.getCardinalDirection(p,y,2),w=this.getCardinalDirection(E,y,4)),!n)switch(I){case 1:W=o,S=f+-N/y,e[0]=S,e[1]=W;break;case 2:S=d,W=i+u*y,e[0]=S,e[1]=W;break;case 3:W=T,S=f+N/y,e[0]=S,e[1]=W;break;case 4:S=l,W=i+-u*y,e[0]=S,e[1]=W;break}if(!m)switch(w){case 1:q=M,x=g+-rt/y,e[2]=x,e[3]=q;break;case 2:x=k,q=t+D*y,e[2]=x,e[3]=q;break;case 3:q=Y,x=g+rt/y,e[2]=x,e[3]=q;break;case 4:x=Q,q=t+-D*y,e[2]=x,e[3]=q;break}}return!1},h.getCardinalDirection=function(a,r,e){return a>r?e:1+e%4},h.getIntersection=function(a,r,e,f){if(f==null)return this.getIntersection2(a,r,e);var i=a.x,g=a.y,t=r.x,s=r.y,o=e.x,c=e.y,l=f.x,T=f.y,d=void 0,u=void 0,N=void 0,R=void 0,M=void 0,b=void 0,Q=void 0,Y=void 0,k=void 0;return N=s-g,M=i-t,Q=t*g-i*s,R=T-c,b=o-l,Y=l*c-o*T,k=N*b-R*M,k===0?null:(d=(M*Y-b*Q)/k,u=(R*Q-N*Y)/k,new v(d,u))},h.angleOfVector=function(a,r,e,f){var i=void 0;return a!==e?(i=Math.atan((f-r)/(e-a)),e<a?i+=Math.PI:f<r&&(i+=this.TWO_PI)):f<r?i=this.ONE_AND_HALF_PI:i=this.HALF_PI,i},h.doIntersect=function(a,r,e,f){var i=a.x,g=a.y,t=r.x,s=r.y,o=e.x,c=e.y,l=f.x,T=f.y,d=(t-i)*(T-c)-(l-o)*(s-g);if(d===0)return!1;var u=((T-c)*(l-i)+(o-l)*(T-g))/d,N=((g-s)*(l-i)+(t-i)*(T-g))/d;return 0<u&&u<1&&0<N&&N<1},h.findCircleLineIntersections=function(a,r,e,f,i,g,t){var s=(e-a)*(e-a)+(f-r)*(f-r),o=2*((a-i)*(e-a)+(r-g)*(f-r)),c=(a-i)*(a-i)+(r-g)*(r-g)-t*t,l=o*o-4*s*c;if(l>=0){var T=(-o+Math.sqrt(o*o-4*s*c))/(2*s),d=(-o-Math.sqrt(o*o-4*s*c))/(2*s),u=null;return T>=0&&T<=1?[T]:d>=0&&d<=1?[d]:u}else return null},h.HALF_PI=.5*Math.PI,h.ONE_AND_HALF_PI=1.5*Math.PI,h.TWO_PI=2*Math.PI,h.THREE_PI=3*Math.PI,A.exports=h},function(A,H,L){function v(){}v.sign=function(h){return h>0?1:h<0?-1:0},v.floor=function(h){return h<0?Math.ceil(h):Math.floor(h)},v.ceil=function(h){return h<0?Math.floor(h):Math.ceil(h)},A.exports=v},function(A,H,L){function v(){}v.MAX_VALUE=2147483647,v.MIN_VALUE=-2147483648,A.exports=v},function(A,H,L){var v=function(){function i(g,t){for(var s=0;s<t.length;s++){var o=t[s];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(g,o.key,o)}}return function(g,t,s){return t&&i(g.prototype,t),s&&i(g,s),g}}();function h(i,g){if(!(i instanceof g))throw new TypeError("Cannot call a class as a function")}var a=function(g){return{value:g,next:null,prev:null}},r=function(g,t,s,o){return g!==null?g.next=t:o.head=t,s!==null?s.prev=t:o.tail=t,t.prev=g,t.next=s,o.length++,t},e=function(g,t){var s=g.prev,o=g.next;return s!==null?s.next=o:t.head=o,o!==null?o.prev=s:t.tail=s,g.prev=g.next=null,t.length--,g},f=function(){function i(g){var t=this;h(this,i),this.length=0,this.head=null,this.tail=null,g!=null&&g.forEach(function(s){return t.push(s)})}return v(i,[{key:"size",value:function(){return this.length}},{key:"insertBefore",value:function(t,s){return r(s.prev,a(t),s,this)}},{key:"insertAfter",value:function(t,s){return r(s,a(t),s.next,this)}},{key:"insertNodeBefore",value:function(t,s){return r(s.prev,t,s,this)}},{key:"insertNodeAfter",value:function(t,s){return r(s,t,s.next,this)}},{key:"push",value:function(t){return r(this.tail,a(t),null,this)}},{key:"unshift",value:function(t){return r(null,a(t),this.head,this)}},{key:"remove",value:function(t){return e(t,this)}},{key:"pop",value:function(){return e(this.tail,this).value}},{key:"popNode",value:function(){return e(this.tail,this)}},{key:"shift",value:function(){return e(this.head,this).value}},{key:"shiftNode",value:function(){return e(this.head,this)}},{key:"get_object_at",value:function(t){if(t<=this.length()){for(var s=1,o=this.head;s<t;)o=o.next,s++;return o.value}}},{key:"set_object_at",value:function(t,s){if(t<=this.length()){for(var o=1,c=this.head;o<t;)c=c.next,o++;c.value=s}}}]),i}();A.exports=f},function(A,H,L){function v(h,a,r){this.x=null,this.y=null,h==null&&a==null&&r==null?(this.x=0,this.y=0):typeof h=="number"&&typeof a=="number"&&r==null?(this.x=h,this.y=a):h.constructor.name=="Point"&&a==null&&r==null&&(r=h,this.x=r.x,this.y=r.y)}v.prototype.getX=function(){return this.x},v.prototype.getY=function(){return this.y},v.prototype.getLocation=function(){return new v(this.x,this.y)},v.prototype.setLocation=function(h,a,r){h.constructor.name=="Point"&&a==null&&r==null?(r=h,this.setLocation(r.x,r.y)):typeof h=="number"&&typeof a=="number"&&r==null&&(parseInt(h)==h&&parseInt(a)==a?this.move(h,a):(this.x=Math.floor(h+.5),this.y=Math.floor(a+.5)))},v.prototype.move=function(h,a){this.x=h,this.y=a},v.prototype.translate=function(h,a){this.x+=h,this.y+=a},v.prototype.equals=function(h){if(h.constructor.name=="Point"){var a=h;return this.x==a.x&&this.y==a.y}return this==h},v.prototype.toString=function(){return new v().constructor.name+"[x="+this.x+",y="+this.y+"]"},A.exports=v},function(A,H,L){function v(h,a,r,e){this.x=0,this.y=0,this.width=0,this.height=0,h!=null&&a!=null&&r!=null&&e!=null&&(this.x=h,this.y=a,this.width=r,this.height=e)}v.prototype.getX=function(){return this.x},v.prototype.setX=function(h){this.x=h},v.prototype.getY=function(){return this.y},v.prototype.setY=function(h){this.y=h},v.prototype.getWidth=function(){return this.width},v.prototype.setWidth=function(h){this.width=h},v.prototype.getHeight=function(){return this.height},v.prototype.setHeight=function(h){this.height=h},v.prototype.getRight=function(){return this.x+this.width},v.prototype.getBottom=function(){return this.y+this.height},v.prototype.intersects=function(h){return!(this.getRight()<h.x||this.getBottom()<h.y||h.getRight()<this.x||h.getBottom()<this.y)},v.prototype.getCenterX=function(){return this.x+this.width/2},v.prototype.getMinX=function(){return this.getX()},v.prototype.getMaxX=function(){return this.getX()+this.width},v.prototype.getCenterY=function(){return this.y+this.height/2},v.prototype.getMinY=function(){return this.getY()},v.prototype.getMaxY=function(){return this.getY()+this.height},v.prototype.getWidthHalf=function(){return this.width/2},v.prototype.getHeightHalf=function(){return this.height/2},A.exports=v},function(A,H,L){var v=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(a){return typeof a}:function(a){return a&&typeof Symbol=="function"&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a};function h(){}h.lastID=0,h.createID=function(a){return h.isPrimitive(a)?a:(a.uniqueID!=null||(a.uniqueID=h.getString(),h.lastID++),a.uniqueID)},h.getString=function(a){return a==null&&(a=h.lastID),"Object#"+a},h.isPrimitive=function(a){var r=typeof a=="undefined"?"undefined":v(a);return a==null||r!="object"&&r!="function"},A.exports=h},function(A,H,L){function v(o){if(Array.isArray(o)){for(var c=0,l=Array(o.length);c<o.length;c++)l[c]=o[c];return l}else return Array.from(o)}var h=L(0),a=L(7),r=L(3),e=L(1),f=L(6),i=L(5),g=L(17),t=L(29);function s(o){t.call(this),this.layoutQuality=h.QUALITY,this.createBendsAsNeeded=h.DEFAULT_CREATE_BENDS_AS_NEEDED,this.incremental=h.DEFAULT_INCREMENTAL,this.animationOnLayout=h.DEFAULT_ANIMATION_ON_LAYOUT,this.animationDuringLayout=h.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=h.DEFAULT_ANIMATION_PERIOD,this.uniformLeafNodeSizes=h.DEFAULT_UNIFORM_LEAF_NODE_SIZES,this.edgeToDummyNodes=new Map,this.graphManager=new a(this),this.isLayoutFinished=!1,this.isSubLayout=!1,this.isRemoteUse=!1,o!=null&&(this.isRemoteUse=o)}s.RANDOM_SEED=1,s.prototype=Object.create(t.prototype),s.prototype.getGraphManager=function(){return this.graphManager},s.prototype.getAllNodes=function(){return this.graphManager.getAllNodes()},s.prototype.getAllEdges=function(){return this.graphManager.getAllEdges()},s.prototype.getAllNodesToApplyGravitation=function(){return this.graphManager.getAllNodesToApplyGravitation()},s.prototype.newGraphManager=function(){var o=new a(this);return this.graphManager=o,o},s.prototype.newGraph=function(o){return new f(null,this.graphManager,o)},s.prototype.newNode=function(o){return new r(this.graphManager,o)},s.prototype.newEdge=function(o){return new e(null,null,o)},s.prototype.checkLayoutSuccess=function(){return this.graphManager.getRoot()==null||this.graphManager.getRoot().getNodes().length==0||this.graphManager.includesInvalidEdge()},s.prototype.runLayout=function(){this.isLayoutFinished=!1,this.tilingPreLayout&&this.tilingPreLayout(),this.initParameters();var o;return this.checkLayoutSuccess()?o=!1:o=this.layout(),h.ANIMATE==="during"?!1:(o&&(this.isSubLayout||this.doPostLayout()),this.tilingPostLayout&&this.tilingPostLayout(),this.isLayoutFinished=!0,o)},s.prototype.doPostLayout=function(){this.incremental||this.transform(),this.update()},s.prototype.update2=function(){if(this.createBendsAsNeeded&&(this.createBendpointsFromDummyNodes(),this.graphManager.resetAllEdges()),!this.isRemoteUse){for(var o=this.graphManager.getAllEdges(),c=0;c<o.length;c++)o[c];for(var l=this.graphManager.getRoot().getNodes(),c=0;c<l.length;c++)l[c];this.update(this.graphManager.getRoot())}},s.prototype.update=function(o){if(o==null)this.update2();else if(o instanceof r){var c=o;if(c.getChild()!=null)for(var l=c.getChild().getNodes(),T=0;T<l.length;T++)update(l[T]);if(c.vGraphObject!=null){var d=c.vGraphObject;d.update(c)}}else if(o instanceof e){var u=o;if(u.vGraphObject!=null){var N=u.vGraphObject;N.update(u)}}else if(o instanceof f){var R=o;if(R.vGraphObject!=null){var M=R.vGraphObject;M.update(R)}}},s.prototype.initParameters=function(){this.isSubLayout||(this.layoutQuality=h.QUALITY,this.animationDuringLayout=h.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=h.DEFAULT_ANIMATION_PERIOD,this.animationOnLayout=h.DEFAULT_ANIMATION_ON_LAYOUT,this.incremental=h.DEFAULT_INCREMENTAL,this.createBendsAsNeeded=h.DEFAULT_CREATE_BENDS_AS_NEEDED,this.uniformLeafNodeSizes=h.DEFAULT_UNIFORM_LEAF_NODE_SIZES),this.animationDuringLayout&&(this.animationOnLayout=!1)},s.prototype.transform=function(o){if(o==null)this.transform(new i(0,0));else{var c=new g,l=this.graphManager.getRoot().updateLeftTop();if(l!=null){c.setWorldOrgX(o.x),c.setWorldOrgY(o.y),c.setDeviceOrgX(l.x),c.setDeviceOrgY(l.y);for(var T=this.getAllNodes(),d,u=0;u<T.length;u++)d=T[u],d.transform(c)}}},s.prototype.positionNodesRandomly=function(o){if(o==null)this.positionNodesRandomly(this.getGraphManager().getRoot()),this.getGraphManager().getRoot().updateBounds(!0);else for(var c,l,T=o.getNodes(),d=0;d<T.length;d++)c=T[d],l=c.getChild(),l==null||l.getNodes().length==0?c.scatter():(this.positionNodesRandomly(l),c.updateBounds())},s.prototype.getFlatForest=function(){for(var o=[],c=!0,l=this.graphManager.getRoot().getNodes(),T=!0,d=0;d<l.length;d++)l[d].getChild()!=null&&(T=!1);if(!T)return o;var u=new Set,N=[],R=new Map,M=[];for(M=M.concat(l);M.length>0&&c;){for(N.push(M[0]);N.length>0&&c;){var b=N[0];N.splice(0,1),u.add(b);for(var Q=b.getEdges(),d=0;d<Q.length;d++){var Y=Q[d].getOtherEnd(b);if(R.get(b)!=Y)if(!u.has(Y))N.push(Y),R.set(Y,b);else{c=!1;break}}}if(!c)o=[];else{var k=[].concat(v(u));o.push(k);for(var d=0;d<k.length;d++){var D=k[d],rt=M.indexOf(D);rt>-1&&M.splice(rt,1)}u=new Set,R=new Map}}return o},s.prototype.createDummyNodesForBendpoints=function(o){for(var c=[],l=o.source,T=this.graphManager.calcLowestCommonAncestor(o.source,o.target),d=0;d<o.bendpoints.length;d++){var u=this.newNode(null);u.setRect(new Point(0,0),new Dimension(1,1)),T.add(u);var N=this.newEdge(null);this.graphManager.add(N,l,u),c.add(u),l=u}var N=this.newEdge(null);return this.graphManager.add(N,l,o.target),this.edgeToDummyNodes.set(o,c),o.isInterGraph()?this.graphManager.remove(o):T.remove(o),c},s.prototype.createBendpointsFromDummyNodes=function(){var o=[];o=o.concat(this.graphManager.getAllEdges()),o=[].concat(v(this.edgeToDummyNodes.keys())).concat(o);for(var c=0;c<o.length;c++){var l=o[c];if(l.bendpoints.length>0){for(var T=this.edgeToDummyNodes.get(l),d=0;d<T.length;d++){var u=T[d],N=new i(u.getCenterX(),u.getCenterY()),R=l.bendpoints.get(d);R.x=N.x,R.y=N.y,u.getOwner().remove(u)}this.graphManager.add(l,l.source,l.target)}}},s.transform=function(o,c,l,T){if(l!=null&&T!=null){var d=c;if(o<=50){var u=c/l;d-=(c-u)/50*(50-o)}else{var N=c*T;d+=(N-c)/50*(o-50)}return d}else{var R,M;return o<=50?(R=9*c/500,M=c/10):(R=9*c/50,M=-8*c),R*o+M}},s.findCenterOfTree=function(o){var c=[];c=c.concat(o);var l=[],T=new Map,d=!1,u=null;(c.length==1||c.length==2)&&(d=!0,u=c[0]);for(var N=0;N<c.length;N++){var R=c[N],M=R.getNeighborsList().size;T.set(R,R.getNeighborsList().size),M==1&&l.push(R)}var b=[];for(b=b.concat(l);!d;){var Q=[];Q=Q.concat(b),b=[];for(var N=0;N<c.length;N++){var R=c[N],Y=c.indexOf(R);Y>=0&&c.splice(Y,1);var k=R.getNeighborsList();k.forEach(function(n){if(l.indexOf(n)<0){var m=T.get(n),p=m-1;p==1&&b.push(n),T.set(n,p)}})}l=l.concat(b),(c.length==1||c.length==2)&&(d=!0,u=c[0])}return u},s.prototype.setGraphManager=function(o){this.graphManager=o},A.exports=s},function(A,H,L){function v(){}v.seed=1,v.x=0,v.nextDouble=function(){return v.x=Math.sin(v.seed++)*1e4,v.x-Math.floor(v.x)},A.exports=v},function(A,H,L){var v=L(5);function h(a,r){this.lworldOrgX=0,this.lworldOrgY=0,this.ldeviceOrgX=0,this.ldeviceOrgY=0,this.lworldExtX=1,this.lworldExtY=1,this.ldeviceExtX=1,this.ldeviceExtY=1}h.prototype.getWorldOrgX=function(){return this.lworldOrgX},h.prototype.setWorldOrgX=function(a){this.lworldOrgX=a},h.prototype.getWorldOrgY=function(){return this.lworldOrgY},h.prototype.setWorldOrgY=function(a){this.lworldOrgY=a},h.prototype.getWorldExtX=function(){return this.lworldExtX},h.prototype.setWorldExtX=function(a){this.lworldExtX=a},h.prototype.getWorldExtY=function(){return this.lworldExtY},h.prototype.setWorldExtY=function(a){this.lworldExtY=a},h.prototype.getDeviceOrgX=function(){return this.ldeviceOrgX},h.prototype.setDeviceOrgX=function(a){this.ldeviceOrgX=a},h.prototype.getDeviceOrgY=function(){return this.ldeviceOrgY},h.prototype.setDeviceOrgY=function(a){this.ldeviceOrgY=a},h.prototype.getDeviceExtX=function(){return this.ldeviceExtX},h.prototype.setDeviceExtX=function(a){this.ldeviceExtX=a},h.prototype.getDeviceExtY=function(){return this.ldeviceExtY},h.prototype.setDeviceExtY=function(a){this.ldeviceExtY=a},h.prototype.transformX=function(a){var r=0,e=this.lworldExtX;return e!=0&&(r=this.ldeviceOrgX+(a-this.lworldOrgX)*this.ldeviceExtX/e),r},h.prototype.transformY=function(a){var r=0,e=this.lworldExtY;return e!=0&&(r=this.ldeviceOrgY+(a-this.lworldOrgY)*this.ldeviceExtY/e),r},h.prototype.inverseTransformX=function(a){var r=0,e=this.ldeviceExtX;return e!=0&&(r=this.lworldOrgX+(a-this.ldeviceOrgX)*this.lworldExtX/e),r},h.prototype.inverseTransformY=function(a){var r=0,e=this.ldeviceExtY;return e!=0&&(r=this.lworldOrgY+(a-this.ldeviceOrgY)*this.lworldExtY/e),r},h.prototype.inverseTransformPoint=function(a){var r=new v(this.inverseTransformX(a.x),this.inverseTransformY(a.y));return r},A.exports=h},function(A,H,L){function v(t){if(Array.isArray(t)){for(var s=0,o=Array(t.length);s<t.length;s++)o[s]=t[s];return o}else return Array.from(t)}var h=L(15),a=L(4),r=L(0),e=L(8),f=L(9);function i(){h.call(this),this.useSmartIdealEdgeLengthCalculation=a.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.gravityConstant=a.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=a.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=a.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=a.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.displacementThresholdPerNode=3*a.DEFAULT_EDGE_LENGTH/100,this.coolingFactor=a.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.initialCoolingFactor=a.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.totalDisplacement=0,this.oldTotalDisplacement=0,this.maxIterations=a.MAX_ITERATIONS}i.prototype=Object.create(h.prototype);for(var g in h)i[g]=h[g];i.prototype.initParameters=function(){h.prototype.initParameters.call(this,arguments),this.totalIterations=0,this.notAnimatedIterations=0,this.useFRGridVariant=a.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION,this.grid=[]},i.prototype.calcIdealEdgeLengths=function(){for(var t,s,o,c,l,T,d,u=this.getGraphManager().getAllEdges(),N=0;N<u.length;N++)t=u[N],s=t.idealLength,t.isInterGraph&&(c=t.getSource(),l=t.getTarget(),T=t.getSourceInLca().getEstimatedSize(),d=t.getTargetInLca().getEstimatedSize(),this.useSmartIdealEdgeLengthCalculation&&(t.idealLength+=T+d-2*r.SIMPLE_NODE_SIZE),o=t.getLca().getInclusionTreeDepth(),t.idealLength+=s*a.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR*(c.getInclusionTreeDepth()+l.getInclusionTreeDepth()-2*o))},i.prototype.initSpringEmbedder=function(){var t=this.getAllNodes().length;this.incremental?(t>a.ADAPTATION_LOWER_NODE_LIMIT&&(this.coolingFactor=Math.max(this.coolingFactor*a.COOLING_ADAPTATION_FACTOR,this.coolingFactor-(t-a.ADAPTATION_LOWER_NODE_LIMIT)/(a.ADAPTATION_UPPER_NODE_LIMIT-a.ADAPTATION_LOWER_NODE_LIMIT)*this.coolingFactor*(1-a.COOLING_ADAPTATION_FACTOR))),this.maxNodeDisplacement=a.MAX_NODE_DISPLACEMENT_INCREMENTAL):(t>a.ADAPTATION_LOWER_NODE_LIMIT?this.coolingFactor=Math.max(a.COOLING_ADAPTATION_FACTOR,1-(t-a.ADAPTATION_LOWER_NODE_LIMIT)/(a.ADAPTATION_UPPER_NODE_LIMIT-a.ADAPTATION_LOWER_NODE_LIMIT)*(1-a.COOLING_ADAPTATION_FACTOR)):this.coolingFactor=1,this.initialCoolingFactor=this.coolingFactor,this.maxNodeDisplacement=a.MAX_NODE_DISPLACEMENT),this.maxIterations=Math.max(this.getAllNodes().length*5,this.maxIterations),this.displacementThresholdPerNode=3*a.DEFAULT_EDGE_LENGTH/100,this.totalDisplacementThreshold=this.displacementThresholdPerNode*this.getAllNodes().length,this.repulsionRange=this.calcRepulsionRange()},i.prototype.calcSpringForces=function(){for(var t=this.getAllEdges(),s,o=0;o<t.length;o++)s=t[o],this.calcSpringForce(s,s.idealLength)},i.prototype.calcRepulsionForces=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,o,c,l,T,d=this.getAllNodes(),u;if(this.useFRGridVariant)for(this.totalIterations%a.GRID_CALCULATION_CHECK_PERIOD==1&&t&&this.updateGrid(),u=new Set,o=0;o<d.length;o++)l=d[o],this.calculateRepulsionForceOfANode(l,u,t,s),u.add(l);else for(o=0;o<d.length;o++)for(l=d[o],c=o+1;c<d.length;c++)T=d[c],l.getOwner()==T.getOwner()&&this.calcRepulsionForce(l,T)},i.prototype.calcGravitationalForces=function(){for(var t,s=this.getAllNodesToApplyGravitation(),o=0;o<s.length;o++)t=s[o],this.calcGravitationalForce(t)},i.prototype.moveNodes=function(){for(var t=this.getAllNodes(),s,o=0;o<t.length;o++)s=t[o],s.move()},i.prototype.calcSpringForce=function(t,s){var o=t.getSource(),c=t.getTarget(),l,T,d,u;if(this.uniformLeafNodeSizes&&o.getChild()==null&&c.getChild()==null)t.updateLengthSimple();else if(t.updateLength(),t.isOverlapingSourceAndTarget)return;l=t.getLength(),l!=0&&(T=t.edgeElasticity*(l-s),d=T*(t.lengthX/l),u=T*(t.lengthY/l),o.springForceX+=d,o.springForceY+=u,c.springForceX-=d,c.springForceY-=u)},i.prototype.calcRepulsionForce=function(t,s){var o=t.getRect(),c=s.getRect(),l=new Array(2),T=new Array(4),d,u,N,R,M,b,Q;if(o.intersects(c)){e.calcSeparationAmount(o,c,l,a.DEFAULT_EDGE_LENGTH/2),b=2*l[0],Q=2*l[1];var Y=t.noOfChildren*s.noOfChildren/(t.noOfChildren+s.noOfChildren);t.repulsionForceX-=Y*b,t.repulsionForceY-=Y*Q,s.repulsionForceX+=Y*b,s.repulsionForceY+=Y*Q}else this.uniformLeafNodeSizes&&t.getChild()==null&&s.getChild()==null?(d=c.getCenterX()-o.getCenterX(),u=c.getCenterY()-o.getCenterY()):(e.getIntersection(o,c,T),d=T[2]-T[0],u=T[3]-T[1]),Math.abs(d)<a.MIN_REPULSION_DIST&&(d=f.sign(d)*a.MIN_REPULSION_DIST),Math.abs(u)<a.MIN_REPULSION_DIST&&(u=f.sign(u)*a.MIN_REPULSION_DIST),N=d*d+u*u,R=Math.sqrt(N),M=(t.nodeRepulsion/2+s.nodeRepulsion/2)*t.noOfChildren*s.noOfChildren/N,b=M*d/R,Q=M*u/R,t.repulsionForceX-=b,t.repulsionForceY-=Q,s.repulsionForceX+=b,s.repulsionForceY+=Q},i.prototype.calcGravitationalForce=function(t){var s,o,c,l,T,d,u,N;s=t.getOwner(),o=(s.getRight()+s.getLeft())/2,c=(s.getTop()+s.getBottom())/2,l=t.getCenterX()-o,T=t.getCenterY()-c,d=Math.abs(l)+t.getWidth()/2,u=Math.abs(T)+t.getHeight()/2,t.getOwner()==this.graphManager.getRoot()?(N=s.getEstimatedSize()*this.gravityRangeFactor,(d>N||u>N)&&(t.gravitationForceX=-this.gravityConstant*l,t.gravitationForceY=-this.gravityConstant*T)):(N=s.getEstimatedSize()*this.compoundGravityRangeFactor,(d>N||u>N)&&(t.gravitationForceX=-this.gravityConstant*l*this.compoundGravityConstant,t.gravitationForceY=-this.gravityConstant*T*this.compoundGravityConstant))},i.prototype.isConverged=function(){var t,s=!1;return this.totalIterations>this.maxIterations/3&&(s=Math.abs(this.totalDisplacement-this.oldTotalDisplacement)<2),t=this.totalDisplacement<this.totalDisplacementThreshold,this.oldTotalDisplacement=this.totalDisplacement,t||s},i.prototype.animate=function(){this.animationDuringLayout&&!this.isSubLayout&&(this.notAnimatedIterations==this.animationPeriod?(this.update(),this.notAnimatedIterations=0):this.notAnimatedIterations++)},i.prototype.calcNoOfChildrenForAllNodes=function(){for(var t,s=this.graphManager.getAllNodes(),o=0;o<s.length;o++)t=s[o],t.noOfChildren=t.getNoOfChildren()},i.prototype.calcGrid=function(t){var s=0,o=0;s=parseInt(Math.ceil((t.getRight()-t.getLeft())/this.repulsionRange)),o=parseInt(Math.ceil((t.getBottom()-t.getTop())/this.repulsionRange));for(var c=new Array(s),l=0;l<s;l++)c[l]=new Array(o);for(var l=0;l<s;l++)for(var T=0;T<o;T++)c[l][T]=new Array;return c},i.prototype.addNodeToGrid=function(t,s,o){var c=0,l=0,T=0,d=0;c=parseInt(Math.floor((t.getRect().x-s)/this.repulsionRange)),l=parseInt(Math.floor((t.getRect().width+t.getRect().x-s)/this.repulsionRange)),T=parseInt(Math.floor((t.getRect().y-o)/this.repulsionRange)),d=parseInt(Math.floor((t.getRect().height+t.getRect().y-o)/this.repulsionRange));for(var u=c;u<=l;u++)for(var N=T;N<=d;N++)this.grid[u][N].push(t),t.setGridCoordinates(c,l,T,d)},i.prototype.updateGrid=function(){var t,s,o=this.getAllNodes();for(this.grid=this.calcGrid(this.graphManager.getRoot()),t=0;t<o.length;t++)s=o[t],this.addNodeToGrid(s,this.graphManager.getRoot().getLeft(),this.graphManager.getRoot().getTop())},i.prototype.calculateRepulsionForceOfANode=function(t,s,o,c){if(this.totalIterations%a.GRID_CALCULATION_CHECK_PERIOD==1&&o||c){var l=new Set;t.surrounding=new Array;for(var T,d=this.grid,u=t.startX-1;u<t.finishX+2;u++)for(var N=t.startY-1;N<t.finishY+2;N++)if(!(u<0||N<0||u>=d.length||N>=d[0].length)){for(var R=0;R<d[u][N].length;R++)if(T=d[u][N][R],!(t.getOwner()!=T.getOwner()||t==T)&&!s.has(T)&&!l.has(T)){var M=Math.abs(t.getCenterX()-T.getCenterX())-(t.getWidth()/2+T.getWidth()/2),b=Math.abs(t.getCenterY()-T.getCenterY())-(t.getHeight()/2+T.getHeight()/2);M<=this.repulsionRange&&b<=this.repulsionRange&&l.add(T)}}t.surrounding=[].concat(v(l))}for(u=0;u<t.surrounding.length;u++)this.calcRepulsionForce(t,t.surrounding[u])},i.prototype.calcRepulsionRange=function(){return 0},A.exports=i},function(A,H,L){var v=L(1),h=L(4);function a(e,f,i){v.call(this,e,f,i),this.idealLength=h.DEFAULT_EDGE_LENGTH,this.edgeElasticity=h.DEFAULT_SPRING_STRENGTH}a.prototype=Object.create(v.prototype);for(var r in v)a[r]=v[r];A.exports=a},function(A,H,L){var v=L(3),h=L(4);function a(e,f,i,g){v.call(this,e,f,i,g),this.nodeRepulsion=h.DEFAULT_REPULSION_STRENGTH,this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0,this.startX=0,this.finishX=0,this.startY=0,this.finishY=0,this.surrounding=[]}a.prototype=Object.create(v.prototype);for(var r in v)a[r]=v[r];a.prototype.setGridCoordinates=function(e,f,i,g){this.startX=e,this.finishX=f,this.startY=i,this.finishY=g},A.exports=a},function(A,H,L){function v(h,a){this.width=0,this.height=0,h!==null&&a!==null&&(this.height=a,this.width=h)}v.prototype.getWidth=function(){return this.width},v.prototype.setWidth=function(h){this.width=h},v.prototype.getHeight=function(){return this.height},v.prototype.setHeight=function(h){this.height=h},A.exports=v},function(A,H,L){var v=L(14);function h(){this.map={},this.keys=[]}h.prototype.put=function(a,r){var e=v.createID(a);this.contains(e)||(this.map[e]=r,this.keys.push(a))},h.prototype.contains=function(a){return v.createID(a),this.map[a]!=null},h.prototype.get=function(a){var r=v.createID(a);return this.map[r]},h.prototype.keySet=function(){return this.keys},A.exports=h},function(A,H,L){var v=L(14);function h(){this.set={}}h.prototype.add=function(a){var r=v.createID(a);this.contains(r)||(this.set[r]=a)},h.prototype.remove=function(a){delete this.set[v.createID(a)]},h.prototype.clear=function(){this.set={}},h.prototype.contains=function(a){return this.set[v.createID(a)]==a},h.prototype.isEmpty=function(){return this.size()===0},h.prototype.size=function(){return Object.keys(this.set).length},h.prototype.addAllTo=function(a){for(var r=Object.keys(this.set),e=r.length,f=0;f<e;f++)a.push(this.set[r[f]])},h.prototype.size=function(){return Object.keys(this.set).length},h.prototype.addAll=function(a){for(var r=a.length,e=0;e<r;e++){var f=a[e];this.add(f)}},A.exports=h},function(A,H,L){function v(){}v.multMat=function(h,a){for(var r=[],e=0;e<h.length;e++){r[e]=[];for(var f=0;f<a[0].length;f++){r[e][f]=0;for(var i=0;i<h[0].length;i++)r[e][f]+=h[e][i]*a[i][f]}}return r},v.transpose=function(h){for(var a=[],r=0;r<h[0].length;r++){a[r]=[];for(var e=0;e<h.length;e++)a[r][e]=h[e][r]}return a},v.multCons=function(h,a){for(var r=[],e=0;e<h.length;e++)r[e]=h[e]*a;return r},v.minusOp=function(h,a){for(var r=[],e=0;e<h.length;e++)r[e]=h[e]-a[e];return r},v.dotProduct=function(h,a){for(var r=0,e=0;e<h.length;e++)r+=h[e]*a[e];return r},v.mag=function(h){return Math.sqrt(this.dotProduct(h,h))},v.normalize=function(h){for(var a=[],r=this.mag(h),e=0;e<h.length;e++)a[e]=h[e]/r;return a},v.multGamma=function(h){for(var a=[],r=0,e=0;e<h.length;e++)r+=h[e];r*=-1/h.length;for(var f=0;f<h.length;f++)a[f]=r+h[f];return a},v.multL=function(h,a,r){for(var e=[],f=[],i=[],g=0;g<a[0].length;g++){for(var t=0,s=0;s<a.length;s++)t+=-.5*a[s][g]*h[s];f[g]=t}for(var o=0;o<r.length;o++){for(var c=0,l=0;l<r.length;l++)c+=r[o][l]*f[l];i[o]=c}for(var T=0;T<a.length;T++){for(var d=0,u=0;u<a[0].length;u++)d+=a[T][u]*i[u];e[T]=d}return e},A.exports=v},function(A,H,L){var v=function(){function e(f,i){for(var g=0;g<i.length;g++){var t=i[g];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(f,t.key,t)}}return function(f,i,g){return i&&e(f.prototype,i),g&&e(f,g),f}}();function h(e,f){if(!(e instanceof f))throw new TypeError("Cannot call a class as a function")}var a=L(11),r=function(){function e(f,i){h(this,e),(i!==null||i!==void 0)&&(this.compareFunction=this._defaultCompareFunction);var g=void 0;f instanceof a?g=f.size():g=f.length,this._quicksort(f,0,g-1)}return v(e,[{key:"_quicksort",value:function(i,g,t){if(g<t){var s=this._partition(i,g,t);this._quicksort(i,g,s),this._quicksort(i,s+1,t)}}},{key:"_partition",value:function(i,g,t){for(var s=this._get(i,g),o=g,c=t;;){for(;this.compareFunction(s,this._get(i,c));)c--;for(;this.compareFunction(this._get(i,o),s);)o++;if(o<c)this._swap(i,o,c),o++,c--;else return c}}},{key:"_get",value:function(i,g){return i instanceof a?i.get_object_at(g):i[g]}},{key:"_set",value:function(i,g,t){i instanceof a?i.set_object_at(g,t):i[g]=t}},{key:"_swap",value:function(i,g,t){var s=this._get(i,g);this._set(i,g,this._get(i,t)),this._set(i,t,s)}},{key:"_defaultCompareFunction",value:function(i,g){return g>i}}]),e}();A.exports=r},function(A,H,L){function v(){}v.svd=function(h){this.U=null,this.V=null,this.s=null,this.m=0,this.n=0,this.m=h.length,this.n=h[0].length;var a=Math.min(this.m,this.n);this.s=function(Nt){for(var Mt=[];Nt-- >0;)Mt.push(0);return Mt}(Math.min(this.m+1,this.n)),this.U=function(Nt){var Mt=function Zt(Gt){if(Gt.length==0)return 0;for(var $t=[],Ft=0;Ft<Gt[0];Ft++)$t.push(Zt(Gt.slice(1)));return $t};return Mt(Nt)}([this.m,a]),this.V=function(Nt){var Mt=function Zt(Gt){if(Gt.length==0)return 0;for(var $t=[],Ft=0;Ft<Gt[0];Ft++)$t.push(Zt(Gt.slice(1)));return $t};return Mt(Nt)}([this.n,this.n]);for(var r=function(Nt){for(var Mt=[];Nt-- >0;)Mt.push(0);return Mt}(this.n),e=function(Nt){for(var Mt=[];Nt-- >0;)Mt.push(0);return Mt}(this.m),f=!0,i=Math.min(this.m-1,this.n),g=Math.max(0,Math.min(this.n-2,this.m)),t=0;t<Math.max(i,g);t++){if(t<i){this.s[t]=0;for(var s=t;s<this.m;s++)this.s[t]=v.hypot(this.s[t],h[s][t]);if(this.s[t]!==0){h[t][t]<0&&(this.s[t]=-this.s[t]);for(var o=t;o<this.m;o++)h[o][t]/=this.s[t];h[t][t]+=1}this.s[t]=-this.s[t]}for(var c=t+1;c<this.n;c++){if(function(Nt,Mt){return Nt&&Mt}(t<i,this.s[t]!==0)){for(var l=0,T=t;T<this.m;T++)l+=h[T][t]*h[T][c];l=-l/h[t][t];for(var d=t;d<this.m;d++)h[d][c]+=l*h[d][t]}r[c]=h[t][c]}if(function(Nt,Mt){return Nt&&Mt}(f,t<i))for(var u=t;u<this.m;u++)this.U[u][t]=h[u][t];if(t<g){r[t]=0;for(var N=t+1;N<this.n;N++)r[t]=v.hypot(r[t],r[N]);if(r[t]!==0){r[t+1]<0&&(r[t]=-r[t]);for(var R=t+1;R<this.n;R++)r[R]/=r[t];r[t+1]+=1}if(r[t]=-r[t],function(Nt,Mt){return Nt&&Mt}(t+1<this.m,r[t]!==0)){for(var M=t+1;M<this.m;M++)e[M]=0;for(var b=t+1;b<this.n;b++)for(var Q=t+1;Q<this.m;Q++)e[Q]+=r[b]*h[Q][b];for(var Y=t+1;Y<this.n;Y++)for(var k=-r[Y]/r[t+1],D=t+1;D<this.m;D++)h[D][Y]+=k*e[D]}for(var rt=t+1;rt<this.n;rt++)this.V[rt][t]=r[rt]}}var n=Math.min(this.n,this.m+1);i<this.n&&(this.s[i]=h[i][i]),this.m<n&&(this.s[n-1]=0),g+1<n&&(r[g]=h[g][n-1]),r[n-1]=0;{for(var m=i;m<a;m++){for(var p=0;p<this.m;p++)this.U[p][m]=0;this.U[m][m]=1}for(var E=i-1;E>=0;E--)if(this.s[E]!==0){for(var y=E+1;y<a;y++){for(var I=0,w=E;w<this.m;w++)I+=this.U[w][E]*this.U[w][y];I=-I/this.U[E][E];for(var S=E;S<this.m;S++)this.U[S][y]+=I*this.U[S][E]}for(var W=E;W<this.m;W++)this.U[W][E]=-this.U[W][E];this.U[E][E]=1+this.U[E][E];for(var x=0;x<E-1;x++)this.U[x][E]=0}else{for(var q=0;q<this.m;q++)this.U[q][E]=0;this.U[E][E]=1}}for(var V=this.n-1;V>=0;V--){if(function(Nt,Mt){return Nt&&Mt}(V<g,r[V]!==0))for(var U=V+1;U<a;U++){for(var et=0,z=V+1;z<this.n;z++)et+=this.V[z][V]*this.V[z][U];et=-et/this.V[V+1][V];for(var O=V+1;O<this.n;O++)this.V[O][U]+=et*this.V[O][V]}for(var X=0;X<this.n;X++)this.V[X][V]=0;this.V[V][V]=1}for(var B=n-1,_=Math.pow(2,-52),lt=Math.pow(2,-966);n>0;){var J=void 0,Rt=void 0;for(J=n-2;J>=-1&&J!==-1;J--)if(Math.abs(r[J])<=lt+_*(Math.abs(this.s[J])+Math.abs(this.s[J+1]))){r[J]=0;break}if(J===n-2)Rt=4;else{var Lt=void 0;for(Lt=n-1;Lt>=J&&Lt!==J;Lt--){var vt=(Lt!==n?Math.abs(r[Lt]):0)+(Lt!==J+1?Math.abs(r[Lt-1]):0);if(Math.abs(this.s[Lt])<=lt+_*vt){this.s[Lt]=0;break}}Lt===J?Rt=3:Lt===n-1?Rt=1:(Rt=2,J=Lt)}switch(J++,Rt){case 1:{var it=r[n-2];r[n-2]=0;for(var ut=n-2;ut>=J;ut--){var Tt=v.hypot(this.s[ut],it),At=this.s[ut]/Tt,Dt=it/Tt;this.s[ut]=Tt,ut!==J&&(it=-Dt*r[ut-1],r[ut-1]=At*r[ut-1]);for(var mt=0;mt<this.n;mt++)Tt=At*this.V[mt][ut]+Dt*this.V[mt][n-1],this.V[mt][n-1]=-Dt*this.V[mt][ut]+At*this.V[mt][n-1],this.V[mt][ut]=Tt}}break;case 2:{var xt=r[J-1];r[J-1]=0;for(var St=J;St<n;St++){var Vt=v.hypot(this.s[St],xt),Xt=this.s[St]/Vt,Ut=xt/Vt;this.s[St]=Vt,xt=-Ut*r[St],r[St]=Xt*r[St];for(var bt=0;bt<this.m;bt++)Vt=Xt*this.U[bt][St]+Ut*this.U[bt][J-1],this.U[bt][J-1]=-Ut*this.U[bt][St]+Xt*this.U[bt][J-1],this.U[bt][St]=Vt}}break;case 3:{var Ht=Math.max(Math.max(Math.max(Math.max(Math.abs(this.s[n-1]),Math.abs(this.s[n-2])),Math.abs(r[n-2])),Math.abs(this.s[J])),Math.abs(r[J])),Bt=this.s[n-1]/Ht,F=this.s[n-2]/Ht,P=r[n-2]/Ht,$=this.s[J]/Ht,K=r[J]/Ht,Z=((F+Bt)*(F-Bt)+P*P)/2,at=Bt*P*(Bt*P),gt=0;(function(Nt,Mt){return Nt||Mt})(Z!==0,at!==0)&&(gt=Math.sqrt(Z*Z+at),Z<0&&(gt=-gt),gt=at/(Z+gt));for(var ot=($+Bt)*($-Bt)+gt,tt=$*K,j=J;j<n-1;j++){var dt=v.hypot(ot,tt),wt=ot/dt,yt=tt/dt;j!==J&&(r[j-1]=dt),ot=wt*this.s[j]+yt*r[j],r[j]=wt*r[j]-yt*this.s[j],tt=yt*this.s[j+1],this.s[j+1]=wt*this.s[j+1];for(var It=0;It<this.n;It++)dt=wt*this.V[It][j]+yt*this.V[It][j+1],this.V[It][j+1]=-yt*this.V[It][j]+wt*this.V[It][j+1],this.V[It][j]=dt;if(dt=v.hypot(ot,tt),wt=ot/dt,yt=tt/dt,this.s[j]=dt,ot=wt*r[j]+yt*this.s[j+1],this.s[j+1]=-yt*r[j]+wt*this.s[j+1],tt=yt*r[j+1],r[j+1]=wt*r[j+1],j<this.m-1)for(var ft=0;ft<this.m;ft++)dt=wt*this.U[ft][j]+yt*this.U[ft][j+1],this.U[ft][j+1]=-yt*this.U[ft][j]+wt*this.U[ft][j+1],this.U[ft][j]=dt}r[n-2]=ot}break;case 4:{if(this.s[J]<=0){this.s[J]=this.s[J]<0?-this.s[J]:0;for(var st=0;st<=B;st++)this.V[st][J]=-this.V[st][J]}for(;J<B&&!(this.s[J]>=this.s[J+1]);){var Ct=this.s[J];if(this.s[J]=this.s[J+1],this.s[J+1]=Ct,J<this.n-1)for(var ct=0;ct<this.n;ct++)Ct=this.V[ct][J+1],this.V[ct][J+1]=this.V[ct][J],this.V[ct][J]=Ct;if(J<this.m-1)for(var ht=0;ht<this.m;ht++)Ct=this.U[ht][J+1],this.U[ht][J+1]=this.U[ht][J],this.U[ht][J]=Ct;J++}n--}break}}var Wt={U:this.U,V:this.V,S:this.s};return Wt},v.hypot=function(h,a){var r=void 0;return Math.abs(h)>Math.abs(a)?(r=a/h,r=Math.abs(h)*Math.sqrt(1+r*r)):a!=0?(r=h/a,r=Math.abs(a)*Math.sqrt(1+r*r)):r=0,r},A.exports=v},function(A,H,L){var v=function(){function r(e,f){for(var i=0;i<f.length;i++){var g=f[i];g.enumerable=g.enumerable||!1,g.configurable=!0,"value"in g&&(g.writable=!0),Object.defineProperty(e,g.key,g)}}return function(e,f,i){return f&&r(e.prototype,f),i&&r(e,i),e}}();function h(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}var a=function(){function r(e,f){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,g=arguments.length>3&&arguments[3]!==void 0?arguments[3]:-1,t=arguments.length>4&&arguments[4]!==void 0?arguments[4]:-1;h(this,r),this.sequence1=e,this.sequence2=f,this.match_score=i,this.mismatch_penalty=g,this.gap_penalty=t,this.iMax=e.length+1,this.jMax=f.length+1,this.grid=new Array(this.iMax);for(var s=0;s<this.iMax;s++){this.grid[s]=new Array(this.jMax);for(var o=0;o<this.jMax;o++)this.grid[s][o]=0}this.tracebackGrid=new Array(this.iMax);for(var c=0;c<this.iMax;c++){this.tracebackGrid[c]=new Array(this.jMax);for(var l=0;l<this.jMax;l++)this.tracebackGrid[c][l]=[null,null,null]}this.alignments=[],this.score=-1,this.computeGrids()}return v(r,[{key:"getScore",value:function(){return this.score}},{key:"getAlignments",value:function(){return this.alignments}},{key:"computeGrids",value:function(){for(var f=1;f<this.jMax;f++)this.grid[0][f]=this.grid[0][f-1]+this.gap_penalty,this.tracebackGrid[0][f]=[!1,!1,!0];for(var i=1;i<this.iMax;i++)this.grid[i][0]=this.grid[i-1][0]+this.gap_penalty,this.tracebackGrid[i][0]=[!1,!0,!1];for(var g=1;g<this.iMax;g++)for(var t=1;t<this.jMax;t++){var s=void 0;this.sequence1[g-1]===this.sequence2[t-1]?s=this.grid[g-1][t-1]+this.match_score:s=this.grid[g-1][t-1]+this.mismatch_penalty;var o=this.grid[g-1][t]+this.gap_penalty,c=this.grid[g][t-1]+this.gap_penalty,l=[s,o,c],T=this.arrayAllMaxIndexes(l);this.grid[g][t]=l[T[0]],this.tracebackGrid[g][t]=[T.includes(0),T.includes(1),T.includes(2)]}this.score=this.grid[this.iMax-1][this.jMax-1]}},{key:"alignmentTraceback",value:function(){var f=[];for(f.push({pos:[this.sequence1.length,this.sequence2.length],seq1:"",seq2:""});f[0];){var i=f[0],g=this.tracebackGrid[i.pos[0]][i.pos[1]];g[0]&&f.push({pos:[i.pos[0]-1,i.pos[1]-1],seq1:this.sequence1[i.pos[0]-1]+i.seq1,seq2:this.sequence2[i.pos[1]-1]+i.seq2}),g[1]&&f.push({pos:[i.pos[0]-1,i.pos[1]],seq1:this.sequence1[i.pos[0]-1]+i.seq1,seq2:"-"+i.seq2}),g[2]&&f.push({pos:[i.pos[0],i.pos[1]-1],seq1:"-"+i.seq1,seq2:this.sequence2[i.pos[1]-1]+i.seq2}),i.pos[0]===0&&i.pos[1]===0&&this.alignments.push({sequence1:i.seq1,sequence2:i.seq2}),f.shift()}return this.alignments}},{key:"getAllIndexes",value:function(f,i){for(var g=[],t=-1;(t=f.indexOf(i,t+1))!==-1;)g.push(t);return g}},{key:"arrayAllMaxIndexes",value:function(f){return this.getAllIndexes(f,Math.max.apply(null,f))}}]),r}();A.exports=a},function(A,H,L){var v=function(){};v.FDLayout=L(18),v.FDLayoutConstants=L(4),v.FDLayoutEdge=L(19),v.FDLayoutNode=L(20),v.DimensionD=L(21),v.HashMap=L(22),v.HashSet=L(23),v.IGeometry=L(8),v.IMath=L(9),v.Integer=L(10),v.Point=L(12),v.PointD=L(5),v.RandomSeed=L(16),v.RectangleD=L(13),v.Transform=L(17),v.UniqueIDGeneretor=L(14),v.Quicksort=L(25),v.LinkedList=L(11),v.LGraphObject=L(2),v.LGraph=L(6),v.LEdge=L(1),v.LGraphManager=L(7),v.LNode=L(3),v.Layout=L(15),v.LayoutConstants=L(0),v.NeedlemanWunsch=L(27),v.Matrix=L(24),v.SVD=L(26),A.exports=v},function(A,H,L){function v(){this.listeners=[]}var h=v.prototype;h.addListener=function(a,r){this.listeners.push({event:a,callback:r})},h.removeListener=function(a,r){for(var e=this.listeners.length;e>=0;e--){var f=this.listeners[e];f.event===a&&f.callback===r&&this.listeners.splice(e,1)}},h.emit=function(a,r){for(var e=0;e<this.listeners.length;e++){var f=this.listeners[e];a===f.event&&f.callback(r)}},A.exports=v}])})})(be);(function(C,G){(function(H,L){C.exports=L(be.exports)})(me,function(A){return(()=>{var H={45:(a,r,e)=>{var f={};f.layoutBase=e(551),f.CoSEConstants=e(806),f.CoSEEdge=e(767),f.CoSEGraph=e(880),f.CoSEGraphManager=e(578),f.CoSELayout=e(765),f.CoSENode=e(991),f.ConstraintHandler=e(902),a.exports=f},806:(a,r,e)=>{var f=e(551).FDLayoutConstants;function i(){}for(var g in f)i[g]=f[g];i.DEFAULT_USE_MULTI_LEVEL_SCALING=!1,i.DEFAULT_RADIAL_SEPARATION=f.DEFAULT_EDGE_LENGTH,i.DEFAULT_COMPONENT_SEPERATION=60,i.TILE=!0,i.TILING_PADDING_VERTICAL=10,i.TILING_PADDING_HORIZONTAL=10,i.TRANSFORM_ON_CONSTRAINT_HANDLING=!0,i.ENFORCE_CONSTRAINTS=!0,i.APPLY_LAYOUT=!0,i.RELAX_MOVEMENT_ON_CONSTRAINTS=!0,i.TREE_REDUCTION_ON_INCREMENTAL=!0,i.PURE_INCREMENTAL=i.DEFAULT_INCREMENTAL,a.exports=i},767:(a,r,e)=>{var f=e(551).FDLayoutEdge;function i(t,s,o){f.call(this,t,s,o)}i.prototype=Object.create(f.prototype);for(var g in f)i[g]=f[g];a.exports=i},880:(a,r,e)=>{var f=e(551).LGraph;function i(t,s,o){f.call(this,t,s,o)}i.prototype=Object.create(f.prototype);for(var g in f)i[g]=f[g];a.exports=i},578:(a,r,e)=>{var f=e(551).LGraphManager;function i(t){f.call(this,t)}i.prototype=Object.create(f.prototype);for(var g in f)i[g]=f[g];a.exports=i},765:(a,r,e)=>{var f=e(551).FDLayout,i=e(578),g=e(880),t=e(991),s=e(767),o=e(806),c=e(902),l=e(551).FDLayoutConstants,T=e(551).LayoutConstants,d=e(551).Point,u=e(551).PointD,N=e(551).DimensionD,R=e(551).Layout,M=e(551).Integer,b=e(551).IGeometry,Q=e(551).LGraph,Y=e(551).Transform,k=e(551).LinkedList;function D(){f.call(this),this.toBeTiled={},this.constraints={}}D.prototype=Object.create(f.prototype);for(var rt in f)D[rt]=f[rt];D.prototype.newGraphManager=function(){var n=new i(this);return this.graphManager=n,n},D.prototype.newGraph=function(n){return new g(null,this.graphManager,n)},D.prototype.newNode=function(n){return new t(this.graphManager,n)},D.prototype.newEdge=function(n){return new s(null,null,n)},D.prototype.initParameters=function(){f.prototype.initParameters.call(this,arguments),this.isSubLayout||(o.DEFAULT_EDGE_LENGTH<10?this.idealEdgeLength=10:this.idealEdgeLength=o.DEFAULT_EDGE_LENGTH,this.useSmartIdealEdgeLengthCalculation=o.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.gravityConstant=l.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=l.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=l.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=l.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.prunedNodesAll=[],this.growTreeIterations=0,this.afterGrowthIterations=0,this.isTreeGrowing=!1,this.isGrowthFinished=!1)},D.prototype.initSpringEmbedder=function(){f.prototype.initSpringEmbedder.call(this),this.coolingCycle=0,this.maxCoolingCycle=this.maxIterations/l.CONVERGENCE_CHECK_PERIOD,this.finalTemperature=.04,this.coolingAdjuster=1},D.prototype.layout=function(){var n=T.DEFAULT_CREATE_BENDS_AS_NEEDED;return n&&(this.createBendpoints(),this.graphManager.resetAllEdges()),this.level=0,this.classicLayout()},D.prototype.classicLayout=function(){if(this.nodesWithGravity=this.calculateNodesToApplyGravitationTo(),this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity),this.calcNoOfChildrenForAllNodes(),this.graphManager.calcLowestCommonAncestors(),this.graphManager.calcInclusionTreeDepths(),this.graphManager.getRoot().calcEstimatedSize(),this.calcIdealEdgeLengths(),this.incremental){if(o.TREE_REDUCTION_ON_INCREMENTAL){this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var m=new Set(this.getAllNodes()),p=this.nodesWithGravity.filter(function(I){return m.has(I)});this.graphManager.setAllNodesToApplyGravitation(p)}}else{var n=this.getFlatForest();if(n.length>0)this.positionNodesRadially(n);else{this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var m=new Set(this.getAllNodes()),p=this.nodesWithGravity.filter(function(E){return m.has(E)});this.graphManager.setAllNodesToApplyGravitation(p),this.positionNodesRandomly()}}return Object.keys(this.constraints).length>0&&(c.handleConstraints(this),this.initConstraintVariables()),this.initSpringEmbedder(),o.APPLY_LAYOUT&&this.runSpringEmbedder(),!0},D.prototype.tick=function(){if(this.totalIterations++,this.totalIterations===this.maxIterations&&!this.isTreeGrowing&&!this.isGrowthFinished)if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;if(this.totalIterations%l.CONVERGENCE_CHECK_PERIOD==0&&!this.isTreeGrowing&&!this.isGrowthFinished){if(this.isConverged())if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;this.coolingCycle++,this.layoutQuality==0?this.coolingAdjuster=this.coolingCycle:this.layoutQuality==1&&(this.coolingAdjuster=this.coolingCycle/3),this.coolingFactor=Math.max(this.initialCoolingFactor-Math.pow(this.coolingCycle,Math.log(100*(this.initialCoolingFactor-this.finalTemperature))/Math.log(this.maxCoolingCycle))/100*this.coolingAdjuster,this.finalTemperature),this.animationPeriod=Math.ceil(this.initialAnimationPeriod*Math.sqrt(this.coolingFactor))}if(this.isTreeGrowing){if(this.growTreeIterations%10==0)if(this.prunedNodesAll.length>0){this.graphManager.updateBounds(),this.updateGrid(),this.growTree(this.prunedNodesAll),this.graphManager.resetAllNodesToApplyGravitation();var n=new Set(this.getAllNodes()),m=this.nodesWithGravity.filter(function(y){return n.has(y)});this.graphManager.setAllNodesToApplyGravitation(m),this.graphManager.updateBounds(),this.updateGrid(),o.PURE_INCREMENTAL?this.coolingFactor=l.DEFAULT_COOLING_FACTOR_INCREMENTAL/2:this.coolingFactor=l.DEFAULT_COOLING_FACTOR_INCREMENTAL}else this.isTreeGrowing=!1,this.isGrowthFinished=!0;this.growTreeIterations++}if(this.isGrowthFinished){if(this.isConverged())return!0;this.afterGrowthIterations%10==0&&(this.graphManager.updateBounds(),this.updateGrid()),o.PURE_INCREMENTAL?this.coolingFactor=l.DEFAULT_COOLING_FACTOR_INCREMENTAL/2*((100-this.afterGrowthIterations)/100):this.coolingFactor=l.DEFAULT_COOLING_FACTOR_INCREMENTAL*((100-this.afterGrowthIterations)/100),this.afterGrowthIterations++}var p=!this.isTreeGrowing&&!this.isGrowthFinished,E=this.growTreeIterations%10==1&&this.isTreeGrowing||this.afterGrowthIterations%10==1&&this.isGrowthFinished;return this.totalDisplacement=0,this.graphManager.updateBounds(),this.calcSpringForces(),this.calcRepulsionForces(p,E),this.calcGravitationalForces(),this.moveNodes(),this.animate(),!1},D.prototype.getPositionsData=function(){for(var n=this.graphManager.getAllNodes(),m={},p=0;p<n.length;p++){var E=n[p].rect,y=n[p].id;m[y]={id:y,x:E.getCenterX(),y:E.getCenterY(),w:E.width,h:E.height}}return m},D.prototype.runSpringEmbedder=function(){this.initialAnimationPeriod=25,this.animationPeriod=this.initialAnimationPeriod;var n=!1;if(l.ANIMATE==="during")this.emit("layoutstarted");else{for(;!n;)n=this.tick();this.graphManager.updateBounds()}},D.prototype.moveNodes=function(){for(var n=this.getAllNodes(),m,p=0;p<n.length;p++)m=n[p],m.calculateDisplacement();Object.keys(this.constraints).length>0&&this.updateDisplacements();for(var p=0;p<n.length;p++)m=n[p],m.move()},D.prototype.initConstraintVariables=function(){var n=this;this.idToNodeMap=new Map,this.fixedNodeSet=new Set;for(var m=this.graphManager.getAllNodes(),p=0;p<m.length;p++){var E=m[p];this.idToNodeMap.set(E.id,E)}var y=function O(X){for(var B=X.getChild().getNodes(),_,lt=0,J=0;J<B.length;J++)_=B[J],_.getChild()==null?n.fixedNodeSet.has(_.id)&&(lt+=100):lt+=O(_);return lt};if(this.constraints.fixedNodeConstraint){this.constraints.fixedNodeConstraint.forEach(function(B){n.fixedNodeSet.add(B.nodeId)});for(var m=this.graphManager.getAllNodes(),E,p=0;p<m.length;p++)if(E=m[p],E.getChild()!=null){var I=y(E);I>0&&(E.fixedNodeWeight=I)}}if(this.constraints.relativePlacementConstraint){var w=new Map,S=new Map;if(this.dummyToNodeForVerticalAlignment=new Map,this.dummyToNodeForHorizontalAlignment=new Map,this.fixedNodesOnHorizontal=new Set,this.fixedNodesOnVertical=new Set,this.fixedNodeSet.forEach(function(O){n.fixedNodesOnHorizontal.add(O),n.fixedNodesOnVertical.add(O)}),this.constraints.alignmentConstraint){if(this.constraints.alignmentConstraint.vertical)for(var W=this.constraints.alignmentConstraint.vertical,p=0;p<W.length;p++)this.dummyToNodeForVerticalAlignment.set("dummy"+p,[]),W[p].forEach(function(X){w.set(X,"dummy"+p),n.dummyToNodeForVerticalAlignment.get("dummy"+p).push(X),n.fixedNodeSet.has(X)&&n.fixedNodesOnHorizontal.add("dummy"+p)});if(this.constraints.alignmentConstraint.horizontal)for(var x=this.constraints.alignmentConstraint.horizontal,p=0;p<x.length;p++)this.dummyToNodeForHorizontalAlignment.set("dummy"+p,[]),x[p].forEach(function(X){S.set(X,"dummy"+p),n.dummyToNodeForHorizontalAlignment.get("dummy"+p).push(X),n.fixedNodeSet.has(X)&&n.fixedNodesOnVertical.add("dummy"+p)})}if(o.RELAX_MOVEMENT_ON_CONSTRAINTS)this.shuffle=function(O){var X,B,_;for(_=O.length-1;_>=2*O.length/3;_--)X=Math.floor(Math.random()*(_+1)),B=O[_],O[_]=O[X],O[X]=B;return O},this.nodesInRelativeHorizontal=[],this.nodesInRelativeVertical=[],this.nodeToRelativeConstraintMapHorizontal=new Map,this.nodeToRelativeConstraintMapVertical=new Map,this.nodeToTempPositionMapHorizontal=new Map,this.nodeToTempPositionMapVertical=new Map,this.constraints.relativePlacementConstraint.forEach(function(O){if(O.left){var X=w.has(O.left)?w.get(O.left):O.left,B=w.has(O.right)?w.get(O.right):O.right;n.nodesInRelativeHorizontal.includes(X)||(n.nodesInRelativeHorizontal.push(X),n.nodeToRelativeConstraintMapHorizontal.set(X,[]),n.dummyToNodeForVerticalAlignment.has(X)?n.nodeToTempPositionMapHorizontal.set(X,n.idToNodeMap.get(n.dummyToNodeForVerticalAlignment.get(X)[0]).getCenterX()):n.nodeToTempPositionMapHorizontal.set(X,n.idToNodeMap.get(X).getCenterX())),n.nodesInRelativeHorizontal.includes(B)||(n.nodesInRelativeHorizontal.push(B),n.nodeToRelativeConstraintMapHorizontal.set(B,[]),n.dummyToNodeForVerticalAlignment.has(B)?n.nodeToTempPositionMapHorizontal.set(B,n.idToNodeMap.get(n.dummyToNodeForVerticalAlignment.get(B)[0]).getCenterX()):n.nodeToTempPositionMapHorizontal.set(B,n.idToNodeMap.get(B).getCenterX())),n.nodeToRelativeConstraintMapHorizontal.get(X).push({right:B,gap:O.gap}),n.nodeToRelativeConstraintMapHorizontal.get(B).push({left:X,gap:O.gap})}else{var _=S.has(O.top)?S.get(O.top):O.top,lt=S.has(O.bottom)?S.get(O.bottom):O.bottom;n.nodesInRelativeVertical.includes(_)||(n.nodesInRelativeVertical.push(_),n.nodeToRelativeConstraintMapVertical.set(_,[]),n.dummyToNodeForHorizontalAlignment.has(_)?n.nodeToTempPositionMapVertical.set(_,n.idToNodeMap.get(n.dummyToNodeForHorizontalAlignment.get(_)[0]).getCenterY()):n.nodeToTempPositionMapVertical.set(_,n.idToNodeMap.get(_).getCenterY())),n.nodesInRelativeVertical.includes(lt)||(n.nodesInRelativeVertical.push(lt),n.nodeToRelativeConstraintMapVertical.set(lt,[]),n.dummyToNodeForHorizontalAlignment.has(lt)?n.nodeToTempPositionMapVertical.set(lt,n.idToNodeMap.get(n.dummyToNodeForHorizontalAlignment.get(lt)[0]).getCenterY()):n.nodeToTempPositionMapVertical.set(lt,n.idToNodeMap.get(lt).getCenterY())),n.nodeToRelativeConstraintMapVertical.get(_).push({bottom:lt,gap:O.gap}),n.nodeToRelativeConstraintMapVertical.get(lt).push({top:_,gap:O.gap})}});else{var q=new Map,V=new Map;this.constraints.relativePlacementConstraint.forEach(function(O){if(O.left){var X=w.has(O.left)?w.get(O.left):O.left,B=w.has(O.right)?w.get(O.right):O.right;q.has(X)?q.get(X).push(B):q.set(X,[B]),q.has(B)?q.get(B).push(X):q.set(B,[X])}else{var _=S.has(O.top)?S.get(O.top):O.top,lt=S.has(O.bottom)?S.get(O.bottom):O.bottom;V.has(_)?V.get(_).push(lt):V.set(_,[lt]),V.has(lt)?V.get(lt).push(_):V.set(lt,[_])}});var U=function(X,B){var _=[],lt=[],J=new k,Rt=new Set,Lt=0;return X.forEach(function(vt,it){if(!Rt.has(it)){_[Lt]=[],lt[Lt]=!1;var ut=it;for(J.push(ut),Rt.add(ut),_[Lt].push(ut);J.length!=0;){ut=J.shift(),B.has(ut)&&(lt[Lt]=!0);var Tt=X.get(ut);Tt.forEach(function(At){Rt.has(At)||(J.push(At),Rt.add(At),_[Lt].push(At))})}Lt++}}),{components:_,isFixed:lt}},et=U(q,n.fixedNodesOnHorizontal);this.componentsOnHorizontal=et.components,this.fixedComponentsOnHorizontal=et.isFixed;var z=U(V,n.fixedNodesOnVertical);this.componentsOnVertical=z.components,this.fixedComponentsOnVertical=z.isFixed}}},D.prototype.updateDisplacements=function(){var n=this;if(this.constraints.fixedNodeConstraint&&this.constraints.fixedNodeConstraint.forEach(function(z){var O=n.idToNodeMap.get(z.nodeId);O.displacementX=0,O.displacementY=0}),this.constraints.alignmentConstraint){if(this.constraints.alignmentConstraint.vertical)for(var m=this.constraints.alignmentConstraint.vertical,p=0;p<m.length;p++){for(var E=0,y=0;y<m[p].length;y++){if(this.fixedNodeSet.has(m[p][y])){E=0;break}E+=this.idToNodeMap.get(m[p][y]).displacementX}for(var I=E/m[p].length,y=0;y<m[p].length;y++)this.idToNodeMap.get(m[p][y]).displacementX=I}if(this.constraints.alignmentConstraint.horizontal)for(var w=this.constraints.alignmentConstraint.horizontal,p=0;p<w.length;p++){for(var S=0,y=0;y<w[p].length;y++){if(this.fixedNodeSet.has(w[p][y])){S=0;break}S+=this.idToNodeMap.get(w[p][y]).displacementY}for(var W=S/w[p].length,y=0;y<w[p].length;y++)this.idToNodeMap.get(w[p][y]).displacementY=W}}if(this.constraints.relativePlacementConstraint)if(o.RELAX_MOVEMENT_ON_CONSTRAINTS)this.totalIterations%10==0&&(this.shuffle(this.nodesInRelativeHorizontal),this.shuffle(this.nodesInRelativeVertical)),this.nodesInRelativeHorizontal.forEach(function(z){if(!n.fixedNodesOnHorizontal.has(z)){var O=0;n.dummyToNodeForVerticalAlignment.has(z)?O=n.idToNodeMap.get(n.dummyToNodeForVerticalAlignment.get(z)[0]).displacementX:O=n.idToNodeMap.get(z).displacementX,n.nodeToRelativeConstraintMapHorizontal.get(z).forEach(function(X){if(X.right){var B=n.nodeToTempPositionMapHorizontal.get(X.right)-n.nodeToTempPositionMapHorizontal.get(z)-O;B<X.gap&&(O-=X.gap-B)}else{var B=n.nodeToTempPositionMapHorizontal.get(z)-n.nodeToTempPositionMapHorizontal.get(X.left)+O;B<X.gap&&(O+=X.gap-B)}}),n.nodeToTempPositionMapHorizontal.set(z,n.nodeToTempPositionMapHorizontal.get(z)+O),n.dummyToNodeForVerticalAlignment.has(z)?n.dummyToNodeForVerticalAlignment.get(z).forEach(function(X){n.idToNodeMap.get(X).displacementX=O}):n.idToNodeMap.get(z).displacementX=O}}),this.nodesInRelativeVertical.forEach(function(z){if(!n.fixedNodesOnHorizontal.has(z)){var O=0;n.dummyToNodeForHorizontalAlignment.has(z)?O=n.idToNodeMap.get(n.dummyToNodeForHorizontalAlignment.get(z)[0]).displacementY:O=n.idToNodeMap.get(z).displacementY,n.nodeToRelativeConstraintMapVertical.get(z).forEach(function(X){if(X.bottom){var B=n.nodeToTempPositionMapVertical.get(X.bottom)-n.nodeToTempPositionMapVertical.get(z)-O;B<X.gap&&(O-=X.gap-B)}else{var B=n.nodeToTempPositionMapVertical.get(z)-n.nodeToTempPositionMapVertical.get(X.top)+O;B<X.gap&&(O+=X.gap-B)}}),n.nodeToTempPositionMapVertical.set(z,n.nodeToTempPositionMapVertical.get(z)+O),n.dummyToNodeForHorizontalAlignment.has(z)?n.dummyToNodeForHorizontalAlignment.get(z).forEach(function(X){n.idToNodeMap.get(X).displacementY=O}):n.idToNodeMap.get(z).displacementY=O}});else{for(var p=0;p<this.componentsOnHorizontal.length;p++){var x=this.componentsOnHorizontal[p];if(this.fixedComponentsOnHorizontal[p])for(var y=0;y<x.length;y++)this.dummyToNodeForVerticalAlignment.has(x[y])?this.dummyToNodeForVerticalAlignment.get(x[y]).forEach(function(X){n.idToNodeMap.get(X).displacementX=0}):this.idToNodeMap.get(x[y]).displacementX=0;else{for(var q=0,V=0,y=0;y<x.length;y++)if(this.dummyToNodeForVerticalAlignment.has(x[y])){var U=this.dummyToNodeForVerticalAlignment.get(x[y]);q+=U.length*this.idToNodeMap.get(U[0]).displacementX,V+=U.length}else q+=this.idToNodeMap.get(x[y]).displacementX,V++;for(var et=q/V,y=0;y<x.length;y++)this.dummyToNodeForVerticalAlignment.has(x[y])?this.dummyToNodeForVerticalAlignment.get(x[y]).forEach(function(X){n.idToNodeMap.get(X).displacementX=et}):this.idToNodeMap.get(x[y]).displacementX=et}}for(var p=0;p<this.componentsOnVertical.length;p++){var x=this.componentsOnVertical[p];if(this.fixedComponentsOnVertical[p])for(var y=0;y<x.length;y++)this.dummyToNodeForHorizontalAlignment.has(x[y])?this.dummyToNodeForHorizontalAlignment.get(x[y]).forEach(function(B){n.idToNodeMap.get(B).displacementY=0}):this.idToNodeMap.get(x[y]).displacementY=0;else{for(var q=0,V=0,y=0;y<x.length;y++)if(this.dummyToNodeForHorizontalAlignment.has(x[y])){var U=this.dummyToNodeForHorizontalAlignment.get(x[y]);q+=U.length*this.idToNodeMap.get(U[0]).displacementY,V+=U.length}else q+=this.idToNodeMap.get(x[y]).displacementY,V++;for(var et=q/V,y=0;y<x.length;y++)this.dummyToNodeForHorizontalAlignment.has(x[y])?this.dummyToNodeForHorizontalAlignment.get(x[y]).forEach(function(J){n.idToNodeMap.get(J).displacementY=et}):this.idToNodeMap.get(x[y]).displacementY=et}}}},D.prototype.calculateNodesToApplyGravitationTo=function(){var n=[],m,p=this.graphManager.getGraphs(),E=p.length,y;for(y=0;y<E;y++)m=p[y],m.updateConnected(),m.isConnected||(n=n.concat(m.getNodes()));return n},D.prototype.createBendpoints=function(){var n=[];n=n.concat(this.graphManager.getAllEdges());var m=new Set,p;for(p=0;p<n.length;p++){var E=n[p];if(!m.has(E)){var y=E.getSource(),I=E.getTarget();if(y==I)E.getBendpoints().push(new u),E.getBendpoints().push(new u),this.createDummyNodesForBendpoints(E),m.add(E);else{var w=[];if(w=w.concat(y.getEdgeListToNode(I)),w=w.concat(I.getEdgeListToNode(y)),!m.has(w[0])){if(w.length>1){var S;for(S=0;S<w.length;S++){var W=w[S];W.getBendpoints().push(new u),this.createDummyNodesForBendpoints(W)}}w.forEach(function(x){m.add(x)})}}}if(m.size==n.length)break}},D.prototype.positionNodesRadially=function(n){for(var m=new d(0,0),p=Math.ceil(Math.sqrt(n.length)),E=0,y=0,I=0,w=new u(0,0),S=0;S<n.length;S++){S%p==0&&(I=0,y=E,S!=0&&(y+=o.DEFAULT_COMPONENT_SEPERATION),E=0);var W=n[S],x=R.findCenterOfTree(W);m.x=I,m.y=y,w=D.radialLayout(W,x,m),w.y>E&&(E=Math.floor(w.y)),I=Math.floor(w.x+o.DEFAULT_COMPONENT_SEPERATION)}this.transform(new u(T.WORLD_CENTER_X-w.x/2,T.WORLD_CENTER_Y-w.y/2))},D.radialLayout=function(n,m,p){var E=Math.max(this.maxDiagonalInTree(n),o.DEFAULT_RADIAL_SEPARATION);D.branchRadialLayout(m,null,0,359,0,E);var y=Q.calculateBounds(n),I=new Y;I.setDeviceOrgX(y.getMinX()),I.setDeviceOrgY(y.getMinY()),I.setWorldOrgX(p.x),I.setWorldOrgY(p.y);for(var w=0;w<n.length;w++){var S=n[w];S.transform(I)}var W=new u(y.getMaxX(),y.getMaxY());return I.inverseTransformPoint(W)},D.branchRadialLayout=function(n,m,p,E,y,I){var w=(E-p+1)/2;w<0&&(w+=180);var S=(w+p)%360,W=S*b.TWO_PI/360,x=y*Math.cos(W),q=y*Math.sin(W);n.setCenter(x,q);var V=[];V=V.concat(n.getEdges());var U=V.length;m!=null&&U--;for(var et=0,z=V.length,O,X=n.getEdgesBetween(m);X.length>1;){var B=X[0];X.splice(0,1);var _=V.indexOf(B);_>=0&&V.splice(_,1),z--,U--}m!=null?O=(V.indexOf(X[0])+1)%z:O=0;for(var lt=Math.abs(E-p)/U,J=O;et!=U;J=++J%z){var Rt=V[J].getOtherEnd(n);if(Rt!=m){var Lt=(p+et*lt)%360,vt=(Lt+lt)%360;D.branchRadialLayout(Rt,n,Lt,vt,y+I,I),et++}}},D.maxDiagonalInTree=function(n){for(var m=M.MIN_VALUE,p=0;p<n.length;p++){var E=n[p],y=E.getDiagonal();y>m&&(m=y)}return m},D.prototype.calcRepulsionRange=function(){return 2*(this.level+1)*this.idealEdgeLength},D.prototype.groupZeroDegreeMembers=function(){var n=this,m={};this.memberGroups={},this.idToDummyNode={};for(var p=[],E=this.graphManager.getAllNodes(),y=0;y<E.length;y++){var I=E[y],w=I.getParent();this.getNodeDegreeWithChildren(I)===0&&(w.id==null||!this.getToBeTiled(w))&&p.push(I)}for(var y=0;y<p.length;y++){var I=p[y],S=I.getParent().id;typeof m[S]=="undefined"&&(m[S]=[]),m[S]=m[S].concat(I)}Object.keys(m).forEach(function(W){if(m[W].length>1){var x="DummyCompound_"+W;n.memberGroups[x]=m[W];var q=m[W][0].getParent(),V=new t(n.graphManager);V.id=x,V.paddingLeft=q.paddingLeft||0,V.paddingRight=q.paddingRight||0,V.paddingBottom=q.paddingBottom||0,V.paddingTop=q.paddingTop||0,n.idToDummyNode[x]=V;var U=n.getGraphManager().add(n.newGraph(),V),et=q.getChild();et.add(V);for(var z=0;z<m[W].length;z++){var O=m[W][z];et.remove(O),U.add(O)}}})},D.prototype.clearCompounds=function(){var n={},m={};this.performDFSOnCompounds();for(var p=0;p<this.compoundOrder.length;p++)m[this.compoundOrder[p].id]=this.compoundOrder[p],n[this.compoundOrder[p].id]=[].concat(this.compoundOrder[p].getChild().getNodes()),this.graphManager.remove(this.compoundOrder[p].getChild()),this.compoundOrder[p].child=null;this.graphManager.resetAllNodes(),this.tileCompoundMembers(n,m)},D.prototype.clearZeroDegreeMembers=function(){var n=this,m=this.tiledZeroDegreePack=[];Object.keys(this.memberGroups).forEach(function(p){var E=n.idToDummyNode[p];if(m[p]=n.tileNodes(n.memberGroups[p],E.paddingLeft+E.paddingRight),E.rect.width=m[p].width,E.rect.height=m[p].height,E.setCenter(m[p].centerX,m[p].centerY),E.labelMarginLeft=0,E.labelMarginTop=0,o.NODE_DIMENSIONS_INCLUDE_LABELS){var y=E.rect.width,I=E.rect.height;E.labelWidth&&(E.labelPosHorizontal=="left"?(E.rect.x-=E.labelWidth,E.setWidth(y+E.labelWidth),E.labelMarginLeft=E.labelWidth):E.labelPosHorizontal=="center"&&E.labelWidth>y?(E.rect.x-=(E.labelWidth-y)/2,E.setWidth(E.labelWidth),E.labelMarginLeft=(E.labelWidth-y)/2):E.labelPosHorizontal=="right"&&E.setWidth(y+E.labelWidth)),E.labelHeight&&(E.labelPosVertical=="top"?(E.rect.y-=E.labelHeight,E.setHeight(I+E.labelHeight),E.labelMarginTop=E.labelHeight):E.labelPosVertical=="center"&&E.labelHeight>I?(E.rect.y-=(E.labelHeight-I)/2,E.setHeight(E.labelHeight),E.labelMarginTop=(E.labelHeight-I)/2):E.labelPosVertical=="bottom"&&E.setHeight(I+E.labelHeight))}})},D.prototype.repopulateCompounds=function(){for(var n=this.compoundOrder.length-1;n>=0;n--){var m=this.compoundOrder[n],p=m.id,E=m.paddingLeft,y=m.paddingTop,I=m.labelMarginLeft,w=m.labelMarginTop;this.adjustLocations(this.tiledMemberPack[p],m.rect.x,m.rect.y,E,y,I,w)}},D.prototype.repopulateZeroDegreeMembers=function(){var n=this,m=this.tiledZeroDegreePack;Object.keys(m).forEach(function(p){var E=n.idToDummyNode[p],y=E.paddingLeft,I=E.paddingTop,w=E.labelMarginLeft,S=E.labelMarginTop;n.adjustLocations(m[p],E.rect.x,E.rect.y,y,I,w,S)})},D.prototype.getToBeTiled=function(n){var m=n.id;if(this.toBeTiled[m]!=null)return this.toBeTiled[m];var p=n.getChild();if(p==null)return this.toBeTiled[m]=!1,!1;for(var E=p.getNodes(),y=0;y<E.length;y++){var I=E[y];if(this.getNodeDegree(I)>0)return this.toBeTiled[m]=!1,!1;if(I.getChild()==null){this.toBeTiled[I.id]=!1;continue}if(!this.getToBeTiled(I))return this.toBeTiled[m]=!1,!1}return this.toBeTiled[m]=!0,!0},D.prototype.getNodeDegree=function(n){n.id;for(var m=n.getEdges(),p=0,E=0;E<m.length;E++){var y=m[E];y.getSource().id!==y.getTarget().id&&(p=p+1)}return p},D.prototype.getNodeDegreeWithChildren=function(n){var m=this.getNodeDegree(n);if(n.getChild()==null)return m;for(var p=n.getChild().getNodes(),E=0;E<p.length;E++){var y=p[E];m+=this.getNodeDegreeWithChildren(y)}return m},D.prototype.performDFSOnCompounds=function(){this.compoundOrder=[],this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes())},D.prototype.fillCompexOrderByDFS=function(n){for(var m=0;m<n.length;m++){var p=n[m];p.getChild()!=null&&this.fillCompexOrderByDFS(p.getChild().getNodes()),this.getToBeTiled(p)&&this.compoundOrder.push(p)}},D.prototype.adjustLocations=function(n,m,p,E,y,I,w){m+=E+I,p+=y+w;for(var S=m,W=0;W<n.rows.length;W++){var x=n.rows[W];m=S;for(var q=0,V=0;V<x.length;V++){var U=x[V];U.rect.x=m,U.rect.y=p,m+=U.rect.width+n.horizontalPadding,U.rect.height>q&&(q=U.rect.height)}p+=q+n.verticalPadding}},D.prototype.tileCompoundMembers=function(n,m){var p=this;this.tiledMemberPack=[],Object.keys(n).forEach(function(E){var y=m[E];if(p.tiledMemberPack[E]=p.tileNodes(n[E],y.paddingLeft+y.paddingRight),y.rect.width=p.tiledMemberPack[E].width,y.rect.height=p.tiledMemberPack[E].height,y.setCenter(p.tiledMemberPack[E].centerX,p.tiledMemberPack[E].centerY),y.labelMarginLeft=0,y.labelMarginTop=0,o.NODE_DIMENSIONS_INCLUDE_LABELS){var I=y.rect.width,w=y.rect.height;y.labelWidth&&(y.labelPosHorizontal=="left"?(y.rect.x-=y.labelWidth,y.setWidth(I+y.labelWidth),y.labelMarginLeft=y.labelWidth):y.labelPosHorizontal=="center"&&y.labelWidth>I?(y.rect.x-=(y.labelWidth-I)/2,y.setWidth(y.labelWidth),y.labelMarginLeft=(y.labelWidth-I)/2):y.labelPosHorizontal=="right"&&y.setWidth(I+y.labelWidth)),y.labelHeight&&(y.labelPosVertical=="top"?(y.rect.y-=y.labelHeight,y.setHeight(w+y.labelHeight),y.labelMarginTop=y.labelHeight):y.labelPosVertical=="center"&&y.labelHeight>w?(y.rect.y-=(y.labelHeight-w)/2,y.setHeight(y.labelHeight),y.labelMarginTop=(y.labelHeight-w)/2):y.labelPosVertical=="bottom"&&y.setHeight(w+y.labelHeight))}})},D.prototype.tileNodes=function(n,m){var p=this.tileNodesByFavoringDim(n,m,!0),E=this.tileNodesByFavoringDim(n,m,!1),y=this.getOrgRatio(p),I=this.getOrgRatio(E),w;return I<y?w=E:w=p,w},D.prototype.getOrgRatio=function(n){var m=n.width,p=n.height,E=m/p;return E<1&&(E=1/E),E},D.prototype.calcIdealRowWidth=function(n,m){var p=o.TILING_PADDING_VERTICAL,E=o.TILING_PADDING_HORIZONTAL,y=n.length,I=0,w=0,S=0;n.forEach(function(z){I+=z.getWidth(),w+=z.getHeight(),z.getWidth()>S&&(S=z.getWidth())});var W=I/y,x=w/y,q=Math.pow(p-E,2)+4*(W+E)*(x+p)*y,V=(E-p+Math.sqrt(q))/(2*(W+E)),U;m?(U=Math.ceil(V),U==V&&U++):U=Math.floor(V);var et=U*(W+E)-E;return S>et&&(et=S),et+=E*2,et},D.prototype.tileNodesByFavoringDim=function(n,m,p){var E=o.TILING_PADDING_VERTICAL,y=o.TILING_PADDING_HORIZONTAL,I=o.TILING_COMPARE_BY,w={rows:[],rowWidth:[],rowHeight:[],width:0,height:m,verticalPadding:E,horizontalPadding:y,centerX:0,centerY:0};I&&(w.idealRowWidth=this.calcIdealRowWidth(n,p));var S=function(O){return O.rect.width*O.rect.height},W=function(O,X){return S(X)-S(O)};n.sort(function(z,O){var X=W;return w.idealRowWidth?(X=I,X(z.id,O.id)):X(z,O)});for(var x=0,q=0,V=0;V<n.length;V++){var U=n[V];x+=U.getCenterX(),q+=U.getCenterY()}w.centerX=x/n.length,w.centerY=q/n.length;for(var V=0;V<n.length;V++){var U=n[V];if(w.rows.length==0)this.insertNodeToRow(w,U,0,m);else if(this.canAddHorizontal(w,U.rect.width,U.rect.height)){var et=w.rows.length-1;w.idealRowWidth||(et=this.getShortestRowIndex(w)),this.insertNodeToRow(w,U,et,m)}else this.insertNodeToRow(w,U,w.rows.length,m);this.shiftToLastRow(w)}return w},D.prototype.insertNodeToRow=function(n,m,p,E){var y=E;if(p==n.rows.length){var I=[];n.rows.push(I),n.rowWidth.push(y),n.rowHeight.push(0)}var w=n.rowWidth[p]+m.rect.width;n.rows[p].length>0&&(w+=n.horizontalPadding),n.rowWidth[p]=w,n.width<w&&(n.width=w);var S=m.rect.height;p>0&&(S+=n.verticalPadding);var W=0;S>n.rowHeight[p]&&(W=n.rowHeight[p],n.rowHeight[p]=S,W=n.rowHeight[p]-W),n.height+=W,n.rows[p].push(m)},D.prototype.getShortestRowIndex=function(n){for(var m=-1,p=Number.MAX_VALUE,E=0;E<n.rows.length;E++)n.rowWidth[E]<p&&(m=E,p=n.rowWidth[E]);return m},D.prototype.getLongestRowIndex=function(n){for(var m=-1,p=Number.MIN_VALUE,E=0;E<n.rows.length;E++)n.rowWidth[E]>p&&(m=E,p=n.rowWidth[E]);return m},D.prototype.canAddHorizontal=function(n,m,p){if(n.idealRowWidth){var E=n.rows.length-1,y=n.rowWidth[E];return y+m+n.horizontalPadding<=n.idealRowWidth}var I=this.getShortestRowIndex(n);if(I<0)return!0;var w=n.rowWidth[I];if(w+n.horizontalPadding+m<=n.width)return!0;var S=0;n.rowHeight[I]<p&&I>0&&(S=p+n.verticalPadding-n.rowHeight[I]);var W;n.width-w>=m+n.horizontalPadding?W=(n.height+S)/(w+m+n.horizontalPadding):W=(n.height+S)/n.width,S=p+n.verticalPadding;var x;return n.width<m?x=(n.height+S)/m:x=(n.height+S)/n.width,x<1&&(x=1/x),W<1&&(W=1/W),W<x},D.prototype.shiftToLastRow=function(n){var m=this.getLongestRowIndex(n),p=n.rowWidth.length-1,E=n.rows[m],y=E[E.length-1],I=y.width+n.horizontalPadding;if(n.width-n.rowWidth[p]>I&&m!=p){E.splice(-1,1),n.rows[p].push(y),n.rowWidth[m]=n.rowWidth[m]-I,n.rowWidth[p]=n.rowWidth[p]+I,n.width=n.rowWidth[instance.getLongestRowIndex(n)];for(var w=Number.MIN_VALUE,S=0;S<E.length;S++)E[S].height>w&&(w=E[S].height);m>0&&(w+=n.verticalPadding);var W=n.rowHeight[m]+n.rowHeight[p];n.rowHeight[m]=w,n.rowHeight[p]<y.height+n.verticalPadding&&(n.rowHeight[p]=y.height+n.verticalPadding);var x=n.rowHeight[m]+n.rowHeight[p];n.height+=x-W,this.shiftToLastRow(n)}},D.prototype.tilingPreLayout=function(){o.TILE&&(this.groupZeroDegreeMembers(),this.clearCompounds(),this.clearZeroDegreeMembers())},D.prototype.tilingPostLayout=function(){o.TILE&&(this.repopulateZeroDegreeMembers(),this.repopulateCompounds())},D.prototype.reduceTrees=function(){for(var n=[],m=!0,p;m;){var E=this.graphManager.getAllNodes(),y=[];m=!1;for(var I=0;I<E.length;I++)if(p=E[I],p.getEdges().length==1&&!p.getEdges()[0].isInterGraph&&p.getChild()==null){if(o.PURE_INCREMENTAL){var w=p.getEdges()[0].getOtherEnd(p),S=new N(p.getCenterX()-w.getCenterX(),p.getCenterY()-w.getCenterY());y.push([p,p.getEdges()[0],p.getOwner(),S])}else y.push([p,p.getEdges()[0],p.getOwner()]);m=!0}if(m==!0){for(var W=[],x=0;x<y.length;x++)y[x][0].getEdges().length==1&&(W.push(y[x]),y[x][0].getOwner().remove(y[x][0]));n.push(W),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()}}this.prunedNodesAll=n},D.prototype.growTree=function(n){for(var m=n.length,p=n[m-1],E,y=0;y<p.length;y++)E=p[y],this.findPlaceforPrunedNode(E),E[2].add(E[0]),E[2].add(E[1],E[1].source,E[1].target);n.splice(n.length-1,1),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()},D.prototype.findPlaceforPrunedNode=function(n){var m,p,E=n[0];if(E==n[1].source?p=n[1].target:p=n[1].source,o.PURE_INCREMENTAL)E.setCenter(p.getCenterX()+n[3].getWidth(),p.getCenterY()+n[3].getHeight());else{var y=p.startX,I=p.finishX,w=p.startY,S=p.finishY,W=0,x=0,q=0,V=0,U=[W,q,x,V];if(w>0)for(var et=y;et<=I;et++)U[0]+=this.grid[et][w-1].length+this.grid[et][w].length-1;if(I<this.grid.length-1)for(var et=w;et<=S;et++)U[1]+=this.grid[I+1][et].length+this.grid[I][et].length-1;if(S<this.grid[0].length-1)for(var et=y;et<=I;et++)U[2]+=this.grid[et][S+1].length+this.grid[et][S].length-1;if(y>0)for(var et=w;et<=S;et++)U[3]+=this.grid[y-1][et].length+this.grid[y][et].length-1;for(var z=M.MAX_VALUE,O,X,B=0;B<U.length;B++)U[B]<z?(z=U[B],O=1,X=B):U[B]==z&&O++;if(O==3&&z==0)U[0]==0&&U[1]==0&&U[2]==0?m=1:U[0]==0&&U[1]==0&&U[3]==0?m=0:U[0]==0&&U[2]==0&&U[3]==0?m=3:U[1]==0&&U[2]==0&&U[3]==0&&(m=2);else if(O==2&&z==0){var _=Math.floor(Math.random()*2);U[0]==0&&U[1]==0?_==0?m=0:m=1:U[0]==0&&U[2]==0?_==0?m=0:m=2:U[0]==0&&U[3]==0?_==0?m=0:m=3:U[1]==0&&U[2]==0?_==0?m=1:m=2:U[1]==0&&U[3]==0?_==0?m=1:m=3:_==0?m=2:m=3}else if(O==4&&z==0){var _=Math.floor(Math.random()*4);m=_}else m=X;m==0?E.setCenter(p.getCenterX(),p.getCenterY()-p.getHeight()/2-l.DEFAULT_EDGE_LENGTH-E.getHeight()/2):m==1?E.setCenter(p.getCenterX()+p.getWidth()/2+l.DEFAULT_EDGE_LENGTH+E.getWidth()/2,p.getCenterY()):m==2?E.setCenter(p.getCenterX(),p.getCenterY()+p.getHeight()/2+l.DEFAULT_EDGE_LENGTH+E.getHeight()/2):E.setCenter(p.getCenterX()-p.getWidth()/2-l.DEFAULT_EDGE_LENGTH-E.getWidth()/2,p.getCenterY())}},a.exports=D},991:(a,r,e)=>{var f=e(551).FDLayoutNode,i=e(551).IMath;function g(s,o,c,l){f.call(this,s,o,c,l)}g.prototype=Object.create(f.prototype);for(var t in f)g[t]=f[t];g.prototype.calculateDisplacement=function(){var s=this.graphManager.getLayout();this.getChild()!=null&&this.fixedNodeWeight?(this.displacementX+=s.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.fixedNodeWeight,this.displacementY+=s.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.fixedNodeWeight):(this.displacementX+=s.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.noOfChildren,this.displacementY+=s.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.noOfChildren),Math.abs(this.displacementX)>s.coolingFactor*s.maxNodeDisplacement&&(this.displacementX=s.coolingFactor*s.maxNodeDisplacement*i.sign(this.displacementX)),Math.abs(this.displacementY)>s.coolingFactor*s.maxNodeDisplacement&&(this.displacementY=s.coolingFactor*s.maxNodeDisplacement*i.sign(this.displacementY)),this.child&&this.child.getNodes().length>0&&this.propogateDisplacementToChildren(this.displacementX,this.displacementY)},g.prototype.propogateDisplacementToChildren=function(s,o){for(var c=this.getChild().getNodes(),l,T=0;T<c.length;T++)l=c[T],l.getChild()==null?(l.displacementX+=s,l.displacementY+=o):l.propogateDisplacementToChildren(s,o)},g.prototype.move=function(){var s=this.graphManager.getLayout();(this.child==null||this.child.getNodes().length==0)&&(this.moveBy(this.displacementX,this.displacementY),s.totalDisplacement+=Math.abs(this.displacementX)+Math.abs(this.displacementY)),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0},g.prototype.setPred1=function(s){this.pred1=s},g.prototype.getPred1=function(){return pred1},g.prototype.getPred2=function(){return pred2},g.prototype.setNext=function(s){this.next=s},g.prototype.getNext=function(){return next},g.prototype.setProcessed=function(s){this.processed=s},g.prototype.isProcessed=function(){return processed},a.exports=g},902:(a,r,e)=>{function f(c){if(Array.isArray(c)){for(var l=0,T=Array(c.length);l<c.length;l++)T[l]=c[l];return T}else return Array.from(c)}var i=e(806),g=e(551).LinkedList,t=e(551).Matrix,s=e(551).SVD;function o(){}o.handleConstraints=function(c){var l={};l.fixedNodeConstraint=c.constraints.fixedNodeConstraint,l.alignmentConstraint=c.constraints.alignmentConstraint,l.relativePlacementConstraint=c.constraints.relativePlacementConstraint;for(var T=new Map,d=new Map,u=[],N=[],R=c.getAllNodes(),M=0,b=0;b<R.length;b++){var Q=R[b];Q.getChild()==null&&(d.set(Q.id,M++),u.push(Q.getCenterX()),N.push(Q.getCenterY()),T.set(Q.id,Q))}l.relativePlacementConstraint&&l.relativePlacementConstraint.forEach(function(F){!F.gap&&F.gap!=0&&(F.left?F.gap=i.DEFAULT_EDGE_LENGTH+T.get(F.left).getWidth()/2+T.get(F.right).getWidth()/2:F.gap=i.DEFAULT_EDGE_LENGTH+T.get(F.top).getHeight()/2+T.get(F.bottom).getHeight()/2)});var Y=function(P,$){return{x:P.x-$.x,y:P.y-$.y}},k=function(P){var $=0,K=0;return P.forEach(function(Z){$+=u[d.get(Z)],K+=N[d.get(Z)]}),{x:$/P.size,y:K/P.size}},D=function(P,$,K,Z,at){function gt(ft,st){var Ct=new Set(ft),ct=!0,ht=!1,Wt=void 0;try{for(var Nt=st[Symbol.iterator](),Mt;!(ct=(Mt=Nt.next()).done);ct=!0){var Zt=Mt.value;Ct.add(Zt)}}catch(Gt){ht=!0,Wt=Gt}finally{try{!ct&&Nt.return&&Nt.return()}finally{if(ht)throw Wt}}return Ct}var ot=new Map;P.forEach(function(ft,st){ot.set(st,0)}),P.forEach(function(ft,st){ft.forEach(function(Ct){ot.set(Ct.id,ot.get(Ct.id)+1)})});var tt=new Map,j=new Map,dt=new g;ot.forEach(function(ft,st){ft==0?(dt.push(st),K||($=="horizontal"?tt.set(st,d.has(st)?u[d.get(st)]:Z.get(st)):tt.set(st,d.has(st)?N[d.get(st)]:Z.get(st)))):tt.set(st,Number.NEGATIVE_INFINITY),K&&j.set(st,new Set([st]))}),K&&at.forEach(function(ft){var st=[];if(ft.forEach(function(ht){K.has(ht)&&st.push(ht)}),st.length>0){var Ct=0;st.forEach(function(ht){$=="horizontal"?(tt.set(ht,d.has(ht)?u[d.get(ht)]:Z.get(ht)),Ct+=tt.get(ht)):(tt.set(ht,d.has(ht)?N[d.get(ht)]:Z.get(ht)),Ct+=tt.get(ht))}),Ct=Ct/st.length,ft.forEach(function(ht){K.has(ht)||tt.set(ht,Ct)})}else{var ct=0;ft.forEach(function(ht){$=="horizontal"?ct+=d.has(ht)?u[d.get(ht)]:Z.get(ht):ct+=d.has(ht)?N[d.get(ht)]:Z.get(ht)}),ct=ct/ft.length,ft.forEach(function(ht){tt.set(ht,ct)})}});for(var wt=function(){var st=dt.shift(),Ct=P.get(st);Ct.forEach(function(ct){if(tt.get(ct.id)<tt.get(st)+ct.gap)if(K&&K.has(ct.id)){var ht=void 0;if($=="horizontal"?ht=d.has(ct.id)?u[d.get(ct.id)]:Z.get(ct.id):ht=d.has(ct.id)?N[d.get(ct.id)]:Z.get(ct.id),tt.set(ct.id,ht),ht<tt.get(st)+ct.gap){var Wt=tt.get(st)+ct.gap-ht;j.get(st).forEach(function(Nt){tt.set(Nt,tt.get(Nt)-Wt)})}}else tt.set(ct.id,tt.get(st)+ct.gap);ot.set(ct.id,ot.get(ct.id)-1),ot.get(ct.id)==0&&dt.push(ct.id),K&&j.set(ct.id,gt(j.get(st),j.get(ct.id)))})};dt.length!=0;)wt();if(K){var yt=new Set;P.forEach(function(ft,st){ft.length==0&&yt.add(st)});var It=[];j.forEach(function(ft,st){if(yt.has(st)){var Ct=!1,ct=!0,ht=!1,Wt=void 0;try{for(var Nt=ft[Symbol.iterator](),Mt;!(ct=(Mt=Nt.next()).done);ct=!0){var Zt=Mt.value;K.has(Zt)&&(Ct=!0)}}catch(Ft){ht=!0,Wt=Ft}finally{try{!ct&&Nt.return&&Nt.return()}finally{if(ht)throw Wt}}if(!Ct){var Gt=!1,$t=void 0;It.forEach(function(Ft,qt){Ft.has([].concat(f(ft))[0])&&(Gt=!0,$t=qt)}),Gt?ft.forEach(function(Ft){It[$t].add(Ft)}):It.push(new Set(ft))}}}),It.forEach(function(ft,st){var Ct=Number.POSITIVE_INFINITY,ct=Number.POSITIVE_INFINITY,ht=Number.NEGATIVE_INFINITY,Wt=Number.NEGATIVE_INFINITY,Nt=!0,Mt=!1,Zt=void 0;try{for(var Gt=ft[Symbol.iterator](),$t;!(Nt=($t=Gt.next()).done);Nt=!0){var Ft=$t.value,qt=void 0;$=="horizontal"?qt=d.has(Ft)?u[d.get(Ft)]:Z.get(Ft):qt=d.has(Ft)?N[d.get(Ft)]:Z.get(Ft);var _t=tt.get(Ft);qt<Ct&&(Ct=qt),qt>ht&&(ht=qt),_t<ct&&(ct=_t),_t>Wt&&(Wt=_t)}}catch(ie){Mt=!0,Zt=ie}finally{try{!Nt&&Gt.return&&Gt.return()}finally{if(Mt)throw Zt}}var ce=(Ct+ht)/2-(ct+Wt)/2,Kt=!0,te=!1,ee=void 0;try{for(var jt=ft[Symbol.iterator](),se;!(Kt=(se=jt.next()).done);Kt=!0){var re=se.value;tt.set(re,tt.get(re)+ce)}}catch(ie){te=!0,ee=ie}finally{try{!Kt&&jt.return&&jt.return()}finally{if(te)throw ee}}})}return tt},rt=function(P){var $=0,K=0,Z=0,at=0;if(P.forEach(function(j){j.left?u[d.get(j.left)]-u[d.get(j.right)]>=0?$++:K++:N[d.get(j.top)]-N[d.get(j.bottom)]>=0?Z++:at++}),$>K&&Z>at)for(var gt=0;gt<d.size;gt++)u[gt]=-1*u[gt],N[gt]=-1*N[gt];else if($>K)for(var ot=0;ot<d.size;ot++)u[ot]=-1*u[ot];else if(Z>at)for(var tt=0;tt<d.size;tt++)N[tt]=-1*N[tt]},n=function(P){var $=[],K=new g,Z=new Set,at=0;return P.forEach(function(gt,ot){if(!Z.has(ot)){$[at]=[];var tt=ot;for(K.push(tt),Z.add(tt),$[at].push(tt);K.length!=0;){tt=K.shift();var j=P.get(tt);j.forEach(function(dt){Z.has(dt.id)||(K.push(dt.id),Z.add(dt.id),$[at].push(dt.id))})}at++}}),$},m=function(P){var $=new Map;return P.forEach(function(K,Z){$.set(Z,[])}),P.forEach(function(K,Z){K.forEach(function(at){$.get(Z).push(at),$.get(at.id).push({id:Z,gap:at.gap,direction:at.direction})})}),$},p=function(P){var $=new Map;return P.forEach(function(K,Z){$.set(Z,[])}),P.forEach(function(K,Z){K.forEach(function(at){$.get(at.id).push({id:Z,gap:at.gap,direction:at.direction})})}),$},E=[],y=[],I=!1,w=!1,S=new Set,W=new Map,x=new Map,q=[];if(l.fixedNodeConstraint&&l.fixedNodeConstraint.forEach(function(F){S.add(F.nodeId)}),l.relativePlacementConstraint&&(l.relativePlacementConstraint.forEach(function(F){F.left?(W.has(F.left)?W.get(F.left).push({id:F.right,gap:F.gap,direction:"horizontal"}):W.set(F.left,[{id:F.right,gap:F.gap,direction:"horizontal"}]),W.has(F.right)||W.set(F.right,[])):(W.has(F.top)?W.get(F.top).push({id:F.bottom,gap:F.gap,direction:"vertical"}):W.set(F.top,[{id:F.bottom,gap:F.gap,direction:"vertical"}]),W.has(F.bottom)||W.set(F.bottom,[]))}),x=m(W),q=n(x)),i.TRANSFORM_ON_CONSTRAINT_HANDLING){if(l.fixedNodeConstraint&&l.fixedNodeConstraint.length>1)l.fixedNodeConstraint.forEach(function(F,P){E[P]=[F.position.x,F.position.y],y[P]=[u[d.get(F.nodeId)],N[d.get(F.nodeId)]]}),I=!0;else if(l.alignmentConstraint)(function(){var F=0;if(l.alignmentConstraint.vertical){for(var P=l.alignmentConstraint.vertical,$=function(tt){var j=new Set;P[tt].forEach(function(yt){j.add(yt)});var dt=new Set([].concat(f(j)).filter(function(yt){return S.has(yt)})),wt=void 0;dt.size>0?wt=u[d.get(dt.values().next().value)]:wt=k(j).x,P[tt].forEach(function(yt){E[F]=[wt,N[d.get(yt)]],y[F]=[u[d.get(yt)],N[d.get(yt)]],F++})},K=0;K<P.length;K++)$(K);I=!0}if(l.alignmentConstraint.horizontal){for(var Z=l.alignmentConstraint.horizontal,at=function(tt){var j=new Set;Z[tt].forEach(function(yt){j.add(yt)});var dt=new Set([].concat(f(j)).filter(function(yt){return S.has(yt)})),wt=void 0;dt.size>0?wt=u[d.get(dt.values().next().value)]:wt=k(j).y,Z[tt].forEach(function(yt){E[F]=[u[d.get(yt)],wt],y[F]=[u[d.get(yt)],N[d.get(yt)]],F++})},gt=0;gt<Z.length;gt++)at(gt);I=!0}l.relativePlacementConstraint&&(w=!0)})();else if(l.relativePlacementConstraint){for(var V=0,U=0,et=0;et<q.length;et++)q[et].length>V&&(V=q[et].length,U=et);if(V<x.size/2)rt(l.relativePlacementConstraint),I=!1,w=!1;else{var z=new Map,O=new Map,X=[];q[U].forEach(function(F){W.get(F).forEach(function(P){P.direction=="horizontal"?(z.has(F)?z.get(F).push(P):z.set(F,[P]),z.has(P.id)||z.set(P.id,[]),X.push({left:F,right:P.id})):(O.has(F)?O.get(F).push(P):O.set(F,[P]),O.has(P.id)||O.set(P.id,[]),X.push({top:F,bottom:P.id}))})}),rt(X),w=!1;var B=D(z,"horizontal"),_=D(O,"vertical");q[U].forEach(function(F,P){y[P]=[u[d.get(F)],N[d.get(F)]],E[P]=[],B.has(F)?E[P][0]=B.get(F):E[P][0]=u[d.get(F)],_.has(F)?E[P][1]=_.get(F):E[P][1]=N[d.get(F)]}),I=!0}}if(I){for(var lt=void 0,J=t.transpose(E),Rt=t.transpose(y),Lt=0;Lt<J.length;Lt++)J[Lt]=t.multGamma(J[Lt]),Rt[Lt]=t.multGamma(Rt[Lt]);var vt=t.multMat(J,t.transpose(Rt)),it=s.svd(vt);lt=t.multMat(it.V,t.transpose(it.U));for(var ut=0;ut<d.size;ut++){var Tt=[u[ut],N[ut]],At=[lt[0][0],lt[1][0]],Dt=[lt[0][1],lt[1][1]];u[ut]=t.dotProduct(Tt,At),N[ut]=t.dotProduct(Tt,Dt)}w&&rt(l.relativePlacementConstraint)}}if(i.ENFORCE_CONSTRAINTS){if(l.fixedNodeConstraint&&l.fixedNodeConstraint.length>0){var mt={x:0,y:0};l.fixedNodeConstraint.forEach(function(F,P){var $={x:u[d.get(F.nodeId)],y:N[d.get(F.nodeId)]},K=F.position,Z=Y(K,$);mt.x+=Z.x,mt.y+=Z.y}),mt.x/=l.fixedNodeConstraint.length,mt.y/=l.fixedNodeConstraint.length,u.forEach(function(F,P){u[P]+=mt.x}),N.forEach(function(F,P){N[P]+=mt.y}),l.fixedNodeConstraint.forEach(function(F){u[d.get(F.nodeId)]=F.position.x,N[d.get(F.nodeId)]=F.position.y})}if(l.alignmentConstraint){if(l.alignmentConstraint.vertical)for(var xt=l.alignmentConstraint.vertical,St=function(P){var $=new Set;xt[P].forEach(function(at){$.add(at)});var K=new Set([].concat(f($)).filter(function(at){return S.has(at)})),Z=void 0;K.size>0?Z=u[d.get(K.values().next().value)]:Z=k($).x,$.forEach(function(at){S.has(at)||(u[d.get(at)]=Z)})},Vt=0;Vt<xt.length;Vt++)St(Vt);if(l.alignmentConstraint.horizontal)for(var Xt=l.alignmentConstraint.horizontal,Ut=function(P){var $=new Set;Xt[P].forEach(function(at){$.add(at)});var K=new Set([].concat(f($)).filter(function(at){return S.has(at)})),Z=void 0;K.size>0?Z=N[d.get(K.values().next().value)]:Z=k($).y,$.forEach(function(at){S.has(at)||(N[d.get(at)]=Z)})},bt=0;bt<Xt.length;bt++)Ut(bt)}l.relativePlacementConstraint&&function(){var F=new Map,P=new Map,$=new Map,K=new Map,Z=new Map,at=new Map,gt=new Set,ot=new Set;if(S.forEach(function(Yt){gt.add(Yt),ot.add(Yt)}),l.alignmentConstraint){if(l.alignmentConstraint.vertical)for(var tt=l.alignmentConstraint.vertical,j=function(Et){$.set("dummy"+Et,[]),tt[Et].forEach(function(Ot){F.set(Ot,"dummy"+Et),$.get("dummy"+Et).push(Ot),S.has(Ot)&&gt.add("dummy"+Et)}),Z.set("dummy"+Et,u[d.get(tt[Et][0])])},dt=0;dt<tt.length;dt++)j(dt);if(l.alignmentConstraint.horizontal)for(var wt=l.alignmentConstraint.horizontal,yt=function(Et){K.set("dummy"+Et,[]),wt[Et].forEach(function(Ot){P.set(Ot,"dummy"+Et),K.get("dummy"+Et).push(Ot),S.has(Ot)&&ot.add("dummy"+Et)}),at.set("dummy"+Et,N[d.get(wt[Et][0])])},It=0;It<wt.length;It++)yt(It)}var ft=new Map,st=new Map,Ct=function(Et){W.get(Et).forEach(function(Ot){var Jt=void 0,kt=void 0;Ot.direction=="horizontal"?(Jt=F.get(Et)?F.get(Et):Et,F.get(Ot.id)?kt={id:F.get(Ot.id),gap:Ot.gap,direction:Ot.direction}:kt=Ot,ft.has(Jt)?ft.get(Jt).push(kt):ft.set(Jt,[kt]),ft.has(kt.id)||ft.set(kt.id,[])):(Jt=P.get(Et)?P.get(Et):Et,P.get(Ot.id)?kt={id:P.get(Ot.id),gap:Ot.gap,direction:Ot.direction}:kt=Ot,st.has(Jt)?st.get(Jt).push(kt):st.set(Jt,[kt]),st.has(kt.id)||st.set(kt.id,[]))})},ct=!0,ht=!1,Wt=void 0;try{for(var Nt=W.keys()[Symbol.iterator](),Mt;!(ct=(Mt=Nt.next()).done);ct=!0){var Zt=Mt.value;Ct(Zt)}}catch(Yt){ht=!0,Wt=Yt}finally{try{!ct&&Nt.return&&Nt.return()}finally{if(ht)throw Wt}}var Gt=m(ft),$t=m(st),Ft=n(Gt),qt=n($t),_t=p(ft),ce=p(st),Kt=[],te=[];Ft.forEach(function(Yt,Et){Kt[Et]=[],Yt.forEach(function(Ot){_t.get(Ot).length==0&&Kt[Et].push(Ot)})}),qt.forEach(function(Yt,Et){te[Et]=[],Yt.forEach(function(Ot){ce.get(Ot).length==0&&te[Et].push(Ot)})});var ee=D(ft,"horizontal",gt,Z,Kt),jt=D(st,"vertical",ot,at,te),se=function(Et){$.get(Et)?$.get(Et).forEach(function(Ot){u[d.get(Ot)]=ee.get(Et)}):u[d.get(Et)]=ee.get(Et)},re=!0,ie=!1,Le=void 0;try{for(var ge=ee.keys()[Symbol.iterator](),Ce;!(re=(Ce=ge.next()).done);re=!0){var ue=Ce.value;se(ue)}}catch(Yt){ie=!0,Le=Yt}finally{try{!re&&ge.return&&ge.return()}finally{if(ie)throw Le}}var $e=function(Et){K.get(Et)?K.get(Et).forEach(function(Ot){N[d.get(Ot)]=jt.get(Et)}):N[d.get(Et)]=jt.get(Et)},de=!0,Me=!1,Ae=void 0;try{for(var ve=jt.keys()[Symbol.iterator](),we;!(de=(we=ve.next()).done);de=!0){var ue=we.value;$e(ue)}}catch(Yt){Me=!0,Ae=Yt}finally{try{!de&&ve.return&&ve.return()}finally{if(Me)throw Ae}}}()}for(var Ht=0;Ht<R.length;Ht++){var Bt=R[Ht];Bt.getChild()==null&&Bt.setCenter(u[d.get(Bt.id)],N[d.get(Bt.id)])}},a.exports=o},551:a=>{a.exports=A}},L={};function v(a){var r=L[a];if(r!==void 0)return r.exports;var e=L[a]={exports:{}};return H[a](e,e.exports,v),e.exports}var h=v(45);return h})()})})(Fe);(function(C,G){(function(H,L){C.exports=L(Fe.exports)})(me,function(A){return(()=>{var H={658:a=>{a.exports=Object.assign!=null?Object.assign.bind(Object):function(r){for(var e=arguments.length,f=Array(e>1?e-1:0),i=1;i<e;i++)f[i-1]=arguments[i];return f.forEach(function(g){Object.keys(g).forEach(function(t){return r[t]=g[t]})}),r}},548:(a,r,e)=>{var f=function(){function t(s,o){var c=[],l=!0,T=!1,d=void 0;try{for(var u=s[Symbol.iterator](),N;!(l=(N=u.next()).done)&&(c.push(N.value),!(o&&c.length===o));l=!0);}catch(R){T=!0,d=R}finally{try{!l&&u.return&&u.return()}finally{if(T)throw d}}return c}return function(s,o){if(Array.isArray(s))return s;if(Symbol.iterator in Object(s))return t(s,o);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),i=e(140).layoutBase.LinkedList,g={};g.getTopMostNodes=function(t){for(var s={},o=0;o<t.length;o++)s[t[o].id()]=!0;var c=t.filter(function(l,T){typeof l=="number"&&(l=T);for(var d=l.parent()[0];d!=null;){if(s[d.id()])return!1;d=d.parent()[0]}return!0});return c},g.connectComponents=function(t,s,o,c){var l=new i,T=new Set,d=[],u=void 0,N=void 0,R=void 0,M=!1,b=1,Q=[],Y=[],k=function(){var rt=t.collection();Y.push(rt);var n=o[0],m=t.collection();m.merge(n).merge(n.descendants().intersection(s)),d.push(n),m.forEach(function(y){l.push(y),T.add(y),rt.merge(y)});for(var p=function(){n=l.shift();var I=t.collection();n.neighborhood().nodes().forEach(function(x){s.intersection(n.edgesWith(x)).length>0&&I.merge(x)});for(var w=0;w<I.length;w++){var S=I[w];if(u=o.intersection(S.union(S.ancestors())),u!=null&&!T.has(u[0])){var W=u.union(u.descendants());W.forEach(function(x){l.push(x),T.add(x),rt.merge(x),o.has(x)&&d.push(x)})}}};l.length!=0;)p();if(rt.forEach(function(y){s.intersection(y.connectedEdges()).forEach(function(I){rt.has(I.source())&&rt.has(I.target())&&rt.merge(I)})}),d.length==o.length&&(M=!0),!M||M&&b>1){N=d[0],R=N.connectedEdges().length,d.forEach(function(y){y.connectedEdges().length<R&&(R=y.connectedEdges().length,N=y)}),Q.push(N.id());var E=t.collection();E.merge(d[0]),d.forEach(function(y){E.merge(y)}),d=[],o=o.difference(E),b++}};do k();while(!M);return c&&Q.length>0&&c.set("dummy"+(c.size+1),Q),Y},g.relocateComponent=function(t,s,o){if(!o.fixedNodeConstraint){var c=Number.POSITIVE_INFINITY,l=Number.NEGATIVE_INFINITY,T=Number.POSITIVE_INFINITY,d=Number.NEGATIVE_INFINITY;if(o.quality=="draft"){var u=!0,N=!1,R=void 0;try{for(var M=s.nodeIndexes[Symbol.iterator](),b;!(u=(b=M.next()).done);u=!0){var Q=b.value,Y=f(Q,2),k=Y[0],D=Y[1],rt=o.cy.getElementById(k);if(rt){var n=rt.boundingBox(),m=s.xCoords[D]-n.w/2,p=s.xCoords[D]+n.w/2,E=s.yCoords[D]-n.h/2,y=s.yCoords[D]+n.h/2;m<c&&(c=m),p>l&&(l=p),E<T&&(T=E),y>d&&(d=y)}}}catch(x){N=!0,R=x}finally{try{!u&&M.return&&M.return()}finally{if(N)throw R}}var I=t.x-(l+c)/2,w=t.y-(d+T)/2;s.xCoords=s.xCoords.map(function(x){return x+I}),s.yCoords=s.yCoords.map(function(x){return x+w})}else{Object.keys(s).forEach(function(x){var q=s[x],V=q.getRect().x,U=q.getRect().x+q.getRect().width,et=q.getRect().y,z=q.getRect().y+q.getRect().height;V<c&&(c=V),U>l&&(l=U),et<T&&(T=et),z>d&&(d=z)});var S=t.x-(l+c)/2,W=t.y-(d+T)/2;Object.keys(s).forEach(function(x){var q=s[x];q.setCenter(q.getCenterX()+S,q.getCenterY()+W)})}}},g.calcBoundingBox=function(t,s,o,c){for(var l=Number.MAX_SAFE_INTEGER,T=Number.MIN_SAFE_INTEGER,d=Number.MAX_SAFE_INTEGER,u=Number.MIN_SAFE_INTEGER,N=void 0,R=void 0,M=void 0,b=void 0,Q=t.descendants().not(":parent"),Y=Q.length,k=0;k<Y;k++){var D=Q[k];N=s[c.get(D.id())]-D.width()/2,R=s[c.get(D.id())]+D.width()/2,M=o[c.get(D.id())]-D.height()/2,b=o[c.get(D.id())]+D.height()/2,l>N&&(l=N),T<R&&(T=R),d>M&&(d=M),u<b&&(u=b)}var rt={};return rt.topLeftX=l,rt.topLeftY=d,rt.width=T-l,rt.height=u-d,rt},g.calcParentsWithoutChildren=function(t,s){var o=t.collection();return s.nodes(":parent").forEach(function(c){var l=!1;c.children().forEach(function(T){T.css("display")!="none"&&(l=!0)}),l||o.merge(c)}),o},a.exports=g},816:(a,r,e)=>{var f=e(548),i=e(140).CoSELayout,g=e(140).CoSENode,t=e(140).layoutBase.PointD,s=e(140).layoutBase.DimensionD,o=e(140).layoutBase.LayoutConstants,c=e(140).layoutBase.FDLayoutConstants,l=e(140).CoSEConstants,T=function(u,N){var R=u.cy,M=u.eles,b=M.nodes(),Q=M.edges(),Y=void 0,k=void 0,D=void 0,rt={};u.randomize&&(Y=N.nodeIndexes,k=N.xCoords,D=N.yCoords);var n=function(x){return typeof x=="function"},m=function(x,q){return n(x)?x(q):x},p=f.calcParentsWithoutChildren(R,M),E=function W(x,q,V,U){for(var et=q.length,z=0;z<et;z++){var O=q[z],X=null;O.intersection(p).length==0&&(X=O.children());var B=void 0,_=O.layoutDimensions({nodeDimensionsIncludeLabels:U.nodeDimensionsIncludeLabels});if(O.outerWidth()!=null&&O.outerHeight()!=null)if(U.randomize)if(!O.isParent())B=x.add(new g(V.graphManager,new t(k[Y.get(O.id())]-_.w/2,D[Y.get(O.id())]-_.h/2),new s(parseFloat(_.w),parseFloat(_.h))));else{var lt=f.calcBoundingBox(O,k,D,Y);O.intersection(p).length==0?B=x.add(new g(V.graphManager,new t(lt.topLeftX,lt.topLeftY),new s(lt.width,lt.height))):B=x.add(new g(V.graphManager,new t(lt.topLeftX,lt.topLeftY),new s(parseFloat(_.w),parseFloat(_.h))))}else B=x.add(new g(V.graphManager,new t(O.position("x")-_.w/2,O.position("y")-_.h/2),new s(parseFloat(_.w),parseFloat(_.h))));else B=x.add(new g(this.graphManager));if(B.id=O.data("id"),B.nodeRepulsion=m(U.nodeRepulsion,O),B.paddingLeft=parseInt(O.css("padding")),B.paddingTop=parseInt(O.css("padding")),B.paddingRight=parseInt(O.css("padding")),B.paddingBottom=parseInt(O.css("padding")),U.nodeDimensionsIncludeLabels&&(B.labelWidth=O.boundingBox({includeLabels:!0,includeNodes:!1,includeOverlays:!1}).w,B.labelHeight=O.boundingBox({includeLabels:!0,includeNodes:!1,includeOverlays:!1}).h,B.labelPosVertical=O.css("text-valign"),B.labelPosHorizontal=O.css("text-halign")),rt[O.data("id")]=B,isNaN(B.rect.x)&&(B.rect.x=0),isNaN(B.rect.y)&&(B.rect.y=0),X!=null&&X.length>0){var J=void 0;J=V.getGraphManager().add(V.newGraph(),B),W(J,X,V,U)}}},y=function(x,q,V){for(var U=0,et=0,z=0;z<V.length;z++){var O=V[z],X=rt[O.data("source")],B=rt[O.data("target")];if(X&&B&&X!==B&&X.getEdgesBetween(B).length==0){var _=q.add(x.newEdge(),X,B);_.id=O.id(),_.idealLength=m(u.idealEdgeLength,O),_.edgeElasticity=m(u.edgeElasticity,O),U+=_.idealLength,et++}}u.idealEdgeLength!=null&&(et>0?l.DEFAULT_EDGE_LENGTH=c.DEFAULT_EDGE_LENGTH=U/et:n(u.idealEdgeLength)?l.DEFAULT_EDGE_LENGTH=c.DEFAULT_EDGE_LENGTH=50:l.DEFAULT_EDGE_LENGTH=c.DEFAULT_EDGE_LENGTH=u.idealEdgeLength,l.MIN_REPULSION_DIST=c.MIN_REPULSION_DIST=c.DEFAULT_EDGE_LENGTH/10,l.DEFAULT_RADIAL_SEPARATION=c.DEFAULT_EDGE_LENGTH)},I=function(x,q){q.fixedNodeConstraint&&(x.constraints.fixedNodeConstraint=q.fixedNodeConstraint),q.alignmentConstraint&&(x.constraints.alignmentConstraint=q.alignmentConstraint),q.relativePlacementConstraint&&(x.constraints.relativePlacementConstraint=q.relativePlacementConstraint)};u.nestingFactor!=null&&(l.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=c.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=u.nestingFactor),u.gravity!=null&&(l.DEFAULT_GRAVITY_STRENGTH=c.DEFAULT_GRAVITY_STRENGTH=u.gravity),u.numIter!=null&&(l.MAX_ITERATIONS=c.MAX_ITERATIONS=u.numIter),u.gravityRange!=null&&(l.DEFAULT_GRAVITY_RANGE_FACTOR=c.DEFAULT_GRAVITY_RANGE_FACTOR=u.gravityRange),u.gravityCompound!=null&&(l.DEFAULT_COMPOUND_GRAVITY_STRENGTH=c.DEFAULT_COMPOUND_GRAVITY_STRENGTH=u.gravityCompound),u.gravityRangeCompound!=null&&(l.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=c.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=u.gravityRangeCompound),u.initialEnergyOnIncremental!=null&&(l.DEFAULT_COOLING_FACTOR_INCREMENTAL=c.DEFAULT_COOLING_FACTOR_INCREMENTAL=u.initialEnergyOnIncremental),u.tilingCompareBy!=null&&(l.TILING_COMPARE_BY=u.tilingCompareBy),u.quality=="proof"?o.QUALITY=2:o.QUALITY=0,l.NODE_DIMENSIONS_INCLUDE_LABELS=c.NODE_DIMENSIONS_INCLUDE_LABELS=o.NODE_DIMENSIONS_INCLUDE_LABELS=u.nodeDimensionsIncludeLabels,l.DEFAULT_INCREMENTAL=c.DEFAULT_INCREMENTAL=o.DEFAULT_INCREMENTAL=!u.randomize,l.ANIMATE=c.ANIMATE=o.ANIMATE=u.animate,l.TILE=u.tile,l.TILING_PADDING_VERTICAL=typeof u.tilingPaddingVertical=="function"?u.tilingPaddingVertical.call():u.tilingPaddingVertical,l.TILING_PADDING_HORIZONTAL=typeof u.tilingPaddingHorizontal=="function"?u.tilingPaddingHorizontal.call():u.tilingPaddingHorizontal,l.DEFAULT_INCREMENTAL=c.DEFAULT_INCREMENTAL=o.DEFAULT_INCREMENTAL=!0,l.PURE_INCREMENTAL=!u.randomize,o.DEFAULT_UNIFORM_LEAF_NODE_SIZES=u.uniformNodeDimensions,u.step=="transformed"&&(l.TRANSFORM_ON_CONSTRAINT_HANDLING=!0,l.ENFORCE_CONSTRAINTS=!1,l.APPLY_LAYOUT=!1),u.step=="enforced"&&(l.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,l.ENFORCE_CONSTRAINTS=!0,l.APPLY_LAYOUT=!1),u.step=="cose"&&(l.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,l.ENFORCE_CONSTRAINTS=!1,l.APPLY_LAYOUT=!0),u.step=="all"&&(u.randomize?l.TRANSFORM_ON_CONSTRAINT_HANDLING=!0:l.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,l.ENFORCE_CONSTRAINTS=!0,l.APPLY_LAYOUT=!0),u.fixedNodeConstraint||u.alignmentConstraint||u.relativePlacementConstraint?l.TREE_REDUCTION_ON_INCREMENTAL=!1:l.TREE_REDUCTION_ON_INCREMENTAL=!0;var w=new i,S=w.newGraphManager();return E(S.addRoot(),f.getTopMostNodes(b),w,u),y(w,S,Q),I(w,u),w.runLayout(),rt};a.exports={coseLayout:T}},212:(a,r,e)=>{var f=function(){function u(N,R){for(var M=0;M<R.length;M++){var b=R[M];b.enumerable=b.enumerable||!1,b.configurable=!0,"value"in b&&(b.writable=!0),Object.defineProperty(N,b.key,b)}}return function(N,R,M){return R&&u(N.prototype,R),M&&u(N,M),N}}();function i(u,N){if(!(u instanceof N))throw new TypeError("Cannot call a class as a function")}var g=e(658),t=e(548),s=e(657),o=s.spectralLayout,c=e(816),l=c.coseLayout,T=Object.freeze({quality:"default",randomize:!0,animate:!0,animationDuration:1e3,animationEasing:void 0,fit:!0,padding:30,nodeDimensionsIncludeLabels:!1,uniformNodeDimensions:!1,packComponents:!0,step:"all",samplingType:!0,sampleSize:25,nodeSeparation:75,piTol:1e-7,nodeRepulsion:function(N){return 4500},idealEdgeLength:function(N){return 50},edgeElasticity:function(N){return .45},nestingFactor:.1,gravity:.25,numIter:2500,tile:!0,tilingCompareBy:void 0,tilingPaddingVertical:10,tilingPaddingHorizontal:10,gravityRangeCompound:1.5,gravityCompound:1,gravityRange:3.8,initialEnergyOnIncremental:.3,fixedNodeConstraint:void 0,alignmentConstraint:void 0,relativePlacementConstraint:void 0,ready:function(){},stop:function(){}}),d=function(){function u(N){i(this,u),this.options=g({},T,N)}return f(u,[{key:"run",value:function(){var R=this,M=this.options,b=M.cy,Q=M.eles,Y=[],k=[],D=void 0,rt=[];M.fixedNodeConstraint&&(!Array.isArray(M.fixedNodeConstraint)||M.fixedNodeConstraint.length==0)&&(M.fixedNodeConstraint=void 0),M.alignmentConstraint&&(M.alignmentConstraint.vertical&&(!Array.isArray(M.alignmentConstraint.vertical)||M.alignmentConstraint.vertical.length==0)&&(M.alignmentConstraint.vertical=void 0),M.alignmentConstraint.horizontal&&(!Array.isArray(M.alignmentConstraint.horizontal)||M.alignmentConstraint.horizontal.length==0)&&(M.alignmentConstraint.horizontal=void 0)),M.relativePlacementConstraint&&(!Array.isArray(M.relativePlacementConstraint)||M.relativePlacementConstraint.length==0)&&(M.relativePlacementConstraint=void 0);var n=M.fixedNodeConstraint||M.alignmentConstraint||M.relativePlacementConstraint;n&&(M.tile=!1,M.packComponents=!1);var m=void 0,p=!1;if(b.layoutUtilities&&M.packComponents&&(m=b.layoutUtilities("get"),m||(m=b.layoutUtilities()),p=!0),Q.nodes().length>0)if(p){var I=t.getTopMostNodes(M.eles.nodes());if(D=t.connectComponents(b,M.eles,I),D.forEach(function(vt){var it=vt.boundingBox();rt.push({x:it.x1+it.w/2,y:it.y1+it.h/2})}),M.randomize&&D.forEach(function(vt){M.eles=vt,Y.push(o(M))}),M.quality=="default"||M.quality=="proof"){var w=b.collection();if(M.tile){var S=new Map,W=[],x=[],q=0,V={nodeIndexes:S,xCoords:W,yCoords:x},U=[];if(D.forEach(function(vt,it){vt.edges().length==0&&(vt.nodes().forEach(function(ut,Tt){w.merge(vt.nodes()[Tt]),ut.isParent()||(V.nodeIndexes.set(vt.nodes()[Tt].id(),q++),V.xCoords.push(vt.nodes()[0].position().x),V.yCoords.push(vt.nodes()[0].position().y))}),U.push(it))}),w.length>1){var et=w.boundingBox();rt.push({x:et.x1+et.w/2,y:et.y1+et.h/2}),D.push(w),Y.push(V);for(var z=U.length-1;z>=0;z--)D.splice(U[z],1),Y.splice(U[z],1),rt.splice(U[z],1)}}D.forEach(function(vt,it){M.eles=vt,k.push(l(M,Y[it])),t.relocateComponent(rt[it],k[it],M)})}else D.forEach(function(vt,it){t.relocateComponent(rt[it],Y[it],M)});var O=new Set;if(D.length>1){var X=[],B=Q.filter(function(vt){return vt.css("display")=="none"});D.forEach(function(vt,it){var ut=void 0;if(M.quality=="draft"&&(ut=Y[it].nodeIndexes),vt.nodes().not(B).length>0){var Tt={};Tt.edges=[],Tt.nodes=[];var At=void 0;vt.nodes().not(B).forEach(function(Dt){if(M.quality=="draft")if(!Dt.isParent())At=ut.get(Dt.id()),Tt.nodes.push({x:Y[it].xCoords[At]-Dt.boundingbox().w/2,y:Y[it].yCoords[At]-Dt.boundingbox().h/2,width:Dt.boundingbox().w,height:Dt.boundingbox().h});else{var mt=t.calcBoundingBox(Dt,Y[it].xCoords,Y[it].yCoords,ut);Tt.nodes.push({x:mt.topLeftX,y:mt.topLeftY,width:mt.width,height:mt.height})}else k[it][Dt.id()]&&Tt.nodes.push({x:k[it][Dt.id()].getLeft(),y:k[it][Dt.id()].getTop(),width:k[it][Dt.id()].getWidth(),height:k[it][Dt.id()].getHeight()})}),vt.edges().forEach(function(Dt){var mt=Dt.source(),xt=Dt.target();if(mt.css("display")!="none"&&xt.css("display")!="none")if(M.quality=="draft"){var St=ut.get(mt.id()),Vt=ut.get(xt.id()),Xt=[],Ut=[];if(mt.isParent()){var bt=t.calcBoundingBox(mt,Y[it].xCoords,Y[it].yCoords,ut);Xt.push(bt.topLeftX+bt.width/2),Xt.push(bt.topLeftY+bt.height/2)}else Xt.push(Y[it].xCoords[St]),Xt.push(Y[it].yCoords[St]);if(xt.isParent()){var Ht=t.calcBoundingBox(xt,Y[it].xCoords,Y[it].yCoords,ut);Ut.push(Ht.topLeftX+Ht.width/2),Ut.push(Ht.topLeftY+Ht.height/2)}else Ut.push(Y[it].xCoords[Vt]),Ut.push(Y[it].yCoords[Vt]);Tt.edges.push({startX:Xt[0],startY:Xt[1],endX:Ut[0],endY:Ut[1]})}else k[it][mt.id()]&&k[it][xt.id()]&&Tt.edges.push({startX:k[it][mt.id()].getCenterX(),startY:k[it][mt.id()].getCenterY(),endX:k[it][xt.id()].getCenterX(),endY:k[it][xt.id()].getCenterY()})}),Tt.nodes.length>0&&(X.push(Tt),O.add(it))}});var _=m.packComponents(X,M.randomize).shifts;if(M.quality=="draft")Y.forEach(function(vt,it){var ut=vt.xCoords.map(function(At){return At+_[it].dx}),Tt=vt.yCoords.map(function(At){return At+_[it].dy});vt.xCoords=ut,vt.yCoords=Tt});else{var lt=0;O.forEach(function(vt){Object.keys(k[vt]).forEach(function(it){var ut=k[vt][it];ut.setCenter(ut.getCenterX()+_[lt].dx,ut.getCenterY()+_[lt].dy)}),lt++})}}}else{var E=M.eles.boundingBox();if(rt.push({x:E.x1+E.w/2,y:E.y1+E.h/2}),M.randomize){var y=o(M);Y.push(y)}M.quality=="default"||M.quality=="proof"?(k.push(l(M,Y[0])),t.relocateComponent(rt[0],k[0],M)):t.relocateComponent(rt[0],Y[0],M)}var J=function(it,ut){if(M.quality=="default"||M.quality=="proof"){typeof it=="number"&&(it=ut);var Tt=void 0,At=void 0,Dt=it.data("id");return k.forEach(function(xt){Dt in xt&&(Tt={x:xt[Dt].getRect().getCenterX(),y:xt[Dt].getRect().getCenterY()},At=xt[Dt])}),M.nodeDimensionsIncludeLabels&&(At.labelWidth&&(At.labelPosHorizontal=="left"?Tt.x+=At.labelWidth/2:At.labelPosHorizontal=="right"&&(Tt.x-=At.labelWidth/2)),At.labelHeight&&(At.labelPosVertical=="top"?Tt.y+=At.labelHeight/2:At.labelPosVertical=="bottom"&&(Tt.y-=At.labelHeight/2))),Tt==null&&(Tt={x:it.position("x"),y:it.position("y")}),{x:Tt.x,y:Tt.y}}else{var mt=void 0;return Y.forEach(function(xt){var St=xt.nodeIndexes.get(it.id());St!=null&&(mt={x:xt.xCoords[St],y:xt.yCoords[St]})}),mt==null&&(mt={x:it.position("x"),y:it.position("y")}),{x:mt.x,y:mt.y}}};if(M.quality=="default"||M.quality=="proof"||M.randomize){var Rt=t.calcParentsWithoutChildren(b,Q),Lt=Q.filter(function(vt){return vt.css("display")=="none"});M.eles=Q.not(Lt),Q.nodes().not(":parent").not(Lt).layoutPositions(R,M,J),Rt.length>0&&Rt.forEach(function(vt){vt.position(J(vt))})}else console.log("If randomize option is set to false, then quality option must be 'default' or 'proof'.")}}]),u}();a.exports=d},657:(a,r,e)=>{var f=e(548),i=e(140).layoutBase.Matrix,g=e(140).layoutBase.SVD,t=function(o){var c=o.cy,l=o.eles,T=l.nodes(),d=l.nodes(":parent"),u=new Map,N=new Map,R=new Map,M=[],b=[],Q=[],Y=[],k=[],D=[],rt=[],n=[],m=void 0,p=1e8,E=1e-9,y=o.piTol,I=o.samplingType,w=o.nodeSeparation,S=void 0,W=function(){for(var P=0,$=0,K=!1;$<S;){P=Math.floor(Math.random()*m),K=!1;for(var Z=0;Z<$;Z++)if(Y[Z]==P){K=!0;break}if(!K)Y[$]=P,$++;else continue}},x=function(P,$,K){for(var Z=[],at=0,gt=0,ot=0,tt=void 0,j=[],dt=0,wt=1,yt=0;yt<m;yt++)j[yt]=p;for(Z[gt]=P,j[P]=0;gt>=at;){ot=Z[at++];for(var It=M[ot],ft=0;ft<It.length;ft++)tt=N.get(It[ft]),j[tt]==p&&(j[tt]=j[ot]+1,Z[++gt]=tt);D[ot][$]=j[ot]*w}if(K){for(var st=0;st<m;st++)D[st][$]<k[st]&&(k[st]=D[st][$]);for(var Ct=0;Ct<m;Ct++)k[Ct]>dt&&(dt=k[Ct],wt=Ct)}return wt},q=function(P){var $=void 0;if(P){$=Math.floor(Math.random()*m);for(var Z=0;Z<m;Z++)k[Z]=p;for(var at=0;at<S;at++)Y[at]=$,$=x($,at,P)}else{W();for(var K=0;K<S;K++)x(Y[K],K,P)}for(var gt=0;gt<m;gt++)for(var ot=0;ot<S;ot++)D[gt][ot]*=D[gt][ot];for(var tt=0;tt<S;tt++)rt[tt]=[];for(var j=0;j<S;j++)for(var dt=0;dt<S;dt++)rt[j][dt]=D[Y[dt]][j]},V=function(){for(var P=g.svd(rt),$=P.S,K=P.U,Z=P.V,at=$[0]*$[0]*$[0],gt=[],ot=0;ot<S;ot++){gt[ot]=[];for(var tt=0;tt<S;tt++)gt[ot][tt]=0,ot==tt&&(gt[ot][tt]=$[ot]/($[ot]*$[ot]+at/($[ot]*$[ot])))}n=i.multMat(i.multMat(Z,gt),i.transpose(K))},U=function(){for(var P=void 0,$=void 0,K=[],Z=[],at=[],gt=[],ot=0;ot<m;ot++)K[ot]=Math.random(),Z[ot]=Math.random();K=i.normalize(K),Z=i.normalize(Z);for(var tt=E,j=E,dt=void 0;;){for(var wt=0;wt<m;wt++)at[wt]=K[wt];if(K=i.multGamma(i.multL(i.multGamma(at),D,n)),P=i.dotProduct(at,K),K=i.normalize(K),tt=i.dotProduct(at,K),dt=Math.abs(tt/j),dt<=1+y&&dt>=1)break;j=tt}for(var yt=0;yt<m;yt++)at[yt]=K[yt];for(j=E;;){for(var It=0;It<m;It++)gt[It]=Z[It];if(gt=i.minusOp(gt,i.multCons(at,i.dotProduct(at,gt))),Z=i.multGamma(i.multL(i.multGamma(gt),D,n)),$=i.dotProduct(gt,Z),Z=i.normalize(Z),tt=i.dotProduct(gt,Z),dt=Math.abs(tt/j),dt<=1+y&&dt>=1)break;j=tt}for(var ft=0;ft<m;ft++)gt[ft]=Z[ft];b=i.multCons(at,Math.sqrt(Math.abs(P))),Q=i.multCons(gt,Math.sqrt(Math.abs($)))};f.connectComponents(c,l,f.getTopMostNodes(T),u),d.forEach(function(F){f.connectComponents(c,l,f.getTopMostNodes(F.descendants().intersection(l)),u)});for(var et=0,z=0;z<T.length;z++)T[z].isParent()||N.set(T[z].id(),et++);var O=!0,X=!1,B=void 0;try{for(var _=u.keys()[Symbol.iterator](),lt;!(O=(lt=_.next()).done);O=!0){var J=lt.value;N.set(J,et++)}}catch(F){X=!0,B=F}finally{try{!O&&_.return&&_.return()}finally{if(X)throw B}}for(var Rt=0;Rt<N.size;Rt++)M[Rt]=[];d.forEach(function(F){for(var P=F.children().intersection(l);P.nodes(":childless").length==0;)P=P.nodes()[0].children().intersection(l);var $=0,K=P.nodes(":childless")[0].connectedEdges().length;P.nodes(":childless").forEach(function(Z,at){Z.connectedEdges().length<K&&(K=Z.connectedEdges().length,$=at)}),R.set(F.id(),P.nodes(":childless")[$].id())}),T.forEach(function(F){var P=void 0;F.isParent()?P=N.get(R.get(F.id())):P=N.get(F.id()),F.neighborhood().nodes().forEach(function($){l.intersection(F.edgesWith($)).length>0&&($.isParent()?M[P].push(R.get($.id())):M[P].push($.id()))})});var Lt=function(P){var $=N.get(P),K=void 0;u.get(P).forEach(function(Z){c.getElementById(Z).isParent()?K=R.get(Z):K=Z,M[$].push(K),M[N.get(K)].push(P)})},vt=!0,it=!1,ut=void 0;try{for(var Tt=u.keys()[Symbol.iterator](),At;!(vt=(At=Tt.next()).done);vt=!0){var Dt=At.value;Lt(Dt)}}catch(F){it=!0,ut=F}finally{try{!vt&&Tt.return&&Tt.return()}finally{if(it)throw ut}}m=N.size;var mt=void 0;if(m>2){S=m<o.sampleSize?m:o.sampleSize;for(var xt=0;xt<m;xt++)D[xt]=[];for(var St=0;St<S;St++)n[St]=[];return o.quality=="draft"||o.step=="all"?(q(I),V(),U(),mt={nodeIndexes:N,xCoords:b,yCoords:Q}):(N.forEach(function(F,P){b.push(c.getElementById(P).position("x")),Q.push(c.getElementById(P).position("y"))}),mt={nodeIndexes:N,xCoords:b,yCoords:Q}),mt}else{var Vt=N.keys(),Xt=c.getElementById(Vt.next().value),Ut=Xt.position(),bt=Xt.outerWidth();if(b.push(Ut.x),Q.push(Ut.y),m==2){var Ht=c.getElementById(Vt.next().value),Bt=Ht.outerWidth();b.push(Ut.x+bt/2+Bt/2+o.idealEdgeLength),Q.push(Ut.y)}return mt={nodeIndexes:N,xCoords:b,yCoords:Q},mt}};a.exports={spectralLayout:t}},579:(a,r,e)=>{var f=e(212),i=function(t){!t||t("layout","fcose",f)};typeof cytoscape!="undefined"&&i(cytoscape),a.exports=i},140:a=>{a.exports=A}},L={};function v(a){var r=L[a];if(r!==void 0)return r.exports;var e=L[a]={exports:{}};return H[a](e,e.exports,v),e.exports}var h=v(579);return h})()})})(Se);var hr=Se.exports,Oe={L:"left",R:"right",T:"top",B:"bottom"},De={L:nt(C=>`${C},${C/2} 0,${C} 0,0`,"L"),R:nt(C=>`0,${C/2} ${C},0 ${C},${C}`,"R"),T:nt(C=>`0,0 ${C},0 ${C/2},${C}`,"T"),B:nt(C=>`${C/2},0 ${C},${C} 0,${C}`,"B")},he={L:nt((C,G)=>C-G+2,"L"),R:nt((C,G)=>C-2,"R"),T:nt((C,G)=>C-G+2,"T"),B:nt((C,G)=>C-2,"B")},lr=nt(function(C){return zt(C)?C==="L"?"R":"L":C==="T"?"B":"T"},"getOppositeArchitectureDirection"),xe=nt(function(C){const G=C;return G==="L"||G==="R"||G==="T"||G==="B"},"isArchitectureDirection"),zt=nt(function(C){const G=C;return G==="L"||G==="R"},"isArchitectureDirectionX"),Qt=nt(function(C){const G=C;return G==="T"||G==="B"},"isArchitectureDirectionY"),Ne=nt(function(C,G){const A=zt(C)&&Qt(G),H=Qt(C)&&zt(G);return A||H},"isArchitectureDirectionXY"),fr=nt(function(C){const G=C[0],A=C[1],H=zt(G)&&Qt(A),L=Qt(G)&&zt(A);return H||L},"isArchitecturePairXY"),cr=nt(function(C){return C!=="LL"&&C!=="RR"&&C!=="TT"&&C!=="BB"},"isValidArchitectureDirectionPair"),ye=nt(function(C,G){const A=`${C}${G}`;return cr(A)?A:void 0},"getArchitectureDirectionPair"),gr=nt(function([C,G],A){const H=A[0],L=A[1];return zt(H)?Qt(L)?[C+(H==="L"?-1:1),G+(L==="T"?1:-1)]:[C+(H==="L"?-1:1),G]:zt(L)?[C+(L==="L"?1:-1),G+(H==="T"?1:-1)]:[C,G+(H==="T"?1:-1)]},"shiftPositionByArchitectureDirectionPair"),ur=nt(function(C){return C==="LT"||C==="TL"?[1,1]:C==="BL"||C==="LB"?[1,-1]:C==="BR"||C==="RB"?[-1,-1]:[-1,1]},"getArchitectureDirectionXYFactors"),dr=nt(function(C,G){return Ne(C,G)?"bend":zt(C)?"horizontal":"vertical"},"getArchitectureDirectionAlignment"),vr=nt(function(C){return C.type==="service"},"isArchitectureService"),pr=nt(function(C){return C.type==="junction"},"isArchitectureJunction"),Pe=nt(C=>C.data(),"edgeData"),ne=nt(C=>C.data(),"nodeData"),Ge=Ze.architecture,pt=new or(()=>({nodes:{},groups:{},edges:[],registeredIds:{},config:Ge,dataStructures:void 0,elements:{}})),yr=nt(()=>{pt.reset(),rr()},"clear"),Er=nt(function({id:C,icon:G,in:A,title:H,iconText:L}){if(pt.records.registeredIds[C]!==void 0)throw new Error(`The service id [${C}] is already in use by another ${pt.records.registeredIds[C]}`);if(A!==void 0){if(C===A)throw new Error(`The service [${C}] cannot be placed within itself`);if(pt.records.registeredIds[A]===void 0)throw new Error(`The service [${C}]'s parent does not exist. Please make sure the parent is created before this service`);if(pt.records.registeredIds[A]==="node")throw new Error(`The service [${C}]'s parent is not a group`)}pt.records.registeredIds[C]="node",pt.records.nodes[C]={id:C,type:"service",icon:G,iconText:L,title:H,edges:[],in:A}},"addService"),mr=nt(()=>Object.values(pt.records.nodes).filter(vr),"getServices"),Tr=nt(function({id:C,in:G}){pt.records.registeredIds[C]="node",pt.records.nodes[C]={id:C,type:"junction",edges:[],in:G}},"addJunction"),Nr=nt(()=>Object.values(pt.records.nodes).filter(pr),"getJunctions"),Lr=nt(()=>Object.values(pt.records.nodes),"getNodes"),Ee=nt(C=>pt.records.nodes[C],"getNode"),Cr=nt(function({id:C,icon:G,in:A,title:H}){if(pt.records.registeredIds[C]!==void 0)throw new Error(`The group id [${C}] is already in use by another ${pt.records.registeredIds[C]}`);if(A!==void 0){if(C===A)throw new Error(`The group [${C}] cannot be placed within itself`);if(pt.records.registeredIds[A]===void 0)throw new Error(`The group [${C}]'s parent does not exist. Please make sure the parent is created before this group`);if(pt.records.registeredIds[A]==="node")throw new Error(`The group [${C}]'s parent is not a group`)}pt.records.registeredIds[C]="group",pt.records.groups[C]={id:C,icon:G,title:H,in:A}},"addGroup"),Mr=nt(()=>Object.values(pt.records.groups),"getGroups"),Ar=nt(function({lhsId:C,rhsId:G,lhsDir:A,rhsDir:H,lhsInto:L,rhsInto:v,lhsGroup:h,rhsGroup:a,title:r}){if(!xe(A))throw new Error(`Invalid direction given for left hand side of edge ${C}--${G}. Expected (L,R,T,B) got ${A}`);if(!xe(H))throw new Error(`Invalid direction given for right hand side of edge ${C}--${G}. Expected (L,R,T,B) got ${H}`);if(pt.records.nodes[C]===void 0&&pt.records.groups[C]===void 0)throw new Error(`The left-hand id [${C}] does not yet exist. Please create the service/group before declaring an edge to it.`);if(pt.records.nodes[G]===void 0&&pt.records.groups[C]===void 0)throw new Error(`The right-hand id [${G}] does not yet exist. Please create the service/group before declaring an edge to it.`);const e=pt.records.nodes[C].in,f=pt.records.nodes[G].in;if(h&&e&&f&&e==f)throw new Error(`The left-hand id [${C}] is modified to traverse the group boundary, but the edge does not pass through two groups.`);if(a&&e&&f&&e==f)throw new Error(`The right-hand id [${G}] is modified to traverse the group boundary, but the edge does not pass through two groups.`);const i={lhsId:C,lhsDir:A,lhsInto:L,lhsGroup:h,rhsId:G,rhsDir:H,rhsInto:v,rhsGroup:a,title:r};pt.records.edges.push(i),pt.records.nodes[C]&&pt.records.nodes[G]&&(pt.records.nodes[C].edges.push(pt.records.edges[pt.records.edges.length-1]),pt.records.nodes[G].edges.push(pt.records.edges[pt.records.edges.length-1]))},"addEdge"),wr=nt(()=>pt.records.edges,"getEdges"),Or=nt(()=>{if(pt.records.dataStructures===void 0){const C={},G=Object.entries(pt.records.nodes).reduce((a,[r,e])=>(a[r]=e.edges.reduce((f,i)=>{var s,o,c,l;const g=(s=Ee(i.lhsId))==null?void 0:s.in,t=(o=Ee(i.rhsId))==null?void 0:o.in;if(g&&t&&g!==t){const T=dr(i.lhsDir,i.rhsDir);T!=="bend"&&((c=C[g])!=null||(C[g]={}),C[g][t]=T,(l=C[t])!=null||(C[t]={}),C[t][g]=T)}if(i.lhsId===r){const T=ye(i.lhsDir,i.rhsDir);T&&(f[T]=i.rhsId)}else{const T=ye(i.rhsDir,i.lhsDir);T&&(f[T]=i.lhsId)}return f},{}),a),{}),A=Object.keys(G)[0],H={[A]:1},L=Object.keys(G).reduce((a,r)=>r===A?a:{...a,[r]:1},{}),v=nt(a=>{const r={[a]:[0,0]},e=[a];for(;e.length>0;){const f=e.shift();if(f){H[f]=1,delete L[f];const i=G[f],[g,t]=r[f];Object.entries(i).forEach(([s,o])=>{H[o]||(r[o]=gr([g,t],s),e.push(o))})}}return r},"BFS"),h=[v(A)];for(;Object.keys(L).length>0;)h.push(v(Object.keys(L)[0]));pt.records.dataStructures={adjList:G,spatialMaps:h,groupAlignments:C}}return pt.records.dataStructures},"getDataStructures"),Dr=nt((C,G)=>{pt.records.elements[C]=G},"setElementForId"),xr=nt(C=>pt.records.elements[C],"getElementById"),le={clear:yr,setDiagramTitle:Je,getDiagramTitle:Qe,setAccTitle:Ke,getAccTitle:je,setAccDescription:_e,getAccDescription:tr,addService:Er,getServices:mr,addJunction:Tr,getJunctions:Nr,getNodes:Lr,getNode:Ee,addGroup:Cr,getGroups:Mr,addEdge:Ar,getEdges:wr,setElementForId:Dr,getElementById:xr,getDataStructures:Or};function Pt(C){const G=fe().architecture;return G!=null&&G[C]?G[C]:Ge[C]}nt(Pt,"getConfigField");var Ir=nt((C,G)=>{nr(C,G),C.groups.map(G.addGroup),C.services.map(A=>G.addService({...A,type:"service"})),C.junctions.map(A=>G.addJunction({...A,type:"junction"})),C.edges.map(G.addEdge)},"populateDb"),Rr={parse:nt(async C=>{const G=await sr("architecture",C);Ie.debug(G),Ir(G,le)},"parse")},Sr=nt(C=>`
  .edge {
    stroke-width: ${C.archEdgeWidth};
    stroke: ${C.archEdgeColor};
    fill: none;
  }

  .arrow {
    fill: ${C.archEdgeArrowColor};
  }

  .node-bkg {
    fill: none;
    stroke: ${C.archGroupBorderColor};
    stroke-width: ${C.archGroupBorderWidth};
    stroke-dasharray: 8;
  }
  .node-icon-text {
    display: flex; 
    align-items: center;
  }
  
  .node-icon-text > div {
    color: #fff;
    margin: 1px;
    height: fit-content;
    text-align: center;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }
`,"getStyles"),Fr=Sr,ae=nt(C=>`<g><rect width="80" height="80" style="fill: #087ebf; stroke-width: 0px;"/>${C}</g>`,"wrapIcon"),oe={prefix:"mermaid-architecture",height:80,width:80,icons:{database:{body:ae('<path id="b" data-name="4" d="m20,57.86c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path id="c" data-name="3" d="m20,45.95c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path id="d" data-name="2" d="m20,34.05c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse id="e" data-name="1" cx="40" cy="22.14" rx="20" ry="7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="20" y1="57.86" x2="20" y2="22.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="60" y1="57.86" x2="60" y2="22.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},server:{body:ae('<rect x="17.5" y="17.5" width="45" height="45" rx="2" ry="2" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="32.5" x2="62.5" y2="32.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="47.5" x2="62.5" y2="47.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><g><path d="m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><path d="m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><path d="m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g>')},disk:{body:ae('<rect x="20" y="15" width="40" height="50" rx="1" ry="1" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="24" cy="19.17" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="56" cy="19.17" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="24" cy="60.83" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="56" cy="60.83" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="40" cy="33.75" rx="14" ry="14.58" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="40" cy="33.75" rx="4" ry="4.17" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m37.51,42.52l-4.83,13.22c-.26.71-1.1,1.02-1.76.64l-4.18-2.42c-.66-.38-.81-1.26-.33-1.84l9.01-10.8c.88-1.05,2.56-.08,2.09,1.2Z" style="fill: #fff; stroke-width: 0px;"/>')},internet:{body:ae('<circle cx="40" cy="40" r="22.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="40" y1="17.5" x2="40" y2="62.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="40" x2="62.5" y2="40" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m39.99,17.51c-15.28,11.1-15.28,33.88,0,44.98" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m40.01,17.51c15.28,11.1,15.28,33.88,0,44.98" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="19.75" y1="30.1" x2="60.25" y2="30.1" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="19.75" y1="49.9" x2="60.25" y2="49.9" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},cloud:{body:ae('<path d="m65,47.5c0,2.76-2.24,5-5,5H20c-2.76,0-5-2.24-5-5,0-1.87,1.03-3.51,2.56-4.36-.04-.21-.06-.42-.06-.64,0-2.6,2.48-4.74,5.65-4.97,1.65-4.51,6.34-7.76,11.85-7.76.86,0,1.69.08,2.5.23,2.09-1.57,4.69-2.5,7.5-2.5,6.1,0,11.19,4.38,12.28,10.17,2.14.56,3.72,2.51,3.72,4.83,0,.03,0,.07-.01.1,2.29.46,4.01,2.48,4.01,4.9Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},unknown:ke,blank:{body:ae("")}}},br=nt(async function(C,G){const A=Pt("padding"),H=Pt("iconSize"),L=H/2,v=H/6,h=v/2;await Promise.all(G.edges().map(async a=>{var b,Q;const{source:r,sourceDir:e,sourceArrow:f,sourceGroup:i,target:g,targetDir:t,targetArrow:s,targetGroup:o,label:c}=Pe(a);let{x:l,y:T}=a[0].sourceEndpoint();const{x:d,y:u}=a[0].midpoint();let{x:N,y:R}=a[0].targetEndpoint();const M=A+4;if(i&&(zt(e)?l+=e==="L"?-M:M:T+=e==="T"?-M:M+18),o&&(zt(t)?N+=t==="L"?-M:M:R+=t==="T"?-M:M+18),!i&&((b=le.getNode(r))==null?void 0:b.type)==="junction"&&(zt(e)?l+=e==="L"?L:-L:T+=e==="T"?L:-L),!o&&((Q=le.getNode(g))==null?void 0:Q.type)==="junction"&&(zt(t)?N+=t==="L"?L:-L:R+=t==="T"?L:-L),a[0]._private.rscratch){const Y=C.insert("g");if(Y.insert("path").attr("d",`M ${l},${T} L ${d},${u} L${N},${R} `).attr("class","edge"),f){const k=zt(e)?he[e](l,v):l-h,D=Qt(e)?he[e](T,v):T-h;Y.insert("polygon").attr("points",De[e](v)).attr("transform",`translate(${k},${D})`).attr("class","arrow")}if(s){const k=zt(t)?he[t](N,v):N-h,D=Qt(t)?he[t](R,v):R-h;Y.insert("polygon").attr("points",De[t](v)).attr("transform",`translate(${k},${D})`).attr("class","arrow")}if(c){const k=Ne(e,t)?"XY":zt(e)?"X":"Y";let D=0;k==="X"?D=Math.abs(l-N):k==="Y"?D=Math.abs(T-R)/1.5:D=Math.abs(l-N)/2;const rt=Y.append("g");if(await Te(rt,c,{useHtmlLabels:!1,width:D,classes:"architecture-service-label"},fe()),rt.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle"),k==="X")rt.attr("transform","translate("+d+", "+u+")");else if(k==="Y")rt.attr("transform","translate("+d+", "+u+") rotate(-90)");else if(k==="XY"){const n=ye(e,t);if(n&&fr(n)){const m=rt.node().getBoundingClientRect(),[p,E]=ur(n);rt.attr("dominant-baseline","auto").attr("transform",`rotate(${-1*p*E*45})`);const y=rt.node().getBoundingClientRect();rt.attr("transform",`
                translate(${d}, ${u-m.height/2})
                translate(${p*y.width/2}, ${E*y.height/2})
                rotate(${-1*p*E*45}, 0, ${m.height/2})
              `)}}}}}))},"drawEdges"),Pr=nt(async function(C,G){const H=Pt("padding")*.75,L=Pt("fontSize"),h=Pt("iconSize")/2;await Promise.all(G.nodes().map(async a=>{const r=ne(a);if(r.type==="group"){const{h:e,w:f,x1:i,y1:g}=a.boundingBox();C.append("rect").attr("x",i+h).attr("y",g+h).attr("width",f).attr("height",e).attr("class","node-bkg");const t=C.append("g");let s=i,o=g;if(r.icon){const c=t.append("g");c.html(`<g>${await pe(r.icon,{height:H,width:H,fallbackPrefix:oe.prefix})}</g>`),c.attr("transform","translate("+(s+h+1)+", "+(o+h+1)+")"),s+=H,o+=L/2-1-2}if(r.label){const c=t.append("g");await Te(c,r.label,{useHtmlLabels:!1,width:f,classes:"architecture-service-label"},fe()),c.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","start").attr("text-anchor","start"),c.attr("transform","translate("+(s+h+4)+", "+(o+h+2)+")")}}}))},"drawGroups"),Gr=nt(async function(C,G,A){var H;for(const L of A){const v=G.append("g"),h=Pt("iconSize");if(L.title){const f=v.append("g");await Te(f,L.title,{useHtmlLabels:!1,width:h*1.5,classes:"architecture-service-label"},fe()),f.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle"),f.attr("transform","translate("+h/2+", "+h+")")}const a=v.append("g");if(L.icon)a.html(`<g>${await pe(L.icon,{height:h,width:h,fallbackPrefix:oe.prefix})}</g>`);else if(L.iconText){a.html(`<g>${await pe("blank",{height:h,width:h,fallbackPrefix:oe.prefix})}</g>`);const g=a.append("g").append("foreignObject").attr("width",h).attr("height",h).append("div").attr("class","node-icon-text").attr("style",`height: ${h}px;`).append("div").html(L.iconText),t=(H=parseInt(window.getComputedStyle(g.node(),null).getPropertyValue("font-size").replace(/\D/g,"")))!=null?H:16;g.attr("style",`-webkit-line-clamp: ${Math.floor((h-2)/t)};`)}else a.append("path").attr("class","node-bkg").attr("id","node-"+L.id).attr("d",`M0 ${h} v${-h} q0,-5 5,-5 h${h} q5,0 5,5 v${h} H0 Z`);v.attr("class","architecture-service");const{width:r,height:e}=v._groups[0][0].getBBox();L.width=r,L.height=e,C.setElementForId(L.id,v)}return 0},"drawServices"),Ur=nt(function(C,G,A){A.forEach(H=>{const L=G.append("g"),v=Pt("iconSize");L.append("g").append("rect").attr("id","node-"+H.id).attr("fill-opacity","0").attr("width",v).attr("height",v),L.attr("class","architecture-junction");const{width:a,height:r}=L._groups[0][0].getBBox();L.width=a,L.height=r,C.setElementForId(H.id,L)})},"drawJunctions");qe([{name:oe.prefix,icons:oe}]);Re.use(hr);function Ue(C,G){C.forEach(A=>{G.add({group:"nodes",data:{type:"service",id:A.id,icon:A.icon,label:A.title,parent:A.in,width:Pt("iconSize"),height:Pt("iconSize")},classes:"node-service"})})}nt(Ue,"addServices");function Ye(C,G){C.forEach(A=>{G.add({group:"nodes",data:{type:"junction",id:A.id,parent:A.in,width:Pt("iconSize"),height:Pt("iconSize")},classes:"node-junction"})})}nt(Ye,"addJunctions");function Xe(C,G){G.nodes().map(A=>{const H=ne(A);if(H.type==="group")return;H.x=A.position().x,H.y=A.position().y,C.getElementById(H.id).attr("transform","translate("+(H.x||0)+","+(H.y||0)+")")})}nt(Xe,"positionNodes");function He(C,G){C.forEach(A=>{G.add({group:"nodes",data:{type:"group",id:A.id,icon:A.icon,label:A.title,parent:A.in},classes:"node-group"})})}nt(He,"addGroups");function We(C,G){C.forEach(A=>{const{lhsId:H,rhsId:L,lhsInto:v,lhsGroup:h,rhsInto:a,lhsDir:r,rhsDir:e,rhsGroup:f,title:i}=A,g=Ne(A.lhsDir,A.rhsDir)?"segments":"straight",t={id:`${H}-${L}`,label:i,source:H,sourceDir:r,sourceArrow:v,sourceGroup:h,sourceEndpoint:r==="L"?"0 50%":r==="R"?"100% 50%":r==="T"?"50% 0":"50% 100%",target:L,targetDir:e,targetArrow:a,targetGroup:f,targetEndpoint:e==="L"?"0 50%":e==="R"?"100% 50%":e==="T"?"50% 0":"50% 100%"};G.add({group:"edges",data:t,classes:g})})}nt(We,"addEdges");function Ve(C,G,A){const H=nt((a,r)=>Object.entries(a).reduce((e,[f,i])=>{var s,o,c;let g=0;const t=Object.entries(i);if(t.length===1)return e[f]=t[0][1],e;for(let l=0;l<t.length-1;l++)for(let T=l+1;T<t.length;T++){const[d,u]=t[l],[N,R]=t[T];if(((s=A[d])==null?void 0:s[N])===r)(o=e[f])!=null||(e[f]=[]),e[f]=[...e[f],...u,...R];else if(d==="default"||N==="default")(c=e[f])!=null||(e[f]=[]),e[f]=[...e[f],...u,...R];else{const b=`${f}-${g++}`;e[b]=u;const Q=`${f}-${g++}`;e[Q]=R}}return e},{}),"flattenAlignments"),L=G.map(a=>{const r={},e={};return Object.entries(a).forEach(([f,[i,g]])=>{var s,o,c,l,T,d,u,N;const t=(o=(s=C.getNode(f))==null?void 0:s.in)!=null?o:"default";(c=r[g])!=null||(r[g]={}),(T=(l=r[g])[t])!=null||(l[t]=[]),r[g][t].push(f),(d=e[i])!=null||(e[i]={}),(N=(u=e[i])[t])!=null||(u[t]=[]),e[i][t].push(f)}),{horiz:Object.values(H(r,"horizontal")).filter(f=>f.length>1),vert:Object.values(H(e,"vertical")).filter(f=>f.length>1)}}),[v,h]=L.reduce(([a,r],{horiz:e,vert:f})=>[[...a,...e],[...r,...f]],[[],[]]);return{horizontal:v,vertical:h}}nt(Ve,"getAlignments");function ze(C){const G=[],A=nt(L=>`${L[0]},${L[1]}`,"posToStr"),H=nt(L=>L.split(",").map(v=>parseInt(v)),"strToPos");return C.forEach(L=>{const v=Object.fromEntries(Object.entries(L).map(([e,f])=>[A(f),e])),h=[A([0,0])],a={},r={L:[-1,0],R:[1,0],T:[0,1],B:[0,-1]};for(;h.length>0;){const e=h.shift();if(e){a[e]=1;const f=v[e];if(f){const i=H(e);Object.entries(r).forEach(([g,t])=>{const s=A([i[0]+t[0],i[1]+t[1]]),o=v[s];o&&!a[s]&&(h.push(s),G.push({[Oe[g]]:o,[Oe[lr(g)]]:f,gap:1.5*Pt("iconSize")}))})}}}}),G}nt(ze,"getRelativeConstraints");function Be(C,G,A,H,L,{spatialMaps:v,groupAlignments:h}){return new Promise(a=>{const r=er("body").append("div").attr("id","cy").attr("style","display:none"),e=Re({container:document.getElementById("cy"),style:[{selector:"edge",style:{"curve-style":"straight",label:"data(label)","source-endpoint":"data(sourceEndpoint)","target-endpoint":"data(targetEndpoint)"}},{selector:"edge.segments",style:{"curve-style":"segments","segment-weights":"0","segment-distances":[.5],"edge-distances":"endpoints","source-endpoint":"data(sourceEndpoint)","target-endpoint":"data(targetEndpoint)"}},{selector:"node",style:{"compound-sizing-wrt-labels":"include"}},{selector:"node[label]",style:{"text-valign":"bottom","text-halign":"center","font-size":`${Pt("fontSize")}px`}},{selector:".node-service",style:{label:"data(label)",width:"data(width)",height:"data(height)"}},{selector:".node-junction",style:{width:"data(width)",height:"data(height)"}},{selector:".node-group",style:{padding:`${Pt("padding")}px`}}]});r.remove(),He(A,e),Ue(C,e),Ye(G,e),We(H,e);const f=Ve(L,v,h),i=ze(v),g=e.layout({name:"fcose",quality:"proof",styleEnabled:!1,animate:!1,nodeDimensionsIncludeLabels:!1,idealEdgeLength(t){const[s,o]=t.connectedNodes(),{parent:c}=ne(s),{parent:l}=ne(o);return c===l?1.5*Pt("iconSize"):.5*Pt("iconSize")},edgeElasticity(t){const[s,o]=t.connectedNodes(),{parent:c}=ne(s),{parent:l}=ne(o);return c===l?.45:.001},alignmentConstraint:f,relativePlacementConstraint:i});g.one("layoutstop",()=>{var s;function t(o,c,l,T){let d,u;const{x:N,y:R}=o,{x:M,y:b}=c;u=(T-R+(N-l)*(R-b)/(N-M))/Math.sqrt(1+Math.pow((R-b)/(N-M),2)),d=Math.sqrt(Math.pow(T-R,2)+Math.pow(l-N,2)-Math.pow(u,2));const Q=Math.sqrt(Math.pow(M-N,2)+Math.pow(b-R,2));d=d/Q;let Y=(M-N)*(T-R)-(b-R)*(l-N);switch(!0){case Y>=0:Y=1;break;case Y<0:Y=-1;break}let k=(M-N)*(l-N)+(b-R)*(T-R);switch(!0){case k>=0:k=1;break;case k<0:k=-1;break}return u=Math.abs(u)*Y,d=d*k,{distances:u,weights:d}}nt(t,"getSegmentWeights"),e.startBatch();for(const o of Object.values(e.edges()))if((s=o.data)!=null&&s.call(o)){const{x:c,y:l}=o.source().position(),{x:T,y:d}=o.target().position();if(c!==T&&l!==d){const u=o.sourceEndpoint(),N=o.targetEndpoint(),{sourceDir:R}=Pe(o),[M,b]=Qt(R)?[u.x,N.y]:[N.x,u.y],{weights:Q,distances:Y}=t(u,N,M,b);o.style("segment-distances",Y),o.style("segment-weights",Q)}}e.endBatch(),g.run()}),g.run(),e.ready(t=>{Ie.info("Ready",t),a(e)})})}nt(Be,"layoutArchitecture");var Yr=nt(async(C,G,A,H)=>{const L=H.db,v=L.getServices(),h=L.getJunctions(),a=L.getGroups(),r=L.getEdges(),e=L.getDataStructures(),f=ir(G),i=f.append("g");i.attr("class","architecture-edges");const g=f.append("g");g.attr("class","architecture-services");const t=f.append("g");t.attr("class","architecture-groups"),await Gr(L,g,v),Ur(L,g,h);const s=await Be(v,h,a,r,L,e);await br(i,s),await Pr(t,s),Xe(L,s),ar(void 0,f,Pt("padding"),Pt("useMaxWidth"))},"draw"),Xr={draw:Yr},qr={parser:Rr,db:le,renderer:Xr,styles:Fr};export{qr as diagram};
