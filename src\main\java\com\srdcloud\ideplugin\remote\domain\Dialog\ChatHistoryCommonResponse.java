package com.srdcloud.ideplugin.remote.domain.Dialog;


import java.io.Serializable;

/**
 * <AUTHOR> yangy
 * @create 2024/5/13 11:25
 */
public class ChatHistoryCommonResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 消息状态值，0表示操作成功，其他值表示失败
     */
    private int optResult;

    /**
     * 操作失败说明
     */
    private String msg;

    /**
     * 错误信息
     */
    private String errMsg;

    public int getOptResult() {
        return optResult;
    }

    public void setOptResult(int optResult) {
        this.optResult = optResult;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }
}
