package com.srdcloud.ideplugin.assistant;

import com.google.common.collect.Lists;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.project.DumbAwareAction;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Key;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowFactory;
import com.intellij.openapi.wm.ToolWindowManager;
import com.intellij.openapi.wm.ex.ToolWindowEx;
import com.intellij.ui.content.Content;
import com.srdcloud.ideplugin.actions.IndexProgressAction;
import com.srdcloud.ideplugin.common.icons.MyIcons;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.icons.GPTIcons;
import com.srdcloud.ideplugin.general.utils.*;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.swing.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 编程助手 toolWindow
 *
 * <AUTHOR>
 */
public class AssistantToolWindow implements ToolWindowFactory {

    private static final Logger logger = LoggerFactory.getLogger(AssistantToolWindow.class);

    /**
     * 本地缓存：本次IDE使用期间，不同项目打开的toolWindow实例唯一标识key与项目名之间的映射关系
     */
    public static Map<String, Key> ASSISTANT_TOOL_WINDOW_KEY_MAP = new HashMap<>();

    /**
     * 当前处于活跃状态的项目的key标识【全局】
     */
    public static Key ACTIVE_ASSISTANT_TOOL_WINDOW_KEY = null;

    private Project project;

    //@Override
    //public void init(@NotNull ToolWindow toolWindow) {
    //    Project project = IdeUtil.findCurrentProject();
    //    if (project == null) {
    //        project = ProjectManager.getInstance().getDefaultProject();
    //    }
    //    final Project finalProject = project;
    //
    //    // toolWindow扩展设置
    //    ToolWindowEx tw = (ToolWindowEx) toolWindow;


    // 会引起Webview osr渲染问题
    //tw.setAutoHide(true);

    // 进行扩展设置
    //ToolWindowManagerEx twmEx = ToolWindowManagerEx.getInstanceEx(project);
    //twmEx.addToolWindowManagerListener(new ToolWindowManagerListener() {
    //    /**
    //     * 插件状态变化，包括：大小调整、激活状态变化等
    //     */
    //    @Override
    //    public void stateChanged(@NotNull ToolWindowManager toolWindowManager) {
    //        if (toolWindow.isActive()) {
    //            DebugLogUtil.println("AssistantToolWindow stateChanged: active");
    //            CodeChatWebviewRefreshTask.getInstance(finalProject).addTaskItem(finalProject);
    //        }
    //    }
    //});
    //}

    @Override
    public void createToolWindowContent(@NotNull Project project, @NotNull ToolWindow toolWindow) {
        // 暗黑模式，修改侧边栏logo
        boolean isDark = UIUtil.judgeBackgroudDarkTheme();
        if (isDark) {
            toolWindow.setIcon(GPTIcons.TOOL_WINDOW_DARK);
        }

        // toolWindow扩展
        ToolWindowEx tw = (ToolWindowEx) toolWindow;

        // 获取项目唯一标识
        this.project = project;
        String projectLocationHash = project.getLocationHash();
        logger.info("[cf] createToolWindowContent init,projectLocationHash:<{}>,projectName:<{}>", projectLocationHash, project.getName());

        // 初始化toolWindow实例、各功能tab面板实例唯一标识key
        ACTIVE_ASSISTANT_TOOL_WINDOW_KEY = IdeUtil.buildToolWindowKey(projectLocationHash);

        // 扩展header栏
        configureToolWindowHeader(tw);

        // 扩展装饰栏挂载
        //addTreeSitter(tw, project);

        // 添加toolWindow内容
        CodeChatWebview codeChatWebview = CodeChatWebview.getInstance(project);
        Content mainContent = toolWindow.getContentManager().getFactory().createContent(codeChatWebview.getContentComponent(), null, false);
        mainContent.setCloseable(false);
        toolWindow.getContentManager().addContent(mainContent);

        // 副作用：会导致新开项目第一次点开时，无法展开，需要再次点击才能展开侧边窗；
        // 其次，在2022版本会抛出 Caused by: java.lang.InternalError: couldn't create component peer 异常
        //toolWindow.hide();
    }

    /**
     * 设置 ToolWindowHeader 的内容
     */
    private void configureToolWindowHeader(ToolWindowEx toolWindowEx) {
        final List<AnAction> actions = Lists.newArrayListWithExpectedSize(4);
        boolean isDark = UIUtil.judgeBackgroudDarkTheme();

        Icon indexing = MyIcons.AgentIndexing;
        Icon help = MyIcons.Help;
        Icon feedback = MyIcons.FeedBack;
        Icon refresh = MyIcons.Refresh;

        // 暗黑主题
        if (isDark) {
            indexing = MyIcons.AgentIndexingDark;
            help = MyIcons.HelpDark;
            feedback = MyIcons.FeedBackDark;
            refresh = MyIcons.RefreshDark;
        }

        // agent 索引进度展示
        AnAction indexProgressAction = new IndexProgressAction(toolWindowEx.getProject(), indexing);
        actions.add(indexProgressAction);

        AnAction refreshAction = new DumbAwareAction("刷新", "Refresh the chat window", refresh) {

            @Override
            public void actionPerformed(@NotNull AnActionEvent e) {
                Project project = Objects.nonNull(e.getProject()) ? e.getProject() : IdeUtil.findCurrentProject();
                if (Objects.isNull(project)) {
                    return;
                }

                if (CodeChatWebview.getInstance(project).checkReloadAble()) {
                    MessageBalloonNotificationUtil.showCommonNotificationWithConfirm(project, "重新装载问答窗口，请稍候...");
                    CodeChatWebview.getInstance(project).reloadWebview();
                } else {
                    MessageBalloonNotificationUtil.showCommonNotificationWithConfirm(project, "操作频繁，请稍后再试...");
                }
            }
        };
        actions.add(refreshAction);

        // 内外部版本功能隔离：内部版才有帮助和反馈按钮
        if (ConfigWrapper.isInnerVersion) {
            AnAction helpAction = new DumbAwareAction("帮助", "Open help in browser.", help) {
                @Override
                public void actionPerformed(@NotNull AnActionEvent e) {
                    BrowseUtil.Companion.browse(ConfigWrapper.HelpDocsPageUrl);
                }
            };
            actions.add(helpAction);

            AnAction feedBackAction = new DumbAwareAction("反馈", "Open feedback in browser.", feedback) {
                @Override
                public void actionPerformed(@NotNull AnActionEvent e) {
                    BrowseUtil.Companion.browse(ConfigWrapper.FeedBackPageUrl);
                }
            };
            actions.add(feedBackAction);
        }

        // 将 Action 添加到 ToolWindow 的标题栏
        toolWindowEx.setTitleActions(actions);
    }

    public static ToolWindow getToolWindow(Project project) {
        Key key = AssistantToolWindow.ASSISTANT_TOOL_WINDOW_KEY_MAP.get(project.getLocationHash());
        if (key == null) {
            key = IdeUtil.buildToolWindowKey(project.getLocationHash());
        }
        Object toolWindow = project.getUserData(key);
        if (toolWindow == null) {
            toolWindow = ToolWindowManager.getInstance(project).getToolWindow(EnvUtil.isSec("海云智码","研发云CodeFree"));
        }
        return (ToolWindow) toolWindow;
    }


    /**
     * show 方法有时会不起效，但是不能把if改while，存在卡死风险
     *
     * @param project
     */
    public static void toolWindowVisible(Project project) {
        ToolWindow toolWindow = getToolWindow(project);
        if (toolWindow == null) {
            logger.warn("[cf] toolWindowVisible fail,toolWindow is null");
            return;
        }

        if (!toolWindow.isVisible()) {
            // 如果Tool Window处于隐藏状态，则显示
            toolWindow.show(null);
        }
    }

    /**
     * 挂载TreeSitter测试入口
     * 已下线，rlcc内置在tabby-agent中
     */
    //@Deprecated
    //private void addTreeSitter(ToolWindowEx tw, Project project) {
    //    // 调试模式下，挂载WebviewTreeSitter到窗口左侧，提供调试入口
    //    if (EnvUtil.checkTreeSitterMountAble() && JBCefApp.isSupported()) {
    //        DebugLogUtil.println("addTreeSitter to toolWindow");
    //
    //        if (tw.getDecorator() == null) {
    //            logger.warn("[cf] addTreeSitter to toolWindow fail,tw.getDecorator() is null");
    //            return;
    //        }
    //
    //        JPanel webViewContainer = new JPanel();
    //        WebviewTreeSitter treeSitterInstance = WebviewTreeSitter.getInstance(project);
    //        if (treeSitterInstance.getWebView() == null) {
    //            treeSitterInstance.loadWebview();
    //        }
    //
    //        Component webView = treeSitterInstance.getTreeSitterWebViewComponent();
    //        webViewContainer.setOpaque(false);
    //        webViewContainer.setPreferredSize(new Dimension(10, 100));
    //        webViewContainer.setMaximumSize(new Dimension(10, 100));
    //        webView.setPreferredSize(new Dimension(10, 100));
    //        webViewContainer.add(webView);
    //
    //        tw.getDecorator().add(webViewContainer, BorderLayout.WEST);
    //    }
    //}
}
