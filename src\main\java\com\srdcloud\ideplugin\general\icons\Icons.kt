package com.srdcloud.ideplugin.common.icons

import com.intellij.icons.AllIcons
import com.intellij.openapi.util.IconLoader
object MyIcons {

    @JvmField
    val codefree = IconLoader.getIcon("/icons/srd_toolWindow.svg", javaClass)

    @JvmField
    val codefreeDark = IconLoader.getIcon("/icons/srd_toolWindow_dark.svg", javaClass)

    @JvmField
    val stop = AllIcons.Actions.Suspend

    // 状态栏popup菜单图标
    @JvmField
    val codedisable = IconLoader.getIcon("/icons/popup/icon-codedisable-ide.svg", javaClass)

    @JvmField
    val codedisableDark = IconLoader.getIcon("/icons/popup/icon-codedisable-ide-dark.svg", javaClass)

    @JvmField
    val codeenabling = IconLoader.getIcon("/icons/popup/icon-codeenabling-ide.svg", javaClass)

    @JvmField
    val codeenablingDark = IconLoader.getIcon("/icons/popup/icon-codeenabling-ide-dark.svg", javaClass)

    @JvmField
    val codemanual = IconLoader.getIcon("/icons/popup/icon-codemanual-ide.svg", javaClass)

    @JvmField
    val codemanualDark = IconLoader.getIcon("/icons/popup/icon-codemanual-ide-dark.svg", javaClass)

    @JvmField
    val helptips = IconLoader.getIcon("/icons/popup/icon-helptips.svg", javaClass)

    @JvmField
    val helptipsDark = IconLoader.getIcon("/icons/popup/icon-helptips-dark.svg", javaClass)

    @JvmField
    val login = IconLoader.getIcon("/icons/popup/icon-login-ide.svg", javaClass)

    @JvmField
    val loginDark = IconLoader.getIcon("/icons/popup/icon-login-ide-dark.svg", javaClass)

    @JvmField
    val logout = IconLoader.getIcon("/icons/popup/icon-logout-ide.svg", javaClass)

    @JvmField
    val logoutDark = IconLoader.getIcon("/icons/popup/icon-logout-ide-dark.svg", javaClass)

    @JvmField
    val off = IconLoader.getIcon("/icons/popup/icon-off.svg", javaClass)

    @JvmField
    val offDark = IconLoader.getIcon("/icons/popup/icon-off-dark.svg", javaClass)

    @JvmField
    val Refresh = IconLoader.getIcon("/icons/refresh.svg", javaClass)

    @JvmField
    val RefreshDark = IconLoader.getIcon("/icons/refresh_dark.svg", javaClass)

    @JvmField
    val Help = IconLoader.getIcon("/icons/help.svg", javaClass)

    @JvmField
    val HelpDark = IconLoader.getIcon("/icons/help_dark.svg", javaClass)

    @JvmField
    val FeedBack = IconLoader.getIcon("/icons/feedback.svg", javaClass)

    @JvmField
    val FeedBackDark = IconLoader.getIcon("/icons/feedback_dark.svg", javaClass)

    @JvmField
    val Login = IconLoader.getIcon("/icons/srd_login.svg", javaClass)

    @JvmField
    val Logout = IconLoader.getIcon("/icons/srd_logout.svg", javaClass)

    @JvmField
    val NotLoggedIn = IconLoader.getIcon("/icons/srd_not_logged_in.svg", javaClass)

    @JvmField
    val Disconnect = IconLoader.getIcon("/icons/srd_disconnect.svg", javaClass)

    @JvmField
    val CompletionEnable = IconLoader.getIcon("/icons/srd_completion_enable.svg", javaClass)

    @JvmField
    val CompletionDisable = IconLoader.getIcon("/icons/srd_completion_disable.svg", javaClass)

    @JvmField
    val CompletionError = IconLoader.getIcon("/icons/srd_completion_error.svg", javaClass)

    @JvmField
    val SrdCloud = IconLoader.getIcon("/icons/srdcloud.svg", javaClass)

    @JvmField
    var NLC = IconLoader.getIcon("/icons/srd_nlc.svg", javaClass)

    @JvmField
    var NLC_Select = IconLoader.getIcon("/icons/srd_nlc_select.svg", javaClass)

    @JvmField
    var NLC_Delete = IconLoader.getIcon("/icons/srd_nlc_delete.svg", javaClass)

    @JvmField
    var AgentSyncFailure = IconLoader.getIcon("/icons/agent_sync_failure.svg", javaClass)

    @JvmField
    var AgentStartFailure = IconLoader.getIcon("/icons/agent_start_failure.svg", javaClass)

    @JvmField
    var AgentIndexing = IconLoader.getIcon("/icons/agent_indexing.svg", javaClass)

    @JvmField
    var AgentIndexingDark = IconLoader.getIcon("/icons/agent_indexing_dark.svg", javaClass)
}