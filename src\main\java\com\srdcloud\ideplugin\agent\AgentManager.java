package com.srdcloud.ideplugin.agent;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.agent.commclient.AgentCommClient;
import com.srdcloud.ideplugin.agent.commclient.AgentMessageReceiver;
import com.srdcloud.ideplugin.agent.config.AgentConfig;
import com.srdcloud.ideplugin.agent.config.AgentPath;
import com.srdcloud.ideplugin.agent.config.ConfigFileManager;
import com.srdcloud.ideplugin.agent.config.NodeAgentPath;
import com.srdcloud.ideplugin.agent.commclient.CoreAgentClient;
import com.srdcloud.ideplugin.agent.download.AgentDownloader;
import com.srdcloud.ideplugin.agent.download.AgentVersionChecker;
import com.srdcloud.ideplugin.agent.model.AgentVersion;
import com.srdcloud.ideplugin.agent.process.AgentProcess;
import com.srdcloud.ideplugin.agent.process.CoreAgentProcess;
import com.srdcloud.ideplugin.agent.process.TabbyAgentProcess;
import com.srdcloud.ideplugin.agent.commclient.TabbyAgentClient;
import com.srdcloud.ideplugin.agent.commclient.TabbyAgentMessageReceiver;
import com.srdcloud.ideplugin.codeindex.CodeIndexService;
import com.srdcloud.ideplugin.composer.ComposerService;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.constants.AgentNameConstant;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.utils.*;
import com.srdcloud.ideplugin.statusbar.Notify;
import kotlinx.serialization.json.Json;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;

public class AgentManager {

    private static final Logger logger = LoggerFactory.getLogger(AgentManager.class);

    private final static AgentConfig AGENT_CONFIG =
            // todo：抽取成常量
            new AgentConfig(LocalStorageUtil.getApikey(),
                    LocalStorageUtil.getUserId(),
                    EnvUtil.isSec("oscap","codefree"),
                    IdeUtil.getPluginVersion(),
                    "idea",
                    IdeUtil.getIDEVersion(),
                    EnvUtil.isSec("oscap","codefree"),
                    ConfigWrapper.getServerUrl() ,
                    "api/aebackend",
                    "api/acbackend",
                    "emb-composer",
                    "rerank-composer",
                    "codefree-plugin-composer-chat",
                    ConfigWrapper.env,
                    JsonUtil.getInstance().toJson(IgnoreUtils.getIgnoreRule()));

    private final static AgentVersionChecker VERSION_CHECKER =
            new AgentVersionChecker(null, AGENT_CONFIG.getInvokerId(),
                    AGENT_CONFIG.getApiKey(), AGENT_CONFIG);
    ;

    private final static AgentDownloader DOWNLOADER = new AgentDownloader(null, AGENT_CONFIG.getInvokerId(),
            AGENT_CONFIG.getApiKey(), 3);
    ;

    private final Map<String, AgentProcess> agentProcesses;
    private final Map<String, AgentCommClient> agentCommClients;
    private final Map<String, AgentMessageReceiver> messageReceivers;
    private AgentPath nodePath;
    private Project project;

    // 初始化状态
    private final AtomicReference<AgentStatus> status = new AtomicReference<>(AgentStatus.IDLE);

    /**
     * Agent状态枚举
     */
    public enum AgentStatus {
        IDLE, // 空闲中

        INITIALIZING, // 初始化中
        DOWNLOADING,
        INITIALIZED, // 初始化完成

        FAILURE_DOWNLOAD, // 下载失败
        FAILURE_START // 启动失败
    }

    private AgentManager(Project project) {

        // todo 是否去掉node在此处初始化
        nodePath = new NodeAgentPath(EnvUtil.isSec("oscap","codefree"));
        // 启动海云安core
//        this.agentConfig = new AgentConfig("", "", "oscap", "", "", "", "oscap", "https://oscap-poc1.out.secidea.com:40081/oscap", "", "", "");
//        nodePath = new NodeAgentPath("oscap");

        this.project = project;
        this.messageReceivers = new ConcurrentHashMap<>();

        this.agentProcesses = new ConcurrentHashMap<>();
        this.agentCommClients = new ConcurrentHashMap<>();
    }


    /**
     * 获取项目级别的AgentManager实例
     */
    public static AgentManager getInstance(Project project) {
        return project.getService(AgentManager.class);
    }

    /**
     * 初始化 AgentManager，并统一在此处设置启动相关状态
     * @param project 当前项目
     * @param agentVersions 最新 agent 版本信息
     */
    public void initialize(Project project, List<AgentVersion> agentVersions) throws IOException, InterruptedException {
        try {
            // 如果多个初始化同时到达此处，CAS确保只有一个执行，其余直接返回
            if (!status.compareAndSet(AgentStatus.IDLE, AgentStatus.INITIALIZING)) {
                return;
            }
            Notify.Companion.updateStatusNotify();

            // agent检测与下载
            try {
                checkAndDownloadAgents(agentVersions, project);
            } catch (Exception e) {
                status.set(AgentStatus.FAILURE_DOWNLOAD);
                Notify.Companion.updateStatusNotify();
                showRedownloadNotification(project);
                throw e;
            }

            // 启动需要的agent
            try {
                startAgents(project, agentVersions);
            } catch (Exception e) {
                status.set(AgentStatus.FAILURE_START);
                Notify.Companion.updateStatusNotify();
                throw new RuntimeException("[cf] Failed to start agents: ", e);
            }

            // 为每个agent启动AgentCommClient
            try {
                initializeAgentCommClients(project);
                // 发送forceReIndex需要core通讯客户端初始化完成
                CodeIndexService.Companion.getInstance(project).startIndexing(60);
            } catch (Exception e) {
                status.set(AgentStatus.FAILURE_START);
                Notify.Companion.updateStatusNotify();
                throw new RuntimeException("[cf] Failed to initialize agent comm clients: ", e);
            }

            // 初始化成功
            logger.info("[cf] Agent Manager initialized successfully");
            status.set(AgentStatus.INITIALIZED);
            Notify.Companion.updateStatusNotify();
        } catch (Exception e) {
            logger.error("[cf] Failed to initialize Agent Manager: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 获取指定agent名称对应的AgentCommClient实例
     *
     * @param agentName agent名称
     * @return 对应的AgentCommClient实例，如果不存在则返回null
     */
    public AgentCommClient getAgentCommClient(String agentName) {
        return agentCommClients.get(agentName);
    }

    private void startAgents(Project project, List<AgentVersion> agentVersions) throws IOException {
        // todo agentVersions来自后端最新版本，并从LocalStorage读取，一般不会为空。但若某agentVersion为空，处理方式待定。目前直接抛出异常
        try {
            logger.info("[cf] Start agents...");
            // 初始化Node路径
            initNodePath(agentVersions);
            // 启动Core Agent
            startCoreAgent(project, agentVersions);
            // 启动Tabby Agent
            startTabbyAgent(agentVersions);
        } catch (Exception e) {
            logger.error("[cf] Failed to start agents: {}", e.getMessage());
            throw new RuntimeException("Failed to start agents", e);
        }
    }

    private void initNodePath(List<AgentVersion> agentVersions) {
        AgentVersion nodeVersion = findAgentVersion(agentVersions, AgentNameConstant.NODE);
        if (nodeVersion == null) {
            nodePath = new NodeAgentPath(EnvUtil.isSec("oscap","codefree"));
            logger.error("[cf] Node version not found. Use default version.");
        } else {
            nodePath = new NodeAgentPath(EnvUtil.isSec("oscap","codefree"), nodeVersion.getVersion());
            DebugLogUtil.info("[cf] Using Node version: " + nodeVersion.getVersion());
        }
    }

    private void startCoreAgent(Project project, List<AgentVersion> agentVersions) throws IOException {
        AgentVersion coreAgentVersion = findRequiredAgentVersion(agentVersions, AgentNameConstant.CORE_AGENT);
        CoreAgentProcess coreAgentProcess = new CoreAgentProcess(AGENT_CONFIG, this, coreAgentVersion);

        DebugLogUtil.info("[cf] Starting Core Agent from: " + coreAgentProcess.getAgentPath().getAgentFilePath());
        logger.info("[cf] Starting Core Agent with version {}", coreAgentVersion.getVersion());
        coreAgentProcess.start();

        agentProcesses.put(coreAgentProcess.getAgentName(), coreAgentProcess);
        messageReceivers.put(
                coreAgentProcess.getAgentName(),
                ComposerService.Companion.getInstance(project)
        );
    }

    private void startTabbyAgent(List<AgentVersion> agentVersions) throws IOException {
        AgentVersion tabbyAgentVersion = findRequiredAgentVersion(agentVersions, AgentNameConstant.TABBY_AGENT);
        TabbyAgentProcess tabbyAgentProcess = new TabbyAgentProcess(AGENT_CONFIG, this, tabbyAgentVersion);

        DebugLogUtil.info("[cf] Starting Tabby Agent from: " + tabbyAgentProcess.getAgentPath().getAgentFilePath());
        logger.info("[cf] Starting Tabby Agent with version {}", tabbyAgentVersion.getVersion());
        tabbyAgentProcess.start();

        agentProcesses.put(tabbyAgentProcess.getAgentName(), tabbyAgentProcess);
        messageReceivers.put(
                tabbyAgentProcess.getAgentName(),
                new TabbyAgentMessageReceiver()
        );
    }

    private AgentVersion findAgentVersion(List<AgentVersion> agentVersions, String agentName) {
        return agentVersions.stream()
                .filter(v -> agentName.equals(v.getAgentName()))
                .findFirst()
                .orElse(null);
    }

    private AgentVersion findRequiredAgentVersion(List<AgentVersion> agentVersions, String agentName) {
        AgentVersion version = findAgentVersion(agentVersions, agentName);
        if (version == null) {
            String errorMsg = String.format("[cf] Required agent version not found: %s", agentName);
            logger.error(errorMsg);
            throw new IllegalStateException(errorMsg);
        }
        return version;
    }

    private void initializeAgentCommClients(Project project) {
        logger.info("[cf] Initialize agent comm clients...");
        for (Map.Entry<String, AgentProcess> entry : agentProcesses.entrySet()) {
            String agentName = entry.getKey();
            Process process = entry.getValue().getProcess();
            AgentMessageReceiver receiver = messageReceivers.get(agentName);

            if (receiver == null) {
                throw new RuntimeException("[cf] No message receiver found for agent: " + agentName);
            }

            if (CoreAgentClient.getAgentName().equals(agentName)) {
                AgentCommClient commClient = new CoreAgentClient(project, process, receiver);
                agentCommClients.put(agentName, commClient);
            } else if (TabbyAgentClient.getAgentName().equals(agentName)) {
                AgentCommClient commClient = new TabbyAgentClient(project, process, receiver);
                agentCommClients.put(agentName, commClient);
            }
        }
    }

    // Add method to handle agent restart notification
    public void onAgentRestarted(String agentName, Process newProcess) {
        // Close old AgentCommClient if exists
        AgentCommClient oldClient = agentCommClients.get(agentName);
        Project project = oldClient.getProject();
        if (oldClient != null) {
            oldClient.close();
        }

        // Create new AgentCommClient
        AgentMessageReceiver receiver = messageReceivers.get(agentName);
        if (receiver == null) {
            throw new RuntimeException("No message receiver found for agent: " + agentName);
        }

        if (CoreAgentClient.getAgentName().equals(agentName)) {
            agentCommClients.put(agentName, new CoreAgentClient(project, newProcess, receiver));
        } else if (TabbyAgentClient.getAgentName().equals(agentName)) {
            agentCommClients.put(agentName, new TabbyAgentClient(project, newProcess, receiver));
        }
    }

    public AgentPath getNodePath() {
        return nodePath;
    }

    private static void ensureParentPath(String pluginType) {
        String agentPath = Paths.get(new AgentPath(pluginType, "").getAgentBasePath(), "agent").toString();
        File agentDirectory = new File(agentPath);
        if (agentDirectory.mkdirs()) {
            DebugLogUtil.info("[cf] Directory created: " + agentPath);
        } else {
            DebugLogUtil.warn("[cf] Directory exists or create failed: " + agentPath);
        }
    }

    public static void tryInitialize(Project currentProject) {
        String agentVersion = LocalStorageUtil.getAuthProperty(Constants.AgentVersionInfoKey);
        if (agentVersion.isEmpty()) {
            logger.info("[cf] Agent version info is empty. Skip initialize.");
            return;
        }
        ArrayList<AgentVersion> versions;
        try {
            versions = new ObjectMapper().readValue(agentVersion, new com.fasterxml.jackson.core.type.TypeReference<>() {
            });

            if (currentProject == null || currentProject.isDefault()) {
                // 默认项目不执行初始化
                logger.info("[cf] Default project. Skip agent initialize.");
//                checkAndDownloadAgents(versions);
            } else {
                logger.info("[cf] Current project: {}. Start agent initialize.", currentProject.getName());
                // 下载和启动
                AgentManager agentManager = AgentManager.getInstance(currentProject);
                AgentManager.AGENT_CONFIG.setApiKey(LocalStorageUtil.getApikey());
                AgentManager.AGENT_CONFIG.setInvokerId(LocalStorageUtil.getUserId());
                AgentManager.AGENT_CONFIG.setIndexVersion(LocalStorageUtil.getIndexVersion());
                agentManager.initialize(currentProject, versions);
            }
        } catch (Exception e) {
            // todo handle exception
            throw new RuntimeException(e);
        }
    }

    private static void checkAndDownloadAgents(List<AgentVersion> agentVersions, Project project) {
        logger.info("[cf] Start check and download agents...");

        // 提前判断并创建agent父目录
        ensureParentPath(AGENT_CONFIG.getPluginType());

        // 创建锁文件路径
        // 与vsc端统一为agent目录下的 /.lock/agent.lock 文件
        String lockFilePath = Paths.get(new AgentPath(AGENT_CONFIG.getPluginType(), "").getAgentBasePath(), "agent", ".lock", "agent.lock").toString();

        // 使用文件锁控制，防止多IDE同时启动下载的并发问题
        // IDE内部，进行多线程并发下载。更新配置文件时，使用synchronized关键字控制
        LockUtil.executeWithFileLock(lockFilePath, () -> {
            // 并行下载所需的agent
            CompletableFuture<Void> coreAgentFuture = CompletableFuture.runAsync(() -> checkAndDownloadAgent(AgentNameConstant.CORE_AGENT, agentVersions, project));
            CompletableFuture<Void> tabbyAgentFuture = CompletableFuture.runAsync(() -> checkAndDownloadAgent(AgentNameConstant.TABBY_AGENT, agentVersions, project));
            CompletableFuture<Void> nodeFuture = CompletableFuture.runAsync(() -> checkAndDownloadAgent(AgentNameConstant.NODE, agentVersions, project));

            try {
                // 等待所有agent处理完成
                CompletableFuture.allOf(coreAgentFuture, tabbyAgentFuture, nodeFuture).join();
            } catch (Exception e) {
                // 取消所有未完成的任务
                if (!coreAgentFuture.isDone()) coreAgentFuture.cancel(false);
                if (!tabbyAgentFuture.isDone()) tabbyAgentFuture.cancel(false);
                if (!nodeFuture.isDone()) nodeFuture.cancel(false);

                // 记录错误日志
                logger.error("[cf] Failed to download agents: {}", e.getMessage());

                // 重新抛出异常
                throw e;
            }

            return null;
        });

        DebugLogUtil.info("[cf] Complete check and download agents.");
    }

    private static void checkAndDownloadAgent(String agentName, List<AgentVersion> versions, Project project) {
        try {
            // 检查是否需要升级
            if (VERSION_CHECKER.needsDownload(versions, agentName)) {
                // todo 用户提问时交互：底部通知有系统更新

                assert project != null;
                AgentManager agentManager = AgentManager.getInstance(project);

                if (agentManager.status.compareAndSet(AgentStatus.INITIALIZING, AgentStatus.DOWNLOADING)) {
                    Notify.Companion.updateStatusNotify();
                }

                // 找到对应的版本信息
                AgentVersion agentVersion = versions.stream()
                        .filter(v -> v.getAgentName().equals(agentName))
                        .findFirst()
                        .orElseThrow(() -> new RuntimeException("Agent version not found: " + agentName));

                logger.info("[cf] Start download agent: {}, version: {}... ", agentName, agentVersion.getVersion());
                // 下载新版本并解压文件
                DOWNLOADER.downloadAgent(
                        agentVersion,
                        Paths.get(new AgentPath(AGENT_CONFIG.getPluginType(), agentName).getAgentBasePath(), "agent", agentName).toString()
                );

                // 更新配置文件
                ConfigFileManager.updateConfigFile(
                        AGENT_CONFIG.getPluginType(),
                        agentName,
                        agentVersion.getVersion()
                );
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to check and download agent: " + agentName, e);
        }
    }

    public static AgentStatus getStatus(Project project) {
        AgentManager agentManager = AgentManager.getInstance(project);
        return agentManager.status.get();
    }

    public static void setStatus(Project project, AgentStatus agentStatus) {
        AgentManager agentManager = AgentManager.getInstance(project);
        agentManager.status.set(agentStatus);
    }

    /**
     * 显示重新下载通知弹窗
     */
    public static void showRedownloadNotification(Project project) {
        try {
            MessageBalloonNotificationUtil.showRedownloadNotification(project, "服务下载失败，请重新下载");
        } catch (Exception notifyEx) {
            DebugLogUtil.error("[cf] Failed to show notification: " + notifyEx.getMessage());
        }
    }

    /**
     * 清理所有agent进程和通讯客户端
     */
    public void dispose() {
        logger.info("[cf] Disposing AgentManager for project: {}", project.getName());

        // 关闭所有agent进程
        for (AgentProcess process : agentProcesses.values()) {
            try {
                process.stop();
            } catch (Exception e) {
                logger.error("[cf] Error stopping agent process: {}", e.getMessage());
            }
        }
        agentProcesses.clear();

        // 关闭所有通讯客户端
        for (AgentCommClient client : agentCommClients.values()) {
            try {
                client.close();
            } catch (Exception e) {
                logger.error("[cf] Error closing agent comm client: {}", e.getMessage());
            }
        }
        agentCommClients.clear();

        // 清理消息接收器
        messageReceivers.clear();

        // 重置状态
        status.set(AgentStatus.IDLE);

        logger.info("[cf] AgentManager disposed for project: {}", project.getName());
    }
}