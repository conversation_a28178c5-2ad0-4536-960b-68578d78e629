package com.srdcloud.ideplugin.codechat;

import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.codechat.domain.Conversation;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.general.enums.SubServiceType;
import com.srdcloud.ideplugin.general.utils.TimeUtil;
import com.srdcloud.ideplugin.remote.ChatHistoryHandler;
import com.srdcloud.ideplugin.remote.domain.Dialog.ChatHistoryCommonResponse;
import com.srdcloud.ideplugin.remote.domain.Dialog.GetDialogResponse;
import com.srdcloud.ideplugin.remote.domain.Dialog.ListDialogsResponse;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.Dialog;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.DialogBaseInfo;
import com.srdcloud.ideplugin.webview.codechat.conversation.request.ChatHistoryRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 会话数据管理器
 */
public class ConversionManager {
    private Project project;

    // 并发安全的Map，不保证有序
    private final ConcurrentHashMap<String, Conversation> conversationMap = new ConcurrentHashMap<>();

    private final int topLen = 30;
    private final int limit = 50;

    public ConversionManager(Project project) {
        this.project = project;
    }

    public static Dialog mappingConversationToDialog(final Conversation conversation) {
        Dialog dialog = new Dialog();
        dialog.setTitle(conversation.getTitle());
        dialog.setCreateTime(conversation.getCreateTime());
        dialog.setUpdateTime(conversation.getUpdateTime());
        // 这里必须传一个默认的question对象，即使各个字段为空，否则Webview界面会异常
        dialog.setQuestions(conversation.getQuestions());
        return dialog;
    }

    /**
     * 创建一个新的对话
     *
     * @return
     */
    public Conversation addNewConversation(String title) {
        // 创建一个新的会话对象
        Conversation newConversation = new Conversation();

        // 为新会话生成一个唯一的 ID
        newConversation.setId(UUID.randomUUID().toString());
        // 设置会话标题
        newConversation.setTitle(StringUtils.isNotBlank(title) ? title : Constants.NEW_CONVERSATION_NAME);
        // 设置创建时间和更新时间
        newConversation.setCreateTime(TimeUtil.getNowTimeDatetimeStr());
        newConversation.setUpdateTime(TimeUtil.getNowTimeDatetimeStr());

        // 设置子服务类型
        newConversation.setSubService(SubServiceType.ASSISTANT.getName());

        // 标记为新会话
        newConversation.setNewConversation(true);
        newConversation.setModelRouteCondition(null);

        return newConversation;
    }


    /**
     * 加载会话列表：从远端查询，然后加到本地
     */
    public synchronized ListDialogsResponse generateConversations(ChatHistoryRequest req) {
        // 查询远端会话
        ListDialogsResponse listDialogsResponse = ChatHistoryHandler.listDialogs(SubServiceType.getSubServiceTypeAll(), req.getPageNum(), topLen, req.getTitle());
        // 填充到本地会话管理器
        if (listDialogsResponse != null && listDialogsResponse.getOptResult() == 0) {
            // - 清空持有的会话数据
            this.conversationMap.clear();

            // - 重新填充
            if (CollectionUtils.isNotEmpty(listDialogsResponse.getDialogs())) {
                for (DialogBaseInfo dialog : listDialogsResponse.getDialogs()) {
                    this.conversationMap.put(dialog.getDialogId(), new Conversation(dialog));
                }
            }
        }
        return listDialogsResponse;
    }

    /**
     * 刷新会话列表:
     * refreshConversations 转移到 ConversationHandler.conversationRefresh
     */
    //public void refreshConversations(ChatHistoryRequest req) {}

    /**
     * 加载远端会话详情并更新本地会话
     */
    public GetDialogResponse updateConversation(Conversation currentConversation) {
        GetDialogResponse dialogResponse = ChatHistoryHandler.getDialog(currentConversation.getSubService() != null ? currentConversation.getSubService() : SubServiceType.ASSISTANT.getName(), currentConversation.getId());

        // fixme:本地调试用，主要针对新增会话字段时对齐远端字段格式
//        DebugLogUtil.println("远端会话数据：" + JsonUtil.getInstance().toJson(dialogResponse));

        // 更新本地会话数据
        if (RtnCode.SUCCESS == dialogResponse.getOptResult()) {
            currentConversation.setNewConversation(false);

            // 用远端数据覆盖本地数据
            currentConversation.setTitle(dialogResponse.getDialog().getTitle());
            currentConversation.setCreateTime(dialogResponse.getDialog().getCreateTime());
            currentConversation.setUpdateTime(dialogResponse.getDialog().getUpdateTime());
            currentConversation.setModelRouteCondition(dialogResponse.getDialog().getModelRouteCondition());
            currentConversation.setSystemPrompt(dialogResponse.getDialog().getSystemPrompt());
            currentConversation.setQuestions(dialogResponse.getDialog().getQuestions());

            this.setConversation(currentConversation);
        }
        return dialogResponse;
    }

    /**
     * 获取所有会话内容，并排序
     */
    public synchronized List<Conversation> getAllConversations() {
        if (MapUtils.isNotEmpty(conversationMap)) {
            List<Conversation> allConversations = new ArrayList<>(conversationMap.values());
            allConversations.sort(Comparator.comparing(Conversation::getSortDateTime).reversed());
            return allConversations;
        }
        return new ArrayList<>();
    }

    /**
     * 获取最新的几个会话给webview, 按会话创建时间倒序
     */
    public synchronized List<Conversation> getTopConversations() {
        List<Conversation> allConversations = this.getAllConversations();
        if (CollectionUtils.isEmpty(allConversations)) {
            return new ArrayList<>();
        }
        return allConversations.subList(0, Math.min(allConversations.size(), this.topLen));
    }

    /**
     * 添加会话
     */
    public synchronized void addConversation(Conversation conversation) {
        // 1、获取本地最前的会话
        List<Conversation> conversations = this.getTopConversations();

        // 2、添加新的会话到最前
        conversations.add(0, conversation);

        // 3、重新填充会话映射Map
        clearAndTraverseConversations(conversations);

        // 4、超出存储上限的，进行移除
        removeOverLimitConversation();
    }

    /**
     * 重新建立会话映射Map
     *
     * @param conversationList
     */
    private void clearAndTraverseConversations(List<Conversation> conversationList) {
        this.conversationMap.clear();
        for (Conversation conversation : conversationList) {
            this.conversationMap.put(conversation.getId(), conversation);
        }
    }

    /**
     * 删除超过限制的旧会话
     * 需要先排序
     */
    private synchronized void removeOverLimitConversation() {
        int size = this.conversationMap.size();
        if (size <= this.limit) {
            return;
        }
        int toBeDeleteCount = size - this.limit;

        // 获取有序的会话列表（按时间倒序）
        List<Conversation> allConversations = this.getAllConversations();

        // 从底部往前面删
        List<String> removeKeys = new ArrayList<>();
        int count = 0;
        for (int i = allConversations.size() - 1; i >= 0; i--) {
            if (count < toBeDeleteCount) {
                removeKeys.add(allConversations.get(i).getId());
            }
            count++;
        }
        for (String key : removeKeys) {
            this.conversationMap.remove(key);
        }
    }

    /**
     * 更新会话内容：已废弃，相关业务入口均无调用
     */
    @Deprecated
    public void updateAndSendConversation(Conversation conversation) {
    }

    /**
     * 获取会话
     */
    public Conversation getConversation(String id) {
        return this.conversationMap.get(id);
    }

    /**
     * 更新本地维护的会话数据
     *
     * @param conversation
     */
    public void setConversation(Conversation conversation) {
        if (this.conversationMap.containsKey(conversation.getId())) {
            this.conversationMap.put(conversation.getId(), conversation);
        }
    }

    public void delConversation(String id) {
        this.conversationMap.remove(id);
    }

    /**
     * 修改会话标题
     */
    public void saveConversationTitle(Conversation conversation) {
        this.conversationMap.put(conversation.getId(), conversation);
    }


    /**
     * 删除会话
     */
    public ChatHistoryCommonResponse removeConversation(String dialogId) {
        ChatHistoryCommonResponse removeResponse = ChatHistoryHandler.removeDialog(dialogId);
        if (RtnCode.SUCCESS == removeResponse.getOptResult()) {
            this.delConversation(dialogId);
        }
        return removeResponse;
    }

    /**
     * 订正会话级别的subService
     * 场景：新建的会话默认是assistant，而右键提问的相关功能需要订正会话的subService类型
     */
    public void setConverstionSubservice(Conversation conversation, String subService) {
        // 当会话下面没有question时，才根据第一次新问题来订正会话的subService
        if (CollectionUtils.isEmpty(conversation.getMessages())) {
            conversation.setSubService(subService != null ? subService : "assistant");
            this.setConversation(conversation);
        }
    }

    /**
     * 保存所有会话至本地
     * 已废弃，改为远端管理会话
     */
    @Deprecated
    public void saveConversations() {
    }

}
