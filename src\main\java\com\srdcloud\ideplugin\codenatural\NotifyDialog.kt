package com.srdcloud.ideplugin.codenatural

import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.layout.panel
import com.intellij.util.ui.UIUtil
import javax.swing.Action
import javax.swing.JComponent

class NotifyDialog : DialogWrapper(true) {
    init {
        title = "操作提示"
        init()
    }
    override fun createCenterPanel(): JComponent? {
        return panel {
            row {
                label("将自动新建空文件以使用自然语言编程功能", style = UIUtil.ComponentStyle.LARGE)
            }
        }
    }

    override fun createActions(): Array<Action> {
        myCancelAction.putValue(Action.NAME, "取消")
        myOKAction.putValue(Action.NAME, "新建空文件")
        return super.createActions()
    }
}