package com.srdcloud.ideplugin.marker

import com.intellij.codeInsight.daemon.RelatedItemLineMarkerInfo
import com.intellij.codeInsight.daemon.RelatedItemLineMarkerProvider
import com.intellij.openapi.editor.markup.GutterIconRenderer
import com.intellij.psi.PsiElement
import secIdea.marker.AIAssistantIcons

class JavaLineMarkerProvider : RelatedItemLineMarkerProvider() {
    override fun collectNavigationMarkers(
        element: PsiElement,
        result: MutableCollection<in RelatedItemLineMarkerInfo<*>>
    ) {
        if (!isPsiIdentifier(element) || !isPsiMethod(element.parent)) return

        val method = element.parent
        val project = method.project
        val splitService = SplitService.getInstance(project)
        val functionBean = LineMarkerFunctionBean().apply {
            isUtValid = isValidForUnitTest(method)
            isAnnotateValid = true
            isLineAnotateValid = isValidForLineAnnotate(method)
            isCodeExplain = true
            isCodeSplit = splitService.isValidForSplit(method)
            isOptimization = true
        }

        if (functionBean.getFunctionNum() > 0) {
            val markerInfo = RelatedItemLineMarkerInfo(
                element,
                element.textRange,
                AIAssistantIcons.LINE_MARKER_ICON,
                { "海云智码快捷操作" },
                AIAssistantGutterIconClickAction(element, functionBean),
                GutterIconRenderer.Alignment.RIGHT
            ) { emptyList() }
            result.add(markerInfo)
        }
    }

    private fun isPsiIdentifier(element: PsiElement): Boolean {
        return element.javaClass.simpleName == "PsiIdentifierImpl"
    }

    private fun isPsiMethod(element: PsiElement?): Boolean {
        return element?.javaClass?.simpleName == "PsiMethodImpl"
    }

    private fun isValidForUnitTest(method: PsiElement): Boolean {
        return try {
            val modifierList = method.javaClass.getMethod("getModifierList").invoke(method)
            val hasModifierProperty = modifierList?.javaClass?.getMethod("hasModifierProperty", String::class.java)
            val isPublic = hasModifierProperty?.invoke(modifierList, getPublicModifier()) as? Boolean ?: false

            val isConstructor = method.javaClass.getMethod("isConstructor").invoke(method) as? Boolean ?: false
            val containingFile = method.javaClass.getMethod("getContainingFile").invoke(method)

            isPublic && !isConstructor && !SmartUTUtil.isJavaTestFile(containingFile)
        } catch (e: Exception) {
            println("Error in isValidForUnitTest: ${e.message}")
            false
        }
    }

    private fun getPublicModifier(): String {
        return try {
            val psiModifierClass = Class.forName("com.intellij.psi.PsiModifier")
            val publicField = psiModifierClass.getField("PUBLIC")
            publicField.get(null) as String
        } catch (e: Exception) {
            println("Error getting PUBLIC modifier: ${e.message}")
            "public" // 默认值，如果反射失败
        }
    }

    private fun isValidForLineAnnotate(method: PsiElement): Boolean {
        val elementInfo = LineMarkerFunctionBean.getFunctionLinesCount(method)
        return elementInfo?.let { it.endOffset - it.startOffset <= SplitService.CODE_MAX_LENGTH } ?: false
    }
}
