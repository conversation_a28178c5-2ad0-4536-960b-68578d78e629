package com.srdcloud.ideplugin.login

import com.google.gson.Gson
import com.intellij.ide.BrowserUtil
import com.intellij.openapi.project.Project
import com.intellij.ui.JBColor
import com.srdcloud.ideplugin.general.constants.Constants
import com.srdcloud.ideplugin.general.utils.LocalStorageUtil
import com.srdcloud.ideplugin.general.utils.MessageBalloonNotificationUtil
import com.srdcloud.ideplugin.general.utils.pluginSettings
import com.srdcloud.ideplugin.remote.domain.User.UserInfo
import com.srdcloud.ideplugin.service.domain.oauth.AuthResponse
import com.srdcloud.ideplugin.settings.SecIdeaProjectSettingsConfigurable
import org.jetbrains.ide.BuiltInServerManager
import java.awt.Color
import java.io.File
import java.io.IOException
import java.net.URL
import java.nio.charset.StandardCharsets
import java.nio.file.*
import java.nio.file.attribute.PosixFilePermissions
import java.util.*
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec
import javax.swing.JButton
import javax.swing.JLabel

enum class LoginStatus(val value: String) {
    NOT_LOGGED_IN("未登录"), LOGGED_IN("未授权"), AUTHORIZED("已授权"),
}

data class SecideaUserInfo(
    val userId: String = "", val userName: String = "", var authCode: String = ""
)

/**
 * 此类实际充当 adapter，桥接 LoginService 与 secidea 的 login
 */
object LoginUtils {

    private val LOGIN_STORE_DIR =
        "${System.getProperty("user.home")}${File.separator}.secidea${File.separator}plugin${File.separator}store"
    private val LOGIN_STORE_FILE =
        "${LOGIN_STORE_DIR}${File.separator}secidea_auth.json"
    private var currentUserInfo: SecideaUserInfo = SecideaUserInfo()
    private val gson = Gson()
    private val AUTH_PLUGIN_NAME = arrayOf("all", "c10")
    private var project: Project? = null
    private var settingChangePublisher: SecIdeaSettingsListener? = null
    var SPLIT_TEXT = "&secidea&"
    private val LOGIN_REDIRECT_URI = "oauth-redirect"
    private val SUCCESS_COLOR = Color(28, 141, 31)
    private var initLoginStatusFinished = false

    /**
     * initLoginStatus会早于initProject
     */
    fun initLoginStatus() {
        val path: Path = Paths.get(LOGIN_STORE_DIR)
        val file = File(LOGIN_STORE_FILE)
        if (!Files.exists(path)) {
            try {
                // 尝试创建目录及其父目录
                Files.createDirectories(path)
                val osName = System.getProperty("os.name").lowercase()
                // 在 macOS 上设置目录权限为 777 (所有人可以读写执行)
                if (osName.contains("mac") || osName.contains("darwin")) {
                    Files.setPosixFilePermissions(path, PosixFilePermissions.fromString("rwxrwxrwx"))
                }
            } catch (e: Exception) {
                println("无法创建授权文件目录")
                println(e)
            }
        }
        if (file.exists()) {
            try {
                val encryptedText = File(LOGIN_STORE_FILE).readText(StandardCharsets.UTF_8)
                if (encryptedText.trim().isNotEmpty()) {
                    val encryptedMsg = encryptedText.split(SPLIT_TEXT)
                    val secIdeaUserInfo = decrypt(encryptedMsg[0])
                    val address = encryptedMsg[1]
                    updateUserInfoAndAddress(secIdeaUserInfo, address)
                    val authResponse = AuthResponse()
                    authResponse.access_token = encryptedMsg[0]
                    authResponse.ori_session_id = encryptedMsg[0]
                    authResponse.uid = secIdeaUserInfo.userId

                    // 保存信息
                    LocalStorageUtil.saveAuthProperties(authResponse)
                    val userInfo = UserInfo()
                    userInfo.name = secIdeaUserInfo.userName
                    userInfo.userAccount = secIdeaUserInfo.userName
                    LocalStorageUtil.saveUserInfo(userInfo)
                } else {
                    LocalStorageUtil.clearAuthPropertiesAndUserInfo()
                    updateUserInfoAndAddress(SecideaUserInfo(), "")
                }
            } catch (e: Exception) {
                println("初始值解析异常忽略")
            }
        } else {
            file.writeText("")
        }
    }

    @Synchronized
    fun initProject(project: Project) {
        if(initLoginStatusFinished) {
            return
        } else {
            initLoginStatusFinished = true
        }
        listenAuthFile()
        this.project = project
        this.settingChangePublisher = getSecIdeaPublisher(project, SecIdeaSettingsListener.SECIDEA_SETTINGS_TOPIC)
    }

    fun login() {
        try {
            BrowserUtil.browse(URL(getLoginUrl()))
        } catch (e: Exception) {
            println("打开登录页面发生异常$e")
            if (project == null) {
                return
            }
            MessageBalloonNotificationUtil.showCommonNotification(
                this.project!!, "无法在浏览器打开登录页面，请确认服务器扫描地址是否填写正确，或联系客服人员"
            )
        }
    }

    fun logout(address: String? = null) {
        val encryptedText = File(LOGIN_STORE_FILE).readText(StandardCharsets.UTF_8)
        if (address != null) {
            if (encryptedText != "${SPLIT_TEXT}${address}") {
                store("${SPLIT_TEXT}${address}")
            }
        } else {
            if (encryptedText != "${SPLIT_TEXT}${pluginSettings().address}") {
                store("${SPLIT_TEXT}${pluginSettings().address}")
            }
        }
    }

    fun storeUser(response: String) {
        store("${response}${SPLIT_TEXT}${pluginSettings().address}")
    }

    private fun store(content: String) {
        File(LOGIN_STORE_FILE).writeText(content, Charsets.UTF_8)
    }

    private fun updateUserInfoAndAddress(userInfo: SecideaUserInfo, address: String) {
        if (currentUserInfo == userInfo && pluginSettings().address == address) {
            return
        }
        currentUserInfo = userInfo
        // 兼容 D10 之前的 address 用法
        pluginSettings().address = address
        if (settingChangePublisher != null) {
            settingChangePublisher!!.settingsChanged()
        }
    }

    fun getLoginStatus(): LoginStatus {
        if (currentUserInfo.userName.isBlank()) {
            return LoginStatus.NOT_LOGGED_IN
        }

        return if (currentUserInfo.authCode in AUTH_PLUGIN_NAME) {
            LoginStatus.AUTHORIZED
        } else {
            LoginStatus.LOGGED_IN
        }
    }

    /**
     * C10原有的登录状态，只有登录并授权才算登录
     */
    fun getLoginStatusForC10(): Number {
        if (currentUserInfo.userName.isBlank()) {
            return Constants.LoginStatus_NOK
        }
        return if (currentUserInfo.authCode in AUTH_PLUGIN_NAME) {
            Constants.LoginStatus_OK
        } else {
            Constants.LoginStatus_NOK
        }
    }

    fun isAuthorized(authCode: String): Boolean {
        return authCode in AUTH_PLUGIN_NAME
    }

    /**
     * 为右下角状态栏单独定制登录状态，登录后未授权也算登录
     */
    fun getLoginStatusForStatusBar(): Number {
        if (currentUserInfo.userName.isBlank()) {
            return Constants.LoginStatus_NOK
        }
        return Constants.LoginStatus_OK
    }

    fun updateLoginButton(loginButton: JButton) {
        val loginStatus = getLoginStatus()
        if (loginStatus == LoginStatus.NOT_LOGGED_IN) {
            loginButton.text = "点击登录"
            loginButton.foreground = SUCCESS_COLOR
        } else {
            project?.let { SecIdeaProjectSettingsConfigurable(it).updateTabbyAgentConfig() } // 登录成功后发送配置变更到tabby agent
            loginButton.text = "退出登录"
            loginButton.foreground = JBColor.GRAY
        }
        loginButton.isEnabled = true
        loginButton.requestFocusInWindow()
    }

    fun updateUserName(userName: JLabel) {
        val loginStatus = getLoginStatus()
        when (loginStatus) {
            LoginStatus.NOT_LOGGED_IN -> {
                userName.text = "未登录"
                userName.foreground = JBColor.GRAY
            }

            LoginStatus.LOGGED_IN -> {
                userName.text = "${currentUserInfo.userName}(${loginStatus.value})"
                userName.foreground = JBColor.RED
            }

            else -> {
                userName.text = "${currentUserInfo.userName}(${loginStatus.value})"
                userName.foreground = SUCCESS_COLOR
            }
        }
    }

    private fun getLoginUrl(): String {
        return "${pluginSettings().address}/scan/user/login?state=${
            Base64.getEncoder().encodeToString(getRedirectUrl().toByteArray(StandardCharsets.UTF_8))
        }&loginType=6&redirect_uri=${getSecideaServerUrl()}"
    }

    private fun getRedirectUrl(): String {
        return "http://127.0.0.1:${BuiltInServerManager.getInstance().port}/api/${LOGIN_REDIRECT_URI}"
    }

    private fun getSecideaServerUrl(): String {
        return "${pluginSettings().address}/login/oauth/authorize/callback"
    }

    fun decrypt(ciphertext: String): SecideaUserInfo {
        if (ciphertext.isBlank()) {
            return SecideaUserInfo()
        }
        val key = "xxxsecxxxideaxxx".toByteArray()
        val iv = "adci13cdgzxldcud".toByteArray()
        val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
        val keySpec = SecretKeySpec(key, "AES")
        val ivSpec = IvParameterSpec(iv)
        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec)
        val decoded = Base64.getDecoder().decode(ciphertext)
        val decrypted = cipher.doFinal(decoded)
        val decryptedString = String(decrypted, Charsets.UTF_8)
        return gson.fromJson(decryptedString, SecideaUserInfo::class.java)
    }

    private fun listenAuthFile() {
        Thread {
            try {
                val watchService: WatchService = FileSystems.getDefault().newWatchService()
                val path: Path = Paths.get(LOGIN_STORE_DIR)
                val file = File(LOGIN_STORE_FILE)
                path.register(watchService, StandardWatchEventKinds.ENTRY_MODIFY)

                while (true) {
                    val key = watchService.take()
                    for (event in key.pollEvents()) {
                        val kind = event.kind()
                        if (kind === StandardWatchEventKinds.OVERFLOW) {
                            continue
                        }
                        val fileName = event.context()
                        if (file.name.equals(fileName.toString())) {
                            val encryptedText = file.readText(StandardCharsets.UTF_8)
                            if (encryptedText.trim().isNotEmpty()) {
                                val encryptedMsg = encryptedText.split(SPLIT_TEXT)
                                val userInfo = decrypt(encryptedMsg[0])
                                val address = encryptedMsg[1]
                                updateUserInfoAndAddress(userInfo, address)
                            }
                        }
                    }
                    val valid = key.reset()
                    if (!valid) {
                        break
                    }
                }
            } catch (e: IOException) {
                e.printStackTrace()
            } catch (e: InterruptedException) {
                e.printStackTrace()
            }
        }.start()
    }

}