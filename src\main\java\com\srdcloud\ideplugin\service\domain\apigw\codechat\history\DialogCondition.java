package com.srdcloud.ideplugin.service.domain.apigw.codechat.history;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/6/6
 * @desc 对话限定条件
 */
public class DialogCondition implements Serializable {
    private String version;

    private DialogConditionPayload payload;

    public DialogCondition() {
    }

    public DialogCondition(String version, DialogConditionPayload payload) {
        this.version = version;
        this.payload = payload;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public DialogConditionPayload getPayload() {
        return payload;
    }

    public void setPayload(DialogConditionPayload payload) {
        this.payload = payload;
    }
}
