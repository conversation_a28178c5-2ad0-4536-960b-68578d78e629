package com.srdcloud.ideplugin.webview.codechat.chat;

import com.intellij.ide.util.PropertiesComponent;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.command.WriteCommandAction;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.VfsUtil;
import com.intellij.openapi.vfs.VirtualFile;
import com.srdcloud.ideplugin.codechat.ConversionManager;
import com.srdcloud.ideplugin.codechat.domain.ChatMessage;
import com.srdcloud.ideplugin.codechat.domain.ChatMessageSimple;
import com.srdcloud.ideplugin.codechat.domain.Conversation;
import com.srdcloud.ideplugin.composer.ContextInputService;
import com.srdcloud.ideplugin.composer.ContextOutputItem;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.general.constants.RtnMessage;
import com.srdcloud.ideplugin.general.enums.*;
import com.srdcloud.ideplugin.general.utils.*;
import com.srdcloud.ideplugin.remote.ChatHistoryHandler;
import com.srdcloud.ideplugin.remote.FeedbackAnswerCommHandler;
import com.srdcloud.ideplugin.remote.domain.Dialog.ChatHistoryCommonResponse;
import com.srdcloud.ideplugin.remote.domain.FeedbackAnswerResponse;
import com.srdcloud.ideplugin.remote.domain.WorkItem.WorkItemInfo;
import com.srdcloud.ideplugin.service.LoginService;
import com.srdcloud.ideplugin.service.QuestionTask;
import com.srdcloud.ideplugin.service.domain.apigw.ApigwWebsocketRespPayload;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.CodeAIRequestPromptChat;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.CodeChatQuote;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.QuoteItem;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.DialogCondition;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.DialogConditionPayload;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.StopAnswerReq;
import com.srdcloud.ideplugin.service.domain.codechat.AskChannelParam;
import com.srdcloud.ideplugin.service.domain.codechat.AskQuestionParams;
import com.srdcloud.ideplugin.service.interfaces.IQuestionTaskEventHandler;
import com.srdcloud.ideplugin.statusbar.Notify;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;
import com.srdcloud.ideplugin.webview.codechat.chat.request.ChatRequest;
import com.srdcloud.ideplugin.webview.codechat.chat.request.ChatRequestData;
import com.srdcloud.ideplugin.webview.codechat.chat.request.InsertRequest;
import com.srdcloud.ideplugin.webview.codechat.chat.request.StopChatRequest;
import com.srdcloud.ideplugin.webview.codechat.chat.response.AnswerRecvedResponse;
import com.srdcloud.ideplugin.webview.codechat.chat.response.AnswerRecvedResponseData;
import com.srdcloud.ideplugin.webview.codechat.chat.response.FeedBackResponse;
import com.srdcloud.ideplugin.webview.codechat.chat.response.FeedBackResponseCode;
import com.srdcloud.ideplugin.webview.codechat.common.ChatTips;
import com.srdcloud.ideplugin.webview.codechat.common.StatusEventType;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCode;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCommand;
import com.srdcloud.ideplugin.webview.codechat.composer.domain.ContextInputItem;
import com.srdcloud.ideplugin.webview.codechat.conversation.request.ConversationFeedbackRequest;
import com.srdcloud.ideplugin.webview.base.domain.ErrorResponse;
import com.srdcloud.ideplugin.webview.base.domain.ErrorResponseCode;
import com.srdcloud.ideplugin.webview.codechat.relatedfile.RelatedFile;
import kotlin.Pair;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 对话引擎，对标vsc侧的 ConversationEngin + chatViewProvider中的对话部分逻辑
 */
public class CodeChatEngin implements IQuestionTaskEventHandler {
    private static final Logger logger = LoggerFactory.getLogger(CodeChatEngin.class);


    private final Project project;
    private final CodeChatWebview parent;

    // ===== 会话管理 ====
    // 会话管理器:操作会话数据
    private ConversionManager conversionManager;
    // 持有当前激活中的对话
    private Conversation currentConversation;

    // ===== 当前问答对话任务管理 =====
    // 当前对话任务，管理发送中的提问任务
    private QuestionTask currentQuestion = null;

    // 记录完整回答，因为answer是分片的
    // 用不上
    @Deprecated
    private String curAllAnswer = "";

    // 空内容发起提问时自动重新提交一次问题，所需要存储的两个reqId直接的关系【已废弃】
    @Deprecated
    private Map<String, String> reqIdRelateMap = new HashMap<>();


    public CodeChatEngin(Project project, CodeChatWebview parent, ConversionManager conversionManager) {
        this.parent = parent;
        this.project = project;
        this.conversionManager = conversionManager;
    }


    // ==== 会话激活与切换 ====

    /**
     * 设置当前选中的会话
     *
     * @param conversation
     */
    public void selectConversation(Conversation conversation) {
        currentConversation = conversation;

    }

    public DialogCondition buildModelRouteCondition(String tempId) {
        final DialogCondition condition = new DialogCondition();
        final DialogConditionPayload payload = new DialogConditionPayload();
        payload.setTemplateId(StringUtils.isNotBlank(tempId) ? Integer.valueOf(tempId) : null);
        // 预埋：后续可扩展更多会话限定规则

        condition.setVersion(ConfigWrapper.ConversationConditionVersion);
        condition.setPayload(payload);
        return condition;
    }

    /**
     * 获取当前激活中的会话
     *
     * @return
     */
    public Conversation getCurrentConversation() {
        return currentConversation;
    }

    public void setCurrentQuestion(QuestionTask currentQuestion) {
        this.currentQuestion = currentQuestion;
    }

    public void setCurrentConversation(Conversation currentConversation) {
        this.currentConversation = currentConversation;
    }

    /**
     * 基于Webview侧回传的chatContext，提取当前问答分路的PromptList
     *
     * @param chatContext
     * @return
     */
    public List<CodeAIRequestPromptChat> getChatPrompts(ChatMessageSimple chatContext) {
        List<CodeAIRequestPromptChat> prompts = new ArrayList<>();
        List<ChatMessage> messages = Conversation.getChatMessages(chatContext);

        if (Objects.nonNull(currentConversation)) {
            currentConversation.setMessages(messages);
        }

        // 如果会话消息为空，则设置system_prompt
        if (CollectionUtils.isEmpty(messages)) {
            CodeAIRequestPromptChat codeAIRequestPromptChatFirst = new CodeAIRequestPromptChat();
            codeAIRequestPromptChatFirst.setContent(Constants.systemRoleText);
            codeAIRequestPromptChatFirst.setRole(PromptRoleType.SYSTEM.getType());
            prompts.add(codeAIRequestPromptChatFirst);
        } else {
            // 会话问答消息列表，转成Prompts
            prompts.addAll(messages.stream().map(msg -> {
                CodeAIRequestPromptChat prompt = new CodeAIRequestPromptChat();
                prompt.setRole(msg.getRole());
                prompt.setContent(msg.getContent());
                prompt.setFiles(msg.getFiles());
                return prompt;
            }).toList());
        }
        return prompts;
    }

    // ===== 问答实现 =====

    // ----- 提问相关逻辑 -----


    public QuestionTask getCurrentQuestion() {
        return currentQuestion;
    }

    public boolean isChating() {
        return Objects.nonNull(currentQuestion) && StringUtils.isNotBlank(currentQuestion.GetReqId());
    }

    /**
     * 发起提问
     */
    public void chatRequest(String request) throws IOException {
        if (Objects.isNull(this.currentConversation)) {
            logger.warn("[cf] chatRequest error,currentQuestion is null");
            return;
        }

        // 只要发起一次提问，则不再是 纯本地会话
        this.currentConversation.setNewConversation(false);

        // 提取提问信息
        ChatRequest chatRequest = JsonUtil.getInstance().fromJson(request, ChatRequest.class);
        ChatRequestData requestData = chatRequest.getData();
        String question = requestData.getQuestion();
        String type = requestData.getType();
        String subService = requestData.getSubService();
        String kbId = requestData.getKbId();
        String parentId = requestData.getParentReqId();
        String askType = requestData.getQuestionAskType();
        ChatMessageSimple chatContext = requestData.getChatContext();
        QuoteItem[] quotes = requestData.getQuote();
        List<WorkItemInfo> selectedWorkItems = requestData.getSelectedWorkItems();
        QuoteItem quoteItem = quotes != null && quotes.length > 0 ? quotes[0] : null;
        List<ContextInputItem> contextInputItems = requestData.getContextInputItems();
        if (Objects.isNull(contextInputItems)) {
            contextInputItems = new ArrayList<>();
        }

        // 订正会话级别的subService
        this.conversionManager.setConverstionSubservice(currentConversation, subService);

        // 用模板提问，则更新会话路由条件
        String tempId = chatRequest.getData().getTemplateId();
        if (StringUtils.isNotBlank(tempId)) {
            // 当前会话路由条件是否为空，是则先新建
            if (Objects.isNull(currentConversation.getModelRouteCondition()) || Objects.isNull(currentConversation.getModelRouteCondition().getPayload())) {
                currentConversation.setModelRouteCondition(buildModelRouteCondition(tempId));
            }
            // 更新当前会话路由条件
            this.currentConversation.getModelRouteCondition().getPayload().setTemplateId(Integer.valueOf(tempId));
        }

        // 提取知识库id
        Integer chatKbId = null;
        if (kbId != null && !kbId.isEmpty()) {
            chatKbId = Integer.valueOf(kbId);
        }
        final Integer finalChatKbId = chatKbId;

        // 合并文件类关联上下文
        int relatedFilesLengthLimit = PropertiesComponent.getInstance().getInt(Constants.ChatCharacterLimit, Constants.DefaultChatCharacterLimit);
        // 1、起始：关联当前文件、关联具体文件
        final List<RelatedFile> combinedRelatedFiles = new ArrayList<>(requestData.getRelatedFiles());
        // 将上下文处理移到后台线程
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            try {
                if (CollectionUtils.isNotEmpty(requestData.getContextInputItems())) {
                    // 2、根据@codebase、@folder等上下文输入，转换出具体的关联文件列表
                    List<RelatedFile> contextRelatedFiles = new ArrayList<>();
                    ContextInputService contextInputService = new ContextInputService(project);
                    Pair<List<ContextOutputItem>, List<String>> contextResults = contextInputService.getContextCodeItemForInput(question, requestData.getContextInputItems());

                    if (CollectionUtils.isNotEmpty(contextResults.getFirst())) {
                        // 将转换后的ContextOutputItems，转换为RelatedFiles进行合并
                        contextRelatedFiles = contextResults.getFirst().stream()
                                .map(item -> {
                                    String name = item.getName();
                                    Integer startLine = null;
                                    Integer endLine = null;

                                    // 解析文件名中的行号信息，格式为：文件相对地址（起始行-结束行）
                                    if (name.contains("(") && name.contains(")")) {
                                        int startIndex = name.lastIndexOf("(");
                                        int endIndex = name.lastIndexOf(")");
                                        if (startIndex != -1 && endIndex != -1 && startIndex < endIndex) {
                                            String lineRange = name.substring(startIndex + 1, endIndex);
                                            if (lineRange.contains("-")) {
                                                String[] lines = lineRange.split("-");
                                                try {
                                                    startLine = Integer.parseInt(lines[0].trim());
                                                    endLine = Integer.parseInt(lines[1].trim());
                                                } catch (NumberFormatException e) {
                                                    // 解析失败时保持为null
                                                }
                                            }
                                            // 移除行号信息，保留文件路径
                                            name = name.substring(0, startIndex).trim();
                                        }
                                    }

                                    return new RelatedFile(
                                            name,  // path
                                            item.getContent(), // text
                                            startLine,  // startLine
                                            endLine,  // endLine
                                            "@codebase".equals(item.getOrigin()), // 是否@codebase提问
                                            "@codebase".equals(item.getOrigin()) ? "" : item.getOrigin() // 非@codebase提问，则取传过来的folderPath
                                    );
                                }).toList();

                        combinedRelatedFiles.addAll(contextRelatedFiles);
                    } else {
                        logger.warn("[cf] contextInputService.getContextCodeItemForInput:{} error,contextResults is null", JsonUtil.getInstance().toJson(requestData.getContextInputItems()));
                    }
                }

                // 统计关联文件长度，超长需要做截断
                // 20250430版本：截断逻辑会导致@codebase、@folder转换后的提示语文件有截断风险。插件侧不再截断处理，由模型层截断
                if (CollectionUtils.isNotEmpty(combinedRelatedFiles)) {
                    // 处理关联文件，获取关联文件内容
                    parent.getGetIdeUtilsHandler().fillFileContent(combinedRelatedFiles, relatedFilesLengthLimit);
                }

                // 基于chatContext，提取PromptList：兼容切页后的分支
                List<CodeAIRequestPromptChat> prompts = getChatPrompts(chatContext);

                // 转换消息类型
                ChatMessageType messageType = ChatMessageType.CHAT_GENERATE;
                if (StringUtils.isNotBlank(type)) {
                    messageType = ChatMessageType.getChatMessageTypeByType(Integer.parseInt(type));
                }

                // 发起提问
                // 当前问题curReqId，放在currentQuestion中持有
                String reqId = UUID.randomUUID().toString();
                this.curAllAnswer = "";
                currentQuestion = new QuestionTask(this, AnswerMode.ASYNC.getValue());
                Integer askResult = RtnCode.SUCCESS;

                // 发起提问
                if (askType != null && askType.equals("reAsk")) {
                    askResult = currentQuestion.AskQuestion(new AskQuestionParams(reqId, question, finalChatKbId, Constants.QUESTION_TASK_TYPE_CHAT, null, null, null, messageType.getType(), null, QuestionType.RE_ASK, currentConversation.getId(), parentId, this.currentConversation.getModelRouteCondition(), prompts, tempId, quoteItem, null, combinedRelatedFiles, GitUtil.getGitUrls(project), null, selectedWorkItems));
                } else {
                    askResult = currentQuestion.AskQuestion(new AskQuestionParams(reqId, question, finalChatKbId, Constants.QUESTION_TASK_TYPE_CHAT, null, null, null, messageType.getType(), null, QuestionType.NEW_ASK, currentConversation.getId(), parentId, this.currentConversation.getModelRouteCondition(), prompts, tempId, quoteItem, null, combinedRelatedFiles, GitUtil.getGitUrls(project), null, selectedWorkItems));
                }

                // 消除编辑器选中
                EditorUtil.clearSelection(project);

                // 根据登录状态或服务不可达，引起页面变化
                if (askResult == RtnCode.NOT_LOGIN) {
                    // 即刻返回未登录文案作为回答
                    onTaskError(reqId, RtnCode.NOT_LOGIN, null);
                } else if (askResult == RtnCode.NO_CHANNEL) {
                    // 返回服务不可达文案
                    onTaskError(reqId, RtnCode.NO_CHANNEL, null);
                    // 推送 服务不可达 状态，禁用输入框
                    parent.getPushStatusHandler().onStatusChanged(StatusEventType.WSSERVER_ERROR, RtnCode.NO_CHANNEL);
                }
            } catch (Exception e) {
                logger.error("[cf] Error processing chat request", e);
                onTaskError(UUID.randomUUID().toString(), RtnCode.INVALID_QUESTION, null);
            }
        });
    }


    public void cancelChat(String request) {
        DebugLogUtil.println("no used, cancel-chat-request");
        // 暂无
    }

    /**
     * 停止回答
     *
     * @param request
     */
    public void stopChat(String request) {
        StopChatRequest stopChatRequest = JsonUtil.getInstance().fromJson(request, StopChatRequest.class);
        String reqId = stopChatRequest.getData().getReqId();
        // 本地停止接收，回传停止回答响应给Webview
        if (Objects.nonNull(currentQuestion)) {
//            if (StringUtils.isBlank(reqId)) {
            //统一使用 currentQuestion 的 reqId
            reqId = currentQuestion.GetReqId();
//            }
            AskChannelParam askChannelParam = currentQuestion.getQuestionMap().get(reqId);
            if (askChannelParam != null) {
                stopChatRequest.getData().setFiles(currentQuestion.getQuestionMap().get(reqId).getRelatedFiles());
            }
            // 通知远端停止：顺序必须在 stop-answer回传Webview之前，否则会因为会话服务未写入而导致Conversation-switch查不到
            tryStopRemoteAnswer(reqId);

            // 再次检查非空,防止 StopRemoteAnswer 期间答案输出完毕清空currentQuestion
            if (Objects.nonNull(currentQuestion)) currentQuestion.Cancel("");
        } else {
            tryStopRemoteAnswer(reqId);
        }
    }

    /**
     * 停止本次回答，尝试一次原则，不影响主流程执行
     * 此处不能异步，会导致停止回答case下，历史会话列表刷新不一致
     * <p>
     * 20250321版本:停止回答只需回传reqId，不再需要透传一大堆question等
     */
    private void tryStopRemoteAnswer(String reqId) {
        try {
            StopAnswerReq stopAnswerReq = new StopAnswerReq();
            stopAnswerReq.setReqId(reqId);

            LocalDateTime beginTime = LocalDateTime.now();
            ChatHistoryCommonResponse stopAnswerResponse = ChatHistoryHandler.stopAnswer(stopAnswerReq);
            LocalDateTime endTime = LocalDateTime.now();

            // 遇到超时、异常直接返回结果给webview，避免界面卡死
            if (stopAnswerResponse.getOptResult() != RtnCode.SUCCESS) {
                AnswerRecvedResponseData data = new AnswerRecvedResponseData(reqId, true, null, ChatTips.ANSWER_STOP, WebViewRspCode.SUCCESS, null);
                // 回传异常case回答内容给Webview进行展示
                AnswerRecvedResponse answerRecvedResponse = new AnswerRecvedResponse(WebViewRspCommand.ANSWER_RECVED, data);
                String response = JsonUtil.getInstance().toJson(answerRecvedResponse);
                parent.sentMessageToWebviewWithLoadCheck(response);
                return;
            }

            // 性能埋点监控
            Duration duration = Duration.between(beginTime, endTime);
            long milliseconds = duration.toSeconds();
            if (milliseconds > Constants.Stop_answer_alarm_delay) {
                logger.warn("[cf] tryStopRemoteAnswer alarm,stopAnswerReq:{}", JsonUtil.getInstance().toJson(stopAnswerReq));
            }
        } catch (Exception e) {
            logger.error("stopAnswer error:{}", e.getMessage());
        }
    }

    // ----- 流式问答结果 -----

    /**
     * 收到问答流式返回
     * 开发问答用segmentAnswer，传递分段答案回Webview即可，由Webview侧自由拼接
     */
    @Override
    public void onAnswer(String reqId, int end, String accumulateAnswer, String segmentAnswer, int seqNo, ApigwWebsocketRespPayload payload) {
        // 判断是否为当前交互提问的应答
        if (!reqId.equals(currentQuestion.GetReqId())) {
            logger.error("[cf] CodeChatEngin OnAnswer error,reqId:{} not match currQuestion reqId.", reqId);
            return;
        }

        // 记录完整回答，因为answer是分片的
        this.curAllAnswer = accumulateAnswer;

        boolean inValid = payload.isInValid();
        boolean isEnd = end != 0;
        CodeChatQuote quote = payload.getQuote();
        int code = payload.getRetCode();
        String errMsg = payload.getErrMsg();

        // invalid响应
        if (inValid) {
            code = RtnCode.INSERT_ERROR;
            errMsg = RtnMessage.getMessageByCode(RtnCode.INSERT_ERROR);
        }


        // 流式应答内容传递给Webview
        AnswerRecvedResponseData data = new AnswerRecvedResponseData(reqId, isEnd, segmentAnswer, errMsg, code, quote);
        AnswerRecvedResponse answerRecvedResponse = new AnswerRecvedResponse(WebViewRspCommand.ANSWER_RECVED, data);
        String response = JsonUtil.getInstance().toJson(answerRecvedResponse);
        parent.sentMessageToWebviewWithLoadCheck(response);

        // 应答结束，触发一次会话内容更新，清空当前问题
        if (isEnd) {
            this.currentQuestion = null;
            this.conversionManager.updateConversation(currentConversation);

            // 每次应答完毕，纠正状态显示
            Notify.Companion.updateStatusNotify();
        }
    }

    /**
     * 流式问答错误
     */
    @Override
    public void onTaskError(String regId, int eventId, ApigwWebsocketRespPayload payload) {
        AnswerRecvedResponseData data = null;
        switch (eventId) {
            // 该case已废弃
            case RtnCode.CANCEL:
                data = new AnswerRecvedResponseData(regId, true, null, ChatTips.QUESTION_CANCEL, WebViewRspCode.NEED_CLEAR, null);
                break;
            case RtnCode.STOP_ANSWER:
                data = new AnswerRecvedResponseData(regId, true, null, ChatTips.ANSWER_STOP, WebViewRspCode.SUCCESS, null);
                break;
            case RtnCode.NOT_LOGIN:
                data = new AnswerRecvedResponseData(regId, true, null, ChatTips.NOT_LOGIN, WebViewRspCode.NOT_LOGIN, null);
                break;
            case RtnCode.INVALID_ANSWER:
                data = new AnswerRecvedResponseData(regId, true, null, RtnMessage.getMessageByCode(RtnCode.INVALID_ANSWER), eventId, null);
                break;
            case RtnCode.INVALID_QUESTION:
                data = new AnswerRecvedResponseData(regId, true, null, RtnMessage.getMessageByCode(RtnCode.INVALID_QUESTION), eventId, null);
                break;
            case RtnCode.KNOWLEDGE_BASE_DELETED:
                data = new AnswerRecvedResponseData(regId, true, null, RtnMessage.getMessageByCode(RtnCode.KNOWLEDGE_BASE_DELETED), eventId, null);
                break;
            default:
                data = new AnswerRecvedResponseData(regId, true, null, RtnMessage.getMessageByCode(eventId), eventId, null);
                break;
        }

        // 回传异常case回答内容给Webview进行展示
        AnswerRecvedResponse answerRecvedResponse = new AnswerRecvedResponse(WebViewRspCommand.ANSWER_RECVED, data);
        String response = JsonUtil.getInstance().toJson(answerRecvedResponse);
        parent.sentMessageToWebviewWithLoadCheck(response);

        // 触发一次状态栏更新
        Notify.Companion.updateStatusNotify();

        // 清空本次提问
        this.currentQuestion = null;

        // 更新一次会话信息：无需，webview收到消息后，会触发一次conversation-switch，拉取最新会话信息进行展示
    }


    // ----- 针对问答内容进行的后续交互 -----

    /**
     * 点赞点踩
     */
    public void feedBack(String request) {
        if (LoginService.getLoginStatus() != Constants.LoginStatus_OK) {
            parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(ErrorResponse.getNoLoginResponse(WebViewRspCommand.FEEDBACK_CONVERSATION_RESPONSE)));
            return;
        }
        ConversationFeedbackRequest conversationFeedbackRequest = JsonUtil.getInstance().fromJson(request, ConversationFeedbackRequest.class);
        FeedbackAnswerResponse feedbackAnswerResponse = FeedbackAnswerCommHandler.feedback(conversationFeedbackRequest.getData().getDialogId(), conversationFeedbackRequest.getData().getReqId(), conversationFeedbackRequest.getData().getFeedback());
        FeedBackResponseCode feedBackResponseCode;
        if (feedbackAnswerResponse.getCode() != RtnCode.SUCCESS || Objects.isNull(feedbackAnswerResponse.getData())) {
            ErrorResponseCode errorResponseCode = new ErrorResponseCode(feedbackAnswerResponse.getCode(), "点赞点踩失败，请稍后重试。");
            ErrorResponse errorResponse = new ErrorResponse(WebViewRspCommand.FEEDBACK_CONVERSATION_RESPONSE, errorResponseCode);
            parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(errorResponse));
        } else {
            feedBackResponseCode = new FeedBackResponseCode(WebViewRspCode.SUCCESS, feedbackAnswerResponse.getData());
            FeedBackResponse feedBackResponse = new FeedBackResponse(WebViewRspCommand.FEEDBACK_CONVERSATION_RESPONSE, feedBackResponseCode);
            parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(feedBackResponse));
        }
    }


    /**
     * 复制：无需处理msg，复制直接在webview代码中调用操作系统剪切板，并回传数据上报指令
     */

    /**
     * 插入到代码编辑器
     */
    public void insertCodeToEditor(String request) {
        InsertRequest insertCodeRequest = JsonUtil.getInstance().fromJson(request, InsertRequest.class);
        String code = insertCodeRequest.getData().getCode();

        EditorUtil.insertCodeToEditor(project, code);
    }

    /**
     * 新建单测用例文件
     */
    public void insertUnitTestToProject(String request) {
        InsertRequest insertUnitTestRequest = JsonUtil.getInstance().fromJson(request, InsertRequest.class);
        String unitTest = insertUnitTestRequest.getData().getCode();

        // 执行文件写入
        WriteCommandAction.runWriteCommandAction(project, () -> {
            try {
                String extName = "";
                VirtualFile virtualFile = null;
                VirtualFile dir = FileUtil.getProjectBaseDir(project);

                // 尝试基于当前激活的文件，提取语言文件后缀名、所在目录
                Editor documentEditor = FileEditorManager.getInstance(project).getSelectedTextEditor();
                if (documentEditor != null) {
                    Document currentDocument = documentEditor.getDocument();
                    virtualFile = FileDocumentManager.getInstance().getFile(currentDocument);
                    if (virtualFile != null) {
                        String newFileExt = Language.Companion.detectLanguageName(virtualFile.getName());
                        dir = virtualFile.getParent();
                        if (StringUtils.isNotBlank(newFileExt) && !"Unknown".equalsIgnoreCase(newFileExt)) {
                            extName = "." + newFileExt.toLowerCase();
                        }
                    }
                }

                if (dir == null) {
                    MessageBalloonNotificationUtil.showBalloonNotificationByReason(project, "请先打开编辑文件", RtnCode.RtnCode_Not_Select_Editor);
                    return;
                }

                // 构建新文件名
                String fileName = "unittest-" + System.currentTimeMillis() + extName;
                // 在目录下创建新文件
                VirtualFile newFile = dir.createChildData(this, fileName);
                // 在新文件中插入代码
                VfsUtil.saveText(newFile, unitTest);
                ApplicationManager.getApplication().invokeLater(() -> {
                    // 打开新创建的文件
                    FileEditorManager.getInstance(project).openFile(newFile, true);
                });
            } catch (Exception ex) {
                logger.warn("[cf] insertUnitTestToProject fail,error:{}", ex.getMessage());
            }
        });
    }
}
