package com.srdcloud.ideplugin.service.domain.template;

/**
 * <AUTHOR>
 * @date 2024/4/24
 * @desc 提问模板
 */
public class PromptTemplate {

    /**
     * ID
     */
    private String id;

    /**
     * 标题
     */
    private String name;

    /**
     * 内容
     */
    private String content;

    /**
     * 简介
     */
    private String introduction;


    /**
     * 所属分类
     */
    private PromptTemplateCategory category;


    /**
     * 创建者
     */
    private String creator;

    /**
     * 使用次数
     */
    private int useageCount;

    /**
     * 状态
     */
    private int status;

    /**
     * 是否被收藏
     */
    private boolean favorite;

    /**
     * 是否禁用收藏/取消收藏能力
     */
    private boolean forbiddenFavorite = false;

    public PromptTemplate(String id, String name, String content, String introduction, PromptTemplateCategory category, String creator, int useageCount, int status, boolean favorite, boolean forbiddenFavorite) {
        this.id = id;
        this.name = name;
        this.content = content;
        this.introduction = introduction;
        this.category = category;
        this.creator = creator;
        this.useageCount = useageCount;
        this.status = status;
        this.favorite = favorite;
        this.forbiddenFavorite = forbiddenFavorite;
    }

    public PromptTemplate(String id, String name, String content, PromptTemplateCategory category, String creator, int useageCount, int status, boolean favorite) {
        this.id = id;
        this.name = name;
        this.content = content;
        this.category = category;
        this.creator = creator;
        this.useageCount = useageCount;
        this.status = status;
        this.favorite = favorite;
        forbiddenFavorite = false;
    }



    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public PromptTemplateCategory getCategory() {
        return category;
    }

    public void setCategory(PromptTemplateCategory category) {
        this.category = category;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public int getUseageCount() {
        return useageCount;
    }

    public void setUseageCount(int useageCount) {
        this.useageCount = useageCount;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public boolean isFavorite() {
        return favorite;
    }

    public void setFavorite(boolean favorite) {
        this.favorite = favorite;
    }

    public boolean isForbiddenFavorite() {
        return forbiddenFavorite;
    }

    public void setForbiddenFavorite(boolean forbiddenFavorite) {
        this.forbiddenFavorite = forbiddenFavorite;
    }

    public String getIntroduction() {
        return introduction;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }
}
