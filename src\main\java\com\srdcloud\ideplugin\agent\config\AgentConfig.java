package com.srdcloud.ideplugin.agent.config;

/**
 * Agent启动传参
 */
public class AgentConfig {
    private String apiKey;
    private String invokerId;
    private String pluginType;
    private String pluginVersion;
    private String clientType;
    private String clientVersion;
    private String serverType;
    private String serverBaseUrl;
    private String coreApi;
    private String tabbyApi;
    private String embeddingSubservice;
    private String rerankSubservice;
    private String composerSubservice;
    private String indexVersion;
    private String env; //当前运行环境
    private String ignoreList; //扫描忽略列表

    public AgentConfig() {

    }

    public AgentConfig(String apiKey, String invokerId, String pluginType, String pluginVersion, String clientType, String clientVersion, String serverType, String serverBaseUrl, String coreApi, String tabbyApi, String embeddingSubservice, String rerankSubservice, String composerSubservice, String env, String ignoreList) {
        this.apiKey = apiKey;
        this.invokerId = invokerId;
        this.pluginType = pluginType;
        this.pluginVersion = pluginVersion;
        this.clientType = clientType;
        this.clientVersion = clientVersion;
        this.serverType = serverType;
        this.serverBaseUrl = serverBaseUrl;
        this.coreApi = coreApi;
        this.tabbyApi = tabbyApi;
        this.embeddingSubservice = embeddingSubservice;
        this.rerankSubservice = rerankSubservice;
        this.composerSubservice = composerSubservice;
        this.env = env;
        this.ignoreList = ignoreList;
    }


    // Getters
    public String getApiKey() {
        return apiKey;
    }

    public String getInvokerId() {
        return invokerId;
    }

    public String getPluginType() {
        return pluginType;
    }

    public String getPluginVersion() {
        return pluginVersion;
    }

    public String getClientType() {
        return clientType;
    }

    public String getClientVersion() {
        return clientVersion;
    }

    public String getServerType() {
        return serverType;
    }

    public String getServerBaseUrl() {
        return serverBaseUrl;
    }

    public String getEmbeddingSubservice() {
        return embeddingSubservice;
    }

    public String getRerankSubservice() {
        return rerankSubservice;
    }

    public String getComposerSubservice() {
        return composerSubservice;
    }

    public String getCoreApi() {
        return coreApi;
    }

    public String getTabbyApi() {
        return tabbyApi;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public void setInvokerId(String invokerId) {
        this.invokerId = invokerId;
    }

    public void setPluginType(String pluginType) {
        this.pluginType = pluginType;
    }

    public void setPluginVersion(String pluginVersion) {
        this.pluginVersion = pluginVersion;
    }

    public void setClientType(String clientType) {
        this.clientType = clientType;
    }

    public void setClientVersion(String clientVersion) {
        this.clientVersion = clientVersion;
    }

    public void setServerType(String serverType) {
        this.serverType = serverType;
    }

    public void setServerBaseUrl(String serverBaseUrl) {
        this.serverBaseUrl = serverBaseUrl;
    }

    public void setEmbeddingSubservice(String embeddingSubservice) {
        this.embeddingSubservice = embeddingSubservice;
    }

    public void setRerankSubservice(String rerankSubservice) {
        this.rerankSubservice = rerankSubservice;
    }

    public void setComposerSubservice(String composerSubservice) {
        this.composerSubservice = composerSubservice;
    }

    public String getIndexVersion() {
        return indexVersion;
    }

    public void setIndexVersion(String indexVersion) {
        this.indexVersion = indexVersion;
    }

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public String getIgnoreList() {
        return ignoreList;
    }

    public void setIgnoreList(String ignoreList) {
        this.ignoreList = ignoreList;
    }
}