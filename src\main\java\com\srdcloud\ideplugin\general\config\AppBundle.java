package com.srdcloud.ideplugin.general.config;

import com.intellij.DynamicBundle;
import org.jetbrains.annotations.Nls;
import org.jetbrains.annotations.NonNls;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.PropertyKey;

import java.util.function.Supplier;

/**
 * 插件App基础配置
 */
public class AppBundle extends DynamicBundle {

    @NonNls
    private static final String BUNDLE = "bundles.app";
    private static final AppBundle INSTANCE = new AppBundle();

    private AppBundle() {
        super(BUNDLE);
    }

    /**
     * 获取配置项值
     */
    @NotNull
    public static @Nls String message(@NotNull @PropertyKey(resourceBundle = BUNDLE) String key, Object @NotNull ... params) {
        return INSTANCE.getMessage(key, params);
    }

    @NotNull
    public static Supplier<@Nls String> messagePointer(@NotNull @PropertyKey(resourceBundle = BUNDLE) String key, Object @NotNull ... params) {
        return INSTANCE.getLazyMessage(key, params);
    }


}
