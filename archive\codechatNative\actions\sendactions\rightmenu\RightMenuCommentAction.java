package com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions.rightmenu;

import com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions.SendAction;
import com.srdcloud.ideplugin.general.enums.ChatMessageType;
import org.jetbrains.annotations.Nls;

/**
 * @author: yangy
 * @date: 2023/12/15 10:56
 * @Desc
 */
public class RightMenuCommentAction extends SendAction {
    protected ChatMessageType getChatMessageType() {
        return ChatMessageType.COMMENT;
    }

    @Override
    public @Nls String toString() {
        return "生成代码注释";
    }

    @Override
    public String getCMD() {
        return "/generate comment";
    }
}
