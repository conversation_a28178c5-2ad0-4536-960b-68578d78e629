package com.srdcloud.ideplugin.assistant.codechatNative.logics;

import com.intellij.codeInsight.hints.presentation.InputHandler;
import com.intellij.execution.filters.ExceptionWorker;
import com.intellij.ide.DataManager;
import com.intellij.lang.Language;
import com.intellij.lang.LanguageUtil;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.DataContext;
import com.intellij.openapi.actionSystem.Presentation;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.EditorCustomElementRenderer;
import com.intellij.openapi.editor.Inlay;
import com.intellij.openapi.editor.impl.EditorImpl;
import com.intellij.openapi.editor.markup.TextAttributes;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Key;
import com.intellij.openapi.util.TextRange;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.search.FilenameIndex;
import com.intellij.psi.search.GlobalSearchScope;
import com.srdcloud.ideplugin.assistant.AssistantToolWindow;
import com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions.FixExceptionAction;
import com.srdcloud.ideplugin.assistant.codechatNative.ui.CodeChatMainPanel;
import com.srdcloud.ideplugin.general.enums.QuestionType;
import com.srdcloud.ideplugin.general.icons.GPTIcons;
import com.srdcloud.ideplugin.general.utils.IdeUtil;
import com.srdcloud.ideplugin.general.utils.UIUtil;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @author: yangy
 * @date: 2024/4/25 10:10
 * @Desc
 */
public class ExceptionRenderer implements EditorCustomElementRenderer, InputHandler {


    private static final Logger logger = LoggerFactory.getLogger(ExceptionRenderer.class);

    private final Editor editor;

    private final Project myProject;

    private final int startOffset;

    public ExceptionRenderer(Editor editor, Project project, int starOffset) {
        this.editor = editor;
        this.myProject = project;
        this.startOffset = starOffset;
    }

    private String getErrorStacktrace(Document document, int startOffset, int line) {
        String errorHeader = document.getText(new TextRange(startOffset, document.getLineEndOffset(line)));
        StringBuilder sb = new StringBuilder(errorHeader);
        line++;
        while (line < document.getLineCount()) {
            String lineContent = document.getText(new TextRange(document.getLineStartOffset(line), document.getLineEndOffset(line)));
            if (!lineContent.trim().startsWith("at ") &&
                    !lineContent.trim().startsWith("Caused by") &&
                    !lineContent.trim().startsWith("...")) {
                break;
            }
            sb.append("\n");
            sb.append(lineContent);
            line++;
        }
        return sb.toString();
    }

    public void mouseClicked(@NotNull MouseEvent mouseEvent, @NotNull Point point) {
        int line = this.editor.getDocument().getLineNumber(this.startOffset);
        String errorInformation = getErrorStacktrace(this.editor.getDocument(), this.startOffset, line);
        String codeContext = findErrorLineContent(this.myProject, this.editor, line);
        String errorPrompt = String.format("修复异常报错：\n%s\n\n", new Object[]{errorInformation});
        if (StringUtils.isNotBlank(codeContext)) {
            errorPrompt = String.format("修复异常报错：\n%s\n代码上下文内容：\n%s\n", new Object[]{errorInformation, codeContext});
        }
        FixExceptionAction fixExceptionAction = this.myProject.getService(FixExceptionAction.class);
        DataContext context = DataManager.getInstance().getDataContext(mouseEvent.getComponent());
        AnActionEvent event = AnActionEvent.createFromInputEvent(mouseEvent, "", new Presentation(), context);
//        fixExceptionAction.setData(errorPrompt);

        Project project = event.getProject();
        if (Objects.isNull(project)) {
            project = myProject;
        }

        Key key = AssistantToolWindow.CODE_CHAT_TAB_KEY_MAP.get(project.getLocationHash());
        if (key == null) {
            key = IdeUtil.buildCodeChatTabKey(project.getLocationHash());
        }
        Object mainPanel = project.getUserData(key);
        if (Objects.isNull(mainPanel)) {
            return;
        }
        fixExceptionAction.doActionPerformed((CodeChatMainPanel) mainPanel, errorPrompt, null, QuestionType.NEW_ASK, null);
    }

    public void mouseExited() {
        ((EditorImpl) this.editor).setCustomCursor(this, Cursor.getPredefinedCursor(2));
    }

    public void mouseMoved(@NotNull MouseEvent mouseEvent, @NotNull Point point) {
        ((EditorImpl) this.editor).setCustomCursor(this, Cursor.getPredefinedCursor(12));
    }

    public int calcWidthInPixels(@NotNull Inlay inlay) {
        return getExceptionIcon().getIconWidth();
    }

    public int calcHeightInPixels(@NotNull Inlay inlay) {
        return getExceptionIcon().getIconHeight();
    }

    public void paint(@NotNull Inlay inlay, @NotNull Graphics g, @NotNull Rectangle r, @NotNull TextAttributes textAttributes) {
        Icon consoleIcon;
        consoleIcon = getExceptionIcon();
        int curX = r.x + r.width / 2 - consoleIcon.getIconWidth() / 2;
        int curY = r.y + r.height / 2 - consoleIcon.getIconHeight() / 2;
        if (curX < 0 || curY < 0) {
            return;
        }
        consoleIcon.paintIcon(inlay.getEditor().getComponent(), g, curX, curY);
    }

    public static String findErrorLineContent(Project project, Editor editor, int line) {
        while (line < editor.getDocument().getLineCount()) {
            String lineContent = editor.getDocument().getText(new TextRange(editor.getDocument().getLineStartOffset(line), editor.getDocument().getLineEndOffset(line)));
            ExceptionWorker.ParsedLine myInfo = ExceptionWorker.parseExceptionLine(lineContent);
            if (myInfo == null || myInfo.fileName == null) {
                line++;
                continue;
            }
            String fileName = myInfo.fileName;
            int documentLine = myInfo.lineNumber;
            String classFullPath = lineContent.substring(myInfo.classFqnRange.getStartOffset(), myInfo.classFqnRange.getEndOffset());
            List<VirtualFile> vFiles = new ArrayList<>(FilenameIndex.getVirtualFilesByName(project, fileName, GlobalSearchScope.projectScope(project)));
            if (vFiles.isEmpty()) {
                line++;
                continue;
            }
            VirtualFile vFile = findMostRelatedVirtualFile(vFiles, classFullPath);
            try {
                String content = new String(vFile.contentsToByteArray(true), vFile.getCharset());
                Language language = LanguageUtil.getFileLanguage(vFile);
                String languageStr = null;
                if (language != null) {
                    languageStr = language.getDisplayName().toLowerCase();
                }
                StringBuilder sb = getStringBuilder(content, documentLine, languageStr);
                return sb.toString();
            } catch (IOException e) {
                logger.error("findErrorLineContentByDefault exception. ", e);
            } finally {
                line++;
            }
        }
        return null;
    }

    public static VirtualFile findMostRelatedVirtualFile(List<VirtualFile> virtualFiles, String classFullPath) {
        if (virtualFiles.isEmpty() || classFullPath == null) {
            return null;
        }
        for (VirtualFile virtualFile : virtualFiles) {
            String vPath = virtualFile.getPath();
            int extPos = vPath.lastIndexOf(".");
            if (extPos > 0) {
                vPath = vPath.substring(0, extPos);
            }
            String vFileDotPath = vPath.replace("/", ".");
            if (vFileDotPath.endsWith(classFullPath)) {
                return virtualFile;
            }
        }
        return virtualFiles.get(0);
    }


    public static StringBuilder getStringBuilder(String content, int documentLine, String languageStr) {
        String[] contentLines = content.split("\n");
        StringBuilder sb = new StringBuilder();
        sb.append("```");
        if (StringUtils.isNotBlank(languageStr)) {
            sb.append(languageStr.toLowerCase());
        } else {
            sb.append("code");
        }
        sb.append("\n");
        sb.append(findCompleteCodeBlock(contentLines, documentLine, "{", "}", 10));
        sb.append("\n```");

        return sb;
    }

    public static String findCompleteCodeBlock(String[] contentLines, int documentLine, String blockStartSymbol, String blockEndSymbol, int maxSearchLine) {
        int i = 0;
        boolean found = false;
        while (documentLine - i >= 0 && i < maxSearchLine) {
            String line = contentLines[documentLine - i];
            if (line.endsWith(blockStartSymbol)) {
                found = true;
                break;
            }
            i++;
        }
        int j = 0;
        if (found) {
            while (documentLine + j <= contentLines.length - 1 && j < maxSearchLine) {
                String line = contentLines[documentLine + j];
                if (line.endsWith(blockEndSymbol)) {
                    break;
                }
                j++;
            }
        } else {
            j = maxSearchLine;
        }
        StringBuilder sb = new StringBuilder();
        for (int k = Math.max(documentLine - i, 0); k <= Math.min(documentLine + j, contentLines.length - 1); k++) {
            sb.append(contentLines[k]);
            sb.append("\n");
        }
        if (sb.length() > 1) {
            sb.setLength(sb.length() - 1);
        }
        return sb.toString();
    }

    public Icon getExceptionIcon() {
        Icon consoleIcon = GPTIcons.EXCEPTION;
        boolean isDark = UIUtil.judgeBackgroudDarkTheme();
        if (isDark) {
            consoleIcon = GPTIcons.EXCEPTION_DARK;
        }
        return consoleIcon;
    }
}