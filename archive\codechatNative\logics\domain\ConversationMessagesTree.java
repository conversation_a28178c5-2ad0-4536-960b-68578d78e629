package com.srdcloud.ideplugin.assistant.codechatNative.logics.domain;

import com.srdcloud.ideplugin.general.enums.*;
import com.srdcloud.ideplugin.general.utils.JsonUtil;
import com.srdcloud.ideplugin.general.utils.StringUtil;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.CodeChatQuote;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.QuoteItem;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.ChatMessageSimple;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.MultiTypeContent;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.LinkedList;
import java.util.List;

public class ConversationMessagesTree {

    private final ConversationMessageNode root = new ConversationMessageNode();
    private LinkedList<ConversationMessage> messages = new LinkedList<>();
    private ConversationMessage currentMessage = null;

    // 根据对话树初始化消息列表
    public void initTreeByDialogQuestions(ChatMessageSimple questions) {
        if (questions != null) {
            addMessageFromChatMessage(root, questions, null);
        }
    }

    // 将消息放入当前node
    private void addMessageFromChatMessage(ConversationMessageNode node, ChatMessageSimple questions, ConversationMessage cur) {
        // 将当前消息填入node中
        // 获取新聊天消息的内容列表
        List<MultiTypeContent> contentList = questions.getContent();
        String role = questions.getRole();

        // 1、提取内容
        // --提取问题或答案文本
        String content = "";
        // --提取异常文案
        String errMsg = "";
        // --提取知识库id
        Integer kbId = null;
        // --提取知识库引用内容
        CodeChatQuote quote = null;
        // --提取点选的具体知识库API引用内容
        List<QuoteItem> quoteApiList = null;

        // --进行内容提取
        String contentType;
        if (CollectionUtils.isNotEmpty(contentList)) {
            for (MultiTypeContent contentItem : contentList) {
                contentType = contentItem.getType();
                // 提取问答文本
                if (MultiTypeContentType.TEXT.getName().equalsIgnoreCase(contentType)) {
                    content = contentItem.getText();
                }
                // 提取知识库ID
                if (MultiTypeContentType.KNOWLEDGE_BASE.getName().equalsIgnoreCase(contentType)) {
                    kbId = StringUtil.isNumber(contentItem.getKnowledgeBaseId()) ? Integer.parseInt(contentItem.getKnowledgeBaseId()) : null;
                }
                // 提取引用
                if (MultiTypeContentType.QUOTE.getName().equalsIgnoreCase(contentType) && StringUtils.isNotBlank(contentItem.getText())) {
                    // -- 提取知识库候选引用内容
                    if (PromptRoleType.ASSISTANT.getType().equalsIgnoreCase(role)) {
                        quote = JsonUtil.getInstance().fromJson(contentItem.getText(), CodeChatQuote.class);
                    }
                    // -- 提取用户点选的具体知识库API引用内容
                    if (PromptRoleType.USER.getType().equalsIgnoreCase(role)) {
                        quoteApiList = JsonUtil.getInstance().fromJson(contentItem.getText(), JsonUtil.quoteItemListType);
                    }
                }
            }
        }


        //2、消息节点类型判断
        //-- 默认是正常答案类型
        MessageNodeType type = MessageNodeType.ANSWER;
        //-- 判定是否为有入库的无效答案
        if (CollectionUtils.isEmpty(contentList) || StringUtils.isBlank(content)) {
            // 针对非正常回答，取errMsg作为回答内容进行呈现
            errMsg = questions.getErrMsg();
            type = MessageNodeType.VALID_ERROR;
        }
        //-- 最高优先级覆盖：如果是user发出的，必定是问题类型
        if (ConversationMessageType.geConversationMessageTypeByName(questions.getRole()) == ConversationMessageType.USER) {
            type = MessageNodeType.QUESTION;
        }

        // 3、创建会话消息节点，保存对话内容
        ConversationMessage conversationMessage =
                new ConversationMessage(content, errMsg, ConversationMessageType.geConversationMessageTypeByName(questions.getRole()), ChatMessageType.CHAT_GENERATE, questions.getReqTime(), type);
        conversationMessage.setReqId(questions.getReqId());
        // -- 点赞点踩引入：答案反馈结果
        conversationMessage.setFeedback(questions.getFeedback());
        // -- 知识库提问引入：所引用的知识库id
        conversationMessage.setKbId(kbId);
        // -- 知识库提问引入：知识库引用的内容quote
        conversationMessage.setQuote(quote);
        // -- 知识库提问引入：历史会话加载得到的知识库引用，不允许点击交互
        conversationMessage.setQuoteBanned(true);
        // -- 知识库提问引入：用户点选的具体知识库引用
        if (CollectionUtils.isNotEmpty(quoteApiList)) {
            conversationMessage.setQuoteItem(quoteApiList.get(0));
        }

        // 4、消息节点入tree，统一管理
        node.addChild(conversationMessage, cur);

        // 5、递归将当前消息的子消息节点进行提取、入tree
        List<ChatMessageSimple> dialogQuestionsChildren = questions.getChildren();
        if (dialogQuestionsChildren != null && !dialogQuestionsChildren.isEmpty()) {
            for (ChatMessageSimple childrenChatMessage : dialogQuestionsChildren) {
                addMessageFromChatMessage(conversationMessage.getTreeNode(), childrenChatMessage, conversationMessage);
            }
        }
    }

    /**
     * @param message 最新加入的消息
     *                <p>
     *                将一条新消息加入到当前的树中，用于获取新的提问和回答
     */
    public void addNewMessage(ConversationMessage message) {
        if (currentMessage == null) {
            addNewMessage(message, false);
        } else addNewMessage(message, currentMessage.getMessageType() != ConversationMessageType.USER);
    }

    /**
     * @param message    最新加入的消息
     * @param isReAnswer 当前消息是否是重新回答
     *                   <p>
     *                   将一条新消息加入到当前的树中，用于获取新的提问和回答
     */
    public void addNewMessage(ConversationMessage message, boolean isReAnswer) {
        if (isReAnswer) {
            addBrotherToNode(currentMessage, message);
            messages.removeLast();
        } else {
            if (currentMessage == null) {
                root.addChild(message, null);
            } else {
                addChildToNode(currentMessage, message);
            }
        }

        messages.add(message);
        setCurrentMessage(message);
    }

    public void setCurrentMessage(ConversationMessage message) {
        this.currentMessage = message;
    }

    /**
     * 根据Tree的选择情况，更新当前Message列表
     */
    public void updateMessages() {
        ConversationMessageNode node = root;

        // 递归从树中构建消息列表
        messages = new LinkedList<>();
        while (!node.getChildren().isEmpty()) {
            messages.add(node.getSelectedChildren());
            node = node.getSelectedChildren().getTreeNode();
        }

        if (!messages.isEmpty()) {
            setCurrentMessage(messages.getLast());
        }
    }

    /**
     * 获取当前缓存消息列表
     *
     * @param update 是否更新消息
     * @return 消息列表
     */
    public LinkedList<ConversationMessage> getMessagesList(boolean update) {
        if (update) {
            updateMessages();
        }
        return messages;
    }

    /**
     * @return 当前对话的最新一条消息
     */
    public ConversationMessage getCurrentMessage() {
        return currentMessage;
    }


    /**
     * @param cur   当前消息
     * @param child 当前消息的子消息
     *              <p>
     *              为当前消息添加子消息，用于发起新问题以及获得新回复
     */
    public static void addChildToNode(ConversationMessage cur, ConversationMessage child) {
        cur.getTreeNode().addChild(child, cur);
    }

    /**
     * @param cur      当前消息
     * @param children 需要批量添加的子消息
     *                 <p>
     *                 为当前消息批量添加子消息，用于获取新回复
     */
    public static void addChildrenToNode(ConversationMessage cur, List<ConversationMessage> children) {
        for (ConversationMessage child : children) {
            cur.getTreeNode().addChild(child, cur);
        }
    }

    /**
     * @param cur     当前节点
     * @param brother 被添加的兄弟节点
     *                <p>
     *                为当前的消息添加兄弟消息，用于获取重新回答
     */
    public static void addBrotherToNode(ConversationMessage cur, ConversationMessage brother) {
        cur.getTreeNode().addBrother(brother);
    }

    /**
     * @return 最后一个有效回答或者有效错误的ID
     * <p>
     * 获取最后一个有效回答或者有效错误的ID，用于发起新的提问
     */
    public String getLastValidConversationAnswerRegId() {
        ConversationMessage cur = currentMessage;

        while (cur != null && (cur.getNodeType() == MessageNodeType.INVALID_ERROR || cur.getNodeType() == MessageNodeType.QUESTION)) {
            cur = cur.getParent();
        }

        if (cur == null) {
            return null;
        }

        return cur.getReqId();
    }

}
