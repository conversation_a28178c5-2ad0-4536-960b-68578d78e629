package com.srdcloud.ideplugin.aicommit;

import com.intellij.ide.util.PropertiesComponent;
import com.intellij.openapi.actionSystem.ActionUpdateThread;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.Presentation;
import com.intellij.openapi.diff.impl.patch.*;
import com.intellij.openapi.project.DumbAware;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.DialogWrapper;
import com.intellij.openapi.vcs.FilePath;
import com.intellij.openapi.vcs.VcsDataKeys;
import com.intellij.openapi.vcs.VcsException;
import com.intellij.openapi.vcs.changes.Change;
import com.intellij.openapi.vcs.changes.ContentRevision;
import com.intellij.openapi.vcs.changes.CurrentContentRevision;
import com.intellij.openapi.vcs.ui.CommitMessage;
import com.intellij.project.ProjectKt;
import com.intellij.ui.jcef.JBCefApp;
import com.intellij.vcs.commit.AbstractCommitWorkflowHandler;
import com.srdcloud.ideplugin.common.icons.MyIcons;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.general.constants.RtnMessage;
import com.srdcloud.ideplugin.general.enums.AnswerMode;
import com.srdcloud.ideplugin.general.enums.QuestionType;
import com.srdcloud.ideplugin.general.utils.*;
import com.srdcloud.ideplugin.remote.domain.WorkItem.WorkItemInfo;
import com.srdcloud.ideplugin.service.LoginService;
import com.srdcloud.ideplugin.service.QuestionTask;
import com.srdcloud.ideplugin.service.domain.apigw.ApigwWebsocketRespPayload;
import com.srdcloud.ideplugin.service.domain.codechat.AskQuestionParams;
import com.srdcloud.ideplugin.service.interfaces.IQuestionTaskEventHandler;
import com.srdcloud.ideplugin.statusbar.Notify;
import com.srdcloud.ideplugin.webview.workitem.WorkItemWebview;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.io.IOException;
import java.io.StringWriter;
import java.nio.file.Path;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;


public class CommitPluginAction extends AnAction implements DumbAware, IQuestionTaskEventHandler {
    private static final Logger logger = LoggerFactory.getLogger(CommitPluginAction.class);

    private Project project = null;

    // 当前提交面板
    private CommitMessage commitMessage = null;

    // 当前对话任务，管理发送中的提问任务
    private QuestionTask currentQuestion = null;

    private boolean answering = false;

    private List<WorkItemInfo> workItemList;
    private String workItemKey = null;


    @Override
    public @NotNull ActionUpdateThread getActionUpdateThread() {
        return ActionUpdateThread.BGT;
    }

    @Override
    public void update(@NotNull AnActionEvent e) {
        Presentation presentation = e.getPresentation();

        Icon cfIcon = MyIcons.codefree;
        Icon stopIcon = MyIcons.stop;
        if (UIUtil.judgeBackgroudDarkTheme()) {
            cfIcon = MyIcons.codefreeDark;
            stopIcon = MyIcons.stop;
        }

        // 根据问答状态，动态变化图标
        if (answering) {
            presentation.setText("停止生成");
            presentation.setIcon(stopIcon);
        } else {
            presentation.setText("关联工作项并生成提交信息");
            presentation.setIcon(cfIcon);
        }
        super.update(e);
    }

    @Override
    public void actionPerformed(@NotNull AnActionEvent anActionEvent) {
        project = anActionEvent.getProject();

        // 登录状态校验
        if (LoginService.getLoginStatus() == Constants.LoginStatus_NOK) {
            logger.warn("[cf] CommitPluginAction check status fail,loginStatus:{}", Constants.LoginStatus_NOK);
            MessageBalloonNotificationUtil.showCommonNotificationWithConfirm(project, "请先登录");
            // 更新statusbar
            Notify.Companion.updateStatusNotify();
            return;
        }

        // 判断WebSocket通道状态
        if (!LocalStorageUtil.checkChannelConnected()) {
            logger.warn("[cf] CommitPluginAction check status fail,channelStatus:{}", Constants.Channel_Disconnected);
            MessageBalloonNotificationUtil.showCommonNotificationWithConfirm(project, "服务不可达，请重试");
            // 更新statusbar
            Notify.Companion.updateStatusNotify();
            return;
        }

        if (MyIcons.stop.equals(anActionEvent.getPresentation().getIcon()) || MyIcons.stop.equals(anActionEvent.getPresentation().getIcon())) {
            this.finishAnswer();
        } else {
            // 获取当前打开的commit面板对象
            commitMessage = (CommitMessage) VcsDataKeys.COMMIT_MESSAGE_CONTROL.getData(anActionEvent.getDataContext());
            if (commitMessage == null) {
                MessageBalloonNotificationUtil.showCommonNotificationWithConfirm(project, "获取commit信息失败，请重试...");
                return;
            }

            List<Change> changeList = getChangeList(anActionEvent);
            if (CollectionUtils.isEmpty(changeList)) {
                MessageBalloonNotificationUtil.showCommonNotificationWithConfirm(project, "提交内容不能为空");
                this.finishAnswer();
                return;
            }

            // 判断是否支持JCEF，是则弹窗选取工作项关联
            if (JBCefApp.isSupported()) {
                // 弹出自定义dialog,加载webview进行工作项选取
                WorkItemDialog dialog = new WorkItemDialog(anActionEvent.getProject());
                WorkItemWebview webview = dialog.getWebview();

                // 每次弹出都刷新最新数据
                if (Objects.nonNull(webview)) {
                    webview.clearSelectedWorkItemObject();
                    webview.refreshWorkItems(null);
                }

                dialog.show();

                // 选完工作项，开始AI 生成commit
                if (dialog.getExitCode() == DialogWrapper.OK_EXIT_CODE) {
                    // 获取工作项前缀：webview消息回传之后，记录在本次问答实例上
                    if (CollectionUtils.isNotEmpty(webview.getWorkItemList())) {
                        workItemList = webview.getWorkItemList();
                        workItemKey = buildCommitMsgPreFix(workItemList);
                    }

                    // 发起提问
                    doAsk(anActionEvent, changeList, workItemList);
                }
            } else {
                // 直接发起提问，不关联工作项
                doAsk(anActionEvent, changeList, null);
                workItemList = null;
            }
        }
    }

    public void doAsk(AnActionEvent anActionEvent, List<Change> changeList, List<WorkItemInfo> workItemList) {
        // 需要转到线程中操作，大批量文件git diff操作，会导致主线程卡顿
        ThreadPoolUtil.submit(() -> {
            // 变更状态，从而引起界面icon变化
            answering = true;

            // fixme：性能监控
            DebugLogUtil.println("[cf] CommitPluginAction start getDiffList.");
            // git变动勾选内容获取，转变为UnifiedDiff格式【耗时】
            List<String> diffList = getDiffList(anActionEvent, changeList);
            // fixme：性能监控
            DebugLogUtil.println("[cf] CommitPluginAction end getDiffList,diffList size:" + diffList.size());

            // 发起流式提问
            commitChat(anActionEvent.getProject(), diffList, workItemList);
        });

    }

    /**
     * 发起提问
     *
     * @return
     */
    public void commitChat(final Project project, final List<String> diffList, List<WorkItemInfo> workItemList) {
        String reqId = UUID.randomUUID().toString();
        currentQuestion = new QuestionTask(this, AnswerMode.ASYNC.getValue());

        Integer askResult = currentQuestion.AskQuestion(
                new AskQuestionParams(reqId, null, null, Constants.QUESTION_TASK_TYPE_COMMIT,
                        null, null, null, null, null,
                        QuestionType.NEW_ASK, null, null,
                        null, null, null, null, null,
                        null, GitUtil.getGitUrls(project), diffList, workItemList));

        if (askResult == RtnCode.NOT_LOGIN) {
            logger.warn("[cf] commitChat askQuestion failed by not login.");
            this.finishAnswer();
        } else if (askResult == RtnCode.NO_CHANNEL) {
            logger.warn("[cf] commitChat askQuestion failed by no channel.");
            this.finishAnswer();
        } else {
            // 变化状态，从而引起界面icon变化
            answering = true;
        }

    }

    public String buildCommitMsgPreFix(List<WorkItemInfo> workItemList) {
        if (CollectionUtils.isEmpty(workItemList)) {
            return "";
        }

        // 返回特定格式字符串： %workItemKey%workItemKey%workItemKey
        return workItemList.stream()
                .map(item -> "%" + item.getWorkItemKey())
                .collect(Collectors.joining());
    }

    /**
     * 收到流式应答
     */
    @Override
    public void onAnswer(String reqId, int end, String accumulateAnswer, String segmentAnswer, int seqNo, ApigwWebsocketRespPayload payload) {
        // 初始化提交信息：如果前面有选中工作项，则拼接工作项编号作为前缀
        String commitMsgPreFix = StringUtils.isBlank(this.workItemKey) ? "" : this.workItemKey + " ";
        String commitMsg = null;

        // 判断是否为当前交互提问的应答，或者提问是否已被提前终止
        if (currentQuestion == null || !reqId.equals(currentQuestion.GetReqId())) {
            logger.warn("[cf] CommitPluginAction OnAnswer skip,reqId:{} not match currQuestion reqId.", reqId);
            return;
        }

        // 异常应答处理
        boolean inValid = payload.isInValid();
        String errMsg = payload.getErrMsg();
        if (inValid) {
            errMsg = RtnMessage.getMessageByCode(RtnCode.INSERT_ERROR);
        }

        // 拼接应答内容
        if (StringUtils.isNotBlank(errMsg)) {
            commitMsg = commitMsgPreFix + errMsg;
        } else {
            commitMsg = commitMsgPreFix + accumulateAnswer;
        }

        String finalCommitMsg = commitMsg;
        SwingUtilities.invokeLater(() -> {

            // 流式应答内容传递给界面进行渲染
            if (commitMessage != null) {
                commitMessage.setText(finalCommitMsg);
            }

            // 应答结束
            boolean isEnd = end != 0;
            if (isEnd) {
                this.finishAnswer();
            }
        });
    }

    @Override
    public void onTaskError(String regId, int eventId, ApigwWebsocketRespPayload payload) {
        logger.error("[cf] CommitPluginAction onTaskError error,reqId:{},eventId:{},payload:{}.", regId, eventId, JsonUtil.getInstance().toJson(payload));
        if (currentQuestion != null && regId.equals(currentQuestion.GetReqId())) {
            MessageBalloonNotificationUtil.showCommonNotificationWithConfirm(project, "未成功生成提交信息，请重试");
        }
        this.finishAnswer();
    }

    /**
     * 完成回答（正常结束 or 手动停止）
     */
    public void finishAnswer() {
        // 重置task任务
        currentQuestion = null;

        // 重置选中的工作项信息
        workItemList = null;
        workItemKey = null;

        // 修改状态，引起界面icon变化
        answering = false;
    }

    /**
     * 初步获取变更列表，不做diff计算
     * 用于判断变更是否为空
     *
     * @param anActionEvent
     */
    public List<Change> getChangeList(AnActionEvent anActionEvent) {
        Object workflowHandler = anActionEvent.getDataContext().getData(VcsDataKeys.COMMIT_WORKFLOW_HANDLER);
        if (workflowHandler == null) return new ArrayList<>();

        List<Change> changeList = new ArrayList<>();
        if (!(workflowHandler instanceof AbstractCommitWorkflowHandler))
            return new ArrayList<>();

        AbstractCommitWorkflowHandler handler = (AbstractCommitWorkflowHandler) workflowHandler;

        // 添加已纳入版本控制的变更文件
        List<Change> includedChanges = handler.getUi().getIncludedChanges();
        if (CollectionUtils.isNotEmpty(includedChanges))
            changeList.addAll(includedChanges);
        // 添加未纳入版本控制的文件
        List<FilePath> unversionedFiles = handler.getUi().getIncludedUnversionedFiles();
        if (CollectionUtils.isNotEmpty(unversionedFiles)) {
            for (FilePath filePath : unversionedFiles) {
                changeList.add(new Change(null, new CurrentContentRevision(filePath)));
            }
        }
        return changeList;
    }


    /**
     * 获取勾选的变动列表
     *
     * @param anActionEvent 事件对象
     * @return 差异内容列表
     */
    private List<String> getDiffList(AnActionEvent anActionEvent, List<Change> changeList) {
        // 遍历处理变更条目
        List<String> totalCommitLines = new ArrayList<>();
        AtomicLong totalLength = new AtomicLong(0L);
        for (Change change : changeList) {
            try {
                if (BooleanUtils.isFalse(checkIfValidChange(change))) continue;
                Project project = anActionEvent.getProject();
                Path basePath = Path.of(project.getBasePath());
                // 把前后变动内容，转换为git diff patch格式
                List<FilePatch> patches = IdeaTextPatchBuilder.buildPatch(project, Arrays.asList(change), basePath, false, false);

                if (CollectionUtils.isEmpty(patches)) {
                    String fileName = Optional.ofNullable(change.getAfterRevision())
                            .map(r -> r.getFile().getName())
                            .orElseGet(() -> Optional.ofNullable(change.getBeforeRevision())
                                    .map(r -> r.getFile().getName())
                                    .orElse(""));

                    if (StringUtils.isNotBlank(fileName)) {
                        totalCommitLines.add(fileName + " change mod");
                        if (totalLength.get() >= PropertiesComponent.getInstance()
                                .getInt(Constants.ChatCharacterLimit, Constants.DefaultChatCharacterLimit))
                            break;
                    }
                } else {
                    if (BooleanUtils.isTrue(checkChangeLength(patches, totalLength))) {
                        StringWriter writer = new StringWriter();
                        try {
                            // 用UnifiedDiffView的展示格式，来表示文件变更结果
                            UnifiedDiffWriter.write(
                                    project,
                                    ProjectKt.getStateStore(project).getProjectBasePath(),
                                    patches,
                                    writer,
                                    "\n",
                                    null,
                                    List.of()
                            );
                            if (StringUtils.isNotBlank(writer.toString())) {
                                totalCommitLines.add(writer.toString());
                            }

                            if (totalLength.get() >= PropertiesComponent.getInstance().getInt(Constants.ChatCharacterLimit, Constants.DefaultChatCharacterLimit))
                                break;
                        } finally {
                            writer.close();
                        }
                    }
                }
            } catch (VcsException | IOException e) {
                logger.warn("[cf]get changeList error:{}", e.getMessage());
            }
        }
        return totalCommitLines;
    }

    /**
     * 检查是否有效变更【可选，具体规则待定】
     *
     * @param change 变更对象
     * @return 是否有效
     */
    private Boolean checkIfValidChange(Change change) {
        // 变更前、后，内容都为空，则无效
        ContentRevision contentRevision = Optional.ofNullable(change.getAfterRevision())
                .orElse(change.getBeforeRevision());
        if (contentRevision == null) return false;

        // 如果是二进制文件，直接跳过
        if (contentRevision.getFile().getFileType().isBinary()) return false;

        // 预埋：其他更多判断规则
        return true;
    }

    /**
     * 拼接过程中，检查内容是否过长
     *
     * @param patches     变更块列表
     * @param totalLength 当前累计长度
     * @return 是否允许继续添加
     */
    private Boolean checkChangeLength(List<FilePatch> patches, AtomicLong totalLength) {
        long lengthOfChange = 0L;

        for (FilePatch patch : patches) {
            if (!(patch instanceof TextFilePatch)) return false;

            TextFilePatch textPatch = (TextFilePatch) patch;
            List<PatchHunk> patchHunks = textPatch.getHunks();

            if (CollectionUtils.isEmpty(patchHunks)) return false;

            if (patchHunks.size() == 1) {
                PatchHunk hunk = patchHunks.get(0);
                if (hunk.getLines().size() == 1 && hunk.getText().length() > 300)
                    return false;
            }

            for (PatchHunk hunk : patchHunks) {
                lengthOfChange += hunk.getText().length();
            }
        }

        if (totalLength.get() + lengthOfChange > PropertiesComponent.getInstance()
                .getInt(Constants.ChatCharacterLimit, Constants.DefaultChatCharacterLimit)) return false;

        totalLength.addAndGet(lengthOfChange);
        return true;
    }

}
