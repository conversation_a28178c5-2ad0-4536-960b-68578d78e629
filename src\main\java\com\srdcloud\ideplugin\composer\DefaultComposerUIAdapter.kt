package com.srdcloud.ideplugin.composer

import com.intellij.openapi.project.Project
import com.srdcloud.ideplugin.composer.ui.ComposerUIComponent
import com.srdcloud.ideplugin.composer.ui.WebviewComposerUIComponent

/**
 * ComposerUIAdapter的默认实现
 */
class DefaultComposerUIAdapter(project: Project) : ComposerUIAdapter {

    private var component = WebviewComposerUIComponent(project)

    override fun getAndActivateComponent(project: Project): ComposerUIComponent? {
//        val panel = getAndActivateAgentPanel(project) ?: return null
//        return AgentPanelUIComponent(panel)
        return component
    }

    override fun isCurrentWindow(project: Project, windowId: String): Boolean {
//        return AgentWindowIdUtil.getWindowId(project) == windowId
        return false
    }
}