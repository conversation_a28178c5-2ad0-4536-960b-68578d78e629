package com.srdcloud.ideplugin.service.domain.apigw;

import java.io.Serializable;

/**
 * @author: yangy
 * @date: 2023/7/23 20:23
 * @Desc
 */
public class ApigwWebsocketRespContext implements Serializable {

    private String reqId;

    private Integer optResult;

    @Deprecated
    private String messageName;

    @Deprecated
    private String invokerId;

    @Deprecated
    private String msg;

    public String getMessageName() {
        return messageName;
    }

    public void setMessageName(String messageName) {
        this.messageName = messageName;
    }

    public String getReqId() {
        return reqId;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    public String getInvokerId() {
        return invokerId;
    }

    public void setInvokerId(String invokerId) {
        this.invokerId = invokerId;
    }

    public Integer getOptResult() {
        return optResult;
    }

    public void setOptResult(Integer optResult) {
        this.optResult = optResult;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
