package com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc;

import com.intellij.openapi.Disposable;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.LocalFileSystem;
import com.intellij.openapi.vfs.VirtualFile;
import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc.domain.RlccUpdateTaskItem;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import com.srdcloud.ideplugin.general.utils.ExclusionRules;
import com.srdcloud.ideplugin.general.utils.FileUtil;
import com.srdcloud.ideplugin.general.utils.GoUtil;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2024/11/21
 * @desc 通过阻塞队列+工作线程，处理项目文件缓存变动任务
 * - 有序，FIFO
 * - 队列级去重
 * - 限流
 */
@Deprecated
public class CodeObjectIndexSystemUpdateTask implements Disposable {
    private static final Logger logger = LoggerFactory.getLogger(CodeObjectIndexSystemUpdateTask.class);

    // 无界阻塞队列
    private LinkedBlockingQueue<RlccUpdateTaskItem> taskQueue = new LinkedBlockingQueue<>();

    // 队列去重
    private ConcurrentHashMap<String, String> taskMap = new ConcurrentHashMap<>();

    // 工作线程
    private final Thread workThread;

    // 项目引用，用于获取服务和文件操作等
    private final Project project;

    // 初始化行为计数器
    private AtomicInteger initCounter = new AtomicInteger(0);

    // 初始化文件计数器
    private AtomicInteger initFileCounter = new AtomicInteger(0);

    public static CodeObjectIndexSystemUpdateTask getInstance(@NotNull Project project) {
        return project.getService(CodeObjectIndexSystemUpdateTask.class);
    }

    public CodeObjectIndexSystemUpdateTask(Project project) {
        this.project = project;

        // 启动工作线程，监听阻塞队列
        workThread = new Thread(() -> {
            logger.info("[cf] CodeObjectIndexSystemUpdateTask started...");
            try {
                // 一直运行
                while (true) {
                    // 从队列中获取一个元素，如果队列为空，则此方法会阻塞
                    RlccUpdateTaskItem item = taskQueue.take();

                    // 先休眠再处理：错开文件编辑时onchange与findRelativeObject的并发解析
                    // 防止Webview消息堆积：每投送一个文件过去，休眠一个webview轮询间隔等待返回结果，避免消息堆积
                    // 允许堆积，减少消耗
//                    Thread.sleep(Constants.Webview_msg_interval);

                    processItem(item);
                }
            } catch (InterruptedException e) {
                logger.warn("[cf] CodeObjectIndexSystemUpdateTask workThread exited.");
            } catch (Exception e) {
                logger.warn("[cf] CodeObjectIndexSystemUpdateTask workThread exception:");
                e.printStackTrace();
            }
        });
        workThread.setDaemon(true);
        workThread.start();
    }

    /**
     * 处理：进行文件重新解析并更新缓存
     */
    private void processItem(RlccUpdateTaskItem item) {
        try {
            // 初始化缓存结束标记
            if (item.isInitTaskLast()) {
                logger.info("[cf rlcc] initCodeObjectIndexSystem end,process files count:{}", initFileCounter.get());
                DebugLogUtil.println("initCodeObjectIndexSystem end,process files count:" + initFileCounter.get());
                return;
            }

            // 正常文件解析流程
            VirtualFile file = item.getFile();
            Project project = item.getProject();
            taskMap.remove(file.getPath());

            // 获取文件内容，进行解析并放入初始化缓存
            String fileExt = FileUtil.getExtension(file.getName());
            if (ProjectCodeIndexer.checkRlccSupport(fileExt)) {
                RlccFile.Companion.FileInfo fileInfo = RlccFile.Companion.getFileInfo(file, project);
                if (fileInfo == null) {
                    return;
                }
                ProjectCodeIndexer.getInstance(project).ParseFile(fileExt, fileInfo.getFilePath(), fileInfo.getContent());
//                DebugLogUtil.info("CodeObjectIndexSystemUpdateTask process files:" + fileInfo.getFilePath());
            }
        } catch (Exception e) {
            logger.error("[cf] CodeObjectIndexSystemUpdateTask processItem error:");
            e.printStackTrace();
        }
    }

    /**
     * 新增一个文件解析更新任务
     *
     * @param project
     * @param file
     */
    public void addTaskItem(@NotNull Project project, @NotNull VirtualFile file) {
        // 去重检测
        if (taskMap.containsKey(file.getPath())) {
            DebugLogUtil.info("[cf rlcc] CodeObjectIndexSystemUpdateTask addTaskItem:{} skip by duplicates: " + file.getPath());
            return;
        }
        taskMap.put(file.getPath(), file.getPath());
        RlccUpdateTaskItem item = new RlccUpdateTaskItem(project, file);
        taskQueue.offer(item);
    }

    /**
     * 第一遍遍历项目文件建立Go模块映射
     *
     * @param project
     */
    public synchronized void buildGoModuleMapping(@NotNull Project project) {
        //logger.info("[cf rlcc] buildGoModuleMapping begin.");
        //DebugLogUtil.println("buildGoModuleMapping begin.");

        // 遍历文件
        try {
            String basePath = project.getBasePath();
            if (basePath != null) {
                VirtualFile projectDir = LocalFileSystem.getInstance().findFileByPath(basePath);
                if (projectDir != null) {
                    traverseFiles(projectDir, project, true);
                }
            }
        } catch (Exception e) {
            logger.error("[cf rlcc] project:{} buildGoModuleMapping traverseFiles fail,error:{}", project.getName(), e.getMessage());
        }
    }

    /**
     * 项目启动时，遍历项目文件建立索引
     */
    public synchronized void initCodeObjectIndexSystem(@NotNull Project project) {
        // 并发拦截
        if (initCounter.get() > 0) {
            logger.warn("[cf rlcc] initCodeObjectIndexSystem skip,initCounter:{}", initCounter.get());
            return;
        }
        initCounter.getAndAdd(1);

        logger.info("[cf rlcc] initCodeObjectIndexSystem begin.");
        DebugLogUtil.println("initCodeObjectIndexSystem begin.");

        // 遍历文件，入队等待逐个解析
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            ReadAction.run(() -> {
                try {
                    String basePath = project.getBasePath();
                    if (basePath != null) {
                        VirtualFile projectDir = LocalFileSystem.getInstance().findFileByPath(basePath);
                        if (projectDir != null) {
                            traverseFiles(projectDir, project, false);
                        }
                    }
                    logger.info("[cf rlcc] initCodeObjectIndexSystem traverseFiles addTaskItem finished:{}", project.getName());
                    DebugLogUtil.println("initCodeObjectIndexSystem traverseFiles addTaskItem finished.");

                    // 遍历项目文件结束，入队一个初始化缓存结束标志
                    RlccUpdateTaskItem item = new RlccUpdateTaskItem(project, null);
                    item.setInitTaskLast(true);
                    taskQueue.offer(item);

                } catch (Exception e) {
                    logger.error("[cf rlcc] project:{} initCodeObjectIndexSystem traverseFiles addTaskItem fail,error:{}", project.getName(), e.getMessage());
                }
            });
        });
    }

    /**
     * 基于go.mod，建立moduleName与模块目录绝对路径的映射关系
     */
    public void handleGoMod(VirtualFile file, Project project) {
        try {
            if (!"go.mod".equalsIgnoreCase(file.getName())) {
                return;
            }

            String moduleName = GoUtil.extractModuleName(file);
            String moduleDirPath = file.getParent().getPath();

            if (StringUtils.isBlank(moduleName) || StringUtils.isBlank(moduleDirPath)) {
                logger.warn("[cf rlcc] handleGoMod skip,moduleName or moduleDirPath is blank.");
                return;
            }

            ProjectCodeIndexer.getInstance(project).buildGoModMapping(moduleName, moduleDirPath);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    private void traverseFiles(VirtualFile file, Project project, boolean buildGoModuleMapping) {

        try {
            if (file.isDirectory()) {
                // 被排除在的文件，直接返回不处理
                if (ExclusionRules.isExcludeDirectory(file.getName())) {
                    return;
                }
                for (VirtualFile child : file.getChildren()) {
                    traverseFiles(child, project, buildGoModuleMapping);
                }
            } else {
                if (buildGoModuleMapping && "go.mod".equalsIgnoreCase(file.getName())) {
                    // 如果是Go模块文件，则提取模块信息和路径信息，发往Webview侧进行缓存
                    handleGoMod(file, project);
                } else {
                    // 如果是代码文件，则入队等待被解析
                    String fileExt = FileUtil.getExtension(file.getName());
                    if (ProjectCodeIndexer.checkRlccSupport(fileExt)) {
                        addTaskItem(project, file);
                        // 计数
                        initFileCounter.incrementAndGet();
                    }
                }
            }
        } catch (Throwable e) {
            logger.error("[cf rlcc] traverseFiles error:\n");
            e.printStackTrace();
        }
    }

    @Override
    public void dispose() {
        DebugLogUtil.println("CodeObjectIndexSystemUpdateTask disposed.");
        taskQueue.clear();
        taskMap.clear();
        workThread.interrupt();
    }
}
