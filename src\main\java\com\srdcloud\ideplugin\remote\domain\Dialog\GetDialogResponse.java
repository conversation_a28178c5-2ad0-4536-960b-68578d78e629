package com.srdcloud.ideplugin.remote.domain.Dialog;


import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.Dialog;

import java.io.Serializable;

/**
 * <AUTHOR> yangy
 * @create 2024/5/13 11:25
 */
public class GetDialogResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 消息状态值，0表示操作成功，其他值表示失败
     */
    private int optResult;

    /**
     * 操作失败说明
     */
    private String msg;

    /**
     * 对话信息对象列表
     */
    private Dialog dialog;

    public int getOptResult() {
        return optResult;
    }

    public void setOptResult(int optResult) {
        this.optResult = optResult;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Dialog getDialog() {
        return dialog;
    }

    public void setDialog(Dialog dialog) {
        this.dialog = dialog;
    }
}
