package com.srdcloud.ideplugin.webview.workitem.request;

import com.srdcloud.ideplugin.remote.domain.WorkItem.WorkItemInfo;
import com.srdcloud.ideplugin.webview.base.domain.WebViewReqType;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/23
 * 合并工作项相关webview请求体于一身
 */
public class WorkItemCommonRequestData extends WebViewReqType {
    private String searchParam; //为null则说明查全量

    private List<WorkItemInfo> workItemList; // 返回被选中的item信息

    private String workItemURL;

    public WorkItemCommonRequestData() {
    }

    public WorkItemCommonRequestData(String reqType) {
        super(reqType);
    }

    public WorkItemCommonRequestData(String searchParam, List<WorkItemInfo> workItemList, String workItemURL) {
        this.searchParam = searchParam;
        this.workItemList = workItemList;
        this.workItemURL = workItemURL;
    }

    public WorkItemCommonRequestData(String reqType, String searchParam, List<WorkItemInfo> workItemList, String workItemURL) {
        super(reqType);
        this.searchParam = searchParam;
        this.workItemList = workItemList;
        this.workItemURL = workItemURL;
    }

    public String getSearchParam() {
        return searchParam;
    }

    public void setSearchParam(String searchParam) {
        this.searchParam = searchParam;
    }

    public List<WorkItemInfo> getWorkItemList() {
        return workItemList;
    }

    public void setWorkItemList(List<WorkItemInfo> workItemList) {
        this.workItemList = workItemList;
    }

    public String getWorkItemURL() {
        return workItemURL;
    }

    public void setWorkItemURL(String workItemURL) {
        this.workItemURL = workItemURL;
    }
}
