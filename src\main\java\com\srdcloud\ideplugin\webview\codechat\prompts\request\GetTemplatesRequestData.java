package com.srdcloud.ideplugin.webview.codechat.prompts.request;

import com.srdcloud.ideplugin.webview.base.domain.WebViewReqType;

/**
 * 模板中心查询请求体
 */
public class GetTemplatesRequestData extends WebViewReqType {

    // 模板查询条件：type、lastUsed、categoryId  综合决定查询分类
    /**
     * favorite：我的收藏
     * owner：我的创建
     */
    private String type;
    /**
     * true：最近使用
     */
    private boolean lastUsed;
    /**
     * categoryId：写死具体，则查询二开专区
     */
    private String categoryId;

    private int pageNum;
    private String name;
    private int pageDataCount;
    private String templateId;
    private String operationType;

    public GetTemplatesRequestData(String reqType, String type, int pageNum, String name, boolean lastUsed, String categoryId, int pageDataCount, String templateId, String operationType) {
        this.reqType = reqType;
        this.type = type;
        this.pageNum = pageNum;
        this.name = name;
        this.lastUsed = lastUsed;
        this.categoryId = categoryId;
        this.pageDataCount = pageDataCount;
        this.templateId = templateId;
        this.operationType = operationType;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setLastUsed(boolean lastUsed) {
        this.lastUsed = lastUsed;
    }

    public boolean getLastUsed() {
        return lastUsed;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setPageDataCount(int pageDataCount) {
        this.pageDataCount = pageDataCount;
    }

    public int getPageDataCount() {
        return pageDataCount;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getOperationType() {
        return operationType;
    }

}
