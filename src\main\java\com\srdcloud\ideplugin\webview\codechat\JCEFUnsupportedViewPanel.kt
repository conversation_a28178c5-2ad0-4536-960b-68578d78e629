package com.srdcloud.ideplugin.webview.codechat

/**
 * <AUTHOR>
 * @date 2025/1/16
 * @desc 不支持JCEF的视图面板，用于提示用户启用JCEF或检查环境设置。
 */
import com.intellij.openapi.project.DumbAware
import com.intellij.ui.ColorUtil
import com.intellij.ui.components.JBScrollPane
import com.intellij.util.ui.UIUtil
import java.awt.BorderLayout
import javax.swing.BorderFactory
import javax.swing.JPanel
import javax.swing.JTextPane

class JCEFUnsupportedViewPanel : JPanel(), DumbAware {
    private val textPane = JTextPane()
    private val scrollPane = JBScrollPane(textPane)

    init {
        with(textPane) {
            isEditable = false
            contentType = UIUtil.HTML_MIME
            text =
                """
                <html lang="en">
                <body style="color: #${ColorUtil.toHex(UIUtil.getTextFieldForeground())}">
                <h2>JCEF is not available!</h2>
                <p>
                    Please ensure the IDE is running with the Jetbrains Runtime.
                </p>
                <p>
                    This plugin requires JCEF (Chromium Embedded Frame) to load the CodeFree window. 
                </p>
                </body>
                </html>
                """.trimIndent()
            border = BorderFactory.createEmptyBorder(20, 20, 20, 20)
        }
        add(scrollPane, BorderLayout.CENTER)
    }
}