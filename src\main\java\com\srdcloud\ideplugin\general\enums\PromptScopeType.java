package com.srdcloud.ideplugin.general.enums;

/**
 * <AUTHOR>
 * @date 2024/5/13
 * @desc 模板筛选范围
 */
public enum PromptScopeType {
    LAST_USED("lastUsed", "最近使用"),
    STARED("favorite", "我的收藏"),
    MINE("owner", "我的创建"),
    ALL("all", "全部"),
    SECONDARY("secondary", "二开专区"),
    ;


    private final String type;

    private final String desc;

    PromptScopeType(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
