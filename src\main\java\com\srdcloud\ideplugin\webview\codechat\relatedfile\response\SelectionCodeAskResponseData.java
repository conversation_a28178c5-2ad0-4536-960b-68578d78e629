package com.srdcloud.ideplugin.webview.codechat.relatedfile.response;

import com.srdcloud.ideplugin.general.enums.ChatMessageType;

public class SelectionCodeAskResponseData {
    private ChatMessageType type;
    private String code;
    private String filePath;
    private int startLineNumber;
    private int endLineNumber;

    public SelectionCodeAskResponseData(ChatMessageType type, String code, String filePath, int startLineNumber, int endLineNumber) {
        this.type = type;
        this.code = code;
        this.filePath = filePath;
        this.startLineNumber = startLineNumber;
        this.endLineNumber = endLineNumber;
    }

    public ChatMessageType getType() {
        return type;
    }

    public void setType(ChatMessageType type) {
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public int getStartLineNumber() {
        return startLineNumber;
    }

    public void setStartLineNumber(int startLineNumber) {
        this.startLineNumber = startLineNumber;
    }

    public int getEndLineNumber() {
        return endLineNumber;
    }

    public void setEndLineNumber(int endLineNumber) {
        this.endLineNumber = endLineNumber;
    }
}
