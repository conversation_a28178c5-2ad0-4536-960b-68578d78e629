package com.srdcloud.ideplugin.general.utils;

import com.intellij.notification.Notification;
import com.intellij.notification.NotificationAction;
import com.intellij.notification.NotificationType;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.application.ex.ApplicationManagerEx;
import com.intellij.openapi.options.ShowSettingsUtil;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.IconLoader;
import com.srdcloud.ideplugin.agent.AgentManager;
import com.srdcloud.ideplugin.assistant.AssistantToolWindow;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.settings.SecIdeaProjectSettingsConfigurable;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.*;
import java.net.URI;
import java.text.MessageFormat;

/**
 * 气泡通知工具类
 *
 * <AUTHOR>
 */
public class MessageBalloonNotificationUtil {

    private static final Logger logger = LoggerFactory.getLogger(MessageBalloonNotificationUtil.class);

//    private static final String pluginDownloadPath = "/pluginmanager/v1/plugins/{0}/download";

    private static final String pluginDownloadPath = "/smartassist/plugin/versions";

    /**
     * 显示基本的文字通知，不带确认按钮
     */
    public static void showCommonNotification(@NotNull Project project, @NotNull String message) {
        Notification notification = createSrdNotification();
        notification.setContent(message);
        notification.notify(project);
    }

    /**
     * 显示基本的文字通知，带确认按钮才会消失
     */
    public static void showCommonNotificationWithConfirm(@NotNull Project project, @NotNull String message) {
        Notification notification = createSrdNotification();
        notification.setContent(message);

        notification.addAction(new NotificationAction(Constants.NOTIFY_CONFIRM_TEXT) {
            @Override
            public void actionPerformed(@NotNull AnActionEvent e, @NotNull Notification notification) {
                notification.expire();
            }
        });
        notification.notify(project);
    }



    public static void showSettingsNotification(@NotNull Project project, @NotNull String message) {
        Notification notification = createSrdNotification();
        notification.setContent(message);
        notification.addAction(NotificationAction.createSimpleExpiring("设置…", () ->
            ShowSettingsUtil.getInstance().showSettingsDialog(project, SecIdeaProjectSettingsConfigurable.class))
        );
        notification.notify(project);
    }

    /**
     * 显示带原因的提示
     *
     * @param project 当前项目的对象
     * @param message 通知内容
     * @param reason  通知的原因值
     */
    public static void showBalloonNotificationByReason(@NotNull Project project, @NotNull String message,
                                                       @NotNull int reason) {
        Notification notification = createSrdNotification();
        notification.setContent(message);

        notification.addAction(new NotificationAction(Constants.NOTIFY_CONFIRM_TEXT) {
            @Override
            public void actionPerformed(@NotNull AnActionEvent e, @NotNull Notification notification) {
                notification.expire();
            }
        });

        if (reason == RtnCode.INVALID_USER ||
                reason == RtnCode.USER_FORBIDDEN) {
            notification.addAction(new NotificationAction(Constants.NOTIFY_DETAILS_TEXT) {
                @Override
                public void actionPerformed(@NotNull AnActionEvent e, @NotNull Notification notification) {
                    String url = ConfigWrapper.getServerUrl();

                    switch (reason) {
                        case RtnCode.INVALID_USER:
                            url = url + ConfigWrapper.userInvalidPath;
                            break;
                        case RtnCode.USER_FORBIDDEN:
                            url = url + ConfigWrapper.userForbiddenPath;
                            break;
                        default:
                            url = url + ConfigWrapper.oauthServerRedPath;
                    }

                    try {
                        if (Desktop.isDesktopSupported() && Desktop.getDesktop().isSupported(Desktop.Action.BROWSE)) {
                            Desktop.getDesktop().browse(new URI(url));
                        } else {
                            // 处理不支持的情况
                        }
                    } catch (Exception exception) {
                        // 处理不支持的情况
                        exception.printStackTrace();
                    }
                }
            });
        }

        notification.notify(project);
    }

    /**
     * 引导用户打开问答窗口
     */
    public static void showOpenToolWindowNotification(@NotNull Project project, @NotNull String message) {
        Notification notification = createSrdNotification();
        notification.setContent(message);
        notification.addAction(new NotificationAction(Constants.NOTIFY_OPEN_TEXT) {
            @Override
            public void actionPerformed(@NotNull AnActionEvent e, @NotNull Notification notification) {
                // 展开编程助手对话窗
                AssistantToolWindow.toolWindowVisible(project);
            }
        });

        notification.addAction(new NotificationAction(Constants.NOTIFY_CANCEL_TEXT) {
            @Override
            public void actionPerformed(@NotNull AnActionEvent e, @NotNull Notification notification) {
                notification.expire();
            }
        });

        notification.notify(project);
    }


    /**
     * 显示更新信息的弹窗
     *
     * @param project 当前启用的项目对象
     * @param message 需要提示的消息内容
     */
    @Deprecated
    public static void showUpdateBalloonNotification(@NotNull Project project, @NotNull String message, @NotNull String clientUrlSubPath) {
        Notification notification = createSrdNotification();
        notification.setContent(message);
        notification.addAction(new NotificationAction(Constants.NOTIFY_UPDATE_TEXT) {
            @Override
            public void actionPerformed(@NotNull AnActionEvent e, @NotNull Notification notification) {
                String url = ConfigWrapper.getServerHost() + clientUrlSubPath;

                try {
                    if (Desktop.isDesktopSupported() && Desktop.getDesktop().isSupported(Desktop.Action.BROWSE)) {
                        Desktop.getDesktop().browse(new URI(url));
                    }
                } catch (Exception exception) {
                    // 处理不支持的情况
                    exception.printStackTrace();

                }
            }
        });

        notification.addAction(new NotificationAction(Constants.NOTIFY_CANCEL_TEXT) {
            @Override
            public void actionPerformed(@NotNull AnActionEvent e, @NotNull Notification notification) {
                notification.expire();
            }
        });

        notification.notify(project);
    }

    /**
     * 显示更新信息的弹窗
     */
    public static void showUpdateBalloonNotification(@NotNull Project project, String version, String versionId, String changeNotes, String remoteDownloadUrl) {
        // 拼接更新提示
        StringBuilder content = new StringBuilder();
        content.append("发现新版本");
        if (StringUtils.isNotBlank(version)) {
            content.append(version);
        }
        content.append(",升级以享受更好体验,更新详情如下:\n");
        if (StringUtils.isNotBlank(changeNotes)) {
            content.append(changeNotes);
        }
        String notificationContent = content.toString();

        // 弹通知
        Notification notification = createSrdNotification();
        notification.setContent(HtmlUtil.wrapHtmlNotification(notificationContent));

        // 在线更新
        notification.addAction(new NotificationAction(Constants.NOTIFY_ONLINE_TEXT) {
            @Override
            public void actionPerformed(@NotNull AnActionEvent e, @NotNull Notification notification) {
                // 拉起settings窗口，插件更新页面
                PluginUtil.showPluginConfigurable(project);
            }
        });

        // 本地更新
        if (StringUtils.isNotBlank(remoteDownloadUrl)) {
            notification.addAction(new NotificationAction(Constants.NOTIFY_LOCAL_TEXT) {
                @Override
                public void actionPerformed(@NotNull AnActionEvent e, @NotNull Notification notification) {
                    //方案一：跳转web端下载页面
//                    String url = ConfigWrapper.serverHost + pluginDownloadPath + "?version=" + version + "&type=jetbrains";

                    // 方案二：直接拉起下载链接触发下载行为
                    String url = remoteDownloadUrl;

                    try {
                        if (Desktop.isDesktopSupported() && Desktop.getDesktop().isSupported(Desktop.Action.BROWSE)) {
                            Desktop.getDesktop().browse(new URI(url));
                        }
                    } catch (Exception exception) {
                        // 处理不支持的情况
                        exception.printStackTrace();
                    }
                }
            });
        } else {
            notification.addAction(new NotificationAction(Constants.NOTIFY_CANCEL_TEXT) {
                @Override
                public void actionPerformed(@NotNull AnActionEvent e, @NotNull Notification notification) {
                    notification.expire();
                }
            });
        }

        notification.notify(project);
    }


    /**
     * 创建带研发云图标的通知
     *
     * @return 通知框对象
     */
    public static Notification createSrdNotification() {
        return new Notification(
                "com.srdcloud.IDEPlugin",
                UIUtil.loadIcon("/icons/sec/secidea_light_16.svg","/icons/srdcloud_square.svg"),
                EnvUtil.isSec("海云智码","研发云CodeFree"),
                null,
                null,
                NotificationType.INFORMATION,
                null
        );
    }

    /**
     * 显示带有重启按钮的通知
     *
     * @param project 当前项目
     * @param message 通知消息
     */
    public static void showRestartNotification(@NotNull Project project, @NotNull String message) {
        Notification notification = createSrdNotification();
        notification.setContent(message);

        // 添加重启按钮
        notification.addAction(new NotificationAction(Constants.NOTIFY_RESTART_TEXT) {
            @Override
            public void actionPerformed(@NotNull AnActionEvent e, @NotNull Notification notification) {
                notification.expire();

                // 延迟执行重启，给通知足够时间消失
                ApplicationManager.getApplication().invokeLater(() -> {
                    try {
                        logger.info("User initiated IDE restart");
                        ApplicationManagerEx.getApplicationEx().restart(true);
                    } catch (Exception ex) {
                        logger.error("Failed to restart IDE: " + ex.getMessage(), ex);
                    }
                });
            }
        });

        // 添加取消按钮
        notification.addAction(new NotificationAction(Constants.NOTIFY_CANCEL_TEXT) {
            @Override
            public void actionPerformed(@NotNull AnActionEvent e, @NotNull Notification notification) {
                notification.expire();
            }
        });

        notification.notify(project);
    }

    /**
     * 显示带有重新下载Agent按钮的通知
     *
     * @param project 当前项目
     * @param message 通知消息
     */
    public static void showRedownloadNotification(@NotNull Project project, @NotNull String message) {
        Notification notification = createSrdNotification();
        notification.setContent(message);

        // 添加重启按钮
        notification.addAction(new NotificationAction(Constants.NOTIFY_REDOWNLOAD_TEXT) {
            @Override
            public void actionPerformed(@NotNull AnActionEvent e, @NotNull Notification notification) {
                notification.expire();

                ApplicationManager.getApplication().executeOnPooledThread(() -> {
                    try {
                        logger.info("User initiated agent redownload");
                        AgentManager.setStatus(project, AgentManager.AgentStatus.IDLE);
                        AgentManager.tryInitialize(project);
                    } catch (Exception ex) {
                        logger.error("Failed to redownload agent: " + ex.getMessage(), ex);
                    }
                });
            }
        });

        // 添加取消按钮
        notification.addAction(new NotificationAction(Constants.NOTIFY_CANCEL_TEXT) {
            @Override
            public void actionPerformed(@NotNull AnActionEvent e, @NotNull Notification notification) {
                notification.expire();
            }
        });

        notification.notify(project);
    }
}
