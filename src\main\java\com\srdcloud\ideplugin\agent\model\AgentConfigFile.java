package com.srdcloud.ideplugin.agent.model;

import java.util.Map;

public class AgentConfigFile {
    private Map<String, Map<String, OsConfig>> agent;

    public static class OsConfig {
        private Map<String, AgentOsInfo> windows;
        private Map<String, AgentOsInfo> macos;
        private Map<String, AgentOsInfo> linux;

        // Getters and setters
        public Map<String, AgentOsInfo> getWindows() { return windows; }
        public void setWindows(Map<String, AgentOsInfo> windows) { this.windows = windows; }
        public Map<String, AgentOsInfo> getMacos() { return macos; }
        public void setMacos(Map<String, AgentOsInfo> macos) { this.macos = macos; }
        public Map<String, AgentOsInfo> getLinux() { return linux; }
        public void setLinux(Map<String, AgentOsInfo> linux) { this.linux = linux; }
    }

    public static class AgentOsInfo {
        private String path;
        private String version;

        // Getters and setters
        public String getPath() { return path; }
        public void setPath(String path) { this.path = path; }
        public String getVersion() { return version; }
        public void setVersion(String version) { this.version = version; }
    }

    // Getters and setters
    public Map<String, Map<String, OsConfig>> getAgent() { return agent; }
    public void setAgent(Map<String, Map<String, OsConfig>> agent) { this.agent = agent; }
} 