package com.srdcloud.ideplugin.assistant.codechatNative.uicomponent;

import com.intellij.openapi.ui.VerticalFlowLayout;
import javax.swing.*;
import java.awt.*;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 蔡一新
 * @since  : 2024/8/23
 * 抽象类ListViewer提供了一个基础的列表查看器实现，继承自JPanel.
 * 它负责管理一个列表数据集合，并提供显示列表项、添加、删除和选择列表项的功能.
 * 泛型<T>确保了列表可以存储任意类型的元素.
 */
public abstract class ListViewer<T> extends JPanel {

    // 列表数据集合，存储泛型元素T.
    private List<T> list;

    // 当前选中项的索引，无选中项时为-1.
    private int selectedIndex;

    // 列表项的高度.
    private int cellHeight;

    // 列表为空时显示的文本，默认为"EMPTY TEXT".
    private String emptyText = "EMPTY TEXT";

    /**
     * 默认构造函数，初始化一个空列表.
     */
    public ListViewer() {
        this(new ArrayList<>());
        this.selectedIndex = -1; // 初始化时无选中项.
    }

    /**
     * 构造函数，指定初始列表.
     * @param list 初始列表数据.
     */
    public ListViewer(List<T> list) {
        this.list = new ArrayList<>(list); // 复制传入的列表.
        this.setLayout(new VerticalFlowLayout(0, 4)); // 设置布局管理器.
        createView(); // 创建初始视图.
    }

    /**
     * 创建或重新创建列表视图.
     * 如果列表为空，显示空列表视图；否则，遍历列表，为每个元素创建视图并添加到面板.
     */
    public void createView() {
        // 列表为空时，添加空视图
        if(list.isEmpty()) {
            this.add(emptyView(), BorderLayout.CENTER);
            return;
        }

        // 创建并添加头部视图
        JComponent header = createHeader();
        if (header != null) add(header);

        // 遍历列表，为每个元素创建视图
        for(int i = 0; i < list.size(); i++) {
            // 为每个元素创建视图.
            JComponent cellView = createCellView(list, i);
            // 将视图添加到面板.
            this.add(cellView);
        }

        // 创建并添加尾部视图
        JComponent footer = createFooter();
        if (footer != null) add(footer);

        revalidate(); // 重新验证布局.
        repaint(); // 重新绘制组件.
    }


    /**
     * 创建标题组件
     * 此方法用于生成界面的标题部分，但目前实现中并未具体实现，返回null
     * 需要在未来实现中具体定义标题的展示形式和内容
     *
     * @return JComponent 标题组件，当前返回null
     */
    public JComponent createHeader() {
        return null;
    }

    /**
     * 创建页脚组件
     * 此方法用于生成界面的页脚部分，但目前实现中并未具体实现，返回null
     * 需要在未来实现中具体定义页脚的展示形式和内容
     *
     * @return JComponent 页脚组件，当前返回null
     */
    public JComponent createFooter() {
        return null;
    }

    /**
     * 抽象方法，用于创建列表项的视图组件.
     * @param list 列表数据.
     * @param index 当前列表项的索引.
     * @return 返回表示列表项的JComponent组件.
     */
    public abstract JComponent createCellView(List<T> list, int index);

    /**
     * 抽象方法，用于在选中项改变时进行处理.
     */
    public abstract void changeSelection();

    /**
     * 抽象方法，返回当列表为空时所显示的视图.
     * @return 返回表示空列表的视图组件.
     */
    public abstract JComponent emptyView();

    /**
     * 向列表末尾添加一个元素.
     * @param item 要添加的元素.
     */
    public void addItem(T item) {
        list.add(item);
    }

    /**
     * 在指定位置插入一个元素.
     * @param item 要插入的元素.
     * @param index 插入位置的索引.
     */
    public void addItem(T item, int index) {
        list.add(index, item);
    }

    /**
     * 获取指定索引位置的元素.
     * @param index 元素的索引位置.
     * @return 返回该索引位置的元素.
     */
    public T getItemAtIndex(int index) {
        return list.get(index);
    }

    /**
     * 获取当前选中的元素.
     * 如果列表为空或无选中项，返回null.
     * @return 返回当前选中的元素，或null.
     */
    public T getSelectedItem() {
        if(list.isEmpty() || selectedIndex == -1) {
            return null;
        }
        return list.get(selectedIndex);
    }

    /**
     * 获取当前选中项的索引.
     * @return 返回当前选中项的索引.
     */
    public int getSelectedIndex() {
        return selectedIndex;
    }

    /**
     * 设置当前选中项的索引，并调用changeSelection方法更新视图.
     * @param selectedIndex 新的选中项索引.
     */
    public void setSelectedIndex(int selectedIndex) {
        this.selectedIndex = selectedIndex;
        changeSelection(); // 更新视图以反映新的选中状态.
    }

    /**
     * 获取列表项的高度.
     * @return 返回列表项的高度.
     */
    public int getCellHeight() {
        return cellHeight;
    }

    /**
     * 设置列表项的高度.
     * @param cellHeight 新的列表项高度.
     */
    public void setCellHeight(int cellHeight) {
        this.cellHeight = cellHeight;
    }

    /**
     * 刷新列表视图，移除所有组件并重新创建视图.
     */
    public void refresh() {
        removeAll(); // 移除所有组件.
        createView(); // 重新创建视图.
    }

    /**
     * 获取列表数据.
     * @return 返回列表数据.
     */
    public List<T> getList() {
        return list;
    }

    /**
     * 设置列表数据.
     * @param list 新的列表数据.
     */
    public void setList(List<T> list) {
        this.list = list;
    }

    /**
     * 获取列表为空时显示的文本.
     * @return 返回空列表文本.
     */
    public String getEmptyText() {
        return emptyText;
    }

    /**
     * 设置列表为空时显示的文本.
     * @param emptyText 新的空列表文本.
     */
    public void setEmptyText(String emptyText) {
        this.emptyText = emptyText;
    }

    /**
     * 从列表中移除一个元素.
     * @param item 要移除的元素.
     */
    public void removeItem(T item) {
        this.list.remove(item);
    }

    /**
     * 根据索引从列表中移除一个元素.
     * @param index 要移除元素的索引.
     */
    public void removeItem(int index) {
        this.list.remove(index);
    }
}
