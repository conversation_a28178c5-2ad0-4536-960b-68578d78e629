package com.srdcloud.ideplugin.general.enums;

/**
 * <AUTHOR>
 * @date 2024/5/13
 * @desc 模板操作枚举
 */
public enum PromptOperationType {
    USE("use","点选使用"),
    FAVORITE("favorite","收藏"),
    UNFAVORITE("unfavorite","取消收藏")
    ;

    private final String type;

    private final String desc;

    PromptOperationType(String type,String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
