package com.srdcloud.ideplugin.webview.workitem;

import com.intellij.openapi.Disposable;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.DialogWrapper;
import com.intellij.ui.jcef.JBCefApp;
import com.intellij.ui.jcef.JBCefBrowser;
import com.srdcloud.ideplugin.aicommit.WorkItemDialog;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import com.srdcloud.ideplugin.general.utils.EnvUtil;
import com.srdcloud.ideplugin.general.utils.GitUtil;
import com.srdcloud.ideplugin.general.utils.JsonUtil;
import com.srdcloud.ideplugin.remote.WorkItemCommHandler;
import com.srdcloud.ideplugin.remote.domain.WorkItem.WorkItemInfo;
import com.srdcloud.ideplugin.remote.domain.WorkItem.WorkItemListResponse;
import com.srdcloud.ideplugin.webview.base.WebView;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewReqCommand;
import com.srdcloud.ideplugin.webview.codechat.common.WorkItemEventType;
import com.srdcloud.ideplugin.webview.workitem.request.WorkItemRequest;
import com.srdcloud.ideplugin.webview.workitem.response.SearchworkitemsResponse;
import com.srdcloud.ideplugin.webview.workitem.response.SearchworkitemsResponseData;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.cef.CefSettings;
import org.cef.browser.CefBrowser;
import org.cef.browser.CefFrame;
import org.cef.browser.CefMessageRouter;
import org.cef.callback.CefQueryCallback;
import org.cef.handler.CefDisplayHandlerAdapter;
import org.cef.handler.CefLoadHandler;
import org.cef.handler.CefMessageRouterHandlerAdapter;
import org.cef.network.CefRequest;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;
import java.net.URI;
import java.util.List;
import java.util.Objects;

/**
 * 用于AICommit过程中，弹出选中工作项
 */
public class WorkItemWebview implements Disposable {
    private static final Logger logger = LoggerFactory.getLogger(WorkItemWebview.class);

    private Project project;

    // 持有的webview实例
    private WebView webView = null;

    // 资源载入路径
    private final static String RESOURCE_DOMAIN_NAME = "workitem";
    private final static String RESOURCE_URL = "http://" + RESOURCE_DOMAIN_NAME + "/index.html";

    // 通道配置，管理通讯通道命名
    private static final CefMessageRouter.CefMessageRouterConfig config = new CefMessageRouter.CefMessageRouterConfig("workItemQuery", "cancel");

    // webview实例是否已经被加载成功，加载成功后才可以通信：2020版本需要展开ToolWindow才能触发懒加载；2022之后会自动触发加载
    private boolean loaded = false;

    // 交互中的对话框
    private WorkItemDialog parentDialog;

    // 选中的WorkItem对象
    private List<WorkItemInfo> workItemList;

    public WorkItemWebview(Project project) {
        this.project = project;

        // 判断是否支持JCEF
        if (!JBCefApp.isSupported()) {
            loaded = false;
            logger.error("[cf]WorkItemWebview init fail,JCEF is not supported.");
            return;
        }
    }

    /**
     * 获取WebviewTreeSitter的项目级别单例实例
     */
    public static WorkItemWebview getInstance(@NotNull Project project) {
        return project.getService(WorkItemWebview.class);
    }

    /**
     * 进行跨文件关联webview资源加载
     */
    public void loadWebview() {
        logger.info("load WorkItemWebview...");
        DebugLogUtil.println("load WorkItemWebview...");

        // 判断是否支持JCEF
        if (!JBCefApp.isSupported()) {
            loaded = false;
            logger.error("[cf]WorkItemWebview loadWebview skip,JCEF is not supported.");
            return;
        }

        if (webView == null) {
            DebugLogUtil.println("WorkItemWebview createBrowser and loadURL.");

            // 创建业务webview实例
            webView = new WebView(RESOURCE_DOMAIN_NAME);

            //---进行自定义设置---
            // 注册浏览器加载生命周期监听
            addLoadListener();

            // 浏览器console日志监听
            addConsoleListener();

            // 注册业务消息监听与处理逻辑
            addMessageListener();


            DebugLogUtil.println("load WorkItemWebview Resource...");
            logger.info("load WorkItemWebview Resource...");

            // 触发webview即时创建，保证可以在项目启动后派上用场
            webView.getBrowser().getCefBrowser().createImmediately();
            // 加载webview资源
            webView.getBrowser().loadURL(RESOURCE_URL);
        }
    }

    /**
     * 消息监听器，处理来自浏览器回调的消息
     */
    private void addMessageListener() {
        JBCefBrowser browser = webView.getBrowser();
        // 创建对应路由消息控制器
        CefMessageRouter messageRouter = CefMessageRouter.create(config, new CefMessageRouterHandlerAdapter() {

            // 处理收到的JavaScript消息
            @Override
            public boolean onQuery(CefBrowser browser, CefFrame frame, long queryId, String request, boolean persistent, CefQueryCallback callback) {
                if (StringUtils.isBlank(request)) {
                    return true;
                }

                try {
                    // 解析WebView发送的请求中的command消息
                    WorkItemRequest webViewCommandRequest = JsonUtil.getInstance().fromJson(request, WorkItemRequest.class);
                    if (Objects.isNull(webViewCommandRequest)) {
                        logger.warn("[cf] WorkItemWebview webViewCommandRequest is null");
                        return true;
                    }

                    if (EnvUtil.checkWebviewLogAble()) {
                        DebugLogUtil.println("handle WorkItemWebview onQuery request: " + request);
                    }

                    // 根据不同指令，进行业务处理
                    switch (webViewCommandRequest.getCommand()) {
                        // webView加载完成
                        case WebViewReqCommand.WEBVIEW_LOADED:
                            DebugLogUtil.println("WorkItemWebview webviewContentLoaded.");
                            logger.info("WorkItemWebview webviewContentLoaded.");
                            loaded = true;
                            // 设置背景颜色
                            webView.setWebviewBackground();
                            break;

                        case "workitem-request":
                            handleWorkItemRequest(request);
                            callback.success("true");
                            break;
                    }

                    // 回复js端：success 或者 failure
                    callback.success("query success");
                } catch (Exception e) {
                    logger.warn("[cf] WorkItemWebview Query error: can not process query message");
                    e.printStackTrace();
                    callback.failure(0, "query fail");
                }

                return true;
            }

            // Web视图信息回传通道关闭消息
            @Override
            public void onQueryCanceled(CefBrowser browser, CefFrame frame, long queryId) {
                logger.info("[cf rlcc] WorkItemWebview onQueryCanceled canceled.");

                loaded = false;
            }
        });

        browser.getCefBrowser().getClient().addMessageRouter(messageRouter);
    }

    /**
     * 刷新工作项列表
     */
    public void refreshWorkItems(String keyWords) {
        // 先恢复按钮
        SearchworkitemsResponseData recoverButton = new SearchworkitemsResponseData(WorkItemEventType.ANSWERRECVED, 1);
        webView.sentMessageToWebview(JsonUtil.getInstance().toJson(new SearchworkitemsResponse("workitem-response", recoverButton)));

        // 再刷新工作项
        String projectCode = GitUtil.getOriginGitProjectCode(project);
        SearchworkitemsResponseData responseData = new SearchworkitemsResponseData();
        responseData.setSeqType(WorkItemEventType.SEARCH_WORK_ITEMS);
        if (StringUtils.isBlank(projectCode)) {
            responseData.setError("当前工程未关联研发云项目，无法获取工作项");
        } else {
            WorkItemListResponse workItemListResponse = WorkItemCommHandler.getWorkItemList(projectCode, keyWords);
            if (Objects.nonNull(workItemListResponse) && workItemListResponse.getCode() != RtnCode.SUCCESS) {
                logger.error("[cf] WorkItemWebview refreshWorkItems error,code:{},msg:{}", workItemListResponse.getCode(), workItemListResponse.getMsg());
                responseData.setError("获取工作项失败，请重试");
            } else if (StringUtils.isBlank(keyWords) && Objects.nonNull(workItemListResponse) && workItemListResponse.getCode() == RtnCode.SUCCESS && CollectionUtils.isEmpty(workItemListResponse.getData())) {
                responseData.setError("当前项目不存在指派给你的工作项");
            } else {
                responseData.setWorkItemList(workItemListResponse.getData());
            }
        }
        SearchworkitemsResponse response = new SearchworkitemsResponse("workitem-response", responseData);
        webView.sentMessageToWebview(JsonUtil.getInstance().toJson(response));
    }


    public void handleWorkItemRequest(String request) {
        try {
            WorkItemRequest workItemRequest = JsonUtil.getInstance().fromJson(request, WorkItemRequest.class);
            String reqType = workItemRequest.getData().getReqType();
            switch (reqType) {
                case WorkItemEventType.SEARCH_WORK_ITEMS:
                    String keyWords = workItemRequest.getData().getSearchParam();
                    refreshWorkItems(keyWords);
                    break;
                case WorkItemEventType.SELECT_WORK_ITEM:
                    // 提取选中的工作项
                    if (CollectionUtils.isNotEmpty(workItemRequest.getData().getWorkItemList())) {
                        workItemList = workItemRequest.getData().getWorkItemList();
                    }
                    // 控制弹窗退出
                    if (parentDialog != null) {
                        SwingUtilities.invokeLater(() -> {
                            parentDialog.close(DialogWrapper.OK_EXIT_CODE);
                        });
                    }
                    break;

                // 预埋:跳过选取工作项关联?
                //case WorkItemEventType.SELECT_WORK_ITEM:
                //    workItem = null;
                //    // 控制弹窗退出
                //    if (parentDialog != null) {
                //        SwingUtilities.invokeLater(() -> {
                //            parentDialog.close(DialogWrapper.CANCEL_EXIT_CODE);
                //        });
                //    }
                //    break;

                case WorkItemEventType.VIEW_WORK_ITEM:
                    // 提取链接，拉起浏览器跳转
                    String path = workItemRequest.getData().getWorkItemURL();
                    path = ConfigWrapper.getServerHost() + path;
                    Desktop.getDesktop().browse(URI.create(path));
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            logger.error("[cf] WorkItemWebview handleWorkItemRequest error: {}", e.getMessage());
        }
    }

    public WorkItemDialog getParentDialog() {
        return parentDialog;
    }

    public void setParentDialog(WorkItemDialog parentDialog) {
        this.parentDialog = parentDialog;
    }

    public List<WorkItemInfo> getWorkItemList() {
        return workItemList;
    }

    public void setWorkItemList(List<WorkItemInfo> workItemList) {
        this.workItemList = workItemList;
    }

    public void clearSelectedWorkItemObject() {
        this.workItemList = null;
    }

    // ================================ 基础方法 =================================================

    /**
     * 添加webview browser实例加载过程监听器，监控加载状态
     */
    private void addLoadListener() {
        JBCefBrowser browser = webView.getBrowser();
        browser.getJBCefClient().addLoadHandler(new CefLoadHandler() {

            // Web视图状态改变
            @Override
            public void onLoadingStateChange(CefBrowser browser, boolean isLoading, boolean canGoBack, boolean canGoForward) {
            }

            // Web视图开始进入加载状态，该状态下Web视图还无法处理数据
            @Override
            public void onLoadStart(CefBrowser browser, CefFrame frame, CefRequest.TransitionType transitionType) {
            }

            @Override
            public void onLoadEnd(CefBrowser browser, CefFrame frame, int httpStatusCode) {
                DebugLogUtil.println("WorkItemWebview loaded,httpStatusCode:" + httpStatusCode);
                logger.info("WorkItemWebview loaded,httpStatusCode:{}", httpStatusCode);
                // browser加载成功，可以进行后续通信
                loaded = true;
            }

            // Web视图加载发生错误
            @Override
            public void onLoadError(CefBrowser browser, CefFrame frame, ErrorCode errorCode, String errorText, String failedUrl) {
                logger.warn("[cf] WorkItemWebview error,failedUrl:{},errorCode:{},errorText:{}", failedUrl, errorCode.name(), errorText);
            }
        }, browser.getCefBrowser());
    }

    /**
     * 控制台输出监听器，处理来自webview的控制台输出信息，用于调试使用
     */
    private void addConsoleListener() {
        JBCefBrowser browser = webView.getBrowser();
        browser.getJBCefClient().getCefClient().addDisplayHandler(new CefDisplayHandlerAdapter() {
            @Override
            public boolean onConsoleMessage(CefBrowser browser, CefSettings.LogSeverity level, String message, String source, int line) {
                if (CefSettings.LogSeverity.LOGSEVERITY_WARNING == level || CefSettings.LogSeverity.LOGSEVERITY_ERROR == level || CefSettings.LogSeverity.LOGSEVERITY_FATAL == level || CefSettings.LogSeverity.LOGSEVERITY_DISABLE == level) {
//                    logger.warn("[cf] WorkItemWebview console log,{}:{}", level.name(), message);
                } else {
                    if (EnvUtil.checkWebviewLogAble()) {
                        DebugLogUtil.info("WorkItemWebview console log: " + message);
                    }
                }
                return super.onConsoleMessage(browser, level, message, source, line);
            }
        });
    }

    public WebView getWebView() {
        return webView;
    }

    /**
     * 获取Web组件，调试用
     */
    public JComponent getWebViewComponent() {
        if (webView == null) {
            return null;
        }
        return webView.getBrowser().getComponent();
    }

    public Project getProject() {
        return project;
    }

    public boolean isLoaded() {
        return loaded;
    }

    @Override
    public void dispose() {
        DebugLogUtil.println("WorkItemWebview disposed.");
        if (webView != null) {
            webView.dispose();
        }
        project = null;
        webView = null;
        loaded = false;
    }
}
