package com.srdcloud.ideplugin.remote;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Maps;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.general.utils.*;
import com.srdcloud.ideplugin.remote.client.HttpClient;
import com.srdcloud.ideplugin.remote.domain.ApiResponse;
import com.srdcloud.ideplugin.remote.domain.ScanAssistant.*;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.internal.sse.RealEventSource;
import okhttp3.sse.EventSourceListener;
import org.apache.http.HttpEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.entity.mime.content.StringBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/9/9
 * @description 安全扫描
 */
public class ScanAssistantCommHandler {

    private static final Logger logger = LoggerFactory.getLogger(ScanAssistantCommHandler.class);

    private static final String uploadZipFile = "api/sqscapi/ide/upload";
    private static final String stopTask = "api/sqscapi/ide/stopTask";
    private static final String queryIssueList = "api/sqscapi/ide/queryIssues";
    private static final String fixExplain = "api/sqscapi/sqsc-ai-agent/multi-issue/explain";


    /**
     * 上传文件
     */
    public static ScanUploadResponse uploadZipFile(final String projectName, final String language, final Integer fileSize, final String repoUrl, final FileBody fileBody, final String macAddr, final String scannerEngine) {
        if (!LocalStorageUtil.checkNetCondition()) {
            logger.warn("[cf] uploadFiles skip, net condition fail.");
            return new ScanUploadResponse(RtnCode.OFFLINE, "网络条件异常，请稍后重试.", null);
        }

        final String baseUrl = ConfigWrapper.getServerUrl() + uploadZipFile;
        MultipartEntityBuilder builder = MultipartEntityBuilder.create();
        builder.addPart("scannerEngine", new StringBody(scannerEngine, ContentType.TEXT_PLAIN));
        builder.addPart("projectName", new StringBody(projectName, ContentType.TEXT_PLAIN));
        builder.addPart("macAddr", new StringBody(macAddr, ContentType.TEXT_PLAIN));
        builder.addPart("language", new StringBody(language, ContentType.TEXT_PLAIN));
        builder.addPart("ideType", new StringBody("Idea", ContentType.TEXT_PLAIN));
        builder.addPart("fileSize", new StringBody(String.valueOf(fileSize), ContentType.TEXT_PLAIN));
        builder.addPart("repoUrl", new StringBody(repoUrl, ContentType.TEXT_PLAIN));
        builder.addPart("file", fileBody);
        HttpEntity entity = builder.build();

        final HashMap<String, String> headers = generateAuthHeaders();
        final ApiResponse apiResponse = HttpClient.doUpload(baseUrl, entity, headers);

        if (RtnCode.SUCCESS != apiResponse.getRtnCode()) {
            logger.warn("[cf] ScanAssistantCommHandler uploadFiles,rtnCode:{},msg:{}", apiResponse.getRtnCode(), apiResponse.getMessage());
            return new ScanUploadResponse(apiResponse.getRtnCode(), apiResponse.getMessage(), null);
        }

        return JsonUtil.getInstance().fromJson(apiResponse.getMessage(), ScanUploadResponse.class);
    }

    /**
     * 查询扫描任务结果
     * code：0 为扫描完成 ； 1 未完成，且msg说明当前节点状态 ； <0 为扫描错误且msg说明错误原因
     */
    public static IssueListResponse queryIssueList(final String taskId, final Integer page, final Integer pageSize) {
        if (!LocalStorageUtil.checkNetCondition()) {
            logger.warn("[cf] queryIssues skip, net condition fail.");
            return new IssueListResponse(RtnCode.OFFLINE, "网络条件异常，请稍后重试.", 0, null);
        }

        final String baseUrl = ConfigWrapper.getServerUrl() + queryIssueList;
        final HashMap<String, String> params = Maps.newHashMapWithExpectedSize(3);
        params.put("taskId", taskId);
        params.put("page", String.valueOf(page));
        params.put("pageSize", String.valueOf(pageSize));

        final String queryUrl = UrlUtil.buildUrlWithParams(baseUrl, params);
        final HashMap<String, String> headers = generateAuthHeaders();
        final ApiResponse apiResponse = HttpClient.doGet(queryUrl, headers);

        if (RtnCode.SUCCESS != apiResponse.getRtnCode()) {
            logger.warn("[cf] ScanAssistantCommHandler queryIssues,rtnCode:{},msg:{}", apiResponse.getRtnCode(), apiResponse.getMessage());
            return new IssueListResponse(apiResponse.getRtnCode(), apiResponse.getMessage(), 0, null);
        }

        return JsonUtil.getInstance().fromJson(apiResponse.getMessage(), IssueListResponse.class);
    }

    /**
     * 停止扫描任务
     */
    public static ScanBaseResponse stopTask(final String taskId) {
        if (!LocalStorageUtil.checkNetCondition()) {
            logger.warn("[cf] stopTask skip, net condition fail.");
            return new IssueListResponse(RtnCode.OFFLINE, "网络条件异常，请稍后重试.", 0, null);
        }

        final String url = ConfigWrapper.getServerUrl() + stopTask;
        StopTaskRequest request = new StopTaskRequest(taskId);
        String bodyString = JSON.toJSONString(request);

        final HashMap<String, String> headers = generateAuthHeaders();
        final ApiResponse apiResponse = HttpClient.doPost(url, bodyString, headers);

        if (RtnCode.SUCCESS != apiResponse.getRtnCode()) {
            logger.warn("[cf] ScanAssistantCommHandler stopTask,rtnCode:{},msg:{}", apiResponse.getRtnCode(), apiResponse.getMessage());
            return new ScanBaseResponse(apiResponse.getRtnCode(), apiResponse.getMessage());
        }

        return JsonUtil.getInstance().fromJson(apiResponse.getMessage(), ScanBaseResponse.class);
    }

    /**
     * 修复建议
     */
    public static void fixExplain(Integer type, Integer fileStartLine, Integer fileEndLine, String fileContent, List<Issue> issues, EventSourceListener eventSourceListener) {
        if (!LocalStorageUtil.checkNetCondition()) {
            logger.warn("[cf] fixExplain skip, net condition fail.");
            return;
        }

        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(1, TimeUnit.DAYS)
                .readTimeout(1, TimeUnit.DAYS)//这边需要将超时显示设置长一点，不然刚连上就断开，之前以为调用方式错误被坑了半天
                .build();

        // url
        final String baseUrl = ConfigWrapper.getServerUrl() + fixExplain;

        // body
        JSONArray issueArray = new JSONArray(issues);
        JSONObject params = new JSONObject();
        params.put("type", type);
        params.put("fileStartLine", fileStartLine);
        params.put("fileEndLine", fileEndLine);
        params.put("fileContent", fileContent);
        params.put("issueList", issueArray);
        // 创建MediaType对象，指定发送数据的格式
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        // 请求体
        String requestBody = params.toString();
        RequestBody body = RequestBody.create(mediaType, requestBody);

        // request and header
        Request request = new Request.Builder()
                .url(baseUrl)
                .addHeader("Content-Type", "application/json;charset=utf8")
                .addHeader("apiKey", LocalStorageUtil.getApikey())
                .addHeader("userid", LocalStorageUtil.getUserId())
                .addHeader("invokerId", LocalStorageUtil.getUserId())
                .post(body)
                .build();

        // 实例化EventSource，注册EventSource监听器
        RealEventSource realEventSource = new RealEventSource(request, eventSourceListener);

        //真正开始请求的一步
        realEventSource.connect(okHttpClient);
    }

    /**
     * 生成网关层鉴权头域字段
     */
    private static HashMap<String, String> generateAuthHeaders() {
        HashMap<String, String> headers = Maps.newHashMapWithExpectedSize(4);
        headers.put("apiKey", LocalStorageUtil.getApikey());
        headers.put("userid", LocalStorageUtil.getUserId());
        headers.put("invokerId", LocalStorageUtil.getUserId());
        headers.put("x-dup-id", String.valueOf(TimeUtil.getNowTimeSecTimestamp() + "-" + MyRandomUtil.generateRandomString(8)));
        return headers;
    }
}
