package com.srdcloud.ideplugin.webview.treesitter;


import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2024/11/21
 * @desc webview加载兜底任务，如果在插件初始化过程中没有成功加载webview，则弹通知尝试引导用户打开一次侧边栏触发加载
 */
@Deprecated
public class WebviewTreeSitterLoadTask {
    private static final Logger logger = LoggerFactory.getLogger(WebviewTreeSitterLoadTask.class);

    // 无界阻塞队列
    private LinkedBlockingQueue<Project> taskQueue = new LinkedBlockingQueue<>();

    // 工作线程
    private Thread workThread;

    // 计数器
    private AtomicInteger counter = new AtomicInteger(0);

    public static WebviewTreeSitterLoadTask getInstance(@NotNull Project project) {
        return project.getService(WebviewTreeSitterLoadTask.class);
    }

    public WebviewTreeSitterLoadTask() {
        // 启动工作线程，监听阻塞队列
        workThread = new Thread(() -> {
            try {
                // 从队列中获取一个元素，如果队列为空，则此方法会阻塞
                Project project = taskQueue.take();
                // 一直运行，直到加载成功为止
                while (Objects.nonNull(WebviewTreeSitter.getInstance(project)) && !WebviewTreeSitter.getInstance(project).isLoaded() && counter.get() < 5) {
                    DebugLogUtil.warn("[cf] WebviewTreeSitter load call.");
                    counter.addAndGet(1);
                }
                workThread.interrupt();
            } catch (InterruptedException e) {
                logger.info("[cf] WebviewLoadTask workThread exited.");
                DebugLogUtil.println("WebviewLoadTask workThread exited.");
            } catch (Exception e) {
                logger.warn("[cf] WebviewLoadTask workThread exception:");
                e.printStackTrace();
            }
        });
        workThread.setDaemon(true);
        workThread.start();
    }

    /**
     * 新增webview加载任务
     */
    public void addTaskItem(@NotNull Project project) {
        taskQueue.offer(project);
    }
}
