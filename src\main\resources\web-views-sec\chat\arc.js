import{aK as ln,aL as an,aM as un,aN as y,aH as tn,aO as Q,aP as O,aQ as W,aR as _,aS as rn,aT as sn,aU as on,aV as o,aG as K,aW as fn}from"./index.js";function cn(l){return l.innerRadius}function yn(l){return l.outerRadius}function gn(l){return l.startAngle}function dn(l){return l.endAngle}function mn(l){return l&&l.padAngle}function pn(l,h,D,S,v,R,L,a){var E=D-l,i=S-h,n=L-v,d=a-R,u=d*E-n*i;if(!(u*u<y))return u=(n*(h-R)-d*(l-v))/u,[l+u*E,h+u*i]}function J(l,h,D,S,v,R,L){var a=l-D,E=h-S,i=(L?R:-R)/W(a*a+E*E),n=i*E,d=-i*a,u=l+n,s=h+d,f=D+n,c=S+d,M=(u+f)/2,t=(s+c)/2,m=f-u,g=c-s,A=m*m+g*g,T=v-R,P=u*c-f*s,G=(g<0?-1:1)*W(fn(0,T*T*A-P*P)),H=(P*g-m*G)/A,I=(-P*m-g*G)/A,w=(P*g+m*G)/A,p=(-P*m+g*G)/A,x=H-M,e=I-t,r=w-M,N=p-t;return x*x+e*e>r*r+N*N&&(H=w,I=p),{cx:H,cy:I,x01:-n,y01:-d,x11:H*(v/T-1),y11:I*(v/T-1)}}function hn(){var l=cn,h=yn,D=K(0),S=null,v=gn,R=dn,L=mn,a=null,E=ln(i);function i(){var n,d,u=+l.apply(this,arguments),s=+h.apply(this,arguments),f=v.apply(this,arguments)-an,c=R.apply(this,arguments)-an,M=un(c-f),t=c>f;if(a||(a=n=E()),s<u&&(d=s,s=u,u=d),!(s>y))a.moveTo(0,0);else if(M>tn-y)a.moveTo(s*Q(f),s*O(f)),a.arc(0,0,s,f,c,!t),u>y&&(a.moveTo(u*Q(c),u*O(c)),a.arc(0,0,u,c,f,t));else{var m=f,g=c,A=f,T=c,P=M,G=M,H=L.apply(this,arguments)/2,I=H>y&&(S?+S.apply(this,arguments):W(u*u+s*s)),w=_(un(s-u)/2,+D.apply(this,arguments)),p=w,x=w,e,r;if(I>y){var N=rn(I/u*O(H)),j=rn(I/s*O(H));(P-=N*2)>y?(N*=t?1:-1,A+=N,T-=N):(P=0,A=T=(f+c)/2),(G-=j*2)>y?(j*=t?1:-1,m+=j,g-=j):(G=0,m=g=(f+c)/2)}var U=s*Q(m),V=s*O(m),z=u*Q(T),B=u*O(T);if(w>y){var C=s*Q(g),F=s*O(g),X=u*Q(A),Y=u*O(A),q;if(M<sn)if(q=pn(U,V,X,Y,C,F,z,B)){var Z=U-q[0],$=V-q[1],k=C-q[0],b=F-q[1],nn=1/O(on((Z*k+$*b)/(W(Z*Z+$*$)*W(k*k+b*b)))/2),en=W(q[0]*q[0]+q[1]*q[1]);p=_(w,(u-en)/(nn-1)),x=_(w,(s-en)/(nn+1))}else p=x=0}G>y?x>y?(e=J(X,Y,U,V,s,x,t),r=J(C,F,z,B,s,x,t),a.moveTo(e.cx+e.x01,e.cy+e.y01),x<w?a.arc(e.cx,e.cy,x,o(e.y01,e.x01),o(r.y01,r.x01),!t):(a.arc(e.cx,e.cy,x,o(e.y01,e.x01),o(e.y11,e.x11),!t),a.arc(0,0,s,o(e.cy+e.y11,e.cx+e.x11),o(r.cy+r.y11,r.cx+r.x11),!t),a.arc(r.cx,r.cy,x,o(r.y11,r.x11),o(r.y01,r.x01),!t))):(a.moveTo(U,V),a.arc(0,0,s,m,g,!t)):a.moveTo(U,V),!(u>y)||!(P>y)?a.lineTo(z,B):p>y?(e=J(z,B,C,F,u,-p,t),r=J(U,V,X,Y,u,-p,t),a.lineTo(e.cx+e.x01,e.cy+e.y01),p<w?a.arc(e.cx,e.cy,p,o(e.y01,e.x01),o(r.y01,r.x01),!t):(a.arc(e.cx,e.cy,p,o(e.y01,e.x01),o(e.y11,e.x11),!t),a.arc(0,0,u,o(e.cy+e.y11,e.cx+e.x11),o(r.cy+r.y11,r.cx+r.x11),t),a.arc(r.cx,r.cy,p,o(r.y11,r.x11),o(r.y01,r.x01),!t))):a.arc(0,0,u,T,A,t)}if(a.closePath(),n)return a=null,n+""||null}return i.centroid=function(){var n=(+l.apply(this,arguments)+ +h.apply(this,arguments))/2,d=(+v.apply(this,arguments)+ +R.apply(this,arguments))/2-sn/2;return[Q(d)*n,O(d)*n]},i.innerRadius=function(n){return arguments.length?(l=typeof n=="function"?n:K(+n),i):l},i.outerRadius=function(n){return arguments.length?(h=typeof n=="function"?n:K(+n),i):h},i.cornerRadius=function(n){return arguments.length?(D=typeof n=="function"?n:K(+n),i):D},i.padRadius=function(n){return arguments.length?(S=n==null?null:typeof n=="function"?n:K(+n),i):S},i.startAngle=function(n){return arguments.length?(v=typeof n=="function"?n:K(+n),i):v},i.endAngle=function(n){return arguments.length?(R=typeof n=="function"?n:K(+n),i):R},i.padAngle=function(n){return arguments.length?(L=typeof n=="function"?n:K(+n),i):L},i.context=function(n){return arguments.length?(a=n==null?null:n,i):a},i}export{hn as d};
