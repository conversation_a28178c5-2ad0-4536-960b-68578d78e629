package com.srdcloud.ideplugin.general.enums;

/**
 * AI通信消息类型
 * 为null，则是代码补全
 * 其他则是对话类消息
 */
public enum ChatMessageType {
    EXPLAIN(1, "解释代码"),
    UNITTEST(2, "生成单元测试"),
    COMMENT(3, "生成代码注释"),
    MANUAL_GENERATE(4, "自然语言编程"),
    CHAT_GENERATE(5, "编程助手问答"),
    OPTIMIZE(6, "生成优化建议"),
    FIX_EXCEPTION(7, "修复异常报错"),
    KB_ASSISTANT(8, "知识库问答"),
    QA_RELATED_FILES(9, "开始聊天")
    ;

    public int type;
    public String desc;

    ChatMessageType(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static String getDesc(int type) {
        for (ChatMessageType chatMessageType : ChatMessageType.values()) {
            if (chatMessageType.type == type) {
                return chatMessageType.desc;
            }
        }
        return null;
    }

    public static ChatMessageType getChatMessageTypeByType(int type) {
        for (ChatMessageType chatMessageType : ChatMessageType.values()) {
            if (chatMessageType.type == type) {
                return chatMessageType;
            }
        }
        return ChatMessageType.CHAT_GENERATE;
    }

    public static Boolean isRightMenu(int type) {
        if(type == ChatMessageType.EXPLAIN.getType() || type == ChatMessageType.UNITTEST.getType()
                || type == ChatMessageType.COMMENT.getType() || type == ChatMessageType.OPTIMIZE.getType()){
            return true;
        }else{
            return false;
        }
    }
    
    public static SubServiceType getSubServiceTypeByMessageType(Integer messageType) {
        if (messageType != null && messageType == ChatMessageType.MANUAL_GENERATE.getType()) {
            return SubServiceType.CODE_CHAT;
        } else if (messageType != null && messageType == ChatMessageType.EXPLAIN.getType()) {
            return SubServiceType.CODE_EXPLAIN;
        } else if (messageType != null && messageType == ChatMessageType.UNITTEST.getType()) {
            return SubServiceType.CODE_UNITTEST;
        } else if (messageType != null && messageType == ChatMessageType.COMMENT.getType()) {
            return SubServiceType.CODE_COMMENT;
        } else if (messageType != null && messageType == ChatMessageType.OPTIMIZE.getType()) {
            return SubServiceType.CODE_OPTIMIZE;
        } else if (messageType != null && messageType == ChatMessageType.FIX_EXCEPTION.getType()) {
            return SubServiceType.FIX_EXCEPTION;
        } else if (messageType != null && messageType == ChatMessageType.KB_ASSISTANT.getType()) {
            return SubServiceType.KB_ASSISTANT;
        } else {
            return SubServiceType.ASSISTANT;
        }
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}