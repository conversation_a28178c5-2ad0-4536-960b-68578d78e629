package com.srdcloud.ideplugin.assistant.codechatNative.ui.history;

import com.google.gson.JsonArray;
import com.intellij.find.SearchTextArea;
import com.intellij.openapi.Disposable;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.ComboBox;
import com.intellij.openapi.ui.DialogBuilder;
import com.intellij.openapi.ui.MessageDialogBuilder;
import com.intellij.ui.components.*;
import com.intellij.ui.components.panels.VerticalLayout;
import com.intellij.util.ui.FormBuilder;
import com.intellij.util.ui.JBUI;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.CodeChatCompleteEngin;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.ConversationHistoryLoadByAeBackend;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.ConversationManagerByAeBackend;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.PanelMessageBuilder;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.Conversation;
import com.srdcloud.ideplugin.assistant.codechatNative.ui.CodeChatMainPanel;
import com.srdcloud.ideplugin.common.icons.MyIcons;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.enums.SubServiceType;
import com.srdcloud.ideplugin.general.icons.GPTIcons;
import com.srdcloud.ideplugin.general.utils.IdeUtil;
import com.srdcloud.ideplugin.general.utils.MessageBalloonNotificationUtil;
import com.srdcloud.ideplugin.general.utils.UIUtil;
import com.srdcloud.ideplugin.remote.ChatHistoryHandler;
import com.srdcloud.ideplugin.remote.domain.Dialog.ChatHistoryCommonResponse;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.awt.event.*;
import java.util.*;
import java.util.concurrent.ExecutorService;

/**
 * @author: yangy, 蔡一新
 * @date: 2024/5/22 16:26
 * @Desc
 */
public class CodeChatHistoryGroupComponent extends JBLoadingPanel {

    // 日志
    private static final Logger logger = LoggerFactory.getLogger(CodeChatHistoryGroupComponent.class);

    // 当前组件所处的项目
    private Project project;

    // 当前项目中的对话主面板
    private CodeChatMainPanel codeChatMainPanel;

    // 页面中的历史会话列表
    private JBScrollPane scrollPane;


    /**
     * 历史会话数据分页大小
     */
    private static int conversationLoadLimit = 30;

    private JBLabel addSwitch;
    private JBLabel delSwitch;
    private JBLabel editSwitch;


    /**
     * 头部
     */
    private JPanel headerPanel;

    private JPanel historyFilterPanel;

    private JPanel codeChatHistoryGroupBoard = new JPanel(new BorderLayout());
    private CodeChatHistoryGroup codeChatHistoryGroup;

    public CodeChatHistoryGroupComponent(@NotNull Project project, @Nullable final CodeChatMainPanel codeChatMainPanel, @Nullable LayoutManager manager, @NotNull Disposable parent, int startDelayMs) {
        super(manager, parent, startDelayMs);
        this.project = project;
        this.codeChatMainPanel = codeChatMainPanel;
        boolean isDark = UIUtil.judgeBackgroudDarkTheme();
        UIUtil.setBackground(this);

        // 使用当前设置的字体作为插件的label字体
        Font font = IdeUtil.getIDELabelFont();

        historyFilterPanel = new JPanel(new VerticalLayout(JBUI.scale(2)));
        historyFilterPanel.setVisible(true);
        UIUtil.setBackground(historyFilterPanel);

        //搜索栏
        JPanel historySearchInputPanel = new JPanel(new BorderLayout());
        historySearchInputPanel.setVisible(true);
        UIUtil.setBackground(historySearchInputPanel);
        historySearchInputPanel.setBorder(JBUI.Borders.empty(0, 0, 6, 0));
        SearchTextArea historySearchInputArea = new SearchTextArea(new JBTextArea(), true);
        UIUtil.setBackground(historySearchInputArea);
        UIUtil.setBackground(historySearchInputArea.getTextArea());
        historySearchInputArea.setMultilineEnabled(false);
        historySearchInputArea.setBorder(new ComboBox<>().getBorder());// 使用下拉框边框样式
        historySearchInputArea.getTextArea().setLineWrap(true);
        historySearchInputArea.getTextArea().setWrapStyleWord(false);
        historySearchInputArea.setToolTipText("请输入关键字");
        setPlaceholder("搜索历史对话", historySearchInputArea, isDark);


        historySearchInputArea.getTextArea().addKeyListener(new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent e) {
                e.consume();
                if (e.getKeyCode() == KeyEvent.VK_ENTER) {
                    try {
                        String searchText = historySearchInputArea.getTextArea().getText();
                        if (StringUtils.isNotBlank(searchText)) {
                            refreshConversationList(searchText);
                        } else {
                            refreshConversationList(null);
                        }
                    } catch (Exception ex) {
                        logger.warn("historySearchInputArea get text", ex);
                    }
                }
            }
        });

        historySearchInputPanel.add(historySearchInputArea, BorderLayout.CENTER);

        // 窗体header栏位:标题、操作按钮
        headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBorder(JBUI.Borders.empty());
        headerPanel.setVisible(true);
        UIUtil.setBackground(headerPanel);
        setHeaderPanel(project, codeChatMainPanel, isDark);

        historyFilterPanel.add(headerPanel);
        historyFilterPanel.add(historySearchInputPanel);
        this.add(historyFilterPanel, BorderLayout.NORTH);


        // 历史对话列表
        codeChatHistoryGroup = new CodeChatHistoryGroup(codeChatMainPanel, project);

        //628版本，改为懒加载模式，点击展开侧边栏才加载
        //this.refreshConversationList(null);

        scrollPane = new JBScrollPane(codeChatHistoryGroup, ScrollPaneConstants.VERTICAL_SCROLLBAR_AS_NEEDED,
                ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);

        scrollPane.setBorder(new EmptyBorder(0, 0, 0, 0));

        codeChatHistoryGroupBoard.setBackground(getBackground());
        codeChatHistoryGroupBoard.add(scrollPane, BorderLayout.CENTER);

        this.add(codeChatHistoryGroupBoard, BorderLayout.CENTER);


        // 填充边际距离
        historySearchInputPanel.add(Box.createHorizontalStrut(8), BorderLayout.WEST);
        historySearchInputPanel.add(Box.createHorizontalStrut(8), BorderLayout.EAST);
        codeChatHistoryGroupBoard.add(Box.createHorizontalStrut(10), BorderLayout.WEST);
        codeChatHistoryGroupBoard.add(Box.createHorizontalStrut(10), BorderLayout.EAST);


        this.setFont(font);
        this.setVisible(true);


    }

    public void setPlaceholder(String placeholder, SearchTextArea historySearchInputArea, boolean isDark) {
        historySearchInputArea.getTextArea().setText(placeholder);
        historySearchInputArea.getTextArea().setForeground(UIUtil.getTextPlaceHolderColor());
        historySearchInputArea.getTextArea().addFocusListener(new FocusListener() {
            @Override
            public void focusGained(FocusEvent e) {
                if (historySearchInputArea.getTextArea().getText().equals(placeholder)) {
                    historySearchInputArea.getTextArea().setText(null);
                    historySearchInputArea.setForeground(null);
                    historySearchInputArea.getTextArea().setForeground(null);
                }
            }

            @Override
            public void focusLost(FocusEvent e) {
                if (historySearchInputArea.getTextArea().getText().isEmpty()) {
                    historySearchInputArea.getTextArea().setText(placeholder);
                    historySearchInputArea.setForeground(UIUtil.getTextPlaceHolderColor());
                    historySearchInputArea.getTextArea().setForeground(UIUtil.getTextPlaceHolderColor());
                }
            }
        });
    }

    /**
     * 设置header面板
     *
     * @param project
     * @param codeChatMainPanel
     * @param isDark
     */
    private void setHeaderPanel(@NotNull Project project, CodeChatMainPanel codeChatMainPanel, boolean isDark) {
        // 窗体头部-左侧区域:标题
        JPanel headerPanelLeft = new JPanel();
        headerPanelLeft.setOpaque(false);
        headerPanelLeft.setBorder(JBUI.Borders.empty(9, 3, 0, 0));
        headerPanelLeft.setLayout(new FlowLayout(FlowLayout.LEFT, 0, 0));

        headerPanelLeft.add(Box.createHorizontalStrut(10), BorderLayout.WEST);

        JPanel titlePanel = new JPanel(new BorderLayout());
        titlePanel.setBorder(JBUI.Borders.empty());
        titlePanel.setOpaque(false);
        titlePanel.setBorder(BorderFactory.createEmptyBorder());

        JLabel titleLabel = new JLabel(CodeChatMainPanel.conversationCardName);
        Font defaultFont = titleLabel.getFont();
        Font customFont = defaultFont.deriveFont(Font.PLAIN, 13);
        titleLabel.setFont(customFont);
        titleLabel.setVisible(true);
        titleLabel.setVerticalAlignment(JLabel.CENTER);
        titlePanel.add(titleLabel, BorderLayout.CENTER);

        headerPanelLeft.add(titlePanel);
        headerPanelLeft.add(Box.createHorizontalStrut(16));

        // 窗体头部-右侧区域
        JPanel headerPanelRight = new JPanel();
        headerPanelRight.setOpaque(false);
        headerPanelRight.setBorder(JBUI.Borders.empty(3, 0, 1, 7));
        headerPanelRight.setLayout(new FlowLayout(FlowLayout.RIGHT, 0, 0));

        // -- 侧边栏收起按钮
        Icon addIcon = null;
        Icon delIcon = null;
        Icon editIcon = null;
        Icon closeIcon = null;
        if (isDark) {
            addIcon = GPTIcons.ADD_DARK;
            delIcon = GPTIcons.DEL_DARK;
            editIcon = GPTIcons.EDIT_DARK;
            closeIcon = MyIcons.CloseDark;
        } else {
            addIcon = GPTIcons.ADD;
            delIcon = GPTIcons.DEL;
            editIcon = GPTIcons.EDIT;
            closeIcon = MyIcons.Close;
        }
        JBLabel closeSwitch = new JBLabel(closeIcon);
        closeSwitch.setOpaque(false);
        closeSwitch.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                // 收起左侧展开
                codeChatMainPanel.closeLeftWindow();
            }

            @Override
            public void mouseEntered(MouseEvent e) {
                closeSwitch.setCursor(new Cursor(Cursor.HAND_CURSOR));
                closeSwitch.setToolTipText("关闭");
            }
        });

        addSwitch = new JBLabel(addIcon);
        addSwitch.setOpaque(false);
        addSwitch.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (addSwitch.isEnabled()) {
                    // 重置会话id
                    ConversationManagerByAeBackend.getInstance(project).setConversationId(null);

                    // 创建一个新的并应用
                    addAndSelectNewConversation(project, Constants.NEW_CONVERSATION_NAME, SubServiceType.ASSISTANT.getName());
                }
            }

            @Override
            public void mouseEntered(MouseEvent e) {
                addSwitch.setCursor(new Cursor(Cursor.HAND_CURSOR));
                addSwitch.setToolTipText("新建会话");
            }
        });

        delSwitch = new JBLabel(delIcon);
        delSwitch.setOpaque(false);
        delSwitch.setPreferredSize(new Dimension(20, 20));
        delSwitch.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (delSwitch.isEnabled()) {
                    int selectedIndex = codeChatHistoryGroup.getSelectedIndex();
                    if (selectedIndex >= 0) {
                        boolean ask = ((MessageDialogBuilder.YesNo) ((MessageDialogBuilder.YesNo) MessageDialogBuilder.yesNo("删除会话", "是否要删除这个会话？").yesText("是")).noText("否")).ask(codeChatMainPanel.messageAreaPanel);
                        if (ask) {
                            Conversation conversation = codeChatHistoryGroup.getSelectedItem();
                            ConversationManagerByAeBackend.getInstance(project).removeConversation(conversation);


                            if (conversation.getId() != null) {
                                ChatHistoryHandler.removeDialog(conversation.getId());
                            }

                            codeChatHistoryGroup.removeItem(conversation);
                            refreshConversationList(null);
                            codeChatHistoryGroup.setSelectedIndex(0);
                        }

                    }
                }
            }

            @Override
            public void mouseEntered(MouseEvent e) {
                delSwitch.setCursor(new Cursor(Cursor.HAND_CURSOR));
                delSwitch.setToolTipText("删除选中对话");
            }
        });

        editSwitch = new JBLabel(editIcon);
        editSwitch.setOpaque(false);
        editSwitch.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {

                if (editSwitch.isEnabled()) {
                    if (codeChatHistoryGroup.getList().isEmpty()) {
                        return;
                    }

                    Conversation conv = codeChatHistoryGroup.getSelectedItem();
                    if (Objects.isNull(conv)) {
                        return;
                    }

                    String title = conv.getTitle();
                    if (title != null && title.length() > 100) {
                        title = title.substring(0, 100);
                    }
                    JBTextField textField = new JBTextField(title);
                    JPanel panel = FormBuilder.createFormBuilder().addLabeledComponent("会话标题:", textField).getPanel();
                    DialogBuilder dialogBuilder = (new DialogBuilder(codeChatMainPanel.mainPanelContent)).title("编辑标题").centerPanel(panel);
                    dialogBuilder.setHelpId("编辑会话标题");
                    if (dialogBuilder.show() == 0) {
                        ExecutorService executorService = codeChatMainPanel.getSerialThreadPool();
                        executorService.submit(() -> {
                            ChatHistoryCommonResponse chatHistoryCommonResponse = ChatHistoryHandler.editDialogTitle(conv.getId(), textField.getText());
                            if (RtnCode.SUCCESS == chatHistoryCommonResponse.getOptResult()) {
                                conv.setTitle(textField.getText());
                                SwingUtilities.invokeLater(() -> {
                                    codeChatHistoryGroup.refresh();
                                });
                            } else {
                                MessageBalloonNotificationUtil.showCommonBalloonNotification(Objects.requireNonNull(IdeUtil.findCurrentProject()), chatHistoryCommonResponse.getMsg());
                            }
                        });
                    }
                }
            }

            @Override
            public void mouseEntered(MouseEvent e) {
                editSwitch.setCursor(new Cursor(Cursor.HAND_CURSOR));
                editSwitch.setToolTipText("编辑会话标题");
            }
        });

        // 右侧填充:flow布局，预埋更多logo位置
        headerPanelRight.add(addSwitch);
        headerPanelRight.add(Box.createHorizontalStrut(10));
        headerPanelRight.add(delSwitch);
        headerPanelRight.add(Box.createHorizontalStrut(10));
        headerPanelRight.add(editSwitch);
        headerPanelRight.add(Box.createHorizontalStrut(10));
        headerPanelRight.add(closeSwitch);

        headerPanel.add(headerPanelLeft, BorderLayout.WEST);
        headerPanel.add(headerPanelRight, BorderLayout.EAST);
    }

    /**
     * 主会话页面单独出发刷新逻辑，避免重复刷新数据
     */
    public void refreshConversationListFromMainPanel() {
        refreshConversationList(null);
    }

    /**
     * 重新加载会话列表数据，重新获取数据以及刷新列表
     *
     * @param keyword 根据此关键字搜索对话历史，null为读取全部对话
     */
    public void refreshConversationList(String keyword) {
        // 页面开始进入加载状态
        this.startLoading();
        // 清空旧数据
        this.codeChatHistoryGroup.setList(new ArrayList<>());
        // 查询历史会话数据
        Map<String, Conversation> conversations = ConversationHistoryLoadByAeBackend.loadConversationHistoryList(project, conversationLoadLimit, keyword);

        // 对列表UI进行重绘
        SwingUtilities.invokeLater(() -> {
            this.stopLoading();

            // 迭代对话历史并添加到列表中
            Iterator var4;
            Map.Entry entry;
            Conversation value;
            if (MapUtils.isNotEmpty(conversations)) {
                var4 = conversations.entrySet().iterator();
                while (var4.hasNext()) {
                    entry = (Map.Entry) var4.next();
                    value = (Conversation) entry.getValue();
                    codeChatHistoryGroup.addItem(value, 0);
                }
            }

            // 刷新历史列表页
            codeChatHistoryGroup.setBackground(getBackground());
            codeChatHistoryGroup.refresh();

            this.revalidate();
            this.repaint();
        });
    }

    /**
     * 重新向服务器获取数据，并填入列表中
     *
     * @param keyword 根据此关键字搜索对话历史，null为读取全部对话
     */
    public void reLoadConversationList(String keyword) {

    }

    /**
     * 新建并选中、应用新会话
     */
    public void addAndSelectNewConversation(Project project, String conversationTitle, String subServiceType) {
        Conversation conversation = createNewConversation(project, conversationTitle, subServiceType);
        CodeChatCompleteEngin.setCurrentConversation(conversation);

        // 列表添加新元素，并且更新索引
        codeChatHistoryGroup.addItem(conversation);
        codeChatHistoryGroup.setSelectedIndex(0);

        // 重绘UI
        SwingUtilities.invokeLater(() -> {
            codeChatHistoryGroup.refresh();
        });

    }

    /**
     * 创建一个新的会话实例，并初始化相关消息，状态等属性
     */
    public Conversation createNewConversation(Project project, String conversationTitle, String subServiceType) {
        Conversation conversation = Conversation.defaultConversationWithTitle(conversationTitle, true);
        conversation.setSubServiceType(subServiceType);
        JsonArray messages = new JsonArray();
        messages.add(PanelMessageBuilder.assistantMessage(Constants.welcomeMsg));
        codeChatMainPanel.messageAreaPanel.setMessages(messages);

        // 重置会话上下文相关内容
        CodeChatCompleteEngin.currConversationConditionTemplateId = null;
        CodeChatCompleteEngin.currModelRouteCondition = null;
        ConversationManagerByAeBackend.getInstance(project).setConversationId((String) null);
        ConversationManagerByAeBackend.getInstance(project).setParentMessageId(UUID.randomUUID().toString());
        ConversationManagerByAeBackend.getInstance(project).addConversation(conversation);
        ConversationManagerByAeBackend.getInstance(project).setParentReqId(null);

        return conversation;
    }

    /**
     * 获取列表选中的会话，进行后续操作
     * - 用于加载会话到当前对话引擎和聊天面板
     * - 编辑会话
     * - 删除会话
     *
     * @return
     */
    public Conversation getSelectedConversation() {
        if (codeChatHistoryGroup.getList().isEmpty()) {
            return createNewConversation(project, Constants.NEW_CONVERSATION_NAME, SubServiceType.ASSISTANT.getName());
        }
        return codeChatHistoryGroup.getSelectedItem();
    }

    public void enableAddNewConversation() {
        addSwitch.setEnabled(true);
        delSwitch.setEnabled(true);
        editSwitch.setEnabled(true);

        delSwitch.revalidate();
        addSwitch.revalidate();
        editSwitch.revalidate();

        delSwitch.repaint();
        addSwitch.repaint();
        editSwitch.repaint();
    }

    public void disableAddNewConversation() {
        addSwitch.setEnabled(false);
        delSwitch.setEnabled(false);
        editSwitch.setEnabled(false);

        delSwitch.revalidate();
        addSwitch.revalidate();
        editSwitch.revalidate();

        delSwitch.repaint();
        addSwitch.repaint();
        editSwitch.repaint();
    }

}
