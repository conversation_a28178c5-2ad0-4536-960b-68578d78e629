package com.srdcloud.ideplugin.codecomplete.handle

import com.intellij.openapi.Disposable
import com.intellij.openapi.editor.Editor
import com.srdcloud.ideplugin.codecomplete.domain.CompletionElement
import com.srdcloud.ideplugin.codecomplete.render.EditorCompletionRender

/**
 * 代码补全能力接口定义
 */
interface ICompletion : Disposable {

    // 重置补全上下文
    fun reset()

    // 渲染展示补全提示
    fun render(proposal: CompletionElement, offset: Int)

    val offset: Int?
    val isEmpty: Boolean

    companion object {

        // 往补全上下文嵌入render
        fun forEditor(editor: Editor): ICompletion {
            return EditorCompletionRender(editor)
        }
    }
}