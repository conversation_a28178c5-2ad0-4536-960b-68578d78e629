package com.srdcloud.ideplugin.webview.codechat.knowledgebase;

import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.general.utils.JsonUtil;
import com.srdcloud.ideplugin.remote.KnowledgeBaseCommHandler;
import com.srdcloud.ideplugin.remote.domain.KnowledgeBase.KnowledgeBaseDetailResponse;
import com.srdcloud.ideplugin.remote.domain.KnowledgeBase.KnowledgeBaseListResponse;
import com.srdcloud.ideplugin.service.LoginService;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;
import com.srdcloud.ideplugin.webview.codechat.common.KnowledgeBaseEventType;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCode;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCommand;
import com.srdcloud.ideplugin.webview.codechat.knowledgebase.request.KnowledgeBaseRequest;
import com.srdcloud.ideplugin.webview.codechat.knowledgebase.response.*;
import com.srdcloud.ideplugin.webview.base.domain.ErrorResponse;

import java.util.Objects;

/**
 * 知识库处理器类，用于处理来自WebView的知识库请求。
 */
public class KnowledgeBaseHandler {

    /**
     * 当前项目实例。
     */
    private final Project project;

    /**
     * 父级CodeChatWebview实例。
     */
    private final CodeChatWebview parent;

    /**
     * 构造函数，初始化知识库处理器。
     *
     * @param project 当前项目实例
     * @param parent  父级CodeChatWebview实例
     */
    public KnowledgeBaseHandler(Project project, CodeChatWebview parent) {
        this.project = project;
        this.parent = parent;
    }

    /**
     * 处理知识库请求。
     *
     * @param request 请求字符串
     */
    public void processKnowledgeBaseRequest(String request) {
        // 检查用户是否已登录
        if (LoginService.getLoginStatus() != Constants.LoginStatus_OK) {
            parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(ErrorResponse.getNoLoginResponse(WebViewRspCommand.KNOWLEDGE_BASE_RESPONSE)));
            return;
        }

        // 解析webView请求
        KnowledgeBaseRequest knowledgeBaseRequest = JsonUtil.getInstance().fromJson(request, KnowledgeBaseRequest.class);
        String reqType = knowledgeBaseRequest.getData().getReqType();

        switch (reqType) {
            case KnowledgeBaseEventType.SEARCH_DEV_KBS:
                KnowledgeBaseListResponse knowledgeBaseListResponse = KnowledgeBaseCommHandler.getKnowledgeBaseList();

                // webView响应，知识库列表
                SearchDevKbsResponseCode searchDevKbsResponseCode;
                if (knowledgeBaseListResponse.getOptResult() != RtnCode.SUCCESS || Objects.isNull(knowledgeBaseListResponse.getData()) || Objects.isNull(knowledgeBaseListResponse.getData().getRecords())) {
                    searchDevKbsResponseCode = new SearchDevKbsResponseCode(knowledgeBaseListResponse.getOptResult(), null);
                } else {
                    searchDevKbsResponseCode = new SearchDevKbsResponseCode(WebViewRspCode.SUCCESS, knowledgeBaseListResponse.getData().getRecords());
                }
                SearchDevKbsResponseType searchDevKbsResponseType = new SearchDevKbsResponseType(KnowledgeBaseEventType.SEARCH_DEV_KBS, searchDevKbsResponseCode);
                SearchDevKbsResponse searchDevKbsResponse = new SearchDevKbsResponse(WebViewRspCommand.KNOWLEDGE_BASE_RESPONSE, searchDevKbsResponseType);
                parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(searchDevKbsResponse));
                break;
            case KnowledgeBaseEventType.GET_KB_INFO:
                int kbId = knowledgeBaseRequest.getData().getKbId();

                // 网络请求，反查知识库
                KnowledgeBaseDetailResponse knowledgeBaseDetailResponse = KnowledgeBaseCommHandler.getKnowledgeBaseDetail(kbId);

                // webView响应，反查知识库
                GetKbInfoResponseCode getKbInfoResponseCode;
                if (knowledgeBaseDetailResponse.getOptResult() != RtnCode.SUCCESS || Objects.isNull(knowledgeBaseDetailResponse.getData())) {
                    getKbInfoResponseCode = new GetKbInfoResponseCode(knowledgeBaseDetailResponse.getOptResult(), null);
                } else {
                    getKbInfoResponseCode = new GetKbInfoResponseCode(WebViewRspCode.SUCCESS, knowledgeBaseDetailResponse.getData());
                }
                GetKbInfoResponseType getKbInfoResponseType = new GetKbInfoResponseType(KnowledgeBaseEventType.GET_KB_INFO, getKbInfoResponseCode);
                GetKbInfoResponse getKbInfoResponse = new GetKbInfoResponse(WebViewRspCommand.KNOWLEDGE_BASE_RESPONSE, getKbInfoResponseType);
                parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(getKbInfoResponse));
                break;
            default:
                break;
        }
    }
}
