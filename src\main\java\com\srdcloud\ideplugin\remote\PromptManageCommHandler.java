package com.srdcloud.ideplugin.remote;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Maps;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.general.utils.*;
import com.srdcloud.ideplugin.remote.client.FastFailHttpClient;
import com.srdcloud.ideplugin.remote.client.HttpClient;
import com.srdcloud.ideplugin.remote.domain.ApiResponse;
import com.srdcloud.ideplugin.remote.domain.PromptManage.Client;
import com.srdcloud.ideplugin.remote.domain.PromptManage.ClientWrapper;
import com.srdcloud.ideplugin.remote.domain.PromptManage.PromptTemplateCategoryListResponse;
import com.srdcloud.ideplugin.remote.domain.PromptManage.PromptTemplateListResponse;
import com.srdcloud.ideplugin.service.domain.template.PromptTemplateCategory;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/5/13
 * @desc 模板市场相关接口通信封装
 */
public class PromptManageCommHandler {
    private static final Logger logger = LoggerFactory.getLogger(PromptManageCommHandler.class);

    // 标签分类列表
    private static final String listCategoriesPath = "/api/acbackend/promptmgr/v1/admin/categories";

    // 模板列表、搜索接口，支持分页
    private static final String listPromptPath = "/api/acbackend/promptmgr/v1/templates";

    // 模板操作接口：点选、收藏
    private static final String promptOpsPath = "/api/acbackend/promptmgr/v1/templates/{0}/operation/{1}";

    private static Client clientInstance = null;

    private static Client getClientInstance() {
        if (clientInstance == null) {
            clientInstance = new Client(IdeUtil.getIDEType(), IdeUtil.getIDEVersion(), IdeUtil.getPluginVersion());
        }
        return clientInstance;
    }

    public static ArrayList<PromptTemplateCategory> listCategories() {
        ArrayList<PromptTemplateCategory> result = new ArrayList<>();
        if (!LocalStorageUtil.checkNetCondition()) {
            logger.warn("[cf] listCategories skip, net condition fail.");
            return result;
        }

        try {
            String url = ConfigWrapper.getServerUrl() + listCategoriesPath;
            final HashMap<String, String> headers = generateAuthHeaders();
            final ApiResponse apiResponse = FastFailHttpClient.doGet(url, headers);
            if (RtnCode.SUCCESS != apiResponse.getRtnCode()) {
                logger.warn("[cf] PromptManageCommHandler listCategories,rtnCode:{},msg:{}", apiResponse.getRtnCode(), apiResponse.getMessage());
                return result;
            }
            final PromptTemplateCategoryListResponse listResponse = JsonUtil.getInstance().fromJson(apiResponse.getMessage(), PromptTemplateCategoryListResponse.class);
            if (Objects.nonNull(listResponse) && CollectionUtils.isNotEmpty(listResponse.getData())) {
                result = listResponse.getData();
            }
        } catch (Exception e) {
            logger.error("[cf] listCategories error:{}", e.getMessage());
        }
        return result;
    }

    public static PromptTemplateListResponse listPrompts(final Boolean lastUsed, final String type, final String keyword, final String categoryId, final Integer pageNum, final int pageSize) {
        PromptTemplateListResponse response = new PromptTemplateListResponse();
        if (!LocalStorageUtil.checkNetCondition()) {
            logger.warn("[cf] listPrompts skip, net condition fail.");
            response.setOptResult(RtnCode.OFFLINE);
            response.setMsg("网络条件异常，请稍后重试.");
            return response;
        }

        try {
            // 参数拼接
            JSONObject parasParam = new JSONObject();
            parasParam.put("type", type);
            parasParam.put("lastUsed", lastUsed);
            parasParam.put("categoryId", categoryId);

            parasParam.put("name", keyword);
            parasParam.put("pageNum", String.valueOf(pageNum));
            parasParam.put("pageDataCount", String.valueOf(pageSize));

            String url = ConfigWrapper.getServerUrl() + listPromptPath
                    + "?" + FormatUtil.json2UrlParam(parasParam.toString(), false, null);

            final HashMap<String, String> headers = generateAuthHeaders();
            final ApiResponse apiResponse = FastFailHttpClient.doGet(url, headers);

            //if (RtnCode.USER_FORBIDDEN == apiResponse.getRtnCode()) {
            //    ApplicationManager.getApplication().invokeLater(() -> {
            //        MessageDialogUtil.showUserBanDialog();
            //    });
            //}

            if (RtnCode.SUCCESS != apiResponse.getRtnCode()) {
                logger.error("[cf] PromptManageCommHandler listPrompts,rtnCode:{},msg:{}", apiResponse.getRtnCode(), apiResponse.getMessage());
                response.setOptResult(apiResponse.getRtnCode());
                response.setMsg(apiResponse.getMessage());
                return response;
            }
            response = JsonUtil.getInstance().fromJson(apiResponse.getMessage(), PromptTemplateListResponse.class);
        } catch (Exception e) {
            logger.error("[cf] listPrompts error:{}", e.getMessage());
        }
        return response;
    }

    /**
     * 模板操作：点选、收藏
     */
    public static ApiResponse promptOps(final String templateId, final String operationType, final String currentProjectName, final String currentProjectGitUrl) {
        if (!LocalStorageUtil.checkNetCondition()) {
            logger.warn("[cf] listPrompts skip, net condition fail.");
            return new ApiResponse(RtnCode.OFFLINE, "网络条件异常，请稍后重试.");
        }

        String url = ConfigWrapper.getServerUrl() + MessageFormat.format(promptOpsPath, templateId, operationType);
        Client client = getClientInstance();
        ClientWrapper clientWrapper = new ClientWrapper(client);

        // 填充git信息与工程名
        client.setProjectName(currentProjectName);
        client.setGitUrl(currentProjectGitUrl);

        String bodyString = JSON.toJSONString(clientWrapper);
        final HashMap<String, String> headers = generateAuthHeaders();
        final ApiResponse apiResponse = HttpClient.doPost(url, bodyString, headers);

        //if (RtnCode.USER_FORBIDDEN == apiResponse.getRtnCode()) {
        //    ApplicationManager.getApplication().invokeLater(() -> {
        //        MessageDialogUtil.showUserBanDialog();
        //    });
        //}

        if (RtnCode.SUCCESS != apiResponse.getRtnCode()) {
            logger.warn("[cf] PromptManageCommHandler promptOps,rtnCode:{},msg:{}", apiResponse.getRtnCode(), apiResponse.getMessage());
        }

        return apiResponse;
    }


    /**
     * 网关层鉴权头域字段：按需定制
     */
    private static HashMap<String, String> generateAuthHeaders() {
        HashMap<String, String> headers = Maps.newHashMapWithExpectedSize(4);
        headers.put("apiKey", LocalStorageUtil.getApikey());
        headers.put("userid", LocalStorageUtil.getUserId());
        headers.put("invokerId", LocalStorageUtil.getUserId());
        headers.put("x-dup-id", String.valueOf(TimeUtil.getNowTimeSecTimestamp() + "-" + MyRandomUtil.generateRandomString(8)));
        return headers;
    }

}
