package com.srdcloud.ideplugin.remote;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.general.utils.*;
import com.srdcloud.ideplugin.remote.client.HttpClient;
import com.srdcloud.ideplugin.remote.domain.ApiResponse;
import com.srdcloud.ideplugin.remote.domain.FeedBack.FeedbackAnswerData;
import com.srdcloud.ideplugin.remote.domain.FeedbackAnswerResponse;
import com.srdcloud.ideplugin.remote.domain.PromptManage.Client;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2024/5/13
 * @description 回答反馈
 */
public class FeedbackAnswerCommHandler {

    private static final Logger logger = LoggerFactory.getLogger(FeedbackAnswerCommHandler.class);
    private static Client clientInstance = null;

    private static Client getClientInstance() {
        if (clientInstance == null) {
            clientInstance = new Client(IdeUtil.getIDEType(), IdeUtil.getIDEVersion(), IdeUtil.getPluginVersion());
        }
        // 每次取当前项目计算
        clientInstance.setGitUrl(GitUtil.getGitRepositoryUrl(IdeUtil.findCurrentProject()));
        clientInstance.setProjectName(IdeUtil.getProjectName(IdeUtil.findCurrentProject()));
        return clientInstance;
    }

    // 回答反馈
    private static final String feedbackAnswerPath = "api/aebackend/chat-history-admin/v1/feedback-answer";

    /**
     * 回答反馈
     *
     * @return
     */
    public static FeedbackAnswerResponse feedback(final String dialogId, final String reqId, final String feedback) {
        FeedbackAnswerResponse response = new FeedbackAnswerResponse();
        response.setCode(RtnCode.OFFLINE);
        response.setMsg("网络条件异常，请稍后重试.");
        if (!LocalStorageUtil.checkNetCondition()) {
            logger.warn("[cf] feedback skip, net condition fail.");
            return response;
        }

        final String baseUrl = ConfigWrapper.getServerUrl() + feedbackAnswerPath;
        final JSONObject client = new JSONObject();
        client.put("pluginVersion", getClientInstance().getPluginVersion());
        client.put("type", getClientInstance().getType());
        client.put("version", getClientInstance().getVersion());
        client.put("gitUrl", getClientInstance().getGitUrl());
        client.put("projectName", getClientInstance().getProjectName());
        JSONObject params = new JSONObject();
        params.put("type", 0);
        params.put("dialogId", dialogId);
        params.put("reqId", reqId);
        params.put("feedback", feedback);
        params.put("client", client);

        final HashMap<String, String> headers = generateAuthHeaders();
        final ApiResponse apiResponse = HttpClient.doPost(baseUrl, params.toString(), headers);

        //if (RtnCode.USER_FORBIDDEN == apiResponse.getRtnCode()) {
        //    ApplicationManager.getApplication().invokeLater(() -> {
        //        MessageDialogUtil.showUserBanDialog();
        //    });
        //}

        if (RtnCode.SUCCESS == apiResponse.getRtnCode()) {
            response.setCode(RtnCode.SUCCESS);
            response.setData(new FeedbackAnswerData(reqId, feedback));
        } else {
            response.setCode(apiResponse.getRtnCode());
            response.setMsg(apiResponse.getMessage());
        }
        return response;
    }


    /**
     * 生成网关层鉴权头域字段
     */
    private static HashMap<String, String> generateAuthHeaders() {
        HashMap<String, String> headers = Maps.newHashMapWithExpectedSize(4);
        headers.put("apiKey", LocalStorageUtil.getApikey());
        headers.put("userid", LocalStorageUtil.getUserId());
        headers.put("invokerId", LocalStorageUtil.getUserId());
        headers.put("x-dup-id", String.valueOf(TimeUtil.getNowTimeSecTimestamp() + "-" + MyRandomUtil.generateRandomString(8)));
        return headers;
    }
}
