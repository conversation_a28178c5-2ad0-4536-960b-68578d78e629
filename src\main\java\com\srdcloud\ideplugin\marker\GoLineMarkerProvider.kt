package com.srdcloud.ideplugin.marker


import com.intellij.psi.PsiElement

class GoLineMarkerProvider : BaseLineMarkerProvider() {
    companion object {
        private const val ALLOW_LANGUAGE = "Go"
        val ALLOW_LANGUAGE_SUFFIX = listOf("go")
        val ALLOW_IDE = listOf("GoLand", "IntelliJ IDEA")
    }

    override fun isApplicable(element: PsiElement): <PERSON>olean {
        val fileType = element.containingFile.fileType.name
        return ALLOW_IDE.contains(getIdeType()) && ALLOW_LANGUAGE == fileType &&
                (element.node.elementType.toString().equals("FUNCTION_DECLARATION", ignoreCase = true) ||
                 element.node.elementType.toString().equals("METHOD_DECLARATION", ignoreCase = true)) &&
                element.parent?.node?.elementType.toString().equals("GO_FILE", ignoreCase = true)
    }

    override fun filterFunction(element: PsiElement, bean: LineMarkerFunctionBean) {
        bean.apply {
            isUtValid = isValidForUnitTest(element)
            isAnnotateValid = true
            isCodeExplain = true
            isOptimization = true

            val elementInfo = LineMarkerFunctionBean.getFunctionLinesCount(element)
            elementInfo?.let {
                val lineCount = it.endLine - it.startLine
                val charCount = it.endOffset - it.startOffset
                isCodeSplit = lineCount >= 20 && charCount <= 4500
                isLineAnotateValid = charCount <= 4500
            }
        }
    }

    private fun isValidForUnitTest(element: PsiElement): Boolean {
        // Implement Go-specific unit test validation logic
        return !SmartUTUtil.isGoTestFile(element)
    }

    private fun getIdeType(): String {
        // Implement IDE type detection logic
        return "GoLand"
    }
}
