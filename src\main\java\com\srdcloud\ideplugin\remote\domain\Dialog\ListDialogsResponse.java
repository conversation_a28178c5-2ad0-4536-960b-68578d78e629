package com.srdcloud.ideplugin.remote.domain.Dialog;


import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.DialogBaseInfo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> yangy
 * @create 2024/5/13 11:25
 */
public class ListDialogsResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 消息状态值，0表示操作成功，其他值表示失败
     */
    private int optResult;

    /**
     * 操作失败说明
     */
    private String msg;

    /**
     * 当前查询的页数（第几页）
     */
    private int pageNo;

    /**
     * 总的记录条数（以便前端计算分页总数）
     */
    private int totalDataCount;

    /**
     * 对话信息对象列表
     */
    private List<DialogBaseInfo> dialogs;

    public int getOptResult() {
        return optResult;
    }

    public void setOptResult(int optResult) {
        this.optResult = optResult;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getTotalDataCount() {
        return totalDataCount;
    }

    public void setTotalDataCount(int totalDataCount) {
        this.totalDataCount = totalDataCount;
    }

    public List<DialogBaseInfo> getDialogs() {
        return dialogs;
    }

    public void setDialogs(List<DialogBaseInfo> dialogs) {
        this.dialogs = dialogs;
    }
}
