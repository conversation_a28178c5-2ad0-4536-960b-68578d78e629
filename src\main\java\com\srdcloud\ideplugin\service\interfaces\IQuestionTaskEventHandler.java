package com.srdcloud.ideplugin.service.interfaces;

import com.srdcloud.ideplugin.service.domain.apigw.ApigwWebsocketRespPayload;

/**
 * 问答事件处理接口
 */
public interface IQuestionTaskEventHandler {

    /**
     * 收到回答
     */
    public void onAnswer(String regId, int isEnd, String accumulateAnswer, String segmentAnswer, int seqNo, final ApigwWebsocketRespPayload payload);

    /**
     * 任务异常
     */
    public void onTaskError(String regId, int eventId, final ApigwWebsocketRespPayload payload);
}
