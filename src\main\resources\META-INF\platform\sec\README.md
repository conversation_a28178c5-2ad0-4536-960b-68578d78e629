<!-- Plugin description start -->
# 海云智码

C10 是由 Secidea 海云安提供的 AI 工具，是一款使用 AI 能力协助用户进行代码开发的综合解决方案。该工具在 IDE 中实现了代码补全、代码生成、代码优化和编程助手等功能，并与 Secidea 海云安的各种能力集成，将整个研发流程带入到 IDE 环境中，旨在提升代码编写和开发效率。

## 主要功能 Features

### 1. 代码补全 (Code Completion)
- **自动补全**：在 IDE 输入代码时自动触发
- **手动补全**：按默认快捷键 `Ctrl + Enter` 手动触发
- **确认选择**：用 `Tab` 键选择建议项
- **禁用补全**：默认使用 `Ctrl + Shift + Alt + O` 禁用代码补全

### 2. 开发问答 (Code Q&A)
- 快捷键 `Alt + Shift + K` 打开编程助手页面
- 输入问题并回车发送
- AI 生成答案，支持复制代码部分
- 支持查看历史会话、新建会话、指令模板提问、重新回答等

### 3. 代码解释 (Code Explanation)
- 选中代码后右键 → “Secidea Codefree” → “代码解释”
- 在对话框中查看 AI 解析结果

### 4. 代码注释 (Code Annotation)
- 选中代码后右键 → “Secidea Codefree” → “代码注释”
- 自动生成函数注释及行间注释

### 5. 生成单元测试 (Generate Unit Tests)
- 选中代码后右键 → “Secidea Codefree” → “生成单元测试”
- 可一键生成新文件

### 6. 代码优化 (Code Optimization)
- 选中代码后右键 → “Secidea Codefree” → “生成优化建议”
- 获取提升代码质量的建议

### 7. 异常报错解释 (Explanation of Exception Error)
- 在控制台报错处点击 "CodeFree" 按钮
- 查看错误分析与修复建议

### 8. 知识库提问 (Knowledge Base Inquiry)
- 选择 Secidea 海云安上的知识库进行提问
- 若涉及 API 文档，可直接生成调用代码

### 9. 智能编程 (AI Developer)
- 发送需求后查看多个项目文件的修改
- 支持跨文件协作与多文件变更
- 可审查并接受/撤销每个文件的修改
<!-- Plugin description end -->




