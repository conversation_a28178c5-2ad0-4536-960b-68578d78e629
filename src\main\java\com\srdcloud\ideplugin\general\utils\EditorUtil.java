package com.srdcloud.ideplugin.general.utils;

import com.intellij.openapi.command.WriteCommandAction;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.SelectionModel;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.VirtualFile;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.general.enums.Language;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2025/1/7
 * @desc 编辑器操作封装
 */
public class EditorUtil {
    public EditorUtil() {
    }

    /**
     * 获取当前编辑器实例
     */
    public static Editor getCurrentEditor(final Project project) {
        // 获取当前编辑器
        Editor documentEditor = FileEditorManager.getInstance(project).getSelectedTextEditor();
        if (documentEditor == null) {
            MessageBalloonNotificationUtil.showBalloonNotificationByReason(project, "请先打开编辑文件", RtnCode.RtnCode_Not_Select_Editor);
            return null;
        }
        return documentEditor;
    }

    /**
     * 获取当前编辑器中的选中代码块
     */
    public static String getCurrentSelectedCode(final Project project) {
        final Editor currentEditor = getCurrentEditor(project);
        if (currentEditor == null) {
            return null;
        }
        final Document currentDocument = currentEditor.getDocument();
        VirtualFile virtualFile = FileDocumentManager.getInstance().getFile(currentDocument);
        if (virtualFile == null) {
            return null;
        }

        // 获取选中的文本
        String data = currentEditor.getSelectionModel().getSelectedText();
        if (StringUtils.isBlank(data)) {
            return null;
        }

        //todo：获取选中的起始和结束位置
        int startOffset = currentEditor.getSelectionModel().getSelectionStart();
        int endOffset = currentEditor.getSelectionModel().getSelectionEnd();
        // 将偏移量转换为行号
        int startLine = currentDocument.getLineNumber(startOffset) + 1;
        int endLine = currentDocument.getLineNumber(endOffset) + 1;

        //todo：获取编辑器选中代码所在的文件
        String filePath = virtualFile.getPath();
        String fileName = virtualFile.getName();

        // 基于选中内容，拼接md格式代码块
        String extName = Language.Companion.detectLanguageName(virtualFile.getName());
        if (!extName.isEmpty() && !Constants.UNKNOWN.equals(extName)) {
            data = "```" + extName.toLowerCase() + "\n" + data + "\n```";
        } else {
            data = "```code\n" + data + "\n```";
        }
        return data;
    }

    public static Integer[] getCurrentSelectedFileLineNumber(final Project project) {
        final Editor currentEditor = getCurrentEditor(project);
        if (currentEditor == null) {
            return null;
        }

        final Document currentDocument = currentEditor.getDocument();

        int startOffset = currentEditor.getSelectionModel().getSelectionStart();
        int startLine = currentDocument.getLineNumber(startOffset) ;
        int endOffset = currentEditor.getSelectionModel().getSelectionEnd();
        int endLine = currentDocument.getLineNumber(endOffset);

        // 显示行号需要在次基础上 + 1，该行号只是记录行号索引
        return new Integer[] {startLine, endLine};
    }

    /**
     * 获取当前编辑器中的选中代码块的文件路径
     */
    public static String getCurrentSelectedFilePath(final Project project) {
        final Editor currentEditor = getCurrentEditor(project);
        if (currentEditor == null) {
            return null;
        }

        final Document currentDocument = currentEditor.getDocument();
        VirtualFile virtualFile = FileDocumentManager.getInstance().getFile(currentDocument);
        if (virtualFile == null) {
            return null;
        }

        return virtualFile.getPath();
    }

    /**
     * 往编辑器光标位置插入代码
     */
    public static void insertCodeToEditor(final Project project, String code) {
        WriteCommandAction.runWriteCommandAction(project, () -> {
            final Editor currentEditor = getCurrentEditor(project);
            if (currentEditor == null) {
                return;
            }
            int selectionStart = currentEditor.getSelectionModel().getSelectionStart();
            int selectionEnd = currentEditor.getSelectionModel().getSelectionEnd();
            boolean hasSelection = selectionStart > 0 && selectionStart != selectionEnd;
            if (hasSelection) {
                currentEditor.getDocument().replaceString(selectionStart, selectionEnd, code);
            } else {
                int caretOffset = currentEditor.getCaretModel().getOffset();
                currentEditor.getDocument().insertString(caretOffset, code);
            }
        });
    }

    /**
     * 消除光标选中
     */
    public static void clearSelection(final Project project) {
        WriteCommandAction.runWriteCommandAction(project, () -> {
            final Editor currentEditor = FileEditorManager.getInstance(project).getSelectedTextEditor();
            if (currentEditor == null) {
                return;
            }
            SelectionModel selectionModel = currentEditor.getSelectionModel();
            if (selectionModel != null) {
                selectionModel.removeSelection();
            }
        });
    }
}

