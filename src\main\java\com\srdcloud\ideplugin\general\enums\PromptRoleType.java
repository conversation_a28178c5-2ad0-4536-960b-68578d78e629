package com.srdcloud.ideplugin.general.enums;

/**
 * <AUTHOR>
 * @date 2024/4/10
 * @description 提问角色
 */
public enum PromptRoleType {
    SYSTEM("system","系统角色，决定本轮对话的主题"),
    USER("user","用户角色，代表本条内容是用户的提问或回答"),
    ASSISTANT("assistant","助理角色，代表本条内容是AI的回答");


    private final String type;

    private final String desc;
    PromptRoleType(String type,String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
