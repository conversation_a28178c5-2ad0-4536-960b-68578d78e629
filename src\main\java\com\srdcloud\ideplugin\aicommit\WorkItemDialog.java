package com.srdcloud.ideplugin.aicommit;

import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.DialogWrapper;
import com.intellij.openapi.vcs.CommitMessageI;
import com.srdcloud.ideplugin.webview.workitem.WorkItemWebview;
import org.jetbrains.annotations.Nullable;

import javax.swing.*;
import java.awt.*;

/**
 * 自定义对话框
 */
public class WorkItemDialog extends DialogWrapper {

    // 对话框面板
    private final WorkItemWebview webview;

    WorkItemDialog(@Nullable Project project) {
        super(project);
        setTitle("关联工作项");
        webview = WorkItemWebview.getInstance(project);
        init();
    }

    /**
     * 完全移除默认按钮,webview内按钮来控制对话框关闭
     *
     * @return
     */
    @Override
    protected Action[] createActions() {
        return new Action[0];
    }

    /**
     * 创建对话框内容面板
     *
     * @return
     */
    @Nullable
    @Override
    protected JComponent createCenterPanel() {
        if (!webview.isLoaded()) {
            webview.loadWebview();
        }

        // 设置父dialog
        webview.setParentDialog(this);

        // 展示webview界面
        JComponent webViewComponent = webview.getWebViewComponent();
        webViewComponent.setPreferredSize(new Dimension(600, 600));
        return webViewComponent;
    }


    public WorkItemWebview getWebview() {
        return webview;
    }
}
