package com.srdcloud.ideplugin.general.constants;

/**
 * <AUTHOR>
 * @date 2024/4/25
 * @desc Websocket通信业务消息
 */
public class MessageNameConstant {
    // websocket通道注册消息
    public static final String MessageName_RegisterChannel = "RegisterChannel";
    public static final String MessageName_RegisterChannelResult = "RegisterChannelResult";
    public static final String MessageName_RegisterChannelResult_APIGW = "RegisterChannel_resp";

    // apiKey查询消息
    public static final String MessageName_GetUserApiKey = "GetUserApiKey";
    public static final String MessageName_GetUserApiKeyResp = "GetUserApiKey_resp";

    // websocket心跳检测消息
    public static final String MessageName_ServerHeartbeat = "ServerHeartbeat";
    public static final String MessageName_ServerHeartbeatResponse = "ServerHeartbeatResponse";

    // 代码补全消息
    public static final String MessageName_CodeGenRequest = "CodeGenRequest";
    public static final String MessageName_CodeGenResponse = "CodeGenResponse";
    public static final String MessageName_CodeGenResponse_APIGW = "CodeGenRequest_resp";

    // 开发问答通信消息
    public static final String MessageName_CodeChatRequest = "CodeChatRequest";
    public static final String MessageName_CodeChatResponse = "CodeChatResponse";
    public static final String MessageName_CodeChatResponse_APIGW = "CodeChatRequest_resp";
    public static final String MessageName_CodeCancelChatRequest = "CancelCodeChatReq";

    // 用户行为埋点上报消息
    public static final String MessageName_UserActivityNotify = "UserActivityNotify";
    public static final String MessageName_UserActivityNotify_resp = "UserActivityNotify_resp";

    // commit问答消息
    public static final String MessageName_CommitChatRequest = "CommitChatRequest";
    public static final String MessageName_CommitChatResponse = "CommitChatResponse";
    public static final String MessageName_CommitChatResponse_APIGW = "CommitChatRequest_resp";

}
