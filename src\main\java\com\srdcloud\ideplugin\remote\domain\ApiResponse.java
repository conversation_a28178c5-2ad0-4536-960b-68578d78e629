package com.srdcloud.ideplugin.remote.domain;

/**
 * API网关接口通信通用返回格式
 */
public class ApiResponse {

    /**
     * 响应码【客户端set入，标识请求是否发送成功】
     */
    private int rtnCode;

    /**
     * 返回数据或异常描述【后端完整响应体，业务侧按需decode】
     */
    private String message;

    public ApiResponse(int rtnCode, String message) {
        this.rtnCode = rtnCode;
        this.message = message;
    }

    public int getRtnCode() {
        return rtnCode;
    }

    public void setRtnCode(int rtnCode) {
        this.rtnCode = rtnCode;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return "SendMessageRtn{" +
                "rtnCode=" + rtnCode +
                ", message='" + message + '\'' +
                '}';
    }
}
