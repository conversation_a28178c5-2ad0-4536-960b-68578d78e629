package com.srdcloud.ideplugin.diff

import com.intellij.diff.DiffRequestFactory
import com.intellij.diff.chains.SimpleDiffRequestChain
import com.intellij.diff.editor.ChainDiffVirtualFile
import com.intellij.diff.requests.SimpleDiffRequest
import com.intellij.ide.actions.CreateFileAction
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.command.CommandProcessor
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.io.FileUtil
import com.intellij.openapi.vfs.LocalFileSystem
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiManager
import com.intellij.testFramework.LightVirtualFile
import com.intellij.util.SlowOperations
import com.intellij.util.ThrowableRunnable
import com.srdcloud.ideplugin.composer.ComposerUIAdapter
import com.srdcloud.ideplugin.composer.DefaultComposerUIAdapter
import com.srdcloud.ideplugin.diff.diffreport.DiffEditorCreateListener.Companion.DIFF_FILE_KEY
import com.srdcloud.ideplugin.diff.diffreport.DiffEditorCreateListener.Companion.DIFF_KEY
import com.srdcloud.ideplugin.general.utils.DebugLogUtil
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCommand
import com.srdcloud.ideplugin.webview.codechat.composer.response.DiffStatusChangedResponse
import com.srdcloud.ideplugin.webview.codechat.composer.response.DiffStatusChangedResponseData
import org.apache.commons.lang.StringUtils
import java.io.File
import java.io.IOException
import java.util.concurrent.ConcurrentHashMap

/**
 * 收到diff消息后，负责处理：
 * 1、为上层提供服务，负责diff文件的管理
 * 2、启动diff窗口
 */

class DiffService(private val project: Project, message: DiffMessage) {
    companion object {
        const val STATUS_ACCEPTED = "accepted"
        const val STATUS_UNDECIDED = "undecided"
        const val STATUS_REJECTED = "rejected"
        const val STATUS_PARTIAL_ACCEPTED = "partial_accepted"

        // 静态成员变量用于保存DiffService的实例
        private val instances = ConcurrentHashMap<Project, DiffService>()

        // 静态方法用于设置实例
        @JvmStatic
        fun setInstance(project: Project, diffService: DiffService) {
            instances[project] = diffService
        }

        // 静态方法用于获取实例
        @JvmStatic
        fun getInstance(project: Project): DiffService? {
            return instances[project]
        }
    }

    private val logger = Logger.getInstance(this::class.java)
    private lateinit var fileActions: Map<VirtualFile, Pair<() -> Unit, () -> Unit>>
    private lateinit var diffRequests: Map<VirtualFile, SimpleDiffRequest>
    private val fileStatusMap = mutableMapOf<VirtualFile, String>()
    private val openedDiffFiles = mutableMapOf<String, VirtualFile>()
    private var diffFiles = arrayOf<DiffFile>()
    private val uiAdapter: ComposerUIAdapter = DefaultComposerUIAdapter(project)

    // 保存原始diff数据和新文件标记，用于重建被删除的新文件
    private val diffMsgDataMap = mutableMapOf<String, DiffMsgData>()
    private val isNewFileMap = mutableMapOf<String, Boolean>()

    //底层收到diff消息后创建本实例并初始化，然后把该实例回调给上层
    init {
        try {
            logger.info("DiffService init")

            val storePath = "${System.getProperty("user.home")}/.codefree/plugin/composer/"  //zhangwei6
            // 确保存储目录存在
            val storeDir = File(storePath)
            if (!storeDir.exists()) {
                storeDir.mkdirs()
            }

            // 创建文件操作映射
            val fileActions = mutableMapOf<VirtualFile, Pair<() -> Unit, () -> Unit>>()
            val diffRequests = mutableListOf<Pair<VirtualFile, SimpleDiffRequest>>()

            // 遍历所有文件写入操作
            message.data.forEach { diffData ->
                // 获取或创建文件
                val file: File

                /**
                 * 将windows的\统一转换为/
                 */
                val convertedPath = diffData.path.replace("\\", "/")
                file = if (convertedPath.contains(project.basePath!!)) {
                    File(convertedPath)
                } else {
                    File(project.basePath + "/" + convertedPath)
                }

                if (file.path.indexOf('\u0000') >= 0 || file.path.count { it == ':' } > 1) {
                    logger.warn("diff文件路径无效，原始路径为" + diffData.path)
                    logger.warn("项目路径为" + project.basePath)
                    return@forEach
                }

                val isNewFile = !file.exists()
                val filePath = file.canonicalPath.replace("\\", "/")

                // 保存原始数据和新文件标记
                diffMsgDataMap[filePath] = diffData
                isNewFileMap[filePath] = isNewFile

                // 获取VirtualFile，对于新文件，我们直接使用临时文件进行比较
                val virtualFile = if (!isNewFile) {
                    LocalFileSystem.getInstance().refreshAndFindFileByIoFile(file)
                        ?: throw IOException("Cannot find virtual file for ${diffData.path}")
                } else {
                    // 为新文件创建一个空的临时文件用于比较
                    val tempFile = File(project.basePath + "/", convertedPath)
                    FileUtil.writeToFile(tempFile, "")
                    LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempFile)
                        ?: throw IOException("Cannot create temporary file for comparison")
                }

                // 在写命令中执行文件内容更新
                WriteCommandAction.runWriteCommandAction(project) {
                    virtualFile.refresh(false, false)

                    // 在storePath目录下创建备份文件
                    val backupFileName =
                        "${virtualFile.nameWithoutExtension}_backup_${System.currentTimeMillis()}${virtualFile.extension?.let { ".$it" } ?: ""}"
                    val backupFile = File(storeDir, backupFileName)
                    // 保存原始内容用于diff
                    val originalContent = if (!isNewFile) String(virtualFile.contentsToByteArray()) else ""
                    FileUtil.writeToFile(backupFile, originalContent)

                    // 创建修改后的文件
                    val modifiedFileName =
                        "${virtualFile.nameWithoutExtension}_modified_${System.currentTimeMillis()}${virtualFile.extension?.let { ".$it" } ?: ""}"
                    val modifiedFile = File(storeDir, modifiedFileName)
                    FileUtil.writeToFile(modifiedFile, diffData.afterContent)
                    val modifiedVirtualFile = if (!isNewFile) {
                        LocalFileSystem.getInstance().refreshAndFindFileByIoFile(modifiedFile)
                            ?: throw IOException("Cannot create virtual file for modified content")
                    } else {
                        // 对于新文件，使用LightVirtualFile并设置正确的路径
                        LightVirtualFile(
                            file.path, diffData.afterContent
                        )
                    }
                    // 创建diff请求
                    val diffRequest = DiffRequestFactory.getInstance().createFromFiles(
                        project, if (isNewFile) null else virtualFile, modifiedVirtualFile
                    ) as SimpleDiffRequest

                    // 将diff请求添加到列表中
                    diffRequests.add(Pair(virtualFile, diffRequest))

                    // 添加文件操作
                    fileActions[virtualFile] = Pair(
                        // 接受修改 - 将修改后的内容写入原始文件
                        {
                            if (!isNewFile) {
                                val modifiedContent = String(modifiedVirtualFile.contentsToByteArray())
                                WriteCommandAction.runWriteCommandAction(project) {
                                    val document = FileDocumentManager.getInstance().getDocument(virtualFile)
                                    if (document != null && !modifiedContent.contains("\r\n")) {
                                        document.setText(modifiedContent)
                                    } else {
                                        virtualFile.setBinaryContent(modifiedContent.toByteArray())
                                    }
                                }
                            } else {
                                // 对于新文件，使用IDEA标准的文件创建方式
                                WriteCommandAction.runWriteCommandAction(project) {
                                    // 创建文件并写入内容
                                    CommandProcessor.getInstance().executeCommand(project, {
                                        // 使用CreateFileAction的MkDirs来处理目录创建
                                        // 获取根目录
                                        val psiManager = PsiManager.getInstance(project)
                                        val rootDir = psiManager.findDirectory(
                                            LocalFileSystem.getInstance().findFileByPath(project.basePath!!)!!
                                        )
                                        val mkDirs = CreateFileAction.MkDirs(
                                            file.canonicalPath.replace("\\", "/").replace(project.basePath!!, ""),
                                            rootDir!!
                                        )
                                        val newFile = mkDirs.directory.findFile(mkDirs.newName)?: mkDirs.directory.createFile(mkDirs.newName)
                                        if (!diffData.afterContent.contains("\r\n") &&
                                            FileDocumentManager.getInstance().getDocument(newFile.virtualFile) != null
                                        ) {
                                            FileDocumentManager.getInstance().getDocument(newFile.virtualFile)
                                                ?.setText(diffData.afterContent)
                                        } else {
                                            newFile.virtualFile.setBinaryContent(diffData.afterContent.toByteArray())
                                        }
                                    }, "Create New File", null)
                                }
                            }
                        },
                        // 拒绝修改 - 对于新文件删除已创建的文件，对于现有文件保持不变
                        {
                            if (!isNewFile) {
                                WriteCommandAction.runWriteCommandAction(project) {
                                    val document = FileDocumentManager.getInstance().getDocument(virtualFile)
                                    if (document != null && !originalContent.contains("\r\n")) {
                                        document.setText(originalContent)
                                    } else {
                                        virtualFile.setBinaryContent(originalContent.toByteArray())
                                    }
                                }
                            } else {
                                // 对于新文件，使用PSI系统删除
                                if (file.exists()) {
                                    val newVirtualFile =
                                        LocalFileSystem.getInstance().refreshAndFindFileByIoFile(file)
                                    if (newVirtualFile != null) {
                                        CommandProcessor.getInstance().executeCommand(project, {
                                            WriteCommandAction.runWriteCommandAction(project) {
                                                PsiManager.getInstance(project).findFile(newVirtualFile)?.delete()
                                            }
                                        }, "Delete File", null)
                                    }
                                    FileUtil.delete(file)
                                }
                            }
                        })
                    val diffFile = DiffFile(virtualFile.path, virtualFile.name, diffData.afterContent, diffData.beforeContent, STATUS_UNDECIDED)
                    DebugLogUtil.info("diff file:  $diffFile")
                    diffFiles += diffFile

                    // 添加用户数据
                    diffRequest.putUserData(DIFF_KEY, "composer")
                    diffRequest.putUserData(DIFF_FILE_KEY, diffFile)
                }
            }

            // 将实例设置到全局变量中，供其他地方调用
            this.fileActions = fileActions
            this.diffRequests = diffRequests.associate { it.first to it.second}

        } catch (e: Exception) {
            logger.warn("处理diff文件响应错误: ${e.message}")
        }

        // 初始化所有文件状态为undecided
        fileActions.keys.forEach { file ->
            fileStatusMap[file] = STATUS_UNDECIDED
        }
    }

    //1、获取所有文件
    fun getFiles(): Array<DiffFile> {
        return diffFiles
    }

    //2、"接受"
    fun acceptFile(path: String) {
        val file: VirtualFile? = LocalFileSystem.getInstance().findFileByPath(path)
        ApplicationManager.getApplication().invokeLater {
            WriteCommandAction.runWriteCommandAction(project) {
                fileActions[file]?.first?.invoke()
            }
            if (file != null) {
                fileStatusMap[file] = STATUS_ACCEPTED
            }
            onAcceptOrRejectFile(path, file, STATUS_ACCEPTED)
        }
    }

    //3、"拒绝"
    fun rejectFile(path: String) {
        val file: VirtualFile? = LocalFileSystem.getInstance().findFileByPath(path)
        ApplicationManager.getApplication().invokeLater {
            WriteCommandAction.runWriteCommandAction(project) {
                fileActions[file]?.second?.invoke()
            }

            // 对于新文件，执行删除操作后，需要从所有Map中移除失效的VirtualFile key
            if (isNewFileMap[path] == true && file != null) {
                // 创建新的Map，移除失效的key
                fileActions = fileActions - file
                diffRequests = diffRequests - file
                fileStatusMap.remove(file)
            } else if (file != null) {
                fileStatusMap[file] = STATUS_REJECTED
            }
            onAcceptOrRejectFile(path, file, STATUS_REJECTED)
        }
    }

    // 撤回操作
    fun undoFile(path: String) {
        val file: VirtualFile? = LocalFileSystem.getInstance().findFileByPath(path)

        // 检查当前状态
        val currentStatus = if (file != null) {
            fileStatusMap[file]
        } else {
            // 文件不存在，可能是被拒绝的新文件，从diffFiles中查找状态
            diffFiles.find { it.path == path }?.status
        }
        if (currentStatus == null || isNewFileMap[path] == null) {
            logger.error("[cf]In undoFile, current status is null or not in isNewFileMap, skip undo file: $path")
            return
        }

        // 根据当前状态 以及 是否为新文件，修改文件内容
        if (currentStatus == STATUS_ACCEPTED) {
            // 如果之前状态是接受，文件内容恢复到接受前
            if (isNewFileMap[path] == true){
                file?.let { virtualFile ->
                    ApplicationManager.getApplication().invokeLater {
                        val document = FileDocumentManager.getInstance().getDocument(virtualFile)
                        WriteCommandAction.runWriteCommandAction(project) {
                            if (document != null && diffMsgDataMap[path]?.afterContent?.contains("\r\n") == false) {
                                document.setText(StringUtils.EMPTY)
                            } else {
                                virtualFile.setBinaryContent(StringUtils.EMPTY.toByteArray())
                            }
                        }
                    }
                }
            } else if (isNewFileMap[path] == false) {
                ApplicationManager.getApplication().invokeLater {
                    WriteCommandAction.runWriteCommandAction(project) {
                        fileActions[file]?.second?.invoke()
                    }
                }
            }
        } else if (currentStatus == STATUS_REJECTED) {
            // 如果之前状态是拒绝
            if (isNewFileMap[path] == true) {
                // 对于被拒绝的新文件，需要重建整个diff环境
                ApplicationManager.getApplication().executeOnPooledThread {
                    recreateNewFileDiffEnvironment(path) {
                        onUndoFile(path, file)
                    }
                }
                return
            }
            // else-对于已有文件，之前是拒绝的，文件内容没发生改变，不需要对文件内容做任何操作
        }
        onUndoFile(path, file)
    }

    //4、打开diff窗口
    fun openFile(path: String) {
        // 同一path，如果已有对应的diff窗口，则直接打开
        openedDiffFiles[path]?.let {
            ApplicationManager.getApplication().invokeLater {
                SlowOperations.allowSlowOperations (ThrowableRunnable<Nothing> {
                    FileEditorManager.getInstance(project).openFile(it, false)
                })
            }
            return
        }

        val file: VirtualFile? = LocalFileSystem.getInstance().findFileByPath(path)
        ApplicationManager.getApplication().invokeLater {
            diffRequests[file]?.let { diffRequest ->
                val simpleDiffRequestChain = SimpleDiffRequestChain(diffRequest)
                val diffFile = ChainDiffVirtualFile(simpleDiffRequestChain, path)
                openedDiffFiles[path] = diffFile
                SlowOperations.allowSlowOperations (ThrowableRunnable<Nothing> {
                    FileEditorManager.getInstance(project).openFile(diffFile, false)
                })
            }
        }
    }

    fun onAcceptFile(path: String, file: VirtualFile?){
        if (file != null) {
            fileStatusMap[file] = STATUS_ACCEPTED
        }
        onAcceptOrRejectFile(path, file, STATUS_ACCEPTED)
    }

    private fun onAcceptOrRejectFile(path: String, file: VirtualFile?, status: String) {
        // 文件打开操作必须在EDT线程执行，同时需要允许慢操作
        SlowOperations.allowSlowOperations (ThrowableRunnable<Nothing> {
            // 打开源文件。对于新生成文件的拒绝，此时file状态为invalid，打开会报错，不应打开
            if (file != null && file.isValid) {
                FileEditorManager.getInstance(project).openFile(file, false)
            }
            // 关闭diff窗口
            openedDiffFiles[path]?.let {
                FileEditorManager.getInstance(project).closeFile(it)
            }
        })
        // 回传diff-status-changed消息
        diffFiles.filter { it.path == path }.forEach { it.status = status }
        sendDiffStatusChangedResponse(diffFiles)
    }

    fun onUndoFile(path: String, file: VirtualFile?) {
        // 修改fileStatusMap和diffFiles的状态
        file?.let { fileStatusMap[it] = STATUS_UNDECIDED }
        diffFiles.filter { it.path == path }.forEach { it.status = STATUS_UNDECIDED }
        // 唤起diff窗口
        openFile(path)
        // 回传webview消息
        sendDiffStatusChangedResponse(diffFiles)
    }

    // 部分采纳
    // 分块全部完成操作，且部分采纳、部分拒绝才为PARTIAL_ACCEPTED，暂无该状态，预留
    fun onPartialAccepted(path: String) {
        val file: VirtualFile? = LocalFileSystem.getInstance().findFileByPath(path)
        file?.let { fileStatusMap[it] = STATUS_PARTIAL_ACCEPTED }
        diffFiles.filter { it.path == path }.forEach { it.status = STATUS_PARTIAL_ACCEPTED }
        sendDiffStatusChangedResponse(diffFiles)
    }

    //获取文件状态
    private fun getFileStatus(file: VirtualFile): String? {
        return fileStatusMap[file]
    }

    //"接受全部"
    private fun acceptAllFiles() {
        diffFiles.forEach { acceptFile(it.path) }
    }

    //"撤回全部"
    private fun rejectAllFiles() {
        diffFiles.forEach { rejectFile(it.path) }
    }

    //打开所有文件
    private fun showAllFiles() {
        diffFiles.forEach { openFile(it.path) }
    }

    //更新所有文件状态
    private fun updateAllStatus(status: String) {
        fileStatusMap.keys.forEach { file ->
            fileStatusMap[file] = status
        }
        diffFiles.forEach { it.status = status }
    }

    /**
     * 重建被删除新文件的diff环境
     */
    private fun recreateNewFileDiffEnvironment(path: String, onComplete: (() -> Unit)? = null) {
        val diffData = diffMsgDataMap[path] ?: return

        try {
            WriteCommandAction.runWriteCommandAction(project) {
                val convertedPath = diffData.path.replace("\\", "/")
                val file = if (convertedPath.contains(project.basePath!!)) {
                    File(convertedPath)
                } else {
                    File(project.basePath + "/" + convertedPath)
                }

                // 1. 重建virtualFile (临时空文件)
                val tempFile = File(project.basePath + "/", convertedPath)
                FileUtil.writeToFile(tempFile, "")
                val virtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempFile)
                    ?: throw IOException("Cannot recreate temporary file for comparison")

                // 2. 重建modifiedVirtualFile
                val modifiedVirtualFile = LightVirtualFile(file.path, diffData.afterContent)

                // 3. 重建diffRequest
                val diffRequest = DiffRequestFactory.getInstance().createFromFiles(
                    project, null, modifiedVirtualFile
                ) as SimpleDiffRequest

                // 4. 重建fileActions
                val newFileActions = Pair(
                    // 接受修改 - 创建新文件
                    {
                        WriteCommandAction.runWriteCommandAction(project) {
                            CommandProcessor.getInstance().executeCommand(project, {
                                if (!file.exists()) {
                                    FileUtil.writeToFile(file, "")
                                }
                                val psiManager = PsiManager.getInstance(project)
                                val rootDir = psiManager.findDirectory(
                                    LocalFileSystem.getInstance().findFileByPath(project.basePath!!)!!
                                )
                                val mkDirs = CreateFileAction.MkDirs(
                                    file.canonicalPath.replace("\\", "/").replace(project.basePath!!, ""),
                                    rootDir!!
                                )
                                val newFile = mkDirs.directory.findFile(mkDirs.newName) ?: mkDirs.directory.createFile(mkDirs.newName)
                                if (!diffData.afterContent.contains("\r\n") &&
                                    FileDocumentManager.getInstance().getDocument(newFile.virtualFile) != null
                                ) {
                                    FileDocumentManager.getInstance().getDocument(newFile.virtualFile)
                                        ?.setText(diffData.afterContent)
                                } else {
                                    newFile.virtualFile.setBinaryContent(diffData.afterContent.toByteArray())
                                }
                            }, "Create New File", null)
                        }
                    },
                    // 拒绝修改 - 删除临时文件
                    {
                        if (file.exists()) {
                            val newVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(file)
                            if (newVirtualFile != null) {
                                CommandProcessor.getInstance().executeCommand(project, {
                                    WriteCommandAction.runWriteCommandAction(project) {
                                        PsiManager.getInstance(project).findFile(newVirtualFile)?.delete()
                                    }
                                }, "Delete File", null)
                            }
                            FileUtil.delete(file)
                        }
                    }
                )

                // 5. 重新添加到所有Map中
                fileActions = fileActions + (virtualFile to newFileActions)
                diffRequests = diffRequests + (virtualFile to diffRequest)
                fileStatusMap[virtualFile] = STATUS_UNDECIDED

                // 6. 添加用户数据
                val diffFile = diffFiles.find { it.path == path }
                diffRequest.putUserData(DIFF_KEY, "composer")
                diffRequest.putUserData(DIFF_FILE_KEY, diffFile)

                onComplete?.invoke()
            }
        } catch (e: Exception) {
            logger.error("[cf] Undo file failed, in recreateNewFileDiffEnvironment: ${e.message}")
            e.printStackTrace()
        }
    }

    private fun sendDiffStatusChangedResponse(changedFiles: Array<DiffFile>) {
        val uiComponent = uiAdapter.getAndActivateComponent(project)
        uiComponent?.sendDiffStatusChangedResponse(
            DiffStatusChangedResponse(
                WebViewRspCommand.DIFF_STATUS_CHANGED,
                DiffStatusChangedResponseData(changedFiles)
            )
        )
    }
}