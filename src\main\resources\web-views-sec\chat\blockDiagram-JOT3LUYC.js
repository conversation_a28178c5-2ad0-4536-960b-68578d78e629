import{_ as d,d as W,l as L,j as F,bk as ge,H as nt,bl as G,bg as Kt,bm as ue,u as at,bn as pe,aZ as fe,k as xe,v as ye,B as be,b3 as we,bo as me,bp as yt,e as Le,i as Ct}from"./index.js";import{c as Se}from"./clone.js";import{G as ve}from"./graph.js";import{c as Ee}from"./channel.js";import"./_baseUniq.js";var bt=function(){var e=d(function(C,y,g,f){for(g=g||{},f=C.length;f--;g[C[f]]=y);return g},"o"),t=[1,7],a=[1,13],i=[1,14],l=[1,15],s=[1,19],r=[1,16],n=[1,17],c=[1,18],p=[8,30],h=[8,21,28,29,30,31,32,40,44,47],x=[1,23],b=[1,24],m=[8,15,16,21,28,29,30,31,32,40,44,47],v=[8,15,16,21,27,28,29,30,31,32,40,44,47],T=[1,49],S={trace:d(function(){},"trace"),yy:{},symbols_:{error:2,spaceLines:3,SPACELINE:4,NL:5,separator:6,SPACE:7,EOF:8,start:9,BLOCK_DIAGRAM_KEY:10,document:11,stop:12,statement:13,link:14,LINK:15,START_LINK:16,LINK_LABEL:17,STR:18,nodeStatement:19,columnsStatement:20,SPACE_BLOCK:21,blockStatement:22,classDefStatement:23,cssClassStatement:24,styleStatement:25,node:26,SIZE:27,COLUMNS:28,"id-block":29,end:30,block:31,NODE_ID:32,nodeShapeNLabel:33,dirList:34,DIR:35,NODE_DSTART:36,NODE_DEND:37,BLOCK_ARROW_START:38,BLOCK_ARROW_END:39,classDef:40,CLASSDEF_ID:41,CLASSDEF_STYLEOPTS:42,DEFAULT:43,class:44,CLASSENTITY_IDS:45,STYLECLASS:46,style:47,STYLE_ENTITY_IDS:48,STYLE_DEFINITION_DATA:49,$accept:0,$end:1},terminals_:{2:"error",4:"SPACELINE",5:"NL",7:"SPACE",8:"EOF",10:"BLOCK_DIAGRAM_KEY",15:"LINK",16:"START_LINK",17:"LINK_LABEL",18:"STR",21:"SPACE_BLOCK",27:"SIZE",28:"COLUMNS",29:"id-block",30:"end",31:"block",32:"NODE_ID",35:"DIR",36:"NODE_DSTART",37:"NODE_DEND",38:"BLOCK_ARROW_START",39:"BLOCK_ARROW_END",40:"classDef",41:"CLASSDEF_ID",42:"CLASSDEF_STYLEOPTS",43:"DEFAULT",44:"class",45:"CLASSENTITY_IDS",46:"STYLECLASS",47:"style",48:"STYLE_ENTITY_IDS",49:"STYLE_DEFINITION_DATA"},productions_:[0,[3,1],[3,2],[3,2],[6,1],[6,1],[6,1],[9,3],[12,1],[12,1],[12,2],[12,2],[11,1],[11,2],[14,1],[14,4],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[19,3],[19,2],[19,1],[20,1],[22,4],[22,3],[26,1],[26,2],[34,1],[34,2],[33,3],[33,4],[23,3],[23,3],[24,3],[25,3]],performAction:d(function(y,g,f,w,D,o,N){var u=o.length-1;switch(D){case 4:w.getLogger().debug("Rule: separator (NL) ");break;case 5:w.getLogger().debug("Rule: separator (Space) ");break;case 6:w.getLogger().debug("Rule: separator (EOF) ");break;case 7:w.getLogger().debug("Rule: hierarchy: ",o[u-1]),w.setHierarchy(o[u-1]);break;case 8:w.getLogger().debug("Stop NL ");break;case 9:w.getLogger().debug("Stop EOF ");break;case 10:w.getLogger().debug("Stop NL2 ");break;case 11:w.getLogger().debug("Stop EOF2 ");break;case 12:w.getLogger().debug("Rule: statement: ",o[u]),typeof o[u].length=="number"?this.$=o[u]:this.$=[o[u]];break;case 13:w.getLogger().debug("Rule: statement #2: ",o[u-1]),this.$=[o[u-1]].concat(o[u]);break;case 14:w.getLogger().debug("Rule: link: ",o[u],y),this.$={edgeTypeStr:o[u],label:""};break;case 15:w.getLogger().debug("Rule: LABEL link: ",o[u-3],o[u-1],o[u]),this.$={edgeTypeStr:o[u],label:o[u-1]};break;case 18:const E=parseInt(o[u]),I=w.generateId();this.$={id:I,type:"space",label:"",width:E,children:[]};break;case 23:w.getLogger().debug("Rule: (nodeStatement link node) ",o[u-2],o[u-1],o[u]," typestr: ",o[u-1].edgeTypeStr);const _=w.edgeStrToEdgeData(o[u-1].edgeTypeStr);this.$=[{id:o[u-2].id,label:o[u-2].label,type:o[u-2].type,directions:o[u-2].directions},{id:o[u-2].id+"-"+o[u].id,start:o[u-2].id,end:o[u].id,label:o[u-1].label,type:"edge",directions:o[u].directions,arrowTypeEnd:_,arrowTypeStart:"arrow_open"},{id:o[u].id,label:o[u].label,type:w.typeStr2Type(o[u].typeStr),directions:o[u].directions}];break;case 24:w.getLogger().debug("Rule: nodeStatement (abc88 node size) ",o[u-1],o[u]),this.$={id:o[u-1].id,label:o[u-1].label,type:w.typeStr2Type(o[u-1].typeStr),directions:o[u-1].directions,widthInColumns:parseInt(o[u],10)};break;case 25:w.getLogger().debug("Rule: nodeStatement (node) ",o[u]),this.$={id:o[u].id,label:o[u].label,type:w.typeStr2Type(o[u].typeStr),directions:o[u].directions,widthInColumns:1};break;case 26:w.getLogger().debug("APA123",this?this:"na"),w.getLogger().debug("COLUMNS: ",o[u]),this.$={type:"column-setting",columns:o[u]==="auto"?-1:parseInt(o[u])};break;case 27:w.getLogger().debug("Rule: id-block statement : ",o[u-2],o[u-1]),w.generateId(),this.$={...o[u-2],type:"composite",children:o[u-1]};break;case 28:w.getLogger().debug("Rule: blockStatement : ",o[u-2],o[u-1],o[u]);const z=w.generateId();this.$={id:z,type:"composite",label:"",children:o[u-1]};break;case 29:w.getLogger().debug("Rule: node (NODE_ID separator): ",o[u]),this.$={id:o[u]};break;case 30:w.getLogger().debug("Rule: node (NODE_ID nodeShapeNLabel separator): ",o[u-1],o[u]),this.$={id:o[u-1],label:o[u].label,typeStr:o[u].typeStr,directions:o[u].directions};break;case 31:w.getLogger().debug("Rule: dirList: ",o[u]),this.$=[o[u]];break;case 32:w.getLogger().debug("Rule: dirList: ",o[u-1],o[u]),this.$=[o[u-1]].concat(o[u]);break;case 33:w.getLogger().debug("Rule: nodeShapeNLabel: ",o[u-2],o[u-1],o[u]),this.$={typeStr:o[u-2]+o[u],label:o[u-1]};break;case 34:w.getLogger().debug("Rule: BLOCK_ARROW nodeShapeNLabel: ",o[u-3],o[u-2]," #3:",o[u-1],o[u]),this.$={typeStr:o[u-3]+o[u],label:o[u-2],directions:o[u-1]};break;case 35:case 36:this.$={type:"classDef",id:o[u-1].trim(),css:o[u].trim()};break;case 37:this.$={type:"applyClass",id:o[u-1].trim(),styleClass:o[u].trim()};break;case 38:this.$={type:"applyStyles",id:o[u-1].trim(),stylesStr:o[u].trim()};break}},"anonymous"),table:[{9:1,10:[1,2]},{1:[3]},{11:3,13:4,19:5,20:6,21:t,22:8,23:9,24:10,25:11,26:12,28:a,29:i,31:l,32:s,40:r,44:n,47:c},{8:[1,20]},e(p,[2,12],{13:4,19:5,20:6,22:8,23:9,24:10,25:11,26:12,11:21,21:t,28:a,29:i,31:l,32:s,40:r,44:n,47:c}),e(h,[2,16],{14:22,15:x,16:b}),e(h,[2,17]),e(h,[2,18]),e(h,[2,19]),e(h,[2,20]),e(h,[2,21]),e(h,[2,22]),e(m,[2,25],{27:[1,25]}),e(h,[2,26]),{19:26,26:12,32:s},{11:27,13:4,19:5,20:6,21:t,22:8,23:9,24:10,25:11,26:12,28:a,29:i,31:l,32:s,40:r,44:n,47:c},{41:[1,28],43:[1,29]},{45:[1,30]},{48:[1,31]},e(v,[2,29],{33:32,36:[1,33],38:[1,34]}),{1:[2,7]},e(p,[2,13]),{26:35,32:s},{32:[2,14]},{17:[1,36]},e(m,[2,24]),{11:37,13:4,14:22,15:x,16:b,19:5,20:6,21:t,22:8,23:9,24:10,25:11,26:12,28:a,29:i,31:l,32:s,40:r,44:n,47:c},{30:[1,38]},{42:[1,39]},{42:[1,40]},{46:[1,41]},{49:[1,42]},e(v,[2,30]),{18:[1,43]},{18:[1,44]},e(m,[2,23]),{18:[1,45]},{30:[1,46]},e(h,[2,28]),e(h,[2,35]),e(h,[2,36]),e(h,[2,37]),e(h,[2,38]),{37:[1,47]},{34:48,35:T},{15:[1,50]},e(h,[2,27]),e(v,[2,33]),{39:[1,51]},{34:52,35:T,39:[2,31]},{32:[2,15]},e(v,[2,34]),{39:[2,32]}],defaultActions:{20:[2,7],23:[2,14],50:[2,15],52:[2,32]},parseError:d(function(y,g){if(g.recoverable)this.trace(y);else{var f=new Error(y);throw f.hash=g,f}},"parseError"),parse:d(function(y){var g=this,f=[0],w=[],D=[null],o=[],N=this.table,u="",E=0,I=0,_=2,z=1,tt=o.slice.call(arguments,1),A=Object.create(this.lexer),q={yy:{}};for(var $ in this.yy)Object.prototype.hasOwnProperty.call(this.yy,$)&&(q.yy[$]=this.yy[$]);A.setInput(y,q.yy),q.yy.lexer=A,q.yy.parser=this,typeof A.yylloc=="undefined"&&(A.yylloc={});var J=A.yylloc;o.push(J);var he=A.options&&A.options.ranges;typeof q.yy.parseError=="function"?this.parseError=q.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function de(K){f.length=f.length-2*K,D.length=D.length-K,o.length=o.length-K}d(de,"popStack");function Nt(){var K;return K=w.pop()||A.lex()||z,typeof K!="number"&&(K instanceof Array&&(w=K,K=w.pop()),K=g.symbols_[K]||K),K}d(Nt,"lex");for(var H,et,U,ft,rt={},lt,Q,Tt,ct;;){if(et=f[f.length-1],this.defaultActions[et]?U=this.defaultActions[et]:((H===null||typeof H=="undefined")&&(H=Nt()),U=N[et]&&N[et][H]),typeof U=="undefined"||!U.length||!U[0]){var xt="";ct=[];for(lt in N[et])this.terminals_[lt]&&lt>_&&ct.push("'"+this.terminals_[lt]+"'");A.showPosition?xt="Parse error on line "+(E+1)+`:
`+A.showPosition()+`
Expecting `+ct.join(", ")+", got '"+(this.terminals_[H]||H)+"'":xt="Parse error on line "+(E+1)+": Unexpected "+(H==z?"end of input":"'"+(this.terminals_[H]||H)+"'"),this.parseError(xt,{text:A.match,token:this.terminals_[H]||H,line:A.yylineno,loc:J,expected:ct})}if(U[0]instanceof Array&&U.length>1)throw new Error("Parse Error: multiple actions possible at state: "+et+", token: "+H);switch(U[0]){case 1:f.push(H),D.push(A.yytext),o.push(A.yylloc),f.push(U[1]),H=null,I=A.yyleng,u=A.yytext,E=A.yylineno,J=A.yylloc;break;case 2:if(Q=this.productions_[U[1]][1],rt.$=D[D.length-Q],rt._$={first_line:o[o.length-(Q||1)].first_line,last_line:o[o.length-1].last_line,first_column:o[o.length-(Q||1)].first_column,last_column:o[o.length-1].last_column},he&&(rt._$.range=[o[o.length-(Q||1)].range[0],o[o.length-1].range[1]]),ft=this.performAction.apply(rt,[u,I,E,q.yy,U[1],D,o].concat(tt)),typeof ft!="undefined")return ft;Q&&(f=f.slice(0,-1*Q*2),D=D.slice(0,-1*Q),o=o.slice(0,-1*Q)),f.push(this.productions_[U[1]][0]),D.push(rt.$),o.push(rt._$),Tt=N[f[f.length-2]][f[f.length-1]],f.push(Tt);break;case 3:return!0}}return!0},"parse")},B=function(){var C={EOF:1,parseError:d(function(g,f){if(this.yy.parser)this.yy.parser.parseError(g,f);else throw new Error(g)},"parseError"),setInput:d(function(y,g){return this.yy=g||this.yy||{},this._input=y,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:d(function(){var y=this._input[0];this.yytext+=y,this.yyleng++,this.offset++,this.match+=y,this.matched+=y;var g=y.match(/(?:\r\n?|\n).*/g);return g?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),y},"input"),unput:d(function(y){var g=y.length,f=y.split(/(?:\r\n?|\n)/g);this._input=y+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-g),this.offset-=g;var w=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),f.length-1&&(this.yylineno-=f.length-1);var D=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:f?(f.length===w.length?this.yylloc.first_column:0)+w[w.length-f.length].length-f[0].length:this.yylloc.first_column-g},this.options.ranges&&(this.yylloc.range=[D[0],D[0]+this.yyleng-g]),this.yyleng=this.yytext.length,this},"unput"),more:d(function(){return this._more=!0,this},"more"),reject:d(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:d(function(y){this.unput(this.match.slice(y))},"less"),pastInput:d(function(){var y=this.matched.substr(0,this.matched.length-this.match.length);return(y.length>20?"...":"")+y.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:d(function(){var y=this.match;return y.length<20&&(y+=this._input.substr(0,20-y.length)),(y.substr(0,20)+(y.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:d(function(){var y=this.pastInput(),g=new Array(y.length+1).join("-");return y+this.upcomingInput()+`
`+g+"^"},"showPosition"),test_match:d(function(y,g){var f,w,D;if(this.options.backtrack_lexer&&(D={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(D.yylloc.range=this.yylloc.range.slice(0))),w=y[0].match(/(?:\r\n?|\n).*/g),w&&(this.yylineno+=w.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:w?w[w.length-1].length-w[w.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+y[0].length},this.yytext+=y[0],this.match+=y[0],this.matches=y,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(y[0].length),this.matched+=y[0],f=this.performAction.call(this,this.yy,this,g,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),f)return f;if(this._backtrack){for(var o in D)this[o]=D[o];return!1}return!1},"test_match"),next:d(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var y,g,f,w;this._more||(this.yytext="",this.match="");for(var D=this._currentRules(),o=0;o<D.length;o++)if(f=this._input.match(this.rules[D[o]]),f&&(!g||f[0].length>g[0].length)){if(g=f,w=o,this.options.backtrack_lexer){if(y=this.test_match(f,D[o]),y!==!1)return y;if(this._backtrack){g=!1;continue}else return!1}else if(!this.options.flex)break}return g?(y=this.test_match(g,D[w]),y!==!1?y:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:d(function(){var g=this.next();return g||this.lex()},"lex"),begin:d(function(g){this.conditionStack.push(g)},"begin"),popState:d(function(){var g=this.conditionStack.length-1;return g>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:d(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:d(function(g){return g=this.conditionStack.length-1-Math.abs(g||0),g>=0?this.conditionStack[g]:"INITIAL"},"topState"),pushState:d(function(g){this.begin(g)},"pushState"),stateStackSize:d(function(){return this.conditionStack.length},"stateStackSize"),options:{},performAction:d(function(g,f,w,D){switch(w){case 0:return 10;case 1:return g.getLogger().debug("Found space-block"),31;case 2:return g.getLogger().debug("Found nl-block"),31;case 3:return g.getLogger().debug("Found space-block"),29;case 4:g.getLogger().debug(".",f.yytext);break;case 5:g.getLogger().debug("_",f.yytext);break;case 6:return 5;case 7:return f.yytext=-1,28;case 8:return f.yytext=f.yytext.replace(/columns\s+/,""),g.getLogger().debug("COLUMNS (LEX)",f.yytext),28;case 9:this.pushState("md_string");break;case 10:return"MD_STR";case 11:this.popState();break;case 12:this.pushState("string");break;case 13:g.getLogger().debug("LEX: POPPING STR:",f.yytext),this.popState();break;case 14:return g.getLogger().debug("LEX: STR end:",f.yytext),"STR";case 15:return f.yytext=f.yytext.replace(/space\:/,""),g.getLogger().debug("SPACE NUM (LEX)",f.yytext),21;case 16:return f.yytext="1",g.getLogger().debug("COLUMNS (LEX)",f.yytext),21;case 17:return 43;case 18:return"LINKSTYLE";case 19:return"INTERPOLATE";case 20:return this.pushState("CLASSDEF"),40;case 21:return this.popState(),this.pushState("CLASSDEFID"),"DEFAULT_CLASSDEF_ID";case 22:return this.popState(),this.pushState("CLASSDEFID"),41;case 23:return this.popState(),42;case 24:return this.pushState("CLASS"),44;case 25:return this.popState(),this.pushState("CLASS_STYLE"),45;case 26:return this.popState(),46;case 27:return this.pushState("STYLE_STMNT"),47;case 28:return this.popState(),this.pushState("STYLE_DEFINITION"),48;case 29:return this.popState(),49;case 30:return this.pushState("acc_title"),"acc_title";case 31:return this.popState(),"acc_title_value";case 32:return this.pushState("acc_descr"),"acc_descr";case 33:return this.popState(),"acc_descr_value";case 34:this.pushState("acc_descr_multiline");break;case 35:this.popState();break;case 36:return"acc_descr_multiline_value";case 37:return 30;case 38:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";case 39:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";case 40:return this.popState(),g.getLogger().debug("Lex: ))"),"NODE_DEND";case 41:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";case 42:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";case 43:return this.popState(),g.getLogger().debug("Lex: (-"),"NODE_DEND";case 44:return this.popState(),g.getLogger().debug("Lex: -)"),"NODE_DEND";case 45:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";case 46:return this.popState(),g.getLogger().debug("Lex: ]]"),"NODE_DEND";case 47:return this.popState(),g.getLogger().debug("Lex: ("),"NODE_DEND";case 48:return this.popState(),g.getLogger().debug("Lex: ])"),"NODE_DEND";case 49:return this.popState(),g.getLogger().debug("Lex: /]"),"NODE_DEND";case 50:return this.popState(),g.getLogger().debug("Lex: /]"),"NODE_DEND";case 51:return this.popState(),g.getLogger().debug("Lex: )]"),"NODE_DEND";case 52:return this.popState(),g.getLogger().debug("Lex: )"),"NODE_DEND";case 53:return this.popState(),g.getLogger().debug("Lex: ]>"),"NODE_DEND";case 54:return this.popState(),g.getLogger().debug("Lex: ]"),"NODE_DEND";case 55:return g.getLogger().debug("Lexa: -)"),this.pushState("NODE"),36;case 56:return g.getLogger().debug("Lexa: (-"),this.pushState("NODE"),36;case 57:return g.getLogger().debug("Lexa: ))"),this.pushState("NODE"),36;case 58:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;case 59:return g.getLogger().debug("Lex: ((("),this.pushState("NODE"),36;case 60:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;case 61:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;case 62:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;case 63:return g.getLogger().debug("Lexc: >"),this.pushState("NODE"),36;case 64:return g.getLogger().debug("Lexa: (["),this.pushState("NODE"),36;case 65:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;case 66:return this.pushState("NODE"),36;case 67:return this.pushState("NODE"),36;case 68:return this.pushState("NODE"),36;case 69:return this.pushState("NODE"),36;case 70:return this.pushState("NODE"),36;case 71:return this.pushState("NODE"),36;case 72:return this.pushState("NODE"),36;case 73:return g.getLogger().debug("Lexa: ["),this.pushState("NODE"),36;case 74:return this.pushState("BLOCK_ARROW"),g.getLogger().debug("LEX ARR START"),38;case 75:return g.getLogger().debug("Lex: NODE_ID",f.yytext),32;case 76:return g.getLogger().debug("Lex: EOF",f.yytext),8;case 77:this.pushState("md_string");break;case 78:this.pushState("md_string");break;case 79:return"NODE_DESCR";case 80:this.popState();break;case 81:g.getLogger().debug("Lex: Starting string"),this.pushState("string");break;case 82:g.getLogger().debug("LEX ARR: Starting string"),this.pushState("string");break;case 83:return g.getLogger().debug("LEX: NODE_DESCR:",f.yytext),"NODE_DESCR";case 84:g.getLogger().debug("LEX POPPING"),this.popState();break;case 85:g.getLogger().debug("Lex: =>BAE"),this.pushState("ARROW_DIR");break;case 86:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (right): dir:",f.yytext),"DIR";case 87:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (left):",f.yytext),"DIR";case 88:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (x):",f.yytext),"DIR";case 89:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (y):",f.yytext),"DIR";case 90:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (up):",f.yytext),"DIR";case 91:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (down):",f.yytext),"DIR";case 92:return f.yytext="]>",g.getLogger().debug("Lex (ARROW_DIR end):",f.yytext),this.popState(),this.popState(),"BLOCK_ARROW_END";case 93:return g.getLogger().debug("Lex: LINK","#"+f.yytext+"#"),15;case 94:return g.getLogger().debug("Lex: LINK",f.yytext),15;case 95:return g.getLogger().debug("Lex: LINK",f.yytext),15;case 96:return g.getLogger().debug("Lex: LINK",f.yytext),15;case 97:return g.getLogger().debug("Lex: START_LINK",f.yytext),this.pushState("LLABEL"),16;case 98:return g.getLogger().debug("Lex: START_LINK",f.yytext),this.pushState("LLABEL"),16;case 99:return g.getLogger().debug("Lex: START_LINK",f.yytext),this.pushState("LLABEL"),16;case 100:this.pushState("md_string");break;case 101:return g.getLogger().debug("Lex: Starting string"),this.pushState("string"),"LINK_LABEL";case 102:return this.popState(),g.getLogger().debug("Lex: LINK","#"+f.yytext+"#"),15;case 103:return this.popState(),g.getLogger().debug("Lex: LINK",f.yytext),15;case 104:return this.popState(),g.getLogger().debug("Lex: LINK",f.yytext),15;case 105:return g.getLogger().debug("Lex: COLON",f.yytext),f.yytext=f.yytext.slice(1),27}},"anonymous"),rules:[/^(?:block-beta\b)/,/^(?:block\s+)/,/^(?:block\n+)/,/^(?:block:)/,/^(?:[\s]+)/,/^(?:[\n]+)/,/^(?:((\u000D\u000A)|(\u000A)))/,/^(?:columns\s+auto\b)/,/^(?:columns\s+[\d]+)/,/^(?:["][`])/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["])/,/^(?:["])/,/^(?:[^"]*)/,/^(?:space[:]\d+)/,/^(?:space\b)/,/^(?:default\b)/,/^(?:linkStyle\b)/,/^(?:interpolate\b)/,/^(?:classDef\s+)/,/^(?:DEFAULT\s+)/,/^(?:\w+\s+)/,/^(?:[^\n]*)/,/^(?:class\s+)/,/^(?:(\w+)+((,\s*\w+)*))/,/^(?:[^\n]*)/,/^(?:style\s+)/,/^(?:(\w+)+((,\s*\w+)*))/,/^(?:[^\n]*)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:end\b\s*)/,/^(?:\(\(\()/,/^(?:\)\)\))/,/^(?:[\)]\))/,/^(?:\}\})/,/^(?:\})/,/^(?:\(-)/,/^(?:-\))/,/^(?:\(\()/,/^(?:\]\])/,/^(?:\()/,/^(?:\]\))/,/^(?:\\\])/,/^(?:\/\])/,/^(?:\)\])/,/^(?:[\)])/,/^(?:\]>)/,/^(?:[\]])/,/^(?:-\))/,/^(?:\(-)/,/^(?:\)\))/,/^(?:\))/,/^(?:\(\(\()/,/^(?:\(\()/,/^(?:\{\{)/,/^(?:\{)/,/^(?:>)/,/^(?:\(\[)/,/^(?:\()/,/^(?:\[\[)/,/^(?:\[\|)/,/^(?:\[\()/,/^(?:\)\)\))/,/^(?:\[\\)/,/^(?:\[\/)/,/^(?:\[\\)/,/^(?:\[)/,/^(?:<\[)/,/^(?:[^\(\[\n\-\)\{\}\s\<\>:]+)/,/^(?:$)/,/^(?:["][`])/,/^(?:["][`])/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["])/,/^(?:["])/,/^(?:[^"]+)/,/^(?:["])/,/^(?:\]>\s*\()/,/^(?:,?\s*right\s*)/,/^(?:,?\s*left\s*)/,/^(?:,?\s*x\s*)/,/^(?:,?\s*y\s*)/,/^(?:,?\s*up\s*)/,/^(?:,?\s*down\s*)/,/^(?:\)\s*)/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?:\s*~~[\~]+\s*)/,/^(?:\s*[xo<]?--\s*)/,/^(?:\s*[xo<]?==\s*)/,/^(?:\s*[xo<]?-\.\s*)/,/^(?:["][`])/,/^(?:["])/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?::\d+)/],conditions:{STYLE_DEFINITION:{rules:[29],inclusive:!1},STYLE_STMNT:{rules:[28],inclusive:!1},CLASSDEFID:{rules:[23],inclusive:!1},CLASSDEF:{rules:[21,22],inclusive:!1},CLASS_STYLE:{rules:[26],inclusive:!1},CLASS:{rules:[25],inclusive:!1},LLABEL:{rules:[100,101,102,103,104],inclusive:!1},ARROW_DIR:{rules:[86,87,88,89,90,91,92],inclusive:!1},BLOCK_ARROW:{rules:[77,82,85],inclusive:!1},NODE:{rules:[38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,78,81],inclusive:!1},md_string:{rules:[10,11,79,80],inclusive:!1},space:{rules:[],inclusive:!1},string:{rules:[13,14,83,84],inclusive:!1},acc_descr_multiline:{rules:[35,36],inclusive:!1},acc_descr:{rules:[33],inclusive:!1},acc_title:{rules:[31],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,7,8,9,12,15,16,17,18,19,20,24,27,30,32,34,37,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,93,94,95,96,97,98,99,105],inclusive:!0}}};return C}();S.lexer=B;function k(){this.yy={}}return d(k,"Parser"),k.prototype=S,S.Parser=k,new k}();bt.parser=bt;var _e=bt,V=new Map,vt=[],wt=new Map,Bt="color",It="fill",ke="bgFill",Xt=",",De=W(),dt=new Map,Ne=d(e=>Le.sanitizeText(e,De),"sanitizeText"),Te=d(function(e,t=""){let a=dt.get(e);a||(a={id:e,styles:[],textStyles:[]},dt.set(e,a)),t!=null&&t.split(Xt).forEach(i=>{const l=i.replace(/([^;]*);/,"$1").trim();if(RegExp(Bt).exec(i)){const r=l.replace(It,ke).replace(Bt,It);a.textStyles.push(r)}a.styles.push(l)})},"addStyleClass"),Ce=d(function(e,t=""){const a=V.get(e);t!=null&&(a.styles=t.split(Xt))},"addStyle2Node"),Be=d(function(e,t){e.split(",").forEach(function(a){let i=V.get(a);if(i===void 0){const l=a.trim();i={id:l,type:"na",children:[]},V.set(l,i)}i.classes||(i.classes=[]),i.classes.push(t)})},"setCssClass"),Ut=d((e,t)=>{var l,s,r,n;const a=e.flat(),i=[];for(const c of a){if(c.label&&(c.label=Ne(c.label)),c.type==="classDef"){Te(c.id,c.css);continue}if(c.type==="applyClass"){Be(c.id,(l=c==null?void 0:c.styleClass)!=null?l:"");continue}if(c.type==="applyStyles"){c!=null&&c.stylesStr&&Ce(c.id,c==null?void 0:c.stylesStr);continue}if(c.type==="column-setting")t.columns=(s=c.columns)!=null?s:-1;else if(c.type==="edge"){const p=((r=wt.get(c.id))!=null?r:0)+1;wt.set(c.id,p),c.id=p+"-"+c.id,vt.push(c)}else{c.label||(c.type==="composite"?c.label="":c.label=c.id);const p=V.get(c.id);if(p===void 0?V.set(c.id,c):(c.type!=="na"&&(p.type=c.type),c.label!==c.id&&(p.label=c.label)),c.children&&Ut(c.children,c),c.type==="space"){const h=(n=c.width)!=null?n:1;for(let x=0;x<h;x++){const b=Se(c);b.id=b.id+"-"+x,V.set(b.id,b),i.push(b)}}else p===void 0&&i.push(c)}}t.children=i},"populateBlockDatabase"),Et=[],it={id:"root",type:"composite",children:[],columns:-1},Ie=d(()=>{L.debug("Clear called"),ye(),it={id:"root",type:"composite",children:[],columns:-1},V=new Map([["root",it]]),Et=[],dt=new Map,vt=[],wt=new Map},"clear");function jt(e){switch(L.debug("typeStr2Type",e),e){case"[]":return"square";case"()":return L.debug("we have a round"),"round";case"(())":return"circle";case">]":return"rect_left_inv_arrow";case"{}":return"diamond";case"{{}}":return"hexagon";case"([])":return"stadium";case"[[]]":return"subroutine";case"[()]":return"cylinder";case"((()))":return"doublecircle";case"[//]":return"lean_right";case"[\\\\]":return"lean_left";case"[/\\]":return"trapezoid";case"[\\/]":return"inv_trapezoid";case"<[]>":return"block_arrow";default:return"na"}}d(jt,"typeStr2Type");function Vt(e){switch(L.debug("typeStr2Type",e),e){case"==":return"thick";default:return"normal"}}d(Vt,"edgeTypeStr2Type");function Zt(e){switch(e.trim()){case"--x":return"arrow_cross";case"--o":return"arrow_circle";default:return"arrow_point"}}d(Zt,"edgeStrToEdgeData");var Ot=0,Oe=d(()=>(Ot++,"id-"+Math.random().toString(36).substr(2,12)+"-"+Ot),"generateId"),Re=d(e=>{it.children=e,Ut(e,it),Et=it.children},"setHierarchy"),ze=d(e=>{const t=V.get(e);return t?t.columns?t.columns:t.children?t.children.length:-1:-1},"getColumns"),Ae=d(()=>[...V.values()],"getBlocksFlat"),Me=d(()=>Et||[],"getBlocks"),Fe=d(()=>vt,"getEdges"),We=d(e=>V.get(e),"getBlock"),Pe=d(e=>{V.set(e.id,e)},"setBlock"),Ye=d(()=>console,"getLogger"),He=d(function(){return dt},"getClasses"),Ke={getConfig:d(()=>nt().block,"getConfig"),typeStr2Type:jt,edgeTypeStr2Type:Vt,edgeStrToEdgeData:Zt,getLogger:Ye,getBlocksFlat:Ae,getBlocks:Me,getEdges:Fe,setHierarchy:Re,getBlock:We,setBlock:Pe,getColumns:ze,getClasses:He,clear:Ie,generateId:Oe},Xe=Ke,ot=d((e,t)=>{const a=Ee,i=a(e,"r"),l=a(e,"g"),s=a(e,"b");return be(i,l,s,t)},"fade"),Ue=d(e=>`.label {
    font-family: ${e.fontFamily};
    color: ${e.nodeTextColor||e.textColor};
  }
  .cluster-label text {
    fill: ${e.titleColor};
  }
  .cluster-label span,p {
    color: ${e.titleColor};
  }



  .label text,span,p {
    fill: ${e.nodeTextColor||e.textColor};
    color: ${e.nodeTextColor||e.textColor};
  }

  .node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${e.mainBkg};
    stroke: ${e.nodeBorder};
    stroke-width: 1px;
  }
  .flowchart-label text {
    text-anchor: middle;
  }
  // .flowchart-label .text-outer-tspan {
  //   text-anchor: middle;
  // }
  // .flowchart-label .text-inner-tspan {
  //   text-anchor: start;
  // }

  .node .label {
    text-align: center;
  }
  .node.clickable {
    cursor: pointer;
  }

  .arrowheadPath {
    fill: ${e.arrowheadColor};
  }

  .edgePath .path {
    stroke: ${e.lineColor};
    stroke-width: 2.0px;
  }

  .flowchart-link {
    stroke: ${e.lineColor};
    fill: none;
  }

  .edgeLabel {
    background-color: ${e.edgeLabelBackground};
    rect {
      opacity: 0.5;
      background-color: ${e.edgeLabelBackground};
      fill: ${e.edgeLabelBackground};
    }
    text-align: center;
  }

  /* For html labels only */
  .labelBkg {
    background-color: ${ot(e.edgeLabelBackground,.5)};
    // background-color:
  }

  .node .cluster {
    // fill: ${ot(e.mainBkg,.5)};
    fill: ${ot(e.clusterBkg,.5)};
    stroke: ${ot(e.clusterBorder,.2)};
    box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px, rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;
    stroke-width: 1px;
  }

  .cluster text {
    fill: ${e.titleColor};
  }

  .cluster span,p {
    color: ${e.titleColor};
  }
  /* .cluster div {
    color: ${e.titleColor};
  } */

  div.mermaidTooltip {
    position: absolute;
    text-align: center;
    max-width: 200px;
    padding: 2px;
    font-family: ${e.fontFamily};
    font-size: 12px;
    background: ${e.tertiaryColor};
    border: 1px solid ${e.border2};
    border-radius: 2px;
    pointer-events: none;
    z-index: 100;
  }

  .flowchartTitleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${e.textColor};
  }
`,"getStyles"),je=Ue,Ve=d((e,t,a,i)=>{t.forEach(l=>{ar[l](e,a,i)})},"insertMarkers"),Ze=d((e,t,a)=>{L.trace("Making markers for ",a),e.append("defs").append("marker").attr("id",a+"_"+t+"-extensionStart").attr("class","marker extension "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 1,7 L18,13 V 1 Z"),e.append("defs").append("marker").attr("id",a+"_"+t+"-extensionEnd").attr("class","marker extension "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 1,1 V 13 L18,7 Z")},"extension"),Ge=d((e,t,a)=>{e.append("defs").append("marker").attr("id",a+"_"+t+"-compositionStart").attr("class","marker composition "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",a+"_"+t+"-compositionEnd").attr("class","marker composition "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"composition"),qe=d((e,t,a)=>{e.append("defs").append("marker").attr("id",a+"_"+t+"-aggregationStart").attr("class","marker aggregation "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",a+"_"+t+"-aggregationEnd").attr("class","marker aggregation "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"aggregation"),Je=d((e,t,a)=>{e.append("defs").append("marker").attr("id",a+"_"+t+"-dependencyStart").attr("class","marker dependency "+t).attr("refX",6).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 5,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",a+"_"+t+"-dependencyEnd").attr("class","marker dependency "+t).attr("refX",13).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"dependency"),Qe=d((e,t,a)=>{e.append("defs").append("marker").attr("id",a+"_"+t+"-lollipopStart").attr("class","marker lollipop "+t).attr("refX",13).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6),e.append("defs").append("marker").attr("id",a+"_"+t+"-lollipopEnd").attr("class","marker lollipop "+t).attr("refX",1).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6)},"lollipop"),$e=d((e,t,a)=>{e.append("marker").attr("id",a+"_"+t+"-pointEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",6).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",a+"_"+t+"-pointStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",4.5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 5 L 10 10 L 10 0 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"point"),tr=d((e,t,a)=>{e.append("marker").attr("id",a+"_"+t+"-circleEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",11).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",a+"_"+t+"-circleStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",-1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"circle"),er=d((e,t,a)=>{e.append("marker").attr("id",a+"_"+t+"-crossEnd").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",12).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0"),e.append("marker").attr("id",a+"_"+t+"-crossStart").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",-1).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0")},"cross"),rr=d((e,t,a)=>{e.append("defs").append("marker").attr("id",a+"_"+t+"-barbEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",14).attr("markerUnits","strokeWidth").attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")},"barb"),ar={extension:Ze,composition:Ge,aggregation:qe,dependency:Je,lollipop:Qe,point:$e,circle:tr,cross:er,barb:rr},sr=Ve,Pt,Yt,Ht,M=(Ht=(Yt=(Pt=W())==null?void 0:Pt.block)==null?void 0:Yt.padding)!=null?Ht:8;function Gt(e,t){if(e===0||!Number.isInteger(e))throw new Error("Columns must be an integer !== 0.");if(t<0||!Number.isInteger(t))throw new Error("Position must be a non-negative integer."+t);if(e<0)return{px:t,py:0};if(e===1)return{px:0,py:t};const a=t%e,i=Math.floor(t/e);return{px:a,py:i}}d(Gt,"calculateBlockPosition");var ir=d(e=>{var i,l;let t=0,a=0;for(const s of e.children){const{width:r,height:n,x:c,y:p}=(i=s.size)!=null?i:{width:0,height:0,x:0,y:0};L.debug("getMaxChildSize abc95 child:",s.id,"width:",r,"height:",n,"x:",c,"y:",p,s.type),s.type!=="space"&&(r>t&&(t=r/((l=e.widthInColumns)!=null?l:1)),n>a&&(a=n))}return{width:t,height:a}},"getMaxChildSize");function gt(e,t,a=0,i=0){var r,n,c,p,h,x,b,m,v,T,S,B,k,C,y;L.debug("setBlockSizes abc95 (start)",e.id,(r=e==null?void 0:e.size)==null?void 0:r.x,"block width =",e==null?void 0:e.size,"sieblingWidth",a),(n=e==null?void 0:e.size)!=null&&n.width||(e.size={width:a,height:i,x:0,y:0});let l=0,s=0;if(((c=e.children)==null?void 0:c.length)>0){for(const E of e.children)gt(E,t);const g=ir(e);l=g.width,s=g.height,L.debug("setBlockSizes abc95 maxWidth of",e.id,":s children is ",l,s);for(const E of e.children)E.size&&(L.debug(`abc95 Setting size of children of ${e.id} id=${E.id} ${l} ${s} ${JSON.stringify(E.size)}`),E.size.width=l*((p=E.widthInColumns)!=null?p:1)+M*(((h=E.widthInColumns)!=null?h:1)-1),E.size.height=s,E.size.x=0,E.size.y=0,L.debug(`abc95 updating size of ${e.id} children child:${E.id} maxWidth:${l} maxHeight:${s}`));for(const E of e.children)gt(E,t,l,s);const f=(x=e.columns)!=null?x:-1;let w=0;for(const E of e.children)w+=(b=E.widthInColumns)!=null?b:1;let D=e.children.length;f>0&&f<w&&(D=f);const o=Math.ceil(w/D);let N=D*(l+M)+M,u=o*(s+M)+M;if(N<a){L.debug(`Detected to small siebling: abc95 ${e.id} sieblingWidth ${a} sieblingHeight ${i} width ${N}`),N=a,u=i;const E=(a-D*M-M)/D,I=(i-o*M-M)/o;L.debug("Size indata abc88",e.id,"childWidth",E,"maxWidth",l),L.debug("Size indata abc88",e.id,"childHeight",I,"maxHeight",s),L.debug("Size indata abc88 xSize",D,"padding",M);for(const _ of e.children)_.size&&(_.size.width=E,_.size.height=I,_.size.x=0,_.size.y=0)}if(L.debug(`abc95 (finale calc) ${e.id} xSize ${D} ySize ${o} columns ${f}${e.children.length} width=${Math.max(N,((m=e.size)==null?void 0:m.width)||0)}`),N<(((v=e==null?void 0:e.size)==null?void 0:v.width)||0)){N=((T=e==null?void 0:e.size)==null?void 0:T.width)||0;const E=f>0?Math.min(e.children.length,f):e.children.length;if(E>0){const I=(N-E*M-M)/E;L.debug("abc95 (growing to fit) width",e.id,N,(S=e.size)==null?void 0:S.width,I);for(const _ of e.children)_.size&&(_.size.width=I)}}e.size={width:N,height:u,x:0,y:0}}L.debug("setBlockSizes abc94 (done)",e.id,(B=e==null?void 0:e.size)==null?void 0:B.x,(k=e==null?void 0:e.size)==null?void 0:k.width,(C=e==null?void 0:e.size)==null?void 0:C.y,(y=e==null?void 0:e.size)==null?void 0:y.height)}d(gt,"setBlockSizes");function _t(e,t){var i,l,s,r,n,c,p,h,x,b,m,v,T,S,B,k,C,y,g,f,w,D;L.debug(`abc85 layout blocks (=>layoutBlocks) ${e.id} x: ${(i=e==null?void 0:e.size)==null?void 0:i.x} y: ${(l=e==null?void 0:e.size)==null?void 0:l.y} width: ${(s=e==null?void 0:e.size)==null?void 0:s.width}`);const a=(r=e.columns)!=null?r:-1;if(L.debug("layoutBlocks columns abc95",e.id,"=>",a,e),e.children&&e.children.length>0){const o=(p=(c=(n=e==null?void 0:e.children[0])==null?void 0:n.size)==null?void 0:c.width)!=null?p:0,N=e.children.length*o+(e.children.length-1)*M;L.debug("widthOfChildren 88",N,"posX");let u=0;L.debug("abc91 block?.size?.x",e.id,(h=e==null?void 0:e.size)==null?void 0:h.x);let E=(x=e==null?void 0:e.size)!=null&&x.x?((b=e==null?void 0:e.size)==null?void 0:b.x)+(-((m=e==null?void 0:e.size)==null?void 0:m.width)/2||0):-M,I=0;for(const _ of e.children){const z=e;if(!_.size)continue;const{width:tt,height:A}=_.size,{px:q,py:$}=Gt(a,u);if($!=I&&(I=$,E=(v=e==null?void 0:e.size)!=null&&v.x?((T=e==null?void 0:e.size)==null?void 0:T.x)+(-((S=e==null?void 0:e.size)==null?void 0:S.width)/2||0):-M,L.debug("New row in layout for block",e.id," and child ",_.id,I)),L.debug(`abc89 layout blocks (child) id: ${_.id} Pos: ${u} (px, py) ${q},${$} (${(B=z==null?void 0:z.size)==null?void 0:B.x},${(k=z==null?void 0:z.size)==null?void 0:k.y}) parent: ${z.id} width: ${tt}${M}`),z.size){const J=tt/2;_.size.x=E+M+J,L.debug(`abc91 layout blocks (calc) px, pyid:${_.id} startingPos=X${E} new startingPosX${_.size.x} ${J} padding=${M} width=${tt} halfWidth=${J} => x:${_.size.x} y:${_.size.y} ${_.widthInColumns} (width * (child?.w || 1)) / 2 ${tt*((C=_==null?void 0:_.widthInColumns)!=null?C:1)/2}`),E=_.size.x+J,_.size.y=z.size.y-z.size.height/2+$*(A+M)+A/2+M,L.debug(`abc88 layout blocks (calc) px, pyid:${_.id}startingPosX${E}${M}${J}=>x:${_.size.x}y:${_.size.y}${_.widthInColumns}(width * (child?.w || 1)) / 2${tt*((y=_==null?void 0:_.widthInColumns)!=null?y:1)/2}`)}_.children&&_t(_),u+=(g=_==null?void 0:_.widthInColumns)!=null?g:1,L.debug("abc88 columnsPos",_,u)}}L.debug(`layout blocks (<==layoutBlocks) ${e.id} x: ${(f=e==null?void 0:e.size)==null?void 0:f.x} y: ${(w=e==null?void 0:e.size)==null?void 0:w.y} width: ${(D=e==null?void 0:e.size)==null?void 0:D.width}`)}d(_t,"layoutBlocks");function kt(e,{minX:t,minY:a,maxX:i,maxY:l}={minX:0,minY:0,maxX:0,maxY:0}){if(e.size&&e.id!=="root"){const{x:s,y:r,width:n,height:c}=e.size;s-n/2<t&&(t=s-n/2),r-c/2<a&&(a=r-c/2),s+n/2>i&&(i=s+n/2),r+c/2>l&&(l=r+c/2)}if(e.children)for(const s of e.children)({minX:t,minY:a,maxX:i,maxY:l}=kt(s,{minX:t,minY:a,maxX:i,maxY:l}));return{minX:t,minY:a,maxX:i,maxY:l}}d(kt,"findBounds");function qt(e){const t=e.getBlock("root");if(!t)return;gt(t,e,0,0),_t(t),L.debug("getBlocks",JSON.stringify(t,null,2));const{minX:a,minY:i,maxX:l,maxY:s}=kt(t),r=s-i,n=l-a;return{x:a,y:i,width:n,height:r}}d(qt,"layout");function mt(e,t){t&&e.attr("style",t)}d(mt,"applyStyle");function Jt(e){const t=F(document.createElementNS("http://www.w3.org/2000/svg","foreignObject")),a=t.append("xhtml:div"),i=e.label,l=e.isNode?"nodeLabel":"edgeLabel",s=a.append("span");return s.html(i),mt(s,e.labelStyle),s.attr("class",l),mt(a,e.labelStyle),a.style("display","inline-block"),a.style("white-space","nowrap"),a.attr("xmlns","http://www.w3.org/1999/xhtml"),t.node()}d(Jt,"addHtmlLabel");var nr=d((e,t,a,i)=>{let l=e||"";if(typeof l=="object"&&(l=l[0]),G(W().flowchart.htmlLabels)){l=l.replace(/\\n|\n/g,"<br />"),L.debug("vertexText"+l);const s={isNode:i,label:me(yt(l)),labelStyle:t.replace("fill:","color:")};return Jt(s)}else{const s=document.createElementNS("http://www.w3.org/2000/svg","text");s.setAttribute("style",t.replace("color:","fill:"));let r=[];typeof l=="string"?r=l.split(/\\n|\n|<br\s*\/?>/gi):Array.isArray(l)?r=l:r=[];for(const n of r){const c=document.createElementNS("http://www.w3.org/2000/svg","tspan");c.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),c.setAttribute("dy","1em"),c.setAttribute("x","0"),a?c.setAttribute("class","title-row"):c.setAttribute("class","row"),c.textContent=n.trim(),s.appendChild(c)}return s}},"createLabel"),j=nr,lr=d((e,t,a,i,l)=>{t.arrowTypeStart&&Rt(e,"start",t.arrowTypeStart,a,i,l),t.arrowTypeEnd&&Rt(e,"end",t.arrowTypeEnd,a,i,l)},"addEdgeMarkers"),cr={arrow_cross:"cross",arrow_point:"point",arrow_barb:"barb",arrow_circle:"circle",aggregation:"aggregation",extension:"extension",composition:"composition",dependency:"dependency",lollipop:"lollipop"},Rt=d((e,t,a,i,l,s)=>{const r=cr[a];if(!r){L.warn(`Unknown arrow type: ${a}`);return}const n=t==="start"?"Start":"End";e.attr(`marker-${t}`,`url(${i}#${l}_${s}-${r}${n})`)},"addEdgeMarker"),Lt={},Y={},or=d((e,t)=>{const a=W(),i=G(a.flowchart.htmlLabels),l=t.labelType==="markdown"?Kt(e,t.label,{style:t.labelStyle,useHtmlLabels:i,addSvgBackground:!0},a):j(t.label,t.labelStyle),s=e.insert("g").attr("class","edgeLabel"),r=s.insert("g").attr("class","label");r.node().appendChild(l);let n=l.getBBox();if(i){const p=l.children[0],h=F(l);n=p.getBoundingClientRect(),h.attr("width",n.width),h.attr("height",n.height)}r.attr("transform","translate("+-n.width/2+", "+-n.height/2+")"),Lt[t.id]=s,t.width=n.width,t.height=n.height;let c;if(t.startLabelLeft){const p=j(t.startLabelLeft,t.labelStyle),h=e.insert("g").attr("class","edgeTerminals"),x=h.insert("g").attr("class","inner");c=x.node().appendChild(p);const b=p.getBBox();x.attr("transform","translate("+-b.width/2+", "+-b.height/2+")"),Y[t.id]||(Y[t.id]={}),Y[t.id].startLeft=h,st(c,t.startLabelLeft)}if(t.startLabelRight){const p=j(t.startLabelRight,t.labelStyle),h=e.insert("g").attr("class","edgeTerminals"),x=h.insert("g").attr("class","inner");c=h.node().appendChild(p),x.node().appendChild(p);const b=p.getBBox();x.attr("transform","translate("+-b.width/2+", "+-b.height/2+")"),Y[t.id]||(Y[t.id]={}),Y[t.id].startRight=h,st(c,t.startLabelRight)}if(t.endLabelLeft){const p=j(t.endLabelLeft,t.labelStyle),h=e.insert("g").attr("class","edgeTerminals"),x=h.insert("g").attr("class","inner");c=x.node().appendChild(p);const b=p.getBBox();x.attr("transform","translate("+-b.width/2+", "+-b.height/2+")"),h.node().appendChild(p),Y[t.id]||(Y[t.id]={}),Y[t.id].endLeft=h,st(c,t.endLabelLeft)}if(t.endLabelRight){const p=j(t.endLabelRight,t.labelStyle),h=e.insert("g").attr("class","edgeTerminals"),x=h.insert("g").attr("class","inner");c=x.node().appendChild(p);const b=p.getBBox();x.attr("transform","translate("+-b.width/2+", "+-b.height/2+")"),h.node().appendChild(p),Y[t.id]||(Y[t.id]={}),Y[t.id].endRight=h,st(c,t.endLabelRight)}return l},"insertEdgeLabel");function st(e,t){W().flowchart.htmlLabels&&e&&(e.style.width=t.length*9+"px",e.style.height="12px")}d(st,"setTerminalWidth");var hr=d((e,t)=>{L.debug("Moving label abc88 ",e.id,e.label,Lt[e.id],t);let a=t.updatedPath?t.updatedPath:t.originalPath;const i=W(),{subGraphTitleTotalMargin:l}=ue(i);if(e.label){const s=Lt[e.id];let r=e.x,n=e.y;if(a){const c=at.calcLabelPosition(a);L.debug("Moving label "+e.label+" from (",r,",",n,") to (",c.x,",",c.y,") abc88"),t.updatedPath&&(r=c.x,n=c.y)}s.attr("transform",`translate(${r}, ${n+l/2})`)}if(e.startLabelLeft){const s=Y[e.id].startLeft;let r=e.x,n=e.y;if(a){const c=at.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_left",a);r=c.x,n=c.y}s.attr("transform",`translate(${r}, ${n})`)}if(e.startLabelRight){const s=Y[e.id].startRight;let r=e.x,n=e.y;if(a){const c=at.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_right",a);r=c.x,n=c.y}s.attr("transform",`translate(${r}, ${n})`)}if(e.endLabelLeft){const s=Y[e.id].endLeft;let r=e.x,n=e.y;if(a){const c=at.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_left",a);r=c.x,n=c.y}s.attr("transform",`translate(${r}, ${n})`)}if(e.endLabelRight){const s=Y[e.id].endRight;let r=e.x,n=e.y;if(a){const c=at.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_right",a);r=c.x,n=c.y}s.attr("transform",`translate(${r}, ${n})`)}},"positionEdgeLabel"),dr=d((e,t)=>{const a=e.x,i=e.y,l=Math.abs(t.x-a),s=Math.abs(t.y-i),r=e.width/2,n=e.height/2;return l>=r||s>=n},"outsideNode"),gr=d((e,t,a)=>{L.debug(`intersection calc abc89:
  outsidePoint: ${JSON.stringify(t)}
  insidePoint : ${JSON.stringify(a)}
  node        : x:${e.x} y:${e.y} w:${e.width} h:${e.height}`);const i=e.x,l=e.y,s=Math.abs(i-a.x),r=e.width/2;let n=a.x<t.x?r-s:r+s;const c=e.height/2,p=Math.abs(t.y-a.y),h=Math.abs(t.x-a.x);if(Math.abs(l-t.y)*r>Math.abs(i-t.x)*c){let x=a.y<t.y?t.y-c-l:l-c-t.y;n=h*x/p;const b={x:a.x<t.x?a.x+n:a.x-h+n,y:a.y<t.y?a.y+p-x:a.y-p+x};return n===0&&(b.x=t.x,b.y=t.y),h===0&&(b.x=t.x),p===0&&(b.y=t.y),L.debug(`abc89 topp/bott calc, Q ${p}, q ${x}, R ${h}, r ${n}`,b),b}else{a.x<t.x?n=t.x-r-i:n=i-r-t.x;let x=p*n/h,b=a.x<t.x?a.x+h-n:a.x-h+n,m=a.y<t.y?a.y+x:a.y-x;return L.debug(`sides calc abc89, Q ${p}, q ${x}, R ${h}, r ${n}`,{_x:b,_y:m}),n===0&&(b=t.x,m=t.y),h===0&&(b=t.x),p===0&&(m=t.y),{x:b,y:m}}},"intersection"),zt=d((e,t)=>{L.debug("abc88 cutPathAtIntersect",e,t);let a=[],i=e[0],l=!1;return e.forEach(s=>{if(!dr(t,s)&&!l){const r=gr(t,i,s);let n=!1;a.forEach(c=>{n=n||c.x===r.x&&c.y===r.y}),a.some(c=>c.x===r.x&&c.y===r.y)||a.push(r),l=!0}else i=s,l||a.push(s)}),a},"cutPathAtIntersect"),ur=d(function(e,t,a,i,l,s,r){let n=a.points;L.debug("abc88 InsertEdge: edge=",a,"e=",t);let c=!1;const p=s.node(t.v);var h=s.node(t.w);(h==null?void 0:h.intersect)&&(p==null?void 0:p.intersect)&&(n=n.slice(1,a.points.length-1),n.unshift(p.intersect(n[0])),n.push(h.intersect(n[n.length-1]))),a.toCluster&&(L.debug("to cluster abc88",i[a.toCluster]),n=zt(a.points,i[a.toCluster].node),c=!0),a.fromCluster&&(L.debug("from cluster abc88",i[a.fromCluster]),n=zt(n.reverse(),i[a.fromCluster].node).reverse(),c=!0);const x=n.filter(y=>!Number.isNaN(y.y));let b=we;a.curve&&(l==="graph"||l==="flowchart")&&(b=a.curve);const{x:m,y:v}=pe(a),T=fe().x(m).y(v).curve(b);let S;switch(a.thickness){case"normal":S="edge-thickness-normal";break;case"thick":S="edge-thickness-thick";break;case"invisible":S="edge-thickness-thick";break;default:S=""}switch(a.pattern){case"solid":S+=" edge-pattern-solid";break;case"dotted":S+=" edge-pattern-dotted";break;case"dashed":S+=" edge-pattern-dashed";break}const B=e.append("path").attr("d",T(x)).attr("id",a.id).attr("class"," "+S+(a.classes?" "+a.classes:"")).attr("style",a.style);let k="";(W().flowchart.arrowMarkerAbsolute||W().state.arrowMarkerAbsolute)&&(k=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,k=k.replace(/\(/g,"\\("),k=k.replace(/\)/g,"\\)")),lr(B,a,k,r,l);let C={};return c&&(C.updatedPath=n),C.originalPath=a.points,C},"insertEdge"),pr=d(e=>{const t=new Set;for(const a of e)switch(a){case"x":t.add("right"),t.add("left");break;case"y":t.add("up"),t.add("down");break;default:t.add(a);break}return t},"expandAndDeduplicateDirections"),fr=d((e,t,a)=>{const i=pr(e),l=2,s=t.height+2*a.padding,r=s/l,n=t.width+2*r+a.padding,c=a.padding/2;return i.has("right")&&i.has("left")&&i.has("up")&&i.has("down")?[{x:0,y:0},{x:r,y:0},{x:n/2,y:2*c},{x:n-r,y:0},{x:n,y:0},{x:n,y:-s/3},{x:n+2*c,y:-s/2},{x:n,y:-2*s/3},{x:n,y:-s},{x:n-r,y:-s},{x:n/2,y:-s-2*c},{x:r,y:-s},{x:0,y:-s},{x:0,y:-2*s/3},{x:-2*c,y:-s/2},{x:0,y:-s/3}]:i.has("right")&&i.has("left")&&i.has("up")?[{x:r,y:0},{x:n-r,y:0},{x:n,y:-s/2},{x:n-r,y:-s},{x:r,y:-s},{x:0,y:-s/2}]:i.has("right")&&i.has("left")&&i.has("down")?[{x:0,y:0},{x:r,y:-s},{x:n-r,y:-s},{x:n,y:0}]:i.has("right")&&i.has("up")&&i.has("down")?[{x:0,y:0},{x:n,y:-r},{x:n,y:-s+r},{x:0,y:-s}]:i.has("left")&&i.has("up")&&i.has("down")?[{x:n,y:0},{x:0,y:-r},{x:0,y:-s+r},{x:n,y:-s}]:i.has("right")&&i.has("left")?[{x:r,y:0},{x:r,y:-c},{x:n-r,y:-c},{x:n-r,y:0},{x:n,y:-s/2},{x:n-r,y:-s},{x:n-r,y:-s+c},{x:r,y:-s+c},{x:r,y:-s},{x:0,y:-s/2}]:i.has("up")&&i.has("down")?[{x:n/2,y:0},{x:0,y:-c},{x:r,y:-c},{x:r,y:-s+c},{x:0,y:-s+c},{x:n/2,y:-s},{x:n,y:-s+c},{x:n-r,y:-s+c},{x:n-r,y:-c},{x:n,y:-c}]:i.has("right")&&i.has("up")?[{x:0,y:0},{x:n,y:-r},{x:0,y:-s}]:i.has("right")&&i.has("down")?[{x:0,y:0},{x:n,y:0},{x:0,y:-s}]:i.has("left")&&i.has("up")?[{x:n,y:0},{x:0,y:-r},{x:n,y:-s}]:i.has("left")&&i.has("down")?[{x:n,y:0},{x:0,y:0},{x:n,y:-s}]:i.has("right")?[{x:r,y:-c},{x:r,y:-c},{x:n-r,y:-c},{x:n-r,y:0},{x:n,y:-s/2},{x:n-r,y:-s},{x:n-r,y:-s+c},{x:r,y:-s+c},{x:r,y:-s+c}]:i.has("left")?[{x:r,y:0},{x:r,y:-c},{x:n-r,y:-c},{x:n-r,y:-s+c},{x:r,y:-s+c},{x:r,y:-s},{x:0,y:-s/2}]:i.has("up")?[{x:r,y:-c},{x:r,y:-s+c},{x:0,y:-s+c},{x:n/2,y:-s},{x:n,y:-s+c},{x:n-r,y:-s+c},{x:n-r,y:-c}]:i.has("down")?[{x:n/2,y:0},{x:0,y:-c},{x:r,y:-c},{x:r,y:-s+c},{x:n-r,y:-s+c},{x:n-r,y:-c},{x:n,y:-c}]:[{x:0,y:0}]},"getArrowPoints");function Qt(e,t){return e.intersect(t)}d(Qt,"intersectNode");var xr=Qt;function $t(e,t,a,i){var l=e.x,s=e.y,r=l-i.x,n=s-i.y,c=Math.sqrt(t*t*n*n+a*a*r*r),p=Math.abs(t*a*r/c);i.x<l&&(p=-p);var h=Math.abs(t*a*n/c);return i.y<s&&(h=-h),{x:l+p,y:s+h}}d($t,"intersectEllipse");var te=$t;function ee(e,t,a){return te(e,t,t,a)}d(ee,"intersectCircle");var yr=ee;function re(e,t,a,i){var l,s,r,n,c,p,h,x,b,m,v,T,S,B,k;if(l=t.y-e.y,r=e.x-t.x,c=t.x*e.y-e.x*t.y,b=l*a.x+r*a.y+c,m=l*i.x+r*i.y+c,!(b!==0&&m!==0&&St(b,m))&&(s=i.y-a.y,n=a.x-i.x,p=i.x*a.y-a.x*i.y,h=s*e.x+n*e.y+p,x=s*t.x+n*t.y+p,!(h!==0&&x!==0&&St(h,x))&&(v=l*n-s*r,v!==0)))return T=Math.abs(v/2),S=r*p-n*c,B=S<0?(S-T)/v:(S+T)/v,S=s*c-l*p,k=S<0?(S-T)/v:(S+T)/v,{x:B,y:k}}d(re,"intersectLine");function St(e,t){return e*t>0}d(St,"sameSign");var br=re,wr=ae;function ae(e,t,a){var i=e.x,l=e.y,s=[],r=Number.POSITIVE_INFINITY,n=Number.POSITIVE_INFINITY;typeof t.forEach=="function"?t.forEach(function(v){r=Math.min(r,v.x),n=Math.min(n,v.y)}):(r=Math.min(r,t.x),n=Math.min(n,t.y));for(var c=i-e.width/2-r,p=l-e.height/2-n,h=0;h<t.length;h++){var x=t[h],b=t[h<t.length-1?h+1:0],m=br(e,a,{x:c+x.x,y:p+x.y},{x:c+b.x,y:p+b.y});m&&s.push(m)}return s.length?(s.length>1&&s.sort(function(v,T){var S=v.x-a.x,B=v.y-a.y,k=Math.sqrt(S*S+B*B),C=T.x-a.x,y=T.y-a.y,g=Math.sqrt(C*C+y*y);return k<g?-1:k===g?0:1}),s[0]):e}d(ae,"intersectPolygon");var mr=d((e,t)=>{var a=e.x,i=e.y,l=t.x-a,s=t.y-i,r=e.width/2,n=e.height/2,c,p;return Math.abs(s)*r>Math.abs(l)*n?(s<0&&(n=-n),c=s===0?0:n*l/s,p=n):(l<0&&(r=-r),c=r,p=l===0?0:r*s/l),{x:a+c,y:i+p}},"intersectRect"),Lr=mr,O={node:xr,circle:yr,ellipse:te,polygon:wr,rect:Lr},P=d(async(e,t,a,i)=>{const l=W();let s;const r=t.useHtmlLabels||G(l.flowchart.htmlLabels);a?s=a:s="node default";const n=e.insert("g").attr("class",s).attr("id",t.domId||t.id),c=n.insert("g").attr("class","label").attr("style",t.labelStyle);let p;t.labelText===void 0?p="":p=typeof t.labelText=="string"?t.labelText:t.labelText[0];const h=c.node();let x;t.labelType==="markdown"?x=Kt(c,Ct(yt(p),l),{useHtmlLabels:r,width:t.width||l.flowchart.wrappingWidth,classes:"markdown-node-label"},l):x=h.appendChild(j(Ct(yt(p),l),t.labelStyle,!1,i));let b=x.getBBox();const m=t.padding/2;if(G(l.flowchart.htmlLabels)){const v=x.children[0],T=F(x),S=v.getElementsByTagName("img");if(S){const B=p.replace(/<img[^>]*>/g,"").trim()==="";await Promise.all([...S].map(k=>new Promise(C=>{function y(){if(k.style.display="flex",k.style.flexDirection="column",B){const g=l.fontSize?l.fontSize:window.getComputedStyle(document.body).fontSize,f=5,w=parseInt(g,10)*f+"px";k.style.minWidth=w,k.style.maxWidth=w}else k.style.width="100%";C(k)}d(y,"setupImage"),setTimeout(()=>{k.complete&&y()}),k.addEventListener("error",y),k.addEventListener("load",y)})))}b=v.getBoundingClientRect(),T.attr("width",b.width),T.attr("height",b.height)}return r?c.attr("transform","translate("+-b.width/2+", "+-b.height/2+")"):c.attr("transform","translate(0, "+-b.height/2+")"),t.centerLabel&&c.attr("transform","translate("+-b.width/2+", "+-b.height/2+")"),c.insert("rect",":first-child"),{shapeSvg:n,bbox:b,halfPadding:m,label:c}},"labelHelper"),R=d((e,t)=>{const a=t.node().getBBox();e.width=a.width,e.height=a.height},"updateNodeBounds");function Z(e,t,a,i){return e.insert("polygon",":first-child").attr("points",i.map(function(l){return l.x+","+l.y}).join(" ")).attr("class","label-container").attr("transform","translate("+-t/2+","+a/2+")")}d(Z,"insertPolygonShape");var Sr=d(async(e,t)=>{t.useHtmlLabels||W().flowchart.htmlLabels||(t.centerLabel=!0);const{shapeSvg:i,bbox:l,halfPadding:s}=await P(e,t,"node "+t.classes,!0);L.info("Classes = ",t.classes);const r=i.insert("rect",":first-child");return r.attr("rx",t.rx).attr("ry",t.ry).attr("x",-l.width/2-s).attr("y",-l.height/2-s).attr("width",l.width+t.padding).attr("height",l.height+t.padding),R(t,r),t.intersect=function(n){return O.rect(t,n)},i},"note"),vr=Sr,At=d(e=>e?" "+e:"","formatClass"),X=d((e,t)=>`${t||"node default"}${At(e.classes)} ${At(e.class)}`,"getClassesFromNode"),Mt=d(async(e,t)=>{const{shapeSvg:a,bbox:i}=await P(e,t,X(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,r=l+s,n=[{x:r/2,y:0},{x:r,y:-r/2},{x:r/2,y:-r},{x:0,y:-r/2}];L.info("Question main (Circle)");const c=Z(a,r,r,n);return c.attr("style",t.style),R(t,c),t.intersect=function(p){return L.warn("Intersect called"),O.polygon(t,n,p)},a},"question"),Er=d((e,t)=>{const a=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),i=28,l=[{x:0,y:i/2},{x:i/2,y:0},{x:0,y:-i/2},{x:-i/2,y:0}];return a.insert("polygon",":first-child").attr("points",l.map(function(r){return r.x+","+r.y}).join(" ")).attr("class","state-start").attr("r",7).attr("width",28).attr("height",28),t.width=28,t.height=28,t.intersect=function(r){return O.circle(t,14,r)},a},"choice"),_r=d(async(e,t)=>{const{shapeSvg:a,bbox:i}=await P(e,t,X(t,void 0),!0),l=4,s=i.height+t.padding,r=s/l,n=i.width+2*r+t.padding,c=[{x:r,y:0},{x:n-r,y:0},{x:n,y:-s/2},{x:n-r,y:-s},{x:r,y:-s},{x:0,y:-s/2}],p=Z(a,n,s,c);return p.attr("style",t.style),R(t,p),t.intersect=function(h){return O.polygon(t,c,h)},a},"hexagon"),kr=d(async(e,t)=>{const{shapeSvg:a,bbox:i}=await P(e,t,void 0,!0),l=2,s=i.height+2*t.padding,r=s/l,n=i.width+2*r+t.padding,c=fr(t.directions,i,t),p=Z(a,n,s,c);return p.attr("style",t.style),R(t,p),t.intersect=function(h){return O.polygon(t,c,h)},a},"block_arrow"),Dr=d(async(e,t)=>{const{shapeSvg:a,bbox:i}=await P(e,t,X(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,r=[{x:-s/2,y:0},{x:l,y:0},{x:l,y:-s},{x:-s/2,y:-s},{x:0,y:-s/2}];return Z(a,l,s,r).attr("style",t.style),t.width=l+s,t.height=s,t.intersect=function(c){return O.polygon(t,r,c)},a},"rect_left_inv_arrow"),Nr=d(async(e,t)=>{const{shapeSvg:a,bbox:i}=await P(e,t,X(t),!0),l=i.width+t.padding,s=i.height+t.padding,r=[{x:-2*s/6,y:0},{x:l-s/6,y:0},{x:l+2*s/6,y:-s},{x:s/6,y:-s}],n=Z(a,l,s,r);return n.attr("style",t.style),R(t,n),t.intersect=function(c){return O.polygon(t,r,c)},a},"lean_right"),Tr=d(async(e,t)=>{const{shapeSvg:a,bbox:i}=await P(e,t,X(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,r=[{x:2*s/6,y:0},{x:l+s/6,y:0},{x:l-2*s/6,y:-s},{x:-s/6,y:-s}],n=Z(a,l,s,r);return n.attr("style",t.style),R(t,n),t.intersect=function(c){return O.polygon(t,r,c)},a},"lean_left"),Cr=d(async(e,t)=>{const{shapeSvg:a,bbox:i}=await P(e,t,X(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,r=[{x:-2*s/6,y:0},{x:l+2*s/6,y:0},{x:l-s/6,y:-s},{x:s/6,y:-s}],n=Z(a,l,s,r);return n.attr("style",t.style),R(t,n),t.intersect=function(c){return O.polygon(t,r,c)},a},"trapezoid"),Br=d(async(e,t)=>{const{shapeSvg:a,bbox:i}=await P(e,t,X(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,r=[{x:s/6,y:0},{x:l-s/6,y:0},{x:l+2*s/6,y:-s},{x:-2*s/6,y:-s}],n=Z(a,l,s,r);return n.attr("style",t.style),R(t,n),t.intersect=function(c){return O.polygon(t,r,c)},a},"inv_trapezoid"),Ir=d(async(e,t)=>{const{shapeSvg:a,bbox:i}=await P(e,t,X(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,r=[{x:0,y:0},{x:l+s/2,y:0},{x:l,y:-s/2},{x:l+s/2,y:-s},{x:0,y:-s}],n=Z(a,l,s,r);return n.attr("style",t.style),R(t,n),t.intersect=function(c){return O.polygon(t,r,c)},a},"rect_right_inv_arrow"),Or=d(async(e,t)=>{const{shapeSvg:a,bbox:i}=await P(e,t,X(t,void 0),!0),l=i.width+t.padding,s=l/2,r=s/(2.5+l/50),n=i.height+r+t.padding,c="M 0,"+r+" a "+s+","+r+" 0,0,0 "+l+" 0 a "+s+","+r+" 0,0,0 "+-l+" 0 l 0,"+n+" a "+s+","+r+" 0,0,0 "+l+" 0 l 0,"+-n,p=a.attr("label-offset-y",r).insert("path",":first-child").attr("style",t.style).attr("d",c).attr("transform","translate("+-l/2+","+-(n/2+r)+")");return R(t,p),t.intersect=function(h){const x=O.rect(t,h),b=x.x-t.x;if(s!=0&&(Math.abs(b)<t.width/2||Math.abs(b)==t.width/2&&Math.abs(x.y-t.y)>t.height/2-r)){let m=r*r*(1-b*b/(s*s));m!=0&&(m=Math.sqrt(m)),m=r-m,h.y-t.y>0&&(m=-m),x.y+=m}return x},a},"cylinder"),Rr=d(async(e,t)=>{const{shapeSvg:a,bbox:i,halfPadding:l}=await P(e,t,"node "+t.classes+" "+t.class,!0),s=a.insert("rect",":first-child"),r=t.positioned?t.width:i.width+t.padding,n=t.positioned?t.height:i.height+t.padding,c=t.positioned?-r/2:-i.width/2-l,p=t.positioned?-n/2:-i.height/2-l;if(s.attr("class","basic label-container").attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("x",c).attr("y",p).attr("width",r).attr("height",n),t.props){const h=new Set(Object.keys(t.props));t.props.borders&&(ut(s,t.props.borders,r,n),h.delete("borders")),h.forEach(x=>{L.warn(`Unknown node property ${x}`)})}return R(t,s),t.intersect=function(h){return O.rect(t,h)},a},"rect"),zr=d(async(e,t)=>{const{shapeSvg:a,bbox:i,halfPadding:l}=await P(e,t,"node "+t.classes,!0),s=a.insert("rect",":first-child"),r=t.positioned?t.width:i.width+t.padding,n=t.positioned?t.height:i.height+t.padding,c=t.positioned?-r/2:-i.width/2-l,p=t.positioned?-n/2:-i.height/2-l;if(s.attr("class","basic cluster composite label-container").attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("x",c).attr("y",p).attr("width",r).attr("height",n),t.props){const h=new Set(Object.keys(t.props));t.props.borders&&(ut(s,t.props.borders,r,n),h.delete("borders")),h.forEach(x=>{L.warn(`Unknown node property ${x}`)})}return R(t,s),t.intersect=function(h){return O.rect(t,h)},a},"composite"),Ar=d(async(e,t)=>{const{shapeSvg:a}=await P(e,t,"label",!0);L.trace("Classes = ",t.class);const i=a.insert("rect",":first-child"),l=0,s=0;if(i.attr("width",l).attr("height",s),a.attr("class","label edgeLabel"),t.props){const r=new Set(Object.keys(t.props));t.props.borders&&(ut(i,t.props.borders,l,s),r.delete("borders")),r.forEach(n=>{L.warn(`Unknown node property ${n}`)})}return R(t,i),t.intersect=function(r){return O.rect(t,r)},a},"labelRect");function ut(e,t,a,i){const l=[],s=d(n=>{l.push(n,0)},"addBorder"),r=d(n=>{l.push(0,n)},"skipBorder");t.includes("t")?(L.debug("add top border"),s(a)):r(a),t.includes("r")?(L.debug("add right border"),s(i)):r(i),t.includes("b")?(L.debug("add bottom border"),s(a)):r(a),t.includes("l")?(L.debug("add left border"),s(i)):r(i),e.attr("stroke-dasharray",l.join(" "))}d(ut,"applyNodePropertyBorders");var Mr=d((e,t)=>{let a;t.classes?a="node "+t.classes:a="node default";const i=e.insert("g").attr("class",a).attr("id",t.domId||t.id),l=i.insert("rect",":first-child"),s=i.insert("line"),r=i.insert("g").attr("class","label"),n=t.labelText.flat?t.labelText.flat():t.labelText;let c="";typeof n=="object"?c=n[0]:c=n,L.info("Label text abc79",c,n,typeof n=="object");const p=r.node().appendChild(j(c,t.labelStyle,!0,!0));let h={width:0,height:0};if(G(W().flowchart.htmlLabels)){const T=p.children[0],S=F(p);h=T.getBoundingClientRect(),S.attr("width",h.width),S.attr("height",h.height)}L.info("Text 2",n);const x=n.slice(1,n.length);let b=p.getBBox();const m=r.node().appendChild(j(x.join?x.join("<br/>"):x,t.labelStyle,!0,!0));if(G(W().flowchart.htmlLabels)){const T=m.children[0],S=F(m);h=T.getBoundingClientRect(),S.attr("width",h.width),S.attr("height",h.height)}const v=t.padding/2;return F(m).attr("transform","translate( "+(h.width>b.width?0:(b.width-h.width)/2)+", "+(b.height+v+5)+")"),F(p).attr("transform","translate( "+(h.width<b.width?0:-(b.width-h.width)/2)+", 0)"),h=r.node().getBBox(),r.attr("transform","translate("+-h.width/2+", "+(-h.height/2-v+3)+")"),l.attr("class","outer title-state").attr("x",-h.width/2-v).attr("y",-h.height/2-v).attr("width",h.width+t.padding).attr("height",h.height+t.padding),s.attr("class","divider").attr("x1",-h.width/2-v).attr("x2",h.width/2+v).attr("y1",-h.height/2-v+b.height+v).attr("y2",-h.height/2-v+b.height+v),R(t,l),t.intersect=function(T){return O.rect(t,T)},i},"rectWithTitle"),Fr=d(async(e,t)=>{const{shapeSvg:a,bbox:i}=await P(e,t,X(t,void 0),!0),l=i.height+t.padding,s=i.width+l/4+t.padding,r=a.insert("rect",":first-child").attr("style",t.style).attr("rx",l/2).attr("ry",l/2).attr("x",-s/2).attr("y",-l/2).attr("width",s).attr("height",l);return R(t,r),t.intersect=function(n){return O.rect(t,n)},a},"stadium"),Wr=d(async(e,t)=>{const{shapeSvg:a,bbox:i,halfPadding:l}=await P(e,t,X(t,void 0),!0),s=a.insert("circle",":first-child");return s.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",i.width/2+l).attr("width",i.width+t.padding).attr("height",i.height+t.padding),L.info("Circle main"),R(t,s),t.intersect=function(r){return L.info("Circle intersect",t,i.width/2+l,r),O.circle(t,i.width/2+l,r)},a},"circle"),Pr=d(async(e,t)=>{const{shapeSvg:a,bbox:i,halfPadding:l}=await P(e,t,X(t,void 0),!0),s=5,r=a.insert("g",":first-child"),n=r.insert("circle"),c=r.insert("circle");return r.attr("class",t.class),n.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",i.width/2+l+s).attr("width",i.width+t.padding+s*2).attr("height",i.height+t.padding+s*2),c.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",i.width/2+l).attr("width",i.width+t.padding).attr("height",i.height+t.padding),L.info("DoubleCircle main"),R(t,n),t.intersect=function(p){return L.info("DoubleCircle intersect",t,i.width/2+l+s,p),O.circle(t,i.width/2+l+s,p)},a},"doublecircle"),Yr=d(async(e,t)=>{const{shapeSvg:a,bbox:i}=await P(e,t,X(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,r=[{x:0,y:0},{x:l,y:0},{x:l,y:-s},{x:0,y:-s},{x:0,y:0},{x:-8,y:0},{x:l+8,y:0},{x:l+8,y:-s},{x:-8,y:-s},{x:-8,y:0}],n=Z(a,l,s,r);return n.attr("style",t.style),R(t,n),t.intersect=function(c){return O.polygon(t,r,c)},a},"subroutine"),Hr=d((e,t)=>{const a=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),i=a.insert("circle",":first-child");return i.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14),R(t,i),t.intersect=function(l){return O.circle(t,7,l)},a},"start"),Ft=d((e,t,a)=>{const i=e.insert("g").attr("class","node default").attr("id",t.domId||t.id);let l=70,s=10;a==="LR"&&(l=10,s=70);const r=i.append("rect").attr("x",-1*l/2).attr("y",-1*s/2).attr("width",l).attr("height",s).attr("class","fork-join");return R(t,r),t.height=t.height+t.padding/2,t.width=t.width+t.padding/2,t.intersect=function(n){return O.rect(t,n)},i},"forkJoin"),Kr=d((e,t)=>{const a=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),i=a.insert("circle",":first-child"),l=a.insert("circle",":first-child");return l.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14),i.attr("class","state-end").attr("r",5).attr("width",10).attr("height",10),R(t,l),t.intersect=function(s){return O.circle(t,7,s)},a},"end"),Xr=d((e,t)=>{var D;const a=t.padding/2,i=4,l=8;let s;t.classes?s="node "+t.classes:s="node default";const r=e.insert("g").attr("class",s).attr("id",t.domId||t.id),n=r.insert("rect",":first-child"),c=r.insert("line"),p=r.insert("line");let h=0,x=i;const b=r.insert("g").attr("class","label");let m=0;const v=(D=t.classData.annotations)==null?void 0:D[0],T=t.classData.annotations[0]?"\xAB"+t.classData.annotations[0]+"\xBB":"",S=b.node().appendChild(j(T,t.labelStyle,!0,!0));let B=S.getBBox();if(G(W().flowchart.htmlLabels)){const o=S.children[0],N=F(S);B=o.getBoundingClientRect(),N.attr("width",B.width),N.attr("height",B.height)}t.classData.annotations[0]&&(x+=B.height+i,h+=B.width);let k=t.classData.label;t.classData.type!==void 0&&t.classData.type!==""&&(W().flowchart.htmlLabels?k+="&lt;"+t.classData.type+"&gt;":k+="<"+t.classData.type+">");const C=b.node().appendChild(j(k,t.labelStyle,!0,!0));F(C).attr("class","classTitle");let y=C.getBBox();if(G(W().flowchart.htmlLabels)){const o=C.children[0],N=F(C);y=o.getBoundingClientRect(),N.attr("width",y.width),N.attr("height",y.height)}x+=y.height+i,y.width>h&&(h=y.width);const g=[];t.classData.members.forEach(o=>{const N=o.getDisplayDetails();let u=N.displayText;W().flowchart.htmlLabels&&(u=u.replace(/</g,"&lt;").replace(/>/g,"&gt;"));const E=b.node().appendChild(j(u,N.cssStyle?N.cssStyle:t.labelStyle,!0,!0));let I=E.getBBox();if(G(W().flowchart.htmlLabels)){const _=E.children[0],z=F(E);I=_.getBoundingClientRect(),z.attr("width",I.width),z.attr("height",I.height)}I.width>h&&(h=I.width),x+=I.height+i,g.push(E)}),x+=l;const f=[];if(t.classData.methods.forEach(o=>{const N=o.getDisplayDetails();let u=N.displayText;W().flowchart.htmlLabels&&(u=u.replace(/</g,"&lt;").replace(/>/g,"&gt;"));const E=b.node().appendChild(j(u,N.cssStyle?N.cssStyle:t.labelStyle,!0,!0));let I=E.getBBox();if(G(W().flowchart.htmlLabels)){const _=E.children[0],z=F(E);I=_.getBoundingClientRect(),z.attr("width",I.width),z.attr("height",I.height)}I.width>h&&(h=I.width),x+=I.height+i,f.push(E)}),x+=l,v){let o=(h-B.width)/2;F(S).attr("transform","translate( "+(-1*h/2+o)+", "+-1*x/2+")"),m=B.height+i}let w=(h-y.width)/2;return F(C).attr("transform","translate( "+(-1*h/2+w)+", "+(-1*x/2+m)+")"),m+=y.height+i,c.attr("class","divider").attr("x1",-h/2-a).attr("x2",h/2+a).attr("y1",-x/2-a+l+m).attr("y2",-x/2-a+l+m),m+=l,g.forEach(o=>{var u;F(o).attr("transform","translate( "+-h/2+", "+(-1*x/2+m+l/2)+")");const N=o==null?void 0:o.getBBox();m+=((u=N==null?void 0:N.height)!=null?u:0)+i}),m+=l,p.attr("class","divider").attr("x1",-h/2-a).attr("x2",h/2+a).attr("y1",-x/2-a+l+m).attr("y2",-x/2-a+l+m),m+=l,f.forEach(o=>{var u;F(o).attr("transform","translate( "+-h/2+", "+(-1*x/2+m)+")");const N=o==null?void 0:o.getBBox();m+=((u=N==null?void 0:N.height)!=null?u:0)+i}),n.attr("style",t.style).attr("class","outer title-state").attr("x",-h/2-a).attr("y",-(x/2)-a).attr("width",h+t.padding).attr("height",x+t.padding),R(t,n),t.intersect=function(o){return O.rect(t,o)},r},"class_box"),Wt={rhombus:Mt,composite:zr,question:Mt,rect:Rr,labelRect:Ar,rectWithTitle:Mr,choice:Er,circle:Wr,doublecircle:Pr,stadium:Fr,hexagon:_r,block_arrow:kr,rect_left_inv_arrow:Dr,lean_right:Nr,lean_left:Tr,trapezoid:Cr,inv_trapezoid:Br,rect_right_inv_arrow:Ir,cylinder:Or,start:Hr,end:Kr,note:vr,subroutine:Yr,fork:Ft,join:Ft,class_box:Xr},ht={},se=d(async(e,t,a)=>{let i,l;if(t.link){let s;W().securityLevel==="sandbox"?s="_top":t.linkTarget&&(s=t.linkTarget||"_blank"),i=e.insert("svg:a").attr("xlink:href",t.link).attr("target",s),l=await Wt[t.shape](i,t,a)}else l=await Wt[t.shape](e,t,a),i=l;return t.tooltip&&l.attr("title",t.tooltip),t.class&&l.attr("class","node default "+t.class),ht[t.id]=i,t.haveCallback&&ht[t.id].attr("class",ht[t.id].attr("class")+" clickable"),i},"insertNode"),Ur=d(e=>{const t=ht[e.id];L.trace("Transforming node",e.diff,e,"translate("+(e.x-e.width/2-5)+", "+e.width/2+")");const a=8,i=e.diff||0;return e.clusterNode?t.attr("transform","translate("+(e.x+i-e.width/2)+", "+(e.y-e.height/2-a)+")"):t.attr("transform","translate("+e.x+", "+e.y+")"),i},"positionNode");function Dt(e,t,a=!1){var b,m,v,T,S,B,k;const i=e;let l="default";(((b=i==null?void 0:i.classes)==null?void 0:b.length)||0)>0&&(l=((m=i==null?void 0:i.classes)!=null?m:[]).join(" ")),l=l+" flowchart-label";let s=0,r="",n;switch(i.type){case"round":s=5,r="rect";break;case"composite":s=0,r="composite",n=0;break;case"square":r="rect";break;case"diamond":r="question";break;case"hexagon":r="hexagon";break;case"block_arrow":r="block_arrow";break;case"odd":r="rect_left_inv_arrow";break;case"lean_right":r="lean_right";break;case"lean_left":r="lean_left";break;case"trapezoid":r="trapezoid";break;case"inv_trapezoid":r="inv_trapezoid";break;case"rect_left_inv_arrow":r="rect_left_inv_arrow";break;case"circle":r="circle";break;case"ellipse":r="ellipse";break;case"stadium":r="stadium";break;case"subroutine":r="subroutine";break;case"cylinder":r="cylinder";break;case"group":r="rect";break;case"doublecircle":r="doublecircle";break;default:r="rect"}const c=ge((v=i==null?void 0:i.styles)!=null?v:[]),p=i.label,h=(T=i.size)!=null?T:{width:0,height:0,x:0,y:0};return{labelStyle:c.labelStyle,shape:r,labelText:p,rx:s,ry:s,class:l,style:c.style,id:i.id,directions:i.directions,width:h.width,height:h.height,x:h.x,y:h.y,positioned:a,intersect:void 0,type:i.type,padding:(k=n!=null?n:(B=(S=nt())==null?void 0:S.block)==null?void 0:B.padding)!=null?k:0}}d(Dt,"getNodeFromBlock");async function ie(e,t,a){const i=Dt(t,a,!1);if(i.type==="group")return;const l=nt(),s=await se(e,i,{config:l}),r=s.node().getBBox(),n=a.getBlock(i.id);n.size={width:r.width,height:r.height,x:0,y:0,node:s},a.setBlock(n),s.remove()}d(ie,"calculateBlockSize");async function ne(e,t,a){const i=Dt(t,a,!0);if(a.getBlock(i.id).type!=="space"){const s=nt();await se(e,i,{config:s}),t.intersect=i==null?void 0:i.intersect,Ur(i)}}d(ne,"insertBlockPositioned");async function pt(e,t,a,i){for(const l of t)await i(e,l,a),l.children&&await pt(e,l.children,a,i)}d(pt,"performOperations");async function le(e,t,a){await pt(e,t,a,ie)}d(le,"calculateBlockSizes");async function ce(e,t,a){await pt(e,t,a,ne)}d(ce,"insertBlocks");async function oe(e,t,a,i,l){const s=new ve({multigraph:!0,compound:!0});s.setGraph({rankdir:"TB",nodesep:10,ranksep:10,marginx:8,marginy:8});for(const r of a)r.size&&s.setNode(r.id,{width:r.size.width,height:r.size.height,intersect:r.intersect});for(const r of t)if(r.start&&r.end){const n=i.getBlock(r.start),c=i.getBlock(r.end);if((n==null?void 0:n.size)&&(c==null?void 0:c.size)){const p=n.size,h=c.size,x=[{x:p.x,y:p.y},{x:p.x+(h.x-p.x)/2,y:p.y+(h.y-p.y)/2},{x:h.x,y:h.y}];ur(e,{v:r.start,w:r.end,name:r.id},{...r,arrowTypeEnd:r.arrowTypeEnd,arrowTypeStart:r.arrowTypeStart,points:x,classes:"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1"},void 0,"block",s,l),r.label&&(await or(e,{...r,label:r.label,labelStyle:"stroke: #333; stroke-width: 1.5px;fill:none;",arrowTypeEnd:r.arrowTypeEnd,arrowTypeStart:r.arrowTypeStart,points:x,classes:"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1"}),hr({...r,x:x[1].x,y:x[1].y},{originalPath:x}))}}}d(oe,"insertEdges");var jr=d(function(e,t){return t.db.getClasses()},"getClasses"),Vr=d(async function(e,t,a,i){const{securityLevel:l,block:s}=nt(),r=i.db;let n;l==="sandbox"&&(n=F("#i"+t));const c=l==="sandbox"?F(n.nodes()[0].contentDocument.body):F("body"),p=l==="sandbox"?c.select(`[id="${t}"]`):F(`[id="${t}"]`);sr(p,["point","circle","cross"],i.type,t);const x=r.getBlocks(),b=r.getBlocksFlat(),m=r.getEdges(),v=p.insert("g").attr("class","block");await le(v,x,r);const T=qt(r);if(await ce(v,x,r),await oe(v,m,b,r,t),T){const S=T,B=Math.max(1,Math.round(.125*(S.width/S.height))),k=S.height+B+10,C=S.width+10,{useMaxWidth:y}=s;xe(p,k,C,!!y),L.debug("Here Bounds",T,S),p.attr("viewBox",`${S.x-5} ${S.y-5} ${S.width+10} ${S.height+10}`)}},"draw"),Zr={draw:Vr,getClasses:jr},ta={parser:_e,db:Xe,renderer:Zr,styles:je};export{ta as diagram};
