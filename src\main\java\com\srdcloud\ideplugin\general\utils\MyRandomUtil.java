package com.srdcloud.ideplugin.general.utils;

import java.util.Random;

/**
 * <AUTHOR>
 * @date 2024/4/19
 */
public class MyRandomUtil {

    public static String generateRandomString(int length) {
        final Random random = new Random();
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            char c = (char) (random.nextInt(26) + 'a'); // 生成a-z的随机字符
            sb.append(c);
        }
        return sb.toString();
    }
}
