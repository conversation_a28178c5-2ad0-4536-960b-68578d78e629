package com.srdcloud.ideplugin.assistant.codechatNative.uicomponent;

import com.intellij.ui.components.JBScrollPane;
import com.intellij.util.ui.JBUI;

import java.awt.*;

/**
 * <AUTHOR> yangy
 * @create 2023/6/15 10:39
 * 可滚动面板wrapper：把一个UI控件包装成可滚动
 */
public class MyScrollPane extends JBScrollPane {

    public MyScrollPane(Component view, int vsbPolicy, int hsbPolicy) {
        super(view, vsbPolicy, hsbPolicy);
    }

    @Override
    public void updateUI() {
        setBorder(JBUI.Borders.empty());
        super.updateUI();
    }

    @Override
    public void setCorner(String key, Component corner) {
        setBorder(JBUI.Borders.empty());
        super.setCorner(key, corner);
    }
}
