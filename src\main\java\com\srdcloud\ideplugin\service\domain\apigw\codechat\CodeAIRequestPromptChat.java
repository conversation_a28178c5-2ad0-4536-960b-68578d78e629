package com.srdcloud.ideplugin.service.domain.apigw.codechat;

import com.srdcloud.ideplugin.remote.domain.WorkItem.WorkItemInfo;
import com.srdcloud.ideplugin.webview.codechat.relatedfile.RelatedFile;

import java.util.List;

/**
 * @author: yangy
 * @date: 2023/7/23 20:23
 * @Desc
 */
public class CodeAIRequestPromptChat {

    private String content;

    private String role;

    private List<RelatedFile> files;

    private List<WorkItemInfo> workItems;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public void setFiles(List<RelatedFile> files) {
        this.files = files;
    }

    public List<RelatedFile> getFiles() {
        return files;
    }

    public List<WorkItemInfo> getWorkItems() {
        return workItems;
    }

    public void setWorkItems(List<WorkItemInfo> workItems) {
        this.workItems = workItems;
    }
}
