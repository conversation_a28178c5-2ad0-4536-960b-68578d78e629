package com.srdcloud.ideplugin.service.domain.apigw.codechat.history;

import com.srdcloud.ideplugin.general.utils.TimeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: yangy
 * @date: 2024/5/13 11:26
 * @Desc
 */
public class DialogBaseInfo implements Serializable {

    private static final Logger logger = LoggerFactory.getLogger(DialogBaseInfo.class);


    /**
     * 对话ID
     */
    private String dialogId;

    /**
     * 对话标题
     */
    private String title;

    /**
     * 产生本对话的时间，格式为yyyy-MM-dd HH:mm:ss
     */
    private String createTime;

    /**
     * 会话最近更新时间，格式为yyyy-MM-dd HH:mm:ss
     */
    private String updateTime;
    /**
     * 原子能力标识
     */
    private String subService;

    /**
     * 标识本地会话，不进行反查
     */
    private boolean isNewConversation;

    public DialogBaseInfo() {
    }

    public DialogBaseInfo(String dialogId, String title, String createTime, String updateTime, String subService, boolean isNewConversation) {
        this.dialogId = dialogId;
        this.title = title;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.subService = subService;
        this.isNewConversation = isNewConversation;
    }

    public boolean isNewConversation() {
        return isNewConversation;
    }

    public void setNewConversation(boolean newConversation) {
        isNewConversation = newConversation;
    }

    public String getDialogId() {
        return dialogId;
    }

    public void setDialogId(String dialogId) {
        this.dialogId = dialogId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getSubService() {
        return subService;
    }

    public void setSubService(String subService) {
        this.subService = subService;
    }

    @Override
    public String toString() {
        return "DialogBaseInfo{" +
                "dialogId='" + dialogId + '\'' +
                ", title='" + title + '\'' +
                ", createTime='" + createTime + '\'' +
                ", updateTime='" + updateTime + '\'' +
                ", subService='" + subService + '\'' +
                '}';
    }

    /**
     * 获取用于排序的时间
     */
    public Date getSortDateTime() {
        Date date = null;
        try {
            date = TimeUtil.parseTimeStrToDate(createTime);
        } catch (Exception e) {
            logger.error("[cf] getSortDateTime error,DialogBaseInfo:{},e:{}", this.toString(), e.getMessage());
        }
        return date;
    }
}
