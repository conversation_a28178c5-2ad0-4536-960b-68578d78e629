package com.srdcloud.ideplugin.general.config;

import com.intellij.DynamicBundle;
import org.jetbrains.annotations.Nls;
import org.jetbrains.annotations.NonNls;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.PropertyKey;

import java.util.function.Supplier;

/**
 * 测试环境动态配置
 */
public class PipelineBundleTest extends DynamicBundle {

    @NonNls
    private static final String BUNDLE = "bundles.pipeline-test";
    private static final PipelineBundleTest INSTANCE = new PipelineBundleTest();

    public PipelineBundleTest() {
        super(BUNDLE);
    }

    /**
     * 获取配置项值
     */
    @NotNull
    public static @Nls String message(@NotNull @PropertyKey(resourceBundle = BUNDLE) String key, Object @NotNull ... params) {
        return INSTANCE.getMessage(key, params);
    }
}
