package com.srdcloud.ideplugin.webview.codechat.composer.response;

import com.srdcloud.ideplugin.webview.base.domain.WebViewCommand;

public class DiffStatusChangedResponse extends WebViewCommand {

    private DiffStatusChangedResponseData data;

    public DiffStatusChangedResponse(String command, DiffStatusChangedResponseData data) {
        super(command);
        this.data = data;
    }

    public DiffStatusChangedResponseData getData() {
        return data;
    }

    public void setData(DiffStatusChangedResponseData data) {
        this.data = data;
    }
}
