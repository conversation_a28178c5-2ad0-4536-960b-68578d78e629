package com.srdcloud.ideplugin.webview.codechat.getideutils.response;



import com.srdcloud.ideplugin.webview.codechat.getideutils.domain.DirItem;
import com.srdcloud.ideplugin.webview.codechat.getideutils.domain.FileNode;
import com.srdcloud.ideplugin.webview.codechat.getideutils.domain.RecentlyUsedFile;

import java.util.List;

public class GetIdeUtilsResponseData {
    private String reqType;
    private List<RecentlyUsedFile> recentlyUsedFiles;
    private FileNode fileTree;
    private List<DirItem> folderList;
    private String error;
    private int currentIndex;

    public GetIdeUtilsResponseData(String reqType, List<RecentlyUsedFile> recentlyUsedFiles, FileNode fileTree, List<DirItem> folderList, String error, int currentIndex) {
        this.reqType = reqType;
        this.recentlyUsedFiles = recentlyUsedFiles;
        this.fileTree = fileTree;
        this.folderList = folderList;
        this.error = error;
        this.currentIndex = currentIndex;
    }

    public String getReqType() {
        return reqType;
    }

    public void setReqType(String reqType) {
        this.reqType = reqType;
    }

    public List<RecentlyUsedFile> getRecentlyUsedFiles() {
        return recentlyUsedFiles;
    }

    public void setRecentlyUsedFiles(List<RecentlyUsedFile> recentlyUsedFiles) {
        this.recentlyUsedFiles = recentlyUsedFiles;
    }

    public FileNode getFileTree() {
        return fileTree;
    }

    public void setFileTree(FileNode fileTree) {
        this.fileTree = fileTree;
    }

    public List<DirItem> getFolderList() {
        return folderList;
    }

    public void setFolderList(List<DirItem> folderList) {
        this.folderList = folderList;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }
}
