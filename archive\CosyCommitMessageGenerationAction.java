package com.srdcloud.ideplugin.aicommit;


import com.alibabacloud.intellij.cosy.chat.model.GenerateCommitMsgParam;
import com.alibabacloud.intellij.cosy.common.BuildFeature;
import com.alibabacloud.intellij.cosy.common.CosyConfig;
import com.alibabacloud.intellij.cosy.common.CosySetting;
import com.alibabacloud.intellij.cosy.common.VpcFeature;
import com.alibabacloud.intellij.cosy.constants.ScenarioConstants;
import com.alibabacloud.intellij.cosy.core.Cosy;
import com.alibabacloud.intellij.cosy.core.lsp.model.model.AuthStatus;
import com.alibabacloud.intellij.cosy.core.lsp.model.params.GenerateCommitMsgAnswerParams;
import com.alibabacloud.intellij.cosy.core.lsp.model.params.GenerateCommitMsgFinishParams;
import com.alibabacloud.intellij.cosy.core.lsp.model.params.GenerateCommitMsgResult;
import com.alibabacloud.intellij.cosy.search.enums.TrackEventTypeEnum;
import com.alibabacloud.intellij.cosy.service.CosyService;
import com.alibabacloud.intellij.cosy.service.TelemetryService;
import com.alibabacloud.intellij.cosy.service.UserAuthService;
import com.alibabacloud.intellij.cosy.service.impl.CosyServiceImpl;
import com.alibabacloud.intellij.cosy.ui.config.CosyPersistentSetting;
import com.alibabacloud.intellij.cosy.ui.notifications.NotificationFactory;
import com.alibabacloud.intellij.cosy.ui.search.I18NConstant;
import com.alibabacloud.intellij.cosy.ui.search.location.CosyBundle;
import com.alibabacloud.intellij.cosy.util.AuthStatusUtil;
import com.alibabacloud.intellij.cosy.util.ErrorMessageHandler;
import com.alibabacloud.intellij.cosy.util.LoginUtil;
import com.alibabacloud.intellij.cosy.util.ThreadUtil;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.UpdateInBackground;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.application.ModalityState;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.diff.impl.patch.FilePatch;
import com.intellij.openapi.diff.impl.patch.IdeaTextPatchBuilder;
import com.intellij.openapi.diff.impl.patch.PatchHunk;
import com.intellij.openapi.diff.impl.patch.TextFilePatch;
import com.intellij.openapi.diff.impl.patch.UnifiedDiffWriter;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.progress.ProgressIndicator;
import com.intellij.openapi.progress.Task;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vcs.FilePath;
import com.intellij.openapi.vcs.VcsDataKeys;
import com.intellij.openapi.vcs.VcsException;
import com.intellij.openapi.vcs.changes.Change;
import com.intellij.openapi.vcs.changes.CommitContext;
import com.intellij.openapi.vcs.changes.ContentRevision;
import com.intellij.openapi.vcs.changes.CurrentContentRevision;
import com.intellij.openapi.vcs.ui.CommitMessage;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.project.ProjectKt;
import com.intellij.vcs.commit.AbstractCommitWorkflowHandler;
import com.intellij.vcs.log.impl.TimedVcsCommitImpl;
import git4idea.GitCommit;
import git4idea.history.GitHistoryUtils;
import git4idea.repo.GitRepository;
import git4idea.repo.GitRepositoryManager;
import icons.CommonIcons;
import icons.LingmaIcons;
import java.io.IOException;
import java.io.StringWriter;
import java.lang.reflect.InvocationTargetException;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import javax.swing.SwingUtilities;
import lombok.Generated;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

public class CosyCommitMessageGenerationAction extends AnAction implements UpdateInBackground {
    private static Logger log = Logger.getInstance(CosyCommitMessageGenerationAction.class);
    private static final ScheduledExecutorService SCHEDULED_EXECUTOR = Executors.newSingleThreadScheduledExecutor();
    private static final Long MAX_PATCH_LEN = 70000L;
    private static final int MAX_FILE = 50;
    private static final int COSY_GENERATE_TIMEOUT = 20;
    private static final int MAX_SINGLE_LINE_LEN = 300;
    private CosyService cosyService;
    public static final Map<String, Project> COMMIT_MESSAGE_REQUEST_TO_PROJECT = new ConcurrentHashMap();
    public static final Map<String, String> PROJECT_TO_COMMIT_MESSAGE_REQUEST = new ConcurrentHashMap();
    public static final Map<String, CommitMessageInstance> REQUEST_COMMIT_MESSAGE = new ConcurrentHashMap();

    public CosyCommitMessageGenerationAction() {
        super(CosyBundle.message("cosy.plugin.simple.name", new Object[0]), "", LingmaIcons.StatusBarLocalIcon);
    }

    public void update(@NotNull AnActionEvent anActionEvent) {
        if (anActionEvent == null) {
            $$$reportNull$$$0(0);
        }

        if (CosyConfig.getFeature(BuildFeature.VPC_ENABLED.getKey(), false)) {
            List<String> features = new ArrayList();
            List<String> features = CosyConfig.getFeature(BuildFeature.VPC_ENABLED_FEATURES.getKey(), features);
            if (!CollectionUtils.isNotEmpty(features) || !features.contains(VpcFeature.FEATURE_COMMIT_MESSAGE.getFeature())) {
                anActionEvent.getPresentation().setVisible(false);
            }
        }
    }

    public void actionPerformed(@NotNull AnActionEvent anActionEvent) {
        if (anActionEvent == null) {
            $$$reportNull$$$0(1);
        }

        if (CommonIcons.stopIcon.equals(anActionEvent.getPresentation().getIcon())) {
            this.stopAnswer(anActionEvent.getProject(), anActionEvent);
        } else {
            anActionEvent.getPresentation().setText(CosyBundle.message("chat.answer.btn.stop", new Object[0]));
            anActionEvent.getPresentation().setIcon(CommonIcons.stopIcon);
            CommitMessage commitMessage = (CommitMessage)VcsDataKeys.COMMIT_MESSAGE_CONTROL.getData(anActionEvent.getDataContext());
            this.chatAsk(anActionEvent.getProject(), commitMessage, anActionEvent);
        }
    }

    private List<String> getDiff(AnActionEvent anActionEvent) {
        Object workflowHandler = anActionEvent.getDataContext().getData(VcsDataKeys.COMMIT_WORKFLOW_HANDLER);
        if (workflowHandler == null) {
            return new ArrayList();
        } else {
            List<Change> changeList = new ArrayList();
            if (workflowHandler instanceof AbstractCommitWorkflowHandler) {
                List<Change> includedChanges = ((AbstractCommitWorkflowHandler)workflowHandler).getUi().getIncludedChanges();
                if (CollectionUtils.isNotEmpty(includedChanges)) {
                    changeList.addAll(includedChanges);
                }

                List<FilePath> filePaths = ((AbstractCommitWorkflowHandler)anActionEvent.getDataContext().getData(VcsDataKeys.COMMIT_WORKFLOW_HANDLER)).getUi().getIncludedUnversionedFiles();
                log.debug("filePaths is " + filePaths + ",size is " + filePaths.size());
                Iterator var6;
                if (CollectionUtils.isNotEmpty(filePaths)) {
                    var6 = filePaths.iterator();

                    while(var6.hasNext()) {
                        FilePath filePath = (FilePath)var6.next();
                        Change change = new Change((ContentRevision)null, new CurrentContentRevision(filePath));
                        changeList.add(change);
                    }
                }

                List<String> totalCommitLines = new ArrayList();
                AtomicLong totalLength = new AtomicLong(0L);
                var6 = changeList.iterator();

                while(var6.hasNext()) {
                    Change change = (Change)var6.next();

                    try {
                        Boolean isValid = this.checkIfValidChange(change);
                        if (BooleanUtils.isTrue(isValid)) {
                            List<FilePatch> patches = IdeaTextPatchBuilder.buildPatch(anActionEvent.getProject(), Arrays.asList(change), Path.of(anActionEvent.getProject().getBasePath()), false, false);
                            if (CollectionUtils.isEmpty(patches)) {
                                String fileName = change.getAfterRevision() != null ? change.getAfterRevision().getFile().getName() : (change.getBeforeRevision() != null ? change.getBeforeRevision().getFile().getName() : "");
                                if (!StringUtils.isBlank(fileName)) {
                                    totalCommitLines.add(fileName + " change mod");
                                    if (totalLength.get() >= MAX_PATCH_LEN || totalCommitLines.size() >= 50) {
                                        break;
                                    }
                                }
                            } else {
                                Boolean isValidChange = this.checkIfChangeLengthTooLarge(patches, totalLength);
                                if (BooleanUtils.isTrue(isValidChange)) {
                                    StringWriter writer = new StringWriter();

                                    try {
                                        UnifiedDiffWriter.write(anActionEvent.getProject(), ProjectKt.getStateStore(anActionEvent.getProject()).getProjectBasePath(), patches, writer, "\n", (CommitContext)null, List.of());
                                        if (StringUtils.isNotBlank(writer.toString())) {
                                            totalCommitLines.add(writer.toString());
                                        }

                                        if (totalCommitLines.size() >= 50 || totalLength.get() >= MAX_PATCH_LEN) {
                                            break;
                                        }
                                    } finally {
                                        writer.close();
                                    }
                                }
                            }
                        }
                    } catch (VcsException var17) {
                        VcsException e = var17;
                        log.warn("get changeList error", e);
                    } catch (IOException var18) {
                        IOException e = var18;
                        log.warn("get changeList error", e);
                    }
                }

                return totalCommitLines;
            } else {
                return new ArrayList();
            }
        }
    }

    private Boolean checkIfValidChange(Change change) {
        Boolean isBinary = change.getAfterRevision() != null ? change.getAfterRevision().getFile().getFileType().isBinary() : change.getBeforeRevision().getFile().getFileType().isBinary();
        if (isBinary) {
            return false;
        } else {
            ContentRevision contentRevision = change.getAfterRevision() != null ? change.getAfterRevision() : change.getBeforeRevision();
            if (contentRevision == null) {
                return false;
            } else {
                String content = null;

                try {
                    content = contentRevision.getContent();
                } catch (VcsException var6) {
                    VcsException e = var6;
                    log.warn("get content error", e);
                }

                return StringUtils.isNotBlank(content) && !content.contains("\n") && !content.contains("\r") && content.length() > 300 ? false : true;
            }
        }
    }

    private Boolean checkIfChangeLengthTooLarge(List<FilePatch> patches, AtomicLong totalLength) {
        Long lengthOfChange = 0L;
        Iterator var4 = patches.iterator();

        while(var4.hasNext()) {
            FilePatch patch = (FilePatch)var4.next();
            if (!(patch instanceof TextFilePatch)) {
                return false;
            }

            List<PatchHunk> patchHunks = ((TextFilePatch)patch).getHunks();
            if (CollectionUtils.isEmpty(patchHunks)) {
                return false;
            }

            if (patchHunks.size() == 1) {
                PatchHunk patchHunk = (PatchHunk)patchHunks.get(0);
                if (patchHunk.getLines().size() == 1 && patchHunk.getText().length() > 300) {
                    return false;
                }
            }

            PatchHunk patchHunk;
            for(Iterator var9 = patchHunks.iterator(); var9.hasNext(); lengthOfChange = lengthOfChange + (long)patchHunk.getText().length()) {
                patchHunk = (PatchHunk)var9.next();
            }
        }

        if (totalLength.get() + lengthOfChange > MAX_PATCH_LEN) {
            return false;
        } else {
            totalLength.addAndGet(lengthOfChange);
            return true;
        }
    }

    private void chatAsk(final Project project, final CommitMessage commitMessage, final AnActionEvent anActionEvent) {
        final String requestId = UUID.randomUUID().toString();
        this.initGlobalVariable(project, requestId, commitMessage, anActionEvent);
        TelemetryService.getInstance().telemetryGenerateCommitMsg(project, TrackEventTypeEnum.COMMIT_MESSAGE_TRIGGER, requestId);
        (new Task.Backgroundable(project, I18NConstant.GENERATE_TIP) {
            private boolean success = false;

            public void run(@NotNull ProgressIndicator indicator) {
                if (indicator == null) {
                    $$$reportNull$$$0(0);
                }

                try {
                    CosyCommitMessageGenerationAction.this.doChatAsk(project, requestId, commitMessage, anActionEvent);
                } catch (Exception var3) {
                    Exception e = var3;
                    CosyCommitMessageGenerationAction.this.afterGenerateCommitMsg(anActionEvent, project, requestId);
                    CosyCommitMessageGenerationAction.log.warn("generate commit message, errorMsg is " + e.getMessage());
                }

                this.success = true;
            }

            public void onFinished() {
                if (this.success) {
                }

            }
        }).queue();
        this.scheduleChatTimeout(requestId, project, anActionEvent);
    }

    private void doChatAsk(Project project, String requestId, CommitMessage commitMessage, AnActionEvent anActionEvent) {
        if (this.cosyService == null) {
            this.cosyService = new CosyServiceImpl();
        }

        if (!Cosy.INSTANCE.checkCosy(project, true)) {
            ApplicationManager.getApplication().invokeLater(() -> {
                NotificationFactory.showWarnNotification(project, I18NConstant.COSY_RESTARTING);
            });
            this.afterGenerateCommitMsg(anActionEvent, project, requestId);
        } else {
            AuthStatus status = LoginUtil.getAuthStatus(project);
            if (status != null && !status.isAllow()) {
                if (AuthStatusUtil.isNotLogin(status)) {
                    log.warn("Not login when chatting, show welcome panel");
                    if (!UserAuthService.getInstance().requireLogin(project)) {
                        this.afterGenerateCommitMsg(anActionEvent, project, requestId);
                        return;
                    }
                }

                String message = AuthStatusUtil.generateAuthMessage(status);
                log.warn("Login status not allowed");
                if (StringUtils.isNotBlank(message)) {
                    ApplicationManager.getApplication().invokeLater(() -> {
                        NotificationFactory.showWarnNotification(project, message);
                    });
                }

                this.afterGenerateCommitMsg(anActionEvent, project, requestId);
            } else {
                SwingUtilities.invokeLater(() -> {
                    commitMessage.setText("");
                    anActionEvent.getPresentation().setIcon(CommonIcons.stopIcon);
                    anActionEvent.getPresentation().setText(CosyBundle.message("chat.answer.btn.stop", new Object[0]));
                });
                GenerateCommitMsgParam generateCommitMsgParam = new GenerateCommitMsgParam();
                generateCommitMsgParam.setRequestId(requestId);
                generateCommitMsgParam.setStream(true);
                List<String> commitMessages = this.getLatestCommitMessages(anActionEvent.getProject());
                ApplicationManager.getApplication().invokeLater(() -> {
                    List<String> diffList = this.getDiff(anActionEvent);
                    if (CollectionUtils.isEmpty(diffList)) {
                        NotificationFactory.showWarnNotification(project, CosyBundle.message("commit.message.invalid.hint", new Object[0]));
                        this.afterGenerateCommitMsg(anActionEvent, project, requestId);
                    } else {
                        generateCommitMsgParam.setCommitMessages(commitMessages);
                        generateCommitMsgParam.setCodeDiffs(diffList);
                        CosySetting setting = CosyPersistentSetting.getInstance().getState();
                        String preferredLanguage = setting == null ? "" : setting.getCommitMessageLanguage();
                        generateCommitMsgParam.setPreferredLanguage(preferredLanguage);
                        ThreadUtil.execute(() -> {
                            GenerateCommitMsgResult generateCommitMsgResult = Cosy.INSTANCE.getLanguageService(project).generateCommitMsg(generateCommitMsgParam, 10000L);
                            if (generateCommitMsgResult != null && BooleanUtils.isTrue(generateCommitMsgResult.getIsSuccess())) {
                                log.info(String.format("generate commit message result = %s", generateCommitMsgResult.getIsSuccess()));
                            } else {
                                log.warn("generate commit message error, requestId is " + requestId + ", result is " + generateCommitMsgResult);
                                this.afterGenerateCommitMsg(anActionEvent, project, requestId);
                            }

                        });
                    }
                }, ModalityState.defaultModalityState());
            }
        }
    }

    private void initGlobalVariable(Project project, String requestId, CommitMessage commitMessage, AnActionEvent anActionEvent) {
        String curRequestId = (String)PROJECT_TO_COMMIT_MESSAGE_REQUEST.get(project.getName());
        if (curRequestId != null) {
            REQUEST_COMMIT_MESSAGE.remove(curRequestId);
            COMMIT_MESSAGE_REQUEST_TO_PROJECT.remove(curRequestId);
            PROJECT_TO_COMMIT_MESSAGE_REQUEST.remove(project.getName());
        }

        COMMIT_MESSAGE_REQUEST_TO_PROJECT.put(requestId, project);
        PROJECT_TO_COMMIT_MESSAGE_REQUEST.put(project.getName(), requestId);
        CommitMessageInstance commitMessageInstance = new CommitMessageInstance(commitMessage, anActionEvent);
        REQUEST_COMMIT_MESSAGE.put(requestId, commitMessageInstance);
    }

    public Boolean updateAnswer(GenerateCommitMsgAnswerParams generateCommitMsgAnswerParams) {
        if (generateCommitMsgAnswerParams != null && generateCommitMsgAnswerParams.getText() != null) {
            ThreadUtil.execute(() -> {
                CommitMessageInstance commitMessageInstance = (CommitMessageInstance)REQUEST_COMMIT_MESSAGE.get(generateCommitMsgAnswerParams.getRequestId());
                if (commitMessageInstance == null) {
                    log.debug("commit message commitMessageInstance null.");
                } else {
                    Project project = (Project)COMMIT_MESSAGE_REQUEST_TO_PROJECT.get(generateCommitMsgAnswerParams.getRequestId());
                    CommitMessage commitMessage = commitMessageInstance.getCommitMessage();
                    if (project != null && commitMessage != null) {
                        try {
                            SwingUtilities.invokeAndWait(() -> {
                                String var10001 = commitMessage.getText();
                                commitMessage.setText(var10001 + generateCommitMsgAnswerParams.getText());
                            });
                        } catch (InterruptedException var5) {
                            InterruptedException e = var5;
                            throw new RuntimeException(e);
                        } catch (InvocationTargetException var6) {
                            InvocationTargetException ex = var6;
                            throw new RuntimeException(ex);
                        }
                    } else {
                        log.debug("Cannot find project in commit message processor by request_id " + generateCommitMsgAnswerParams.getRequestId());
                    }
                }
            });
            return true;
        } else {
            log.warn("commit message answer params contain null.");
            return false;
        }
    }

    private void scheduleChatTimeout(String requestId, Project project, AnActionEvent anActionEvent) {
        SCHEDULED_EXECUTOR.schedule(() -> {
            if (COMMIT_MESSAGE_REQUEST_TO_PROJECT.get(requestId) != null) {
                CommitMessageInstance commitMessageInstance = (CommitMessageInstance)REQUEST_COMMIT_MESSAGE.get(requestId);
                if (commitMessageInstance != null) {
                    CommitMessage commitMessage = commitMessageInstance.getCommitMessage();
                    if (commitMessage != null && StringUtils.isBlank(commitMessage.getText())) {
                        NotificationFactory.showWarnNotification(project, I18NConstant.CHAT_ANSWER_TIMEOUT);
                        this.afterGenerateCommitMsg(anActionEvent, project, requestId);
                    }

                }
            }
        }, 20L, TimeUnit.SECONDS);
    }

    private void stopAnswer(Project project, AnActionEvent anActionEvent) {
        anActionEvent.getPresentation().setText(CosyBundle.message("cosy.plugin.simple.name", new Object[0]));
        anActionEvent.getPresentation().setIcon(LingmaIcons.StatusBarLocalIcon);
        String requestId = (String)PROJECT_TO_COMMIT_MESSAGE_REQUEST.get(project.getName());
        if (requestId != null) {
            TelemetryService.getInstance().telemetryGenerateCommitMsg(project, TrackEventTypeEnum.COMMIT_MESSAGE_STOP, requestId);
            this.afterGenerateCommitMsg(anActionEvent, project, requestId);
        }
    }

    public Boolean finishAnswer(GenerateCommitMsgFinishParams generateCommitMsgFinishParams) {
        Project project = (Project)COMMIT_MESSAGE_REQUEST_TO_PROJECT.get(generateCommitMsgFinishParams.getRequestId());
        if (project == null) {
            log.debug("commit message project null.");
            return false;
        } else {
            CommitMessageInstance commitMessageInstance = (CommitMessageInstance)REQUEST_COMMIT_MESSAGE.get(generateCommitMsgFinishParams.getRequestId());
            if (commitMessageInstance == null) {
                log.debug("commit message commitMessageInstance null.");
                return false;
            } else {
                AnActionEvent anActionEvent = commitMessageInstance.getAnActionEvent();
                if (generateCommitMsgFinishParams.getStatusCode() == 408) {
                    NotificationFactory.showWarnNotification(project, I18NConstant.CHAT_ANSWER_TIMEOUT);
                } else if (generateCommitMsgFinishParams.getStatusCode() == 403) {
                    String errorMsg = ErrorMessageHandler.convertErrorMessage(project, generateCommitMsgFinishParams.getRequestId(), generateCommitMsgFinishParams.getReason(), ScenarioConstants.SCENARIO_GENERATE_COMMIT_MSG);
                    NotificationFactory.showWarnNotification(project, errorMsg);
                }

                this.afterGenerateCommitMsg(anActionEvent, project, generateCommitMsgFinishParams.getRequestId());
                return true;
            }
        }
    }

    private void afterGenerateCommitMsg(AnActionEvent anActionEvent, Project project, String requestId) {
        COMMIT_MESSAGE_REQUEST_TO_PROJECT.remove(requestId);
        REQUEST_COMMIT_MESSAGE.remove(requestId);
        PROJECT_TO_COMMIT_MESSAGE_REQUEST.remove(project.getName());
        if (anActionEvent != null) {
            SwingUtilities.invokeLater(() -> {
                anActionEvent.getPresentation().setText(CosyBundle.message("cosy.plugin.simple.name", new Object[0]));
                anActionEvent.getPresentation().setIcon(LingmaIcons.StatusBarLocalIcon);
            });
        }

    }

    private List<String> getLatestCommitMessages(Project project) {
        List<String> commitMessageList = new ArrayList();
        VirtualFile projectFile = project.getProjectFile();
        if (projectFile == null) {
            VirtualFile[] selectedFiles = FileEditorManager.getInstance(project).getSelectedFiles();
            if (selectedFiles != null && selectedFiles.length > 0) {
                projectFile = selectedFiles[0];
            }
        }

        GitRepository repository = null;
        if (projectFile != null) {
            repository = (GitRepository)GitRepositoryManager.getInstance(project).getRepositoryForFile(projectFile);
        } else {
            List<GitRepository> repositories = GitRepositoryManager.getInstance(project).getRepositories();
            if (CollectionUtils.isNotEmpty(repositories)) {
                repository = (GitRepository)repositories.get(0);
            }
        }

        if (repository == null) {
            return commitMessageList;
        } else {
            VirtualFile root = repository.getRoot();
            if (root == null) {
                return commitMessageList;
            } else {
                try {
                    List<GitCommit> commits = GitHistoryUtils.history(project, root, new String[]{"--max-count=3"});
                    if (CollectionUtils.isEmpty(commits)) {
                        return commitMessageList;
                    }

                    commits = (List)commits.stream().sorted(Comparator.comparing(TimedVcsCommitImpl::getTimestamp).reversed()).limit(3L).collect(Collectors.toList());
                    Iterator var7 = commits.iterator();

                    while(var7.hasNext()) {
                        GitCommit commit = (GitCommit)var7.next();
                        String commitMessage = commit.getFullMessage();
                        commitMessageList.add(commitMessage);
                    }
                } catch (VcsException var10) {
                    VcsException e = var10;
                    log.warn("getLatestCommitMessages error, errorMsg is " + e.getMessage());
                }

                return commitMessageList;
            }
        }
    }

    public class CommitMessageInstance {
        private CommitMessage commitMessage;
        private AnActionEvent anActionEvent;

        public CommitMessageInstance() {
        }

        public CommitMessageInstance(CommitMessage commitMessage, AnActionEvent anActionEvent) {
            this.commitMessage = commitMessage;
            this.anActionEvent = anActionEvent;
        }

        @Generated
        public CommitMessage getCommitMessage() {
            return this.commitMessage;
        }

        @Generated
        public AnActionEvent getAnActionEvent() {
            return this.anActionEvent;
        }

        @Generated
        public void setCommitMessage(CommitMessage commitMessage) {
            this.commitMessage = commitMessage;
        }

        @Generated
        public void setAnActionEvent(AnActionEvent anActionEvent) {
            this.anActionEvent = anActionEvent;
        }
    }
}

