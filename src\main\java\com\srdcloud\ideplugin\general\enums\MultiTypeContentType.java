package com.srdcloud.ideplugin.general.enums;

/**
 * @author: yangy
 * @date: 2024/5/15 15:20
 * @Desc
 */
public enum MultiTypeContentType {

    TEXT("text","内容为文本"),

    KNOWLEDGE_BASE("knowledge_base","内容为知识库ID"),

    QUOTE("quote","内容为知识库引用内容"),

    IMAGE_URL("image_url","内容为上传的图片地址");

    private String name;
    private String description;

    private MultiTypeContentType(String name, String description) {
        this.name = name;
        this.description = description;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
