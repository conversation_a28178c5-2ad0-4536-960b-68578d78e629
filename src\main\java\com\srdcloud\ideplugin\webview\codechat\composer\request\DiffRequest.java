package com.srdcloud.ideplugin.webview.codechat.composer.request;

import com.srdcloud.ideplugin.webview.base.domain.WebViewCommand;

public class DiffRequest extends WebViewCommand {

    private DiffRequestData data;

    public DiffRequest(String command, DiffRequestData data) {
        this.command = command;
        this.data = data;
    }

    public void setData(DiffRequestData data) {
        this.data = data;
    }

    public DiffRequestData getData() {
        return data;
    }

}
