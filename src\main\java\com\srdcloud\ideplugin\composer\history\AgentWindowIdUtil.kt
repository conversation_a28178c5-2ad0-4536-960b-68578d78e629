package com.srdcloud.ideplugin.composer.history

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.srdcloud.ideplugin.composer.history.AgentHistoryUtil.getHistoryByWindowId
import com.srdcloud.ideplugin.general.utils.DebugLogUtil
import java.io.File
import java.io.FileWriter
import java.nio.file.Files

/**
 * 功能：持久化agent所有的窗口
 * 为后面做历史消息功能预留
 */
object AgentWindowIdUtil {
    private val gson = Gson()
    private val historyDir =
        System.getProperty("user.home") + File.separator + ".codefree" + File.separator + "plugin" + File.separator + "history"
    private val windowIdFile = historyDir + File.separator + "window_ids_v1.json"
    private val logger = Logger.getInstance(AgentWindowIdUtil::class.java)

    // 添加内存缓存
    private var windowDataCache: MutableMap<String?, WindowData>? = null

    init {
        // 确保目录存在
        val dir = File(historyDir)
        if (!dir.exists()) {
            dir.mkdirs()
        }
        // 确保文件存在
        val file = File(windowIdFile)
        if (!file.exists()) {
            try {
                file.createNewFile()
                FileWriter(file).use { writer ->
                    writer.write("{}")
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    /**
     * 获取项目的当前窗口ID（如果没有则创建一个新的）
     */
    fun getWindowId(project: Project): String {
        val projectPath = project.basePath ?: throw Exception("projectPath 为空")

        // 从缓存获取数据
        if (windowDataCache == null) {
            windowDataCache = loadWindowIds()
        }

        val windowData = windowDataCache!![projectPath]
        if (windowData == null || windowData.windowIds.isEmpty()) {
            throw Exception("没有找到对应的windowId")
        }
        return windowData.windowIds[windowData.windowIds.size - 1]
    }

    /**
     * 使用conversation创建一个新的窗口ID
     * @param projectPath 项目路径
     * @param conversationId 会话ID
     * @return
     */
    fun createNewWindowIdByConversationId(project: Project, conversationId: String): String {
        return createNewWindowId(project, conversationId)
    }

    /**
     * 为项目创建一个新的窗口ID
     * 如果当前最新的windowId没有对应的聊天历史，则直接返回该windowId
     */
    private fun createNewWindowId(project: Project, conversationId: String): String {
        val projectPath = project.basePath ?: return conversationId

        if (windowDataCache == null) {
            windowDataCache = loadWindowIds()
            DebugLogUtil.info("[cf] window cache: $windowDataCache")
        }

        val windowData = windowDataCache!![projectPath]
        // 如果已有窗口ID，检查最新的是否有聊天历史
        if (windowData != null && windowData.windowIds.isNotEmpty()) {
            val latestWindowId = windowData.windowIds[windowData.windowIds.size - 1]
            // 检查是否有聊天历史
            if (getHistoryByWindowId(project, latestWindowId) == null) {
                // 如果没有聊天历史，直接返回最新的windowId
                return latestWindowId
            }
        }

        // 如果没有窗口ID或最新的窗口ID有聊天历史，创建新的windowId
        val newWindowId = conversationId
        val windowIds = windowData?.windowIds?.toMutableList() ?: mutableListOf()
        windowIds.add(newWindowId)

        windowDataCache!![projectPath] = WindowData(windowIds)
        saveWindowIds(windowDataCache!!)
        return newWindowId
    }

    private fun loadWindowIds(): MutableMap<String?, WindowData> {
        val file = File(windowIdFile)
        try {
            if (file.length() > 0) {
                val content = String(Files.readAllBytes(file.toPath()))
                return gson.fromJson(content, object : TypeToken<Map<String?, WindowData?>?>() {}.type)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return HashMap()
    }

    private fun saveWindowIds(windowIds: Map<String?, WindowData>) {
        try {
            val file = File(windowIdFile)
            FileWriter(file).use { writer ->
                writer.write(gson.toJson(windowIds))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun clearWindowId(project: Project) {
        if (project.basePath != null) {
            if (windowDataCache == null) {
                windowDataCache = loadWindowIds()
            }
            windowDataCache!!.remove(project.basePath)
            saveWindowIds(windowDataCache!!)
        }
    }

    fun clearAllWindowIds() {
        try {
            val file = File(windowIdFile)
            FileWriter(file).use { writer ->
                writer.write("{}")
            }
            windowDataCache = HashMap()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    data class WindowData(val windowIds: List<String>)
}