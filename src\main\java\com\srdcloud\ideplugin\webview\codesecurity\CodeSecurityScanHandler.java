package com.srdcloud.ideplugin.webview.codesecurity;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.VfsUtil;
import com.intellij.openapi.vfs.VirtualFile;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import com.srdcloud.ideplugin.general.utils.FileUtil;
import com.srdcloud.ideplugin.general.utils.JsonUtil;
import com.srdcloud.ideplugin.remote.ScanAssistantCommHandler;
import com.srdcloud.ideplugin.remote.domain.ScanAssistant.Issue;
import com.srdcloud.ideplugin.remote.domain.ScanAssistant.IssueListResponse;
import com.srdcloud.ideplugin.remote.domain.ScanAssistant.ScanBaseResponse;
import com.srdcloud.ideplugin.remote.domain.ScanAssistant.ScanUploadResponse;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;
import com.srdcloud.ideplugin.webview.codechat.common.CodeSecurityEventType;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCode;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCommand;
import com.srdcloud.ideplugin.webview.base.domain.WebViewCode;
import com.srdcloud.ideplugin.webview.base.domain.WebViewReqTypeRequest;
import com.srdcloud.ideplugin.webview.codesecurity.request.*;
import com.srdcloud.ideplugin.webview.codesecurity.response.*;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import org.apache.http.entity.mime.content.FileBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Arrays;
import java.util.List;

public class CodeSecurityScanHandler {
    private static final Logger logger = LoggerFactory.getLogger(CodeSecurityScanHandler.class);

    private final Project project;

    private final CodeChatWebview parent;

    public CodeSecurityScanHandler(Project project, CodeChatWebview parent) {
        this.project = project;
        this.parent = parent;
    }

    public void processSecurityRequest(String request) {
        WebViewReqTypeRequest webViewReqTypeRequestSecurity = JsonUtil.getInstance().fromJson(request, WebViewReqTypeRequest.class);
        String reqTypeSecurity = webViewReqTypeRequestSecurity.getData().getReqType();
        switch (reqTypeSecurity) {
            // 1、获取项目目录树
            case CodeSecurityEventType.GET_SCAN_FILES:
                getScanFiles();
                break;
            // 2、开始扫描任务，打包上传文件
            case CodeSecurityEventType.RUN_SCAN:
                runScan(request);
                break;
            // 3、查询扫描任务结果
            case CodeSecurityEventType.QUERY_SCAN_ISSUES:
                QueryScanIssues(request);
                break;
            // 4、查看详情
            case CodeSecurityEventType.VIEW_DETAIL:
                ViewDetail(request);
                break;
            // 5、停止扫描任务
            case CodeSecurityEventType.STOP_SCAN:
                StopScan(request);
                break;
            // 6、修复建议
            case CodeSecurityEventType.AI_EXPLAIN:
                AiExplain(request);
                break;
            // 7、停止解释
            case CodeSecurityEventType.STOP_AI_REQUEST:
                StopAiRequest(request);
                break;
            default:
                break;
        }

    }

    private void getScanFiles() {
        GetScanFilesResponseData getScanFilesResponseData = new GetScanFilesResponseData(WebViewRspCode.SUCCESS, FileUtil.getFileTree(project.getBasePath()));
        GetScanFilesResponse getScanFilesResponse = new GetScanFilesResponse(WebViewRspCommand.CODE_SECURITY_SCAN_RESPONSE, CodeSecurityEventType.GET_SCAN_FILES, getScanFilesResponseData);
        parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(getScanFilesResponse));
    }

    private void runScan(String request) {
        RunScanRequest runScanRequest = JsonUtil.getInstance().fromJson(request, RunScanRequest.class);
        // 获取已选的文件列表
        String scannerEngine = runScanRequest.getData().getScannerEngine();
        String[] selectedFiles = runScanRequest.getData().getScanFiles();
        DebugLogUtil.println("已选文件列表: " + Arrays.toString(selectedFiles));

        // 项目目录、临时目录、zip文件名、目标目录（不带右侧/）
        String projectDirectory = project.getBasePath();
        String tmpDirectory = projectDirectory + "/build/tmp";
        String zipFileName = tmpDirectory + "/scan.zip";
        String destinationDirectory = projectDirectory + "/build/tmp/scan";

        // 删除目标目录
        File destinationDirectoryFile = new File(destinationDirectory);
        FileUtil.deleteDirectory(destinationDirectoryFile);

        // 遍历选中的文件
        for (String item : selectedFiles) {
            char target = '/';
            // 找到目标字符第一次出现的位置
            int firstIndex = item.indexOf(target);
            if (firstIndex == -1) {
                continue;
            }
            // 从第一次出现位置之后开始查找
            int secondIndex = item.indexOf(target, firstIndex + 1);
            if (secondIndex == -1) {
                continue;
            }
            // 截取第二次出现位置后的字符（带左侧/）
            String result = item.substring(secondIndex);

            // 拼接源文件路径和目标文件路径
            String source = projectDirectory + result;
            String destination = destinationDirectory + result;
            File sourceFile = new File(source);
            if (sourceFile.exists() && sourceFile.isFile()) {
                DebugLogUtil.println("源文件名: " + source);
                DebugLogUtil.println("目标文件名: " + destination);
                Path destinationPath = Paths.get(destination);
                Path sourcePath = Paths.get(source);
                try {
                    // 创建目标路径所有不存在的父目录
                    Files.createDirectories(destinationPath.getParent());
                    // 复制文件到目标路径
                    Files.copy(sourcePath, destinationPath, StandardCopyOption.REPLACE_EXISTING);
                } catch (IOException e) {
                }
            }
        }

        // 目标目录内容打包成zip
        try {
            FileUtil.zipDirectory(destinationDirectory, zipFileName);
        } catch (IOException e) {
            e.printStackTrace();
        }

        DebugLogUtil.println("已打包文件");

        // 上传zip文件
        File file = new File(zipFileName);
        FileBody fileBody = null;
        if (file != null) {
            fileBody = new FileBody(file);
        }
        ScanUploadResponse scanUploadResponse = ScanAssistantCommHandler.uploadZipFile(
                project.getName(),
                "java",
                (int) file.length(),
                "OptionalRepoUrl",
                fileBody,
                "60:3e:5f:5a:15:56",
                scannerEngine
        );

        DebugLogUtil.println("FileSelectorDialogWrapper, code:" + scanUploadResponse.getCode());
        DebugLogUtil.println("FileSelectorDialogWrapper, msg:" + scanUploadResponse.getMsg());
        DebugLogUtil.println("FileSelectorDialogWrapper, data:" + scanUploadResponse.getData());

        String msg = "";
        if (scanUploadResponse.getCode() == 0) {
            msg = "文件上传成功";
        } else {
            msg = "文件上传失败";
        }
        RunScanResponseData runScanResponseData = new RunScanResponseData(scanUploadResponse.getCode(), msg, scanUploadResponse.getData());
        RunScanResponse runScanResponse = new RunScanResponse(WebViewRspCommand.CODE_SECURITY_SCAN_RESPONSE, CodeSecurityEventType.RUN_SCAN, runScanResponseData);
        parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(runScanResponse));
    }

    private void QueryScanIssues(String request) {
        QueryScanIssuesRequest queryScanIssuesRequest = JsonUtil.getInstance().fromJson(request, QueryScanIssuesRequest.class);
        IssueListResponse issueListResponse = ScanAssistantCommHandler.queryIssueList(
                queryScanIssuesRequest.getData().getTaskId(),
                queryScanIssuesRequest.getData().getPage(),
                queryScanIssuesRequest.getData().getPageSize());
        DebugLogUtil.println("queryIssueList, code:" + issueListResponse.getCode());
        DebugLogUtil.println("queryIssueList, msg:" + issueListResponse.getMsg());
        DebugLogUtil.println("queryIssueList, total:" + issueListResponse.getTotal());

        if (issueListResponse.getCode() == 0) {
            QueryScanIssuesResponseData queryScanIssuesResponseData = new QueryScanIssuesResponseData(issueListResponse.getCode(), null, issueListResponse.getData());
            QueryScanIssuesResponse queryScanIssuesResponse = new QueryScanIssuesResponse(WebViewRspCommand.CODE_SECURITY_SCAN_RESPONSE, CodeSecurityEventType.QUERY_SCAN_ISSUES, queryScanIssuesResponseData);
            parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(queryScanIssuesResponse));
        } else {
            QueryScanIssuesResponseData queryScanIssuesResponseData = new QueryScanIssuesResponseData(issueListResponse.getCode(), issueListResponse.getMsg(), null);
            QueryScanIssuesResponse queryScanIssuesResponse = new QueryScanIssuesResponse(WebViewRspCommand.CODE_SECURITY_SCAN_RESPONSE, CodeSecurityEventType.QUERY_SCAN_ISSUES, queryScanIssuesResponseData);
            parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(queryScanIssuesResponse));
        }
    }

    private void ViewDetail(String request) {
        ViewDetailRequest viewDetailRequest = JsonUtil.getInstance().fromJson(request, ViewDetailRequest.class);
        // 去掉打包的前缀，即去掉前缀"scan/"
        String fileName = viewDetailRequest.getData().getFileName().substring(5);
        int line = viewDetailRequest.getData().getLine();

        // 获取项目的根目录
        String projectBasePath = project.getBasePath();
        if (projectBasePath != null) {
            VirtualFile projectBaseDir = VfsUtil.findFileByIoFile(new java.io.File(projectBasePath), true);
            if (projectBaseDir != null && projectBaseDir.exists()) {
                DebugLogUtil.println("projectBaseDir:" + projectBaseDir.getPath());
                VirtualFile virtualFileName = projectBaseDir.findFileByRelativePath(fileName);
                if (virtualFileName != null && virtualFileName.exists()) {
                    DebugLogUtil.println("找到文件：" + fileName);
                    ApplicationManager.getApplication().invokeLater(() -> {
                        // 打开指定的文件
                        FileEditorManager.getInstance(project).openFile(virtualFileName, true);
                        DebugLogUtil.println("打开文件");
                        Editor editor = FileEditorManager.getInstance(project).getSelectedTextEditor();
                        if (editor != null) {
                            // 光标移动到指定的行号
                            editor.getCaretModel().moveToLogicalPosition(new com.intellij.openapi.editor.LogicalPosition(line - 1, 0));
                            editor.getScrollingModel().scrollToCaret(com.intellij.openapi.editor.ScrollType.CENTER);
                            DebugLogUtil.println("移动光标");
                        }
                    });
                } else {
                    DebugLogUtil.println("未找到文件：" + fileName);
                }
            } else {
                DebugLogUtil.println("项目基目录未找到");
            }
        } else {
            DebugLogUtil.println("项目基目录路径未找到");
        }
    }

    private void StopScan(String request) {
        StopScanRequest stopScanRequest = JsonUtil.getInstance().fromJson(request, StopScanRequest.class);
        ScanBaseResponse scanBaseResponse = ScanAssistantCommHandler.stopTask(stopScanRequest.getData().getTaskId());

        StopScanResponseData stopScanResponseData = new StopScanResponseData(scanBaseResponse.getCode(), scanBaseResponse.getMsg());
        StopScanResponse stopScanResponse = new StopScanResponse(WebViewRspCommand.CODE_SECURITY_SCAN_RESPONSE, CodeSecurityEventType.STOP_SCAN, stopScanResponseData);
        parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(stopScanResponse));
    }

    private void AiExplain(String request) {
        AiExplainRequest aiExplainRequest = JsonUtil.getInstance().fromJson(request, AiExplainRequest.class);
        List<Issue> issueList = aiExplainRequest.getData().getIssueList();
        String engine = aiExplainRequest.getData().getScannerEngine();

        // 获取当前打开的文件编辑器
        FileEditorManager fileEditorManager = FileEditorManager.getInstance(project);
        VirtualFile[] openFiles = fileEditorManager.getOpenFiles();
        if (openFiles.length == 0) {
            return;
        }

        // 获取当前激活的文件
        VirtualFile currentFile = fileEditorManager.getSelectedFiles()[0];
        if (currentFile == null) {
            return;
        }
        // 获取文件对应的文档
        Document document = ReadAction.compute(() -> {
            return FileDocumentManager.getInstance().getDocument(currentFile);
        });
        if (document == null) {
            return;
        }

        // 获取当前文档的行数
        int lineCount = document.getLineCount();

        int lineNumber = issueList.get(0).getLine() - 1;
        int fileStartLine = Math.max(0, lineNumber - 20);
        int fileEndLine = Math.min(lineCount - 1, lineNumber + 20);

        String fileContent = "";
        for (int i = fileStartLine; i <= fileEndLine; i++) {
            // 获取指定行的内容
            int startOffset = document.getLineStartOffset(i);
            int endOffset = document.getLineEndOffset(i);
            String lineContent = document.getText().substring(startOffset, endOffset);
            fileContent += lineContent + "\n";
        }

        EventSourceListener eventSourceListener = new EventSourceListener() {
            @Override
            public void onOpen(EventSource eventSource, Response response) {
            }

            @Override
            public void onEvent(EventSource eventSource, String id, String type, String data) {
                AiExplainResponse aiExplainResponse = new AiExplainResponse(WebViewRspCommand.CODE_SECURITY_SCAN_RESPONSE, CodeSecurityEventType.AI_EXPLAIN, data);
                parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(aiExplainResponse));
            }

            @Override
            public void onFailure(EventSource eventSource, Throwable t, Response response) {
                //这边可以监听并重新打开
                WebViewCode webViewCode = new WebViewCode(response.code());
                AiExplainErrorResponse aiExplainErrorResponse = new AiExplainErrorResponse(WebViewRspCommand.CODE_SECURITY_SCAN_RESPONSE, CodeSecurityEventType.AI_EXPLAIN, webViewCode);
                parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(aiExplainErrorResponse));
            }

            @Override
            public void onClosed(EventSource eventSource) {

            }
        };

        ScanAssistantCommHandler.fixExplain(
                engine.equals("sonar") ? 1 : 2,
                fileStartLine,
                fileEndLine,
                fileContent,
                issueList,
                eventSourceListener
        );
    }

    private void StopAiRequest(String request) {
        DebugLogUtil.println("no used,StopAiRequest");
    }
}
