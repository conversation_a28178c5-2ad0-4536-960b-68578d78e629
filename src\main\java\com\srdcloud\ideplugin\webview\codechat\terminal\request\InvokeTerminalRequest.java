package com.srdcloud.ideplugin.webview.codechat.terminal.request;

import com.srdcloud.ideplugin.webview.base.domain.WebViewCommand;

public class InvokeTerminalRequest extends WebViewCommand {

    private InvokeTerminalRequestData data;

    public InvokeTerminalRequest(String command, InvokeTerminalRequestData data) {
        super(command);
        this.data = data;
    }

    public InvokeTerminalRequestData getData() {
        return data;
    }

    public void setData(InvokeTerminalRequestData data) {
        this.data = data;
    }
}
