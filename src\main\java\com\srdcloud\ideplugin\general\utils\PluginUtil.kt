package com.srdcloud.ideplugin.general.utils

import com.intellij.ide.plugins.PluginManagerConfigurable
import com.intellij.ide.plugins.PluginManagerCore
import com.intellij.openapi.extensions.PluginId
import com.intellij.openapi.project.Project

class PluginUtil {
    companion object {
        const val PLUGIN_ID = "com.srdcloud.IDEPlugin"

        /**
         * 弹出插件设置窗口，并选中codefree
         */
        @JvmStatic
        fun showPluginConfigurable(project: Project) {
            val plugins = listOf(PluginId.getId(PLUGIN_ID))

            try {
                PluginManagerConfigurable.showPluginConfigurable(project, plugins)
            } catch (ex: Exception) {
                ex.printStackTrace()
                MessageBalloonNotificationUtil.showCommonNotificationWithConfirm(
                    project,
                    "唤起窗口失败,请尝试点击File->Settings->Plugins->Installed升级插件"
                )
            }
        }

        private val pluginDescriptor = PluginManagerCore.getPlugin(PluginId.getId("com.srdcloud.IDEPlugin"))
        val version = pluginDescriptor?.version ?: ""
        val changeNotes = pluginDescriptor?.changeNotes ?: ""
    }
}