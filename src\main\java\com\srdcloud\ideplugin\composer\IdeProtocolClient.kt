package com.srdcloud.ideplugin.composer

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.intellij.ide.plugins.PluginManagerCore
import com.intellij.ide.util.PropertiesComponent
import com.intellij.openapi.application.ApplicationInfo
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.extensions.PluginId
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.*
import com.srdcloud.ideplugin.general.constants.Constants
import com.srdcloud.ideplugin.general.constants.IgnoreRules
import com.srdcloud.ideplugin.general.utils.DebugLogUtil
import com.srdcloud.ideplugin.general.utils.IgnoreUtils
import com.srdcloud.ideplugin.general.utils.JsonUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.apache.commons.lang.StringUtils
import java.io.*
import java.net.NetworkInterface
import java.nio.charset.Charset
import java.util.*

fun uuid(): String {
    return UUID.randomUUID().toString()
}

val ideMessageTypes = listOf(
    "isTelemetryEnabled",
    "getUniqueId",
    "getWorkspaceConfigs",
    "listWorkspaceContents",
    "getGlobalIgnoreList",
    "getWorkspaceDirs",
    "fileExists",
    "readFile",
    "getBranch",
    "getIdeInfo",
    "getIdeSettings",
    "getRepoName",
    "getLastModified",
)

data class Position(val line: Int, val character: Int)
data class Range(val start: Position, val end: Position)

fun getMachineUniqueID(): String {
    val sb = StringBuilder()
    val networkInterfaces = NetworkInterface.getNetworkInterfaces()

    while (networkInterfaces.hasMoreElements()) {
        val networkInterface = networkInterfaces.nextElement()
        val mac = networkInterface.hardwareAddress

        if (mac != null) {
            for (i in mac.indices) {
                sb.append(
                    String.format(
                        "%02X%s",
                        mac[i],
                        if (i < mac.size - 1) "-" else ""
                    )
                )
            }
            return sb.toString()
        }
    }

    return "No MAC Address Found"
}


class IdeProtocolClient(
    private val workspacePath: String?,
    private val project: Project
) : DumbAware {
    init {
        initIdeProtocol()
    }

    fun handleMessage(text: String, respond: (Any?) -> Unit) {
        val coroutineScope = CoroutineScope(Dispatchers.IO)
        coroutineScope.launch(Dispatchers.IO) {
            val parsedMessage: Map<String, Any> = Gson().fromJson(
                text,
                object : TypeToken<Map<String, Any>>() {}.type
            )
            val messageType = parsedMessage["messageType"] as? String
            if (messageType == null) {
                DebugLogUtil.println("Received message without type: $text")
                return@launch
            }
            val data = parsedMessage["data"]
            val gson = Gson()
            try {
                when (messageType) {
                    "isTelemetryEnabled" -> {
                        respond(true)
                    }

                    "getUniqueId" -> {
                        respond(uniqueId())
                    }

                    "getWorkspaceConfigs" -> {
                        val workspaceDirs = workspaceDirectories()

                        val configs: List<String> = listOf()
                        for (workspaceDir in workspaceDirs) {
                            val workspacePath = File(workspaceDir)
                            val dir = VirtualFileManager.getInstance().findFileByUrl("file://$workspacePath")
                            if (dir != null) {
                                val contents = dir.children.map { it.name }

                                // Find any .continuerc.json files
                                for (file in contents) {
                                    if (file.endsWith(".continuerc.json")) {
                                        val filePath = workspacePath.resolve(file)
                                        val fileContent = File(filePath.toString()).readText()
                                        configs.plus(fileContent)
                                    }
                                }
                            }
                        }
                        respond(configs)
                    }

                    "listWorkspaceContents" -> {
                        respond(listDirectoryContents(null))
                    }

                    "getGlobalIgnoreList" -> {
                        respond(IgnoreUtils.getIgnoreRule())
                    }

                    "getWorkspaceDirs" -> {
                        respond(workspaceDirectories())
                    }

                    "fileExists" -> {
                        val msg = data as Map<String, String>;
                        val file = File(msg["filepath"])
                        respond(file.exists())
                    }

                    "readFile" -> {
                        val msg = readFile((data as Map<String, String>)["filepath"] as String)
                        val jsonReadyString = gson.toJson(msg)
                        respond(jsonReadyString)
                    }

                    "getBranch" -> {
                        try {
                            // Get the current branch name
                            val builder = ProcessBuilder("git", "rev-parse", "--abbrev-ref", "HEAD")
                            builder.directory(File(workspacePath ?: "."))
                            val process = builder.start()

                            val reader = BufferedReader(InputStreamReader(process.inputStream))
                            val output = reader.readLine()
                            process.waitFor()

                            respond(output ?: "NONE")
                        } catch (e: IOException) {
                            when {
                                e.message?.contains("Cannot run program \"git\"") == true ->
                                    DebugLogUtil.println("ERROR: Git is not installed or not in PATH")

                                else -> DebugLogUtil.println("ERROR: ${e.message}")
                            }
                        } catch (e: Exception) {
                            DebugLogUtil.println("ERROR: ${e.message}")
                        }
                        respond("NONE")
                    }

                    "getIdeInfo" -> {
                        val applicationInfo = ApplicationInfo.getInstance()
                        val ideName: String = applicationInfo.fullApplicationName
                        val ideVersion = applicationInfo.fullVersion
                        val sshClient = System.getenv("SSH_CLIENT")
                        val sshTty = System.getenv("SSH_TTY")

                        var remoteName: String = "local"
                        if (sshClient != null || sshTty != null) {
                            remoteName = "ssh"
                        }

                        val pluginId = "com.srdcloud.IDEPlugin"
                        val plugin = PluginManagerCore.getPlugin(PluginId.getId(pluginId))
                        val extensionVersion = plugin?.version ?: "Unknown"

                        respond(
                            mapOf(
                                "ideType" to "jetbrains",
                                "name" to ideName,
                                "version" to ideVersion,
                                "remoteName" to remoteName,
                                "extensionVersion" to extensionVersion
                            )
                        )
                    }

                    "getIdeSettings" -> {
                        respond(
                            mapOf(
                                "remoteConfigServerUrl" to "",
                                "remoteConfigSyncPeriod" to "60",
                                "userToken" to ""
                            )
                        )
                    }

                    "getRepoName" -> {
                        try {
                            // Get the current repository name
                            val builder = ProcessBuilder("git", "config", "--get", "remote.origin.url")
                            builder.directory(File(workspacePath ?: "."))
                            val process = builder.start()

                            val reader = BufferedReader(InputStreamReader(process.inputStream))
                            val output = reader.readLine()
                            process.waitFor()

                            respond(output ?: "NONE")
                        } catch (e: IOException) {
                            when {
                                e.message?.contains("Cannot run program \"git\"") == true ->
                                    DebugLogUtil.println("ERROR: Git is not installed or not in PATH")

                                else -> DebugLogUtil.println("ERROR: ${e.message}")
                            }
                        } catch (e: Exception) {
                            DebugLogUtil.println("ERROR: ${e.message}")
                        }
                        respond("NONE")
                    }

                    "getLastModified" -> {
                        val data = data as Map<String, Any>
                        val files = data["files"] as List<String>
                        val pathToLastModified = files.map { file ->
                            file to File(file).lastModified()
                        }.toMap()
                        respond(pathToLastModified)
                    }

                    else -> {
                        DebugLogUtil.println("Unknown messageType: $messageType")
                    }
                }
            } catch (error: Exception) {
                showMessage("Error handling message of type $messageType: $error")
            }
        }
    }

    private fun initIdeProtocol() {
        // TODO
    }

    fun uniqueId(): String {
        return getMachineUniqueID()
    }

    fun readFile(filepath: String): String {
        try {
            val content = ApplicationManager.getApplication().runReadAction<String?> {
                val virtualFile = LocalFileSystem.getInstance().findFileByPath(filepath)
                if (virtualFile != null && FileDocumentManager.getInstance().isFileModified(virtualFile)) {
                    return@runReadAction FileDocumentManager.getInstance().getDocument(virtualFile)?.text
                }
                return@runReadAction null
            }

            if (content != null) {
                return content
            }

            val file = File(filepath)
            if (!file.exists()) return ""

            FileInputStream(file).use { fis ->
                val sizeToRead = minOf(100000, file.length()).toInt()
                val buffer = ByteArray(sizeToRead)
                val bytesRead = fis.read(buffer, 0, sizeToRead)
                if (bytesRead <= 0) return ""

                // Here we assume the file encoding is UTF-8; adjust as necessary for different encodings.
                return String(buffer, 0, bytesRead, Charset.forName("UTF-8"))
            }
        } catch (e: Exception) {
            e.printStackTrace()
            return ""
        }
    }

    private fun workspaceDirectories(): Array<String> {
        if (this.workspacePath != null) {
            return arrayOf(this.workspacePath)
        }
        return arrayOf<String>();
    }

    private fun listDirectoryContents(directory: String?): List<String> {
        val dirs: Array<String>;
        if (directory != null) {
            dirs = arrayOf(directory)
        } else {
            dirs = workspaceDirectories()
        }

        val contents = ArrayList<String>()
        for (dir in dirs) {
            if (IgnoreRules.DEFAULT_IGNORE_DIRS.any { dir.contains(it) }) {
                continue
            }

            val workspacePath = File(dir)
            val workspaceDir = VirtualFileManager.getInstance().findFileByUrl("file://$workspacePath")

            if (workspaceDir != null) {
                val filter = object : VirtualFileFilter {
                    override fun accept(file: VirtualFile): Boolean {
                        if (file.isDirectory) {
                            return !shouldIgnoreDirectory(file.name)
                        } else {
                            val filePath = file.path
                            return !shouldIgnoreDirectory(filePath) && !IgnoreRules.DEFAULT_IGNORE_FILETYPES.any {
                                filePath.endsWith(
                                    it
                                )
                            }
                        }
                    }
                }
                VfsUtil.iterateChildrenRecursively(workspaceDir, filter) { virtualFile: VirtualFile ->
                    if (!virtualFile.isDirectory) {
                        contents.add(virtualFile.path)

                        // Set a hard limit on the number of files to list
                        if (contents.size > 10000) {
                            // Completely exit the iteration
                            return@iterateChildrenRecursively false
                        }
                    }
                    true
                }
            }
        }

        return contents
    }

    private fun shouldIgnoreDirectory(name: String): Boolean {
        val components = File(name).path.split(File.separator)
        return IgnoreRules.DEFAULT_IGNORE_DIRS.any { dir ->
            components.contains(dir)
        }
    }

    fun showMessage(msg: String) {
        // TODO
    }

}
