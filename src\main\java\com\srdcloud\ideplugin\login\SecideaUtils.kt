package com.srdcloud.ideplugin.login

import com.intellij.openapi.project.Project
import com.intellij.util.messages.MessageBus
import com.intellij.util.messages.Topic
import org.apache.http.client.ClientProtocolException
import org.apache.http.client.config.RequestConfig
import org.apache.http.client.methods.HttpGet
import org.apache.http.conn.ssl.NoopHostnameVerifier
import org.apache.http.impl.client.CloseableHttpClient
import org.apache.http.impl.client.HttpClients
import org.apache.http.ssl.SSLContexts
import org.apache.http.util.EntityUtils
import java.io.IOException
import java.io.UnsupportedEncodingException
import java.lang.reflect.Method

@Suppress("UNCHECKED_CAST")
fun <L> getSecIdeaPublisher(project: Project, topic: Topic<L>): L? {
    val messageBus = project.messageBus
    if (messageBus.isDisposed) return null

    val messageBusClass = MessageBus::class.java
    val syncPublisherMethod: Method = try {
        // Try to get the newer method first (returns Any)
        messageBusClass.getMethod("syncPublisher", Topic::class.java)
    } catch (e: NoSuchMethodException) {
        // Fall back to the older method if the newer one is not available
        messageBusClass.getMethod("syncPublisher", Topic::class.java)
    }

    return try {
        syncPublisherMethod.invoke(messageBus, topic) as? L
    } catch (e: Exception) {
        null
    }
}

fun doGet(url: String?): String? {
    var getResult: String? = null
    try {
        val closeableHttpClient: CloseableHttpClient = buildSSLCloseableHttpClient()!!
        val httpGet = HttpGet(url)
        val requestConfig = RequestConfig.custom().setConnectTimeout(300000).setSocketTimeout(300000).build()
        httpGet.config = requestConfig
        httpGet.addHeader("Content-Type", "application/json")
        val response = closeableHttpClient.execute(httpGet)
        if (response.statusLine.statusCode == 200) {
            println("查询成功")
            val responseEntity = response.entity
            responseEntity.content
            getResult = EntityUtils.toString(responseEntity, "utf-8")
        } else {
            println("查询失败")
        }
    } catch (e: UnsupportedEncodingException) {
        e.printStackTrace()
    } catch (e: ClientProtocolException) {
        e.printStackTrace()
    } catch (e: IOException) {
        e.printStackTrace()
    } catch (e: java.lang.Exception) {
        e.printStackTrace()
    }
    return getResult
}

/**
 * 忽略证书
 */
fun buildSSLCloseableHttpClient(): CloseableHttpClient? {
    val sslContext =
        SSLContexts.custom().loadTrustMaterial(
            null
        ) { x509Certificates, s -> true }.build()
    return HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(NoopHostnameVerifier()).build()
}
