package com.srdcloud.ideplugin.codecomplete.handle.codeprovider.prompt

import com.intellij.ide.util.PropertiesComponent
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.util.TextRange
import com.intellij.openapi.vfs.VirtualFile
import com.srdcloud.ideplugin.codecomplete.domain.CompletionType
import com.srdcloud.ideplugin.general.constants.Constants
import com.srdcloud.ideplugin.service.domain.apigw.codechat.CodeMessage.ImportSnippets

/**
 * <AUTHOR>
 * @date 2025/6/9
 * @desc 提供默认补全推理上下文信息
 */
class EditorPromptProvider private constructor() {
    companion object {
        val instance: EditorPromptProvider by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            EditorPromptProvider()
        }

        /**
         * 获取代码补全推理上下文信息
         * type：补全类型，包含AUTO（自动补全）和 MANUAL（手动补全）
         */
        fun Editor.getCompletionPrompt(type: CompletionType): Prompt {
            // 通过PSI获取编辑文件对象
            val document = document
            val importSnippets: MutableList<ImportSnippets> = mutableListOf()

            val file: VirtualFile? = FileDocumentManager.getInstance().getFile(document)
            val fileName = file?.name
            val logicalPosition = caretModel.logicalPosition
            val line = logicalPosition.line
            val col = logicalPosition.column

            // 跨文件信息获取：已下线，迁移到tabby-agent中
            // 获取后端配置的跨文件信息最大长度
//            var snippetSizeLimit =
//                PropertiesComponent.getInstance()
//                    .getInt(Constants.SnippetsCharacterLimit, Constants.DefaultSnippetsCharacterLimit)
//            try {
//                project?.let {
//                    val fileInfo = file?.getFileInfo(it)
//                    if (fileInfo == null) {
//                        DebugLogUtil.warn("InlineCompletion getFileInfo is null.")
//                        return@let
//                    }
//
//                    val fileTypeExt = FileUtil.getExtension(fileName)
//                    val logicalPosition = caretModel.logicalPosition
//
//                    DebugLogUtil.info("InlineCompletion FindRelativeObject begin")
//                    // 查找当前光标位置处所代码相关联的对象信息，可能存在多个，返回集合
//                    val col = ProjectCodeIndexer.getInstance(project!!).FindRelativeObject(
//                        fileTypeExt,
//                        file.path,
//                        document.text,
//                        logicalPosition.line,
//                        logicalPosition.column
//                    )
//                    DebugLogUtil.info("InlineCompletion FindRelativeObject end")
//
//                    val relativeObjectsList = ArrayList<RelativeCodeObject>()
//                    if (CollectionUtils.isNotEmpty(col)) {
//                        relativeObjectsList.addAll(col)
//                        // 倒序遍历：最新关联上的文件对象在LRU的末尾
//                        for (i in relativeObjectsList.indices.reversed()) {
//                            val c = relativeObjectsList[i]
//                            if (snippetSizeLimit <= 0 || StringUtils.isBlank(c.relativeText)) {
//                                continue
//                            }
//                            if (snippetSizeLimit >= c.relativeText.length) {
//                                importSnippets.add(ImportSnippets(c.relativeObject, c.relativeText))
//                                snippetSizeLimit -= c.relativeText.length
//                            } else {
//                                // 容量不够，则截取
//                                importSnippets.add(
//                                    ImportSnippets(
//                                        c.relativeObject,
//                                        c.relativeText.substring(0, snippetSizeLimit)
//                                    )
//                                )
//                                snippetSizeLimit = 0;
//                            }
//                        }
//                    }
//                }
//            } catch (e: Throwable) {
//                logger2.warn("[cf] getCompletionPrompt findRelativeObject error: ${e.printStackTrace()}")
//            }

            val max = document.textLength
            // 获取当前光标位置
            val offset = caretModel.offset

            // 计算推理上下文截取边界
            // -- 推理前后缀内容长度大小配置
            var maxPromptSize =
                PropertiesComponent.getInstance()
                    .getInt(Constants.InputCharacterLimit, Constants.DefaultInputCharacterLimit)

            // -- 默认从开始字符，到末尾字符
            var start = 0
            var end = max
            // -- 如果当前文件总长度，大于配置的推理内容长度大小，则需要做计算截取
            if (max > maxPromptSize) {
                // 长度计量单位
                val unitSize = maxPromptSize / 8
                // 前缀推理：从当前光标位置，向前截取7个单位长度
                val prefixSize = unitSize * 7
                // 后缀推理：从当前光标位置，向后截取1个单位长度
                val suffixSize = unitSize
                // 可灵活调整的比例
                val adjustContent: Int

                start = offset - prefixSize
                //如果前缀内容不满prefixSize，则把空余的长度容量挪到end去
                if (start < 0) {
                    // 前缀比例没用完，挪到后面去
                    adjustContent = -start
                    start = 0
                    end = offset + suffixSize + adjustContent
                } else {
                    end = offset + suffixSize
                    if (end > max) {
                        // 后缀比例有盈余，挪到前面去
                        adjustContent = end - max
                        end = max
                        start = offset - prefixSize - adjustContent
                    }
                }

                //最终修正
                //如果start小于0，则取文档开头
                if (start < 0) {
                    start = 0
                }
                //如果end大于文档长度，则取文档长度
                if (end > max) {
                    end = max
                }
            }

            // 从文档内容中，截取推理上下文内容
            val prefix = document.getText(TextRange(start, offset))
            val suffix = if (offset < end) document.getText(TextRange(offset, end)) else null

            // 默认stopwords策略：自动补全取行尾、手动补全取后缀下文
            // 不能合并到tabby，要兼顾本地链路补全判断。tabby-agent侧可按需调优stopwords策略。
            var stopWords = "\n"
            val lineNumber = document.getLineNumber(offset)
            val lineEnd = document.getLineEndOffset(lineNumber)
            if (lineEnd > offset) {
                stopWords = document.getText(TextRange(offset, lineEnd))
            } else if (type == CompletionType.MANUAL) {
                for (i in lineNumber + 1 until document.lineCount) {
                    val lineText =
                        document.getText(TextRange(document.getLineStartOffset(i), document.getLineEndOffset(i)))
                    if (lineText.trim().isNotEmpty()) {
                        stopWords += lineText
                        break
                    }
                }
            }

            // 创建推理提示prompt
            return Prompt(fileName, line, col, prefix, suffix, stopWords, importSnippets)
        }
    }
}