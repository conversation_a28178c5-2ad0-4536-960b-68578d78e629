package com.srdcloud.ideplugin.codecomplete.actions

import com.intellij.codeInsight.hint.HintManagerImpl.ActionToIgnore
import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.DataContext
import com.intellij.openapi.editor.Caret
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.EditorKind
import com.intellij.openapi.editor.actionSystem.EditorAction
import com.intellij.openapi.editor.actionSystem.EditorWriteActionHandler
import com.intellij.openapi.editor.impl.EditorImpl
import com.srdcloud.ideplugin.codecomplete.handle.CompletionContext.Companion.getInlineCompletionContextOrNull
import com.srdcloud.ideplugin.codecomplete.handle.CompletionHandler
import com.srdcloud.ideplugin.codecomplete.domain.CompletionState.Companion.getInlineCompletionState
import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.DefaultCodeProvider
import com.srdcloud.ideplugin.common.icons.MyIcons
import com.srdcloud.ideplugin.general.constants.Constants
import com.srdcloud.ideplugin.general.utils.UIUtil
import com.srdcloud.ideplugin.service.LoginService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob

// 收到补全触发行为枚举：手动快捷键触发、上一条建议、下一条建议
enum class Actions {
    INVOKE,
    PREVIOUS,
    NEXT,
}

// 收到发起补全请求
class ManualCompleteAction : EditorAction(ManualCompleteActionHandler(Actions.INVOKE)), ActionToIgnore,
    CompletionAction {
    override fun update(e: AnActionEvent) {
        var manualIcon = MyIcons.codemanual
        if (UIUtil.judgeBackgroudDarkTheme()) {
            manualIcon = MyIcons.codemanualDark
        }
        e.presentation.icon = manualIcon
        if (LoginService.getLoginStatus() == Constants.LoginStatus_NOK) {
            e.presentation.isEnabled = false
        }
    }

    override fun getActionUpdateThread(): ActionUpdateThread {
        return ActionUpdateThread.BGT
    }
}

// 获取上一条补全建议
class PreviousAction : EditorAction(ManualCompleteActionHandler(Actions.PREVIOUS)), ActionToIgnore, CompletionAction

// 获取下一条补全建议
class NextAction : EditorAction(ManualCompleteActionHandler(Actions.NEXT)), ActionToIgnore, CompletionAction

// 手动补全处理器
class ManualCompleteActionHandler(private val action: Actions) : EditorWriteActionHandler() {
    override fun executeWriteAction(editor: Editor, caret: Caret?, dataContext: DataContext?) {
        val context = editor.getInlineCompletionContextOrNull()
        val state = editor.getInlineCompletionState()

        // 手动触发补全 & 当前补全上下文有内容 & 正在显示补全内容，则return终止提问
        if (action == Actions.INVOKE && context != null && context.isCurrentlyDisplayingInlays) {
            return
        }

        // 如果当前行为是上一条\下一条补全建议
        if (action != Actions.INVOKE) {
            if (context == null || !context.isCurrentlyDisplayingInlays || state == null) {
                return
            }
            if (action == Actions.PREVIOUS && state.suggestionIndex == 0) {
                return
            }

            // 控制补全上下文切换，切换suggestionIndex展示state中暂存的建议
            state.flip(action)
        }

        // fixme：这里切换建议的同时最终都会发起一次手动补全？是否应该根据state.flip(action)的结果来决定是否发起一次补全？
        // 发起一次补全行为
        CompletionHandler(CoroutineScope(Dispatchers.Default + SupervisorJob())).invoke(
            editor as EditorImpl,
            DefaultCodeProvider.instance
        )
    }

    // 当前Handler是否可用
    override fun isEnabledForCaret(editor: Editor, caret: Caret, dataContext: DataContext): Boolean {
        // 项目非空 & 是主编辑器 & 登录成功状态，则可用
        return !(editor.project == null || editor !is EditorImpl || editor.editorKind != EditorKind.MAIN_EDITOR ||
                LoginService.getLoginStatus() == Constants.LoginStatus_NOK)
    }
}