package com.srdcloud.ideplugin.assistant.codechatNative.logics;

import com.google.common.collect.Lists;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.PromptTemplate;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.PromptTemplateCategory;
import com.srdcloud.ideplugin.general.enums.PromptScopeType;
import com.srdcloud.ideplugin.general.utils.LocalStorageUtil;
import com.srdcloud.ideplugin.remote.PromptManageCommHandler;
import com.srdcloud.ideplugin.remote.domain.PromptManage.PromptTemplateListResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/4/24
 */
public class PromptTemplateLoader {
    private static final Logger logger = LoggerFactory.getLogger(PromptTemplateLoader.class);

    public PromptTemplateLoader() {
    }


    public static ArrayList<PromptTemplateCategory> loadPromptCategoryList() {
        ArrayList<PromptTemplateCategory> result = new ArrayList<>();
        // 客户端写死全部分类
        result.add(new PromptTemplateCategory("", null, "全部分类", "0"));
        try {
            // 登录状态下才发起请求
            if (LocalStorageUtil.checkIsLogin()) {
                ArrayList<PromptTemplateCategory> categories = PromptManageCommHandler.listCategories();
                if (CollectionUtils.isNotEmpty(categories)) {
                    result.addAll(categories);
                }
            }
        } catch (Exception e) {
            logger.error("[cf] loadPromptCategoryList error: ", e);
        }
        return result;
    }


    public static PromptTemplateListResponse loadCodeChatPromptTemplates(String scope, final PromptTemplateCategory category, String keyword, final int pageNum, final int pageSize) {
        PromptTemplateListResponse listResponse = null;

        // 0614特殊处理：二开tab其实查的是all范围
        if (PromptScopeType.SECONDARY.getType().equalsIgnoreCase(scope)) {
            scope = PromptScopeType.ALL.getType();
        }

        // 预处理，跳过空白内容
        if (StringUtils.isBlank(keyword)) {
            keyword = null;
        }

        // 有选中具体分类，则使用该分类id进行查询
        String categoryId = Objects.isNull(category) ? null : category.getId();

        try {
            listResponse = PromptManageCommHandler.listPrompts(scope, keyword, categoryId, pageNum, pageSize);

        } catch (Exception e) {
            logger.error("[cf] loadCodeChatPromptTemplates scope:{},categoryId:{},keyword:{},error:{} ", scope, categoryId, keyword, e.getMessage());
        }
        return listResponse;
    }


    @Deprecated
    public static ArrayList<PromptTemplate> loadSRDChatPromptTemplates() {
        return Lists.newArrayListWithExpectedSize(0);
    }
}
