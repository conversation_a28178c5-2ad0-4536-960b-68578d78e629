package com.srdcloud.ideplugin.agent.download;

import com.srdcloud.ideplugin.agent.model.AgentVersion;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import com.srdcloud.ideplugin.general.utils.EnvUtil;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Duration;

public class AgentDownloader {

    private static final Logger logger = LoggerFactory.getLogger(AgentDownloader.class);

    private final String rootUrl;
    private final String invokerId;
    private final String apiKey;
    private final int maxRetries;
    // 默认下载超时时间（秒）
    private static final int DEFAULT_DOWNLOAD_TIMEOUT_SECONDS = 120;

    public AgentDownloader(String rootUrl, String invokerId, String apiKey, int maxRetries) {
        this.rootUrl = rootUrl;
        this.invokerId = invokerId;
        this.apiKey = apiKey;
        this.maxRetries = maxRetries;
    }

    /**
     * 获取最大重试次数
     */
    public int getMaxRetries() {
        return maxRetries;
    }

    /**
     * 下载代理并解压到目标路径
     * 
     * @param agentVersion 代理版本信息
     * @param targetPath 目标路径
     */
    public void downloadAgent(AgentVersion agentVersion, String targetPath) {
        downloadAgent(agentVersion, targetPath, DEFAULT_DOWNLOAD_TIMEOUT_SECONDS);
    }

    /**
     * 下载代理并解压到目标路径，支持自定义超时时间
     * 先将文件下载到内存中，验证MD5后再保存到文件系统
     * 
     * @param agentVersion 代理版本信息
     * @param targetPath 目标路径
     * @param timeoutSeconds 超时时间（秒）
     */
    public void downloadAgent(AgentVersion agentVersion, String targetPath, int timeoutSeconds) {
        int retryCount = 0;
        Exception lastException = null;
        String zipFilePath = targetPath + ".zip";

        while (retryCount < maxRetries) {
            try {
                if (EnvUtil.isSec()) {
                    // 创建一个忽略SSL证书和主机名的HttpClient
                    SSLContext sslContext = SSLContextBuilder.create()
                            .loadTrustMaterial((chain, authType) -> true)
                            .build();

                    try (CloseableHttpClient client = HttpClients.custom()
                            .setSSLContext(sslContext)
                            .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                            .build()) {

                        HttpGet httpGet = new HttpGet(agentVersion.getDownloadUrl());
                        httpGet.setConfig(RequestConfig.custom()
                                .setConnectTimeout(timeoutSeconds * 1000)
                                .setConnectionRequestTimeout(timeoutSeconds * 1000)
                                .setSocketTimeout(timeoutSeconds * 1000)
                                .build());

                        DebugLogUtil.info("[cf] 开始下载代理: " + agentVersion.getAgentName() + ", 版本: " + agentVersion.getVersion());

                        try (CloseableHttpResponse response = client.execute(httpGet)) {
                            int statusCode = response.getStatusLine().getStatusCode();
                            if (statusCode == 200) {
                                byte[] fileBytes = EntityUtils.toByteArray(response.getEntity());
                                DebugLogUtil.info("[cf] 代理下载完成: " + agentVersion.getAgentName() + ", 大小: " + fileBytes.length + " 字节");

                                // 在内存中校验MD5
                                verifyMd5(fileBytes, agentVersion.getMd5(), agentVersion.getAgentName());

                                // MD5校验通过，保存文件
                                saveBytesToFile(fileBytes, zipFilePath);

                                // 解压文件
                                unzipFile(zipFilePath, targetPath);
                                logger.info("[cf] Successfully download and unzip agent: {}, {}, path: {}", agentVersion.getAgentName(), agentVersion.getVersion(), targetPath);
                                return;
                            } else {
                                throw new RuntimeException("[cf] Failed to download agent, status code: " + statusCode);
                            }
                        }
                    }
                } else {
                    HttpClient client = HttpClient.newBuilder()
                            .connectTimeout(Duration.ofSeconds(timeoutSeconds))
                            .build();

                    HttpRequest request = HttpRequest.newBuilder()
                            .uri(URI.create(agentVersion.getDownloadUrl()))
                            .GET()
                            .timeout(Duration.ofSeconds(timeoutSeconds))
                            .build();

                    // 直接下载到内存缓冲区
                    DebugLogUtil.info("[cf] 开始下载代理: " + agentVersion.getAgentName() + ", 版本: " + agentVersion.getVersion());
                    HttpResponse<byte[]> response = client.send(request, HttpResponse.BodyHandlers.ofByteArray());

                    if (response.statusCode() == 200) {
                        byte[] fileBytes = response.body();
                        DebugLogUtil.info("[cf] 代理下载完成: " + agentVersion.getAgentName() + ", 大小: " + fileBytes.length + " 字节");

                        // 在内存中校验MD5
                        verifyMd5(fileBytes, agentVersion.getMd5(), agentVersion.getAgentName());

                        // MD5校验通过，保存文件
                        saveBytesToFile(fileBytes, zipFilePath);

                        // 解压文件
                        unzipFile(zipFilePath, targetPath);
                        logger.info("[cf] Successfully download and unzip agent: {}, {}, path: {}", agentVersion.getAgentName(), agentVersion.getVersion(), targetPath);
                        return;
                    } else {
                        throw new RuntimeException("[cf] Failed to download agent, status code: " + response.statusCode());
                    }
                }
            } catch (Exception e) {
                lastException = e;
                retryCount++;
                
                // 清理可能部分下载的文件
                deleteFile(zipFilePath);
                
                if (retryCount >= maxRetries) {
                    logger.error("[cf] Failed to download agent after {} attempts", maxRetries, e);
                    throw new RuntimeException("[cf] Failed to download agent after " + maxRetries + " attempts", e);
                }
                
                // 计算退避时间：1秒, 2秒, 4秒...
                int backoffSeconds = (1 << (retryCount - 1)) * 1000;
                logger.error("[cf] agent download failed, retry {}/{} agent: {}", retryCount, maxRetries, agentVersion.getAgentName(), e);
                try {
                    Thread.sleep(backoffSeconds);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    logger.error("[cf] Download interrupted", ie);
                    throw new RuntimeException("Download interrupted", ie);
                }
            }
        }

        // 不应该到达这里，但为了安全起见还是添加
        logger.error("[cf] Should not reach here, failed to download agent after {} attempts.", maxRetries);
        if (lastException != null) {
            throw new RuntimeException("Failed to download agent after " + maxRetries + " attempts", lastException);
        } else {
            throw new RuntimeException("Failed to download agent after " + maxRetries + " attempts");
        }
    }
    
    /**
     * 在内存中校验文件字节数组的MD5哈希值
     * 
     * @param fileBytes 文件字节数组
     * @param expectedMd5 期望的MD5值
     * @param agentName 代理名称（用于日志）
     * @throws NoSuchAlgorithmException 如果MD5算法不可用
     */
    private void verifyMd5(byte[] fileBytes, String expectedMd5, String agentName) throws NoSuchAlgorithmException {
        if (expectedMd5 == null || expectedMd5.isEmpty()) {
            DebugLogUtil.warn("期望的MD5值为空，跳过验证: " + agentName);
            return;
        }
        
        MessageDigest digest = MessageDigest.getInstance("MD5");
        byte[] md5Bytes = digest.digest(fileBytes);
        String actualMd5 = bytesToHex(md5Bytes);
        
        if (!expectedMd5.equalsIgnoreCase(actualMd5)) {
            logger.error("[cf] MD5 verify failed，agent: {}, expectedMd5: {}, actualMd5: {}", agentName, expectedMd5, actualMd5);
            throw new RuntimeException("MD5 validation failed for agent: " + agentName);
        }
        
        DebugLogUtil.info("MD5校验成功，代理: " + agentName);
    }
    
    /**
     * 将字节数组保存到文件
     * 
     * @param bytes 字节数组
     * @param filePath 文件路径
     * @throws IOException 如果文件写入失败
     */
    private void saveBytesToFile(byte[] bytes, String filePath) throws IOException {
        try {
            File file = new File(filePath);
            // 确保父目录存在
            File parentDir = file.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }

            try (FileOutputStream fos = new FileOutputStream(file)) {
                fos.write(bytes);
                fos.flush();
            }
            DebugLogUtil.info("文件保存成功: " + filePath);
        } catch (Exception e) {
            logger.error("[cf] Failed to save file: {}", filePath, e);
            throw new IOException("Failed to save file: " + filePath, e);
        }
    }
    
    /**
     * 将字节数组转换为十六进制字符串
     * 
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    // 如果文件夹不存在，则创建文件夹，如果解压出的文件名已存在，则会覆盖
    private void unzipFile(String zipFilePath, String destDirectory) {
        File destDir = new File(destDirectory);
        if (!destDir.exists()) {
            destDir.mkdirs();
        }
        
        try {
            // 使用ZipFile而不是ZipInputStream，对ZIP格式的兼容性更好
            java.util.zip.ZipFile zipFile = new java.util.zip.ZipFile(zipFilePath);
            
            // 获取所有的文件条目
            java.util.Enumeration<? extends java.util.zip.ZipEntry> entries = zipFile.entries();
            
            while (entries.hasMoreElements()) {
                java.util.zip.ZipEntry entry = entries.nextElement();
                String entryName = entry.getName();
                
                File entryFile = new File(destDir, entryName);
                
                if (entry.isDirectory()) {
                    // 确保目录存在
                    entryFile.mkdirs();
                } else {
                    // 确保父目录存在
                    entryFile.getParentFile().mkdirs();
                    
                    try (java.io.InputStream in = zipFile.getInputStream(entry);
                         java.io.FileOutputStream out = new java.io.FileOutputStream(entryFile)) {
                        
                        byte[] buffer = new byte[4096];
                        int bytesRead;
                        while ((bytesRead = in.read(buffer)) != -1) {
                            out.write(buffer, 0, bytesRead);
                        }
                        out.flush();
                    }
                }
            }
            
            // 关闭ZipFile
            zipFile.close();
            
            DebugLogUtil.info("成功解压文件: " + zipFilePath);
            
        } catch (java.io.IOException e) {
            String errorMsg = "[cf] Error unzipping file: " + zipFilePath + " - " + e.getMessage();
            logger.error(errorMsg, e);
            throw new RuntimeException(errorMsg, e);
        } finally {
            // 删除zip文件
            deleteFile(zipFilePath);
        }
    }

    private void deleteFile(String filePath) {
        java.io.File zipFile = new java.io.File(filePath);
        if (zipFile.exists() && zipFile.canWrite()) {
            boolean deleted = zipFile.delete();
            if (!deleted) {
                DebugLogUtil.warn("Failed to delete file: " + filePath);
            }
        }
    }
}