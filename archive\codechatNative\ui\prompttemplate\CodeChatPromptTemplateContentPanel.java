package com.srdcloud.ideplugin.assistant.codechatNative.ui.prompttemplate;

import com.intellij.openapi.Disposable;
import com.intellij.openapi.project.Project;
import com.intellij.ui.components.JBLabel;
import com.intellij.util.ui.JBUI;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.PromptTemplate;
import com.srdcloud.ideplugin.assistant.codechatNative.ui.CodeChatMainPanel;
import com.srdcloud.ideplugin.common.icons.MyIcons;
import com.srdcloud.ideplugin.general.enums.PromptOperationType;
import com.srdcloud.ideplugin.general.utils.HtmlUtil;
import com.srdcloud.ideplugin.general.utils.UIUtil;
import com.srdcloud.ideplugin.service.UserActivityReportService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

/**
 * <AUTHOR>
 * @date 2024/5/22
 */
public class CodeChatPromptTemplateContentPanel extends JPanel implements Disposable {
    private static final Logger logger = LoggerFactory.getLogger(CodeChatPromptTemplateContentPanel.class);

    private final Project project;

    private final CodeChatMainPanel codeChatMainPanel;

    private final CodeChatPromptTemplateGroupComponent templateGroupComponent;

    private final PromptTemplate promptTemplate;

    private JPanel rendererComponent = new JPanel(new BorderLayout());
    private JPanel contentPanel = new JPanel(new BorderLayout());
    private JPanel staredSwitchPanel = new JPanel(new BorderLayout());
    private final JLabel titleLabel = new JLabel();
    private final JLabel contentArea = new JLabel();
    private final JBLabel staredSwitch = new JBLabel();

    public CodeChatPromptTemplateContentPanel(final Project project, final CodeChatMainPanel codeChatMainPanel, CodeChatPromptTemplateGroupComponent templateGroupComponent, final PromptTemplate promptTemplate) {
        this.project = project;
        this.codeChatMainPanel = codeChatMainPanel;
        this.templateGroupComponent = templateGroupComponent;
        this.promptTemplate = promptTemplate;

        setDoubleBuffered(true);
        setOpaque(false);
        setBorder(BorderFactory.createEmptyBorder(2, 2, 2, 2));
        setLayout(new BorderLayout(JBUI.scale(3), 0));
        UIUtil.setBackground(this);

        rendererComponent.setVisible(true);
        rendererComponent.setOpaque(false);
        rendererComponent.setBorder(JBUI.Borders.empty());
        rendererComponent.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));
        UIUtil.setBackground(rendererComponent);

        titleLabel.setVisible(true);
        titleLabel.setOpaque(false);
        UIUtil.setBackground(titleLabel);
        titleLabel.setHorizontalAlignment(SwingConstants.LEFT);

        // 模板标题截断显示
        String fixedText = promptTemplate.getName().trim();
        if (fixedText.length() > 10) {
            fixedText = fixedText.substring(0, 10) + "...";
        }
        titleLabel.setText(fixedText);
        JPanel titlePanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        UIUtil.setBackground(titlePanel);
        titlePanel.add(titleLabel);

        contentArea.setVisible(true);
        contentArea.setOpaque(false);
        UIUtil.setBackground(contentArea);
        contentArea.setHorizontalAlignment(SwingConstants.LEFT);
        UIUtil.setTextContentColor(contentArea);

        // 模板信息展示内容：有简介就使用简介，没有就使用模板详情
        String promptShowContent = StringUtils.isBlank(promptTemplate.getIntroduction()) ? promptTemplate.getContent() : promptTemplate.getIntroduction();
        promptShowContent = promptShowContent.trim();

        // 模板简介截断作为列表元素详情展示
        String fixedContent = promptShowContent;
        if (fixedContent.length() > 20) {
            fixedContent = fixedContent.substring(0, 20) + "...";
        }
        contentArea.setText(fixedContent);
        JPanel contentAreaPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        UIUtil.setBackground(contentAreaPanel);
        contentAreaPanel.add(contentArea);

        contentPanel.setVisible(true);
        contentPanel.setOpaque(false);
        UIUtil.setBackground(contentPanel);
        contentPanel.add(titlePanel, BorderLayout.NORTH);
        contentPanel.add(contentAreaPanel, BorderLayout.CENTER);
        // 模板悬浮提示详细简介
        //contentPanel.setToolTipText(HtmlUtil.wrapHtmlTooltip(FormatUtil.wrapStringByCharacterCount(promptShowContent, 18)));
        contentPanel.setToolTipText(HtmlUtil.wrapHtmlTooltip(promptShowContent));
        rendererComponent.add(contentPanel, BorderLayout.CENTER);


        boolean showStaredSwitch = !promptTemplate.isForbiddenFavorite();
        staredSwitchPanel.setOpaque(false);
        staredSwitchPanel.setVisible(showStaredSwitch);
        UIUtil.setBackground(staredSwitchPanel);
        staredSwitchPanel.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));
        staredSwitch.setIcon(getStarIcon(promptTemplate.isFavorite()));
        staredSwitch.setOpaque(false);
        staredSwitch.setVisible(showStaredSwitch);
        UIUtil.setBackground(staredSwitch);
        // 布局占位
        JPanel staredSwitchEastPanel = new JPanel();
        UIUtil.setBackground(staredSwitchEastPanel);

        staredSwitchPanel.add(staredSwitch, BorderLayout.CENTER);
        staredSwitchPanel.add(staredSwitchEastPanel, BorderLayout.EAST);

        this.add(staredSwitchPanel, BorderLayout.EAST);
        this.add(rendererComponent, BorderLayout.CENTER);

        contentPanel.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                templateGroupComponent.handlePromptTemplateSelection(promptTemplate);
                e.consume(); // 阻止事件进一步传播
            }

            @Override
            public void mouseEntered(MouseEvent e) {
                UIUtil.setTextHoverColor(titleLabel);
                UIUtil.setTextHoverColor(contentArea);
            }

            @Override
            public void mouseExited(MouseEvent e) {
                titleLabel.setForeground(null);
                UIUtil.setTextContentColor(contentArea);
            }
        });

        staredSwitchPanel.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                // 刷新模板状态
                refreshStarStatus();
                // 阻止事件进一步传播
                e.consume();
            }
        });
    }

    public void refreshStarStatus() {
        PromptOperationType type = this.promptTemplate.isFavorite() ? PromptOperationType.UNFAVORITE : PromptOperationType.FAVORITE;

        // 模板收藏/取消收藏行为上报
        UserActivityReportService.promptOpsReport(promptTemplate, type);

        // 更换模板状态
        boolean staredStatus = !this.promptTemplate.isFavorite();
        promptTemplate.setFavorite(staredStatus);

        // 更换模板图标
        SwingUtilities.invokeLater(() -> {
            staredSwitch.setIcon(getStarIcon(staredStatus));
            this.revalidate();
            this.repaint();
        });
    }

    public Icon getStarIcon(final Boolean isStared) {
        Icon starStatusIcon = MyIcons.Unstar;
        if (UIUtil.judgeBackgroudDarkTheme()) {
            if (isStared) {
                starStatusIcon = MyIcons.StaredDark;
            } else {
                starStatusIcon = MyIcons.UnstarDark;
            }
        } else {
            if (isStared) {
                starStatusIcon = MyIcons.Stared;
            } else {
                starStatusIcon = MyIcons.Unstar;
            }
        }
        return starStatusIcon;
    }

    public void dispose() {
        removeAll();
    }

    // ===========setter/getter===

}
