package com.srdcloud.ideplugin.general.enums;

/**
 * <AUTHOR>
 * @date 2024/8/8
 * @desc 开发问答发送异常情况结果枚举
 */
public enum ChatSendResult {
    /**
     * 未登录
     */
    NO_LOGIN("noLogin", "用户未登录"),

    /**
     * 服务不可达
     */
    NO_CHANNEL("noChannel", "服务不可达");


    private String code;
    private String description;

    ChatSendResult(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public void setName(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
