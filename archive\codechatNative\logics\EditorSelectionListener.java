package com.srdcloud.ideplugin.assistant.codechatNative.logics;

/**
 * @author: yangy
 * @date: 2023/12/20 18:10
 * @Desc 编辑器选中事件 监听器
 */

import com.intellij.openapi.Disposable;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.event.SelectionEvent;
import com.intellij.openapi.editor.event.SelectionListener;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Disposer;
import com.intellij.openapi.vfs.VirtualFile;
import com.srdcloud.ideplugin.assistant.codechatNative.ui.CodeChatMainPanel;
import com.srdcloud.ideplugin.assistant.codechatNative.uicomponent.CodeViewer;
import com.srdcloud.ideplugin.general.enums.AssistantChannel;
import com.srdcloud.ideplugin.general.enums.Language;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;

public class EditorSelectionListener implements SelectionListener, Disposable {

    private static final Logger logger = LoggerFactory.getLogger(EditorSelectionListener.class);

    // 聊天主面板
    private CodeChatMainPanel codeChatMainPanel;

    // 聊天主面板-右侧区域
    private JPanel mainPanelContentRight;

    // 代码浮窗面板
    private JPanel flowCodeViewPanel;

    private Project project;

    public EditorSelectionListener(JPanel flowCodeViewPanel, Project project, CodeChatMainPanel codeChatMainPanel, JPanel mainPanelContentRight) {
        this.flowCodeViewPanel = flowCodeViewPanel;
        this.project = project;
        this.codeChatMainPanel = codeChatMainPanel;
        this.mainPanelContentRight = mainPanelContentRight;
    }

    /**
     * 编辑器选中事件处理
     */
    @Override
    public void selectionChanged(SelectionEvent e) {
        // 获取当前编辑器
        Editor editor = e.getEditor();
        // 获取编辑器中选中内容
        String selectedText = editor.getSelectionModel().getSelectedText();
        // 设置主窗体右侧区域划分比例
        //mainPanelContentRight.setProportion(proportionBase);
        // 清空浮窗内容
        flowCodeViewPanel.removeAll();
        // 如果选中内容非空
        if (selectedText != null && !selectedText.isEmpty()) {
            // 设置浮窗可见
            flowCodeViewPanel.setVisible(true);

            // 调整主窗体右侧比例，腾出位置给下方代码选中预览区域
            //float proportion = 0.66F;
            //mainPanelContentRight.setProportion(proportion);
            mainPanelContentRight.revalidate();
            mainPanelContentRight.repaint();

            // 获取文档类型，得到编程语言种类
            VirtualFile virtualFile = FileDocumentManager.getInstance().getFile(editor.getDocument());
            String extName = Language.Companion.detectLanguageName(virtualFile.getName());
            // 创建代码查看器
            CodeViewer codeViewer = new CodeViewer(project, AssistantChannel.CodeChat, selectedText, codeChatMainPanel, extName, false, true, false);
            JComponent editorComponent = codeViewer.getEditorComponent();
            // 注册自动销毁：在不再需要时释放资源
            if (this != null) {
                Disposer.register(this, codeViewer);
            }
            // 添加代码查看器到浮窗面板
            flowCodeViewPanel.add(editorComponent);
            // 拼接markdown代码
            if (extName != null && !extName.isEmpty() && !extName.equals("Unknown")) {
                codeChatMainPanel.setFlowCodeViewPanelText("```" + extName.toLowerCase() + "\n" + selectedText + "\n```");
            } else {
                codeChatMainPanel.setFlowCodeViewPanelText("```code\n" + selectedText + "\n```");
            }
        } else {
            flowPanelDisable();
        }

    }

    public void flowPanelDisable() {
        flowCodeViewPanel.setVisible(false);
        flowCodeViewPanel.removeAll();
        codeChatMainPanel.setFlowCodeViewPanelText("");
        //mainPanelContentRight.setProportion(proportionBase);
    }

    @Override
    public void dispose() {

    }
}

