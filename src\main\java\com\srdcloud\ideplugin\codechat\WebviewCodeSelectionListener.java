package com.srdcloud.ideplugin.codechat;

import com.intellij.openapi.editor.event.SelectionEvent;
import com.intellij.openapi.editor.event.SelectionListener;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.enums.Language;
import com.srdcloud.ideplugin.general.utils.JsonUtil;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCommand;
import com.srdcloud.ideplugin.webview.codechat.message.CodeSelectionChangedResponse;
import com.srdcloud.ideplugin.webview.codechat.message.CodeSelectionChangedResponseData;
import org.jetbrains.annotations.NotNull;

public class WebviewCodeSelectionListener implements SelectionListener {

    private CodeChatWebview parent;

    public WebviewCodeSelectionListener(CodeChatWebview parent) {
        this.parent = parent;
    }

    @Override
    public void selectionChanged(@NotNull SelectionEvent e) {
        String code = e.getEditor().getSelectionModel().getSelectedText();

        String path = FileDocumentManager.getInstance().getFile(e.getEditor().getDocument()).getPath();
        int startOffset = e.getEditor().getSelectionModel().getSelectionStart();
        int startLine = e.getEditor().getDocument().getLineNumber(startOffset) ;
        int endOffset = e.getEditor().getSelectionModel().getSelectionEnd();
        int endLine = e.getEditor().getDocument().getLineNumber(endOffset);

        if (code != null && !code.isEmpty()) {
            String name = FileDocumentManager.getInstance().getFile(e.getEditor().getDocument()).getName();

            String extName = Language.Companion.detectLanguageName(name);
            if (!extName.isEmpty() && !Constants.UNKNOWN.equals(extName)) {
                code = "```" + extName.toLowerCase() + "\n" + code + "\n```";
            } else {
                code = "```code\n" + code + "\n```";
            }

            CodeSelectionChangedResponseData codeSelectionChangedResponseData = new CodeSelectionChangedResponseData(code, path, startLine, endLine);
            CodeSelectionChangedResponse codeSelectionChangedResponse = new CodeSelectionChangedResponse(WebViewRspCommand.CODE_SELECTION_CHANGED, codeSelectionChangedResponseData);
            parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(codeSelectionChangedResponse));
        } else {
            CodeSelectionChangedResponseData codeSelectionChangedResponseData = new CodeSelectionChangedResponseData(null, null, null, null);
            CodeSelectionChangedResponse codeSelectionChangedResponse = new CodeSelectionChangedResponse(WebViewRspCommand.CODE_SELECTION_CHANGED, codeSelectionChangedResponseData);
            parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(codeSelectionChangedResponse));
        }
    }
}
