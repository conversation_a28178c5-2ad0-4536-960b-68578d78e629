package com.srdcloud.ideplugin.webview.codechat.composer.response;

import com.srdcloud.ideplugin.webview.codechat.composer.request.ComposerRequest;

public class ComposerStopComposerChatResponseData extends ComposerResponseData {
    private String dialogId;

    public ComposerStopComposerChatResponseData(String dialogId) {
        super(ComposerRequest.REQ_TYPE_STOP_COMPOSER_CHAT);
        this.dialogId = dialogId;
    }

    public String getDialogId() {
        return dialogId;
    }
}
