package com.srdcloud.ideplugin.webview.base;

import com.intellij.openapi.Disposable;
import com.intellij.openapi.project.Project;
import com.intellij.ui.jcef.JBCefBrowser;
import com.intellij.ui.jcef.JBCefBrowserBuilder;
import com.srdcloud.ideplugin.general.utils.*;
import com.srdcloud.ideplugin.webview.base.domain.PushThemeChangedResponse;
import com.srdcloud.ideplugin.webview.base.domain.PushThemeChangedResponseData;
import com.srdcloud.ideplugin.webview.base.handler.CustomSchemeHandlerFactory;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCommand;
import org.cef.CefApp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * WebView类用于封装一个基于JBCef的Web视图
 * 它提供了加载URL、向Web视图发送消息等功能
 */
public class WebView implements Disposable {
    private static final Logger logger = LoggerFactory.getLogger(WebView.class);

    // JBCef浏览器实例，用于显示Web内容，以及接受消息
    private final JBCefBrowser browser;

    private Project project;

    // 业务侧webview业务名，决定资源加载路径
    private final String domainName;

    /**
     * 构造函数，初始化JBCef浏览器并加载指定的URL
     * 需要注册地址和资源管理
     *
     * @param domainName 当前webview项目相对路径，用于区别webview项目
     */
    public WebView(String domainName) {
        // 1、创建自定义的cefClient【偶现reload时报错】
        //JBCefClient customCefClient = JBCefApp.getInstance().createClient();
        //Disposer.register(ApplicationManager.getApplication(), customCefClient);

        // 2、创建自定义的builder
        JBCefBrowserBuilder builder = new JBCefBrowserBuilder();
        //builder.setClient(customCefClient);

        // 关闭会导致Webview窗口内鼠标闪动，打开则会导致2022.3、2023.1、2023.2、2023.3有OSR兼容bug
        // 需要根据不同版本来设定
        if (IdeUtil.getBuildNumber() < 241) {
            builder.setOffScreenRendering(false);
        } else {
            builder.setOffScreenRendering(true);
        }

        // 是否禁用右键菜单(不起效)
        builder.setEnableOpenDevToolsMenuItem(!EnvUtil.checkDebugAble());

        // 3、创建浏览器实例：位置必须要在registerAppSchemeHandler之前，否则NPE
        browser = builder.build();

        // 4、注册请求处理器，实现静态资源加载映射到本地（全局）
        this.domainName = domainName;
        registerAppSchemeHandler();

        // 5、进一步禁用webview区域右键上下文菜单
        disableRightClick();
    }

    /**
     * 注册应用程序自定义方案处理器
     * 这允许应用程序处理特定方案的URL请求
     * 为没有注册地址HTML添加虚拟URL
     */
    private void registerAppSchemeHandler() {
        try {
            CustomSchemeHandlerFactory customSchemeHandlerFactory = new CustomSchemeHandlerFactory();
            CefApp.getInstance().registerSchemeHandlerFactory("http", domainName, customSchemeHandlerFactory);
        } catch (Exception e) {
            logger.error("[cf]registerAppSchemeHandler error:\n");
            e.printStackTrace();
        }
    }

    /**
     * 注册重新注册应用程序自定义方案处理器，用于解决webview刷新后资源加载失败问题。
     * 这允许应用程序处理特定方案的URL请求
     * 为没有注册地址HTML添加虚拟URL
     */
    public void reRegisterAppSchemeHandler() {
        CefApp.getInstance().clearSchemeHandlerFactories();
        registerAppSchemeHandler();
    }

    /**
     * 生产环境，禁止webview右键菜单
     */
    private void disableRightClick() {
        if (!EnvUtil.checkDebugAble()) {
            String jsCode = "document.addEventListener('contextmenu', function(e) { " +
                    "e.preventDefault(); " +
                    "}, false);";
            getBrowser().getCefBrowser().executeJavaScript(jsCode, getBrowser().getCefBrowser().getURL(), 0);
        }
    }

    /**
     * 向Web视图发送单向消息
     * 这通过执行JavaScript的window.postMessage方法来实现
     *
     * @param data 要发送到Web视图的数据
     */
    public void sentMessageToWebview(String data) {
        if (EnvUtil.checkWebviewLogAble()) {
            DebugLogUtil.println(String.format("WebView sentMessageToWebview : %s", data));
        }

        getBrowser().getCefBrowser().executeJavaScript(
                "window.postMessage(" + data + ")",
                browser.getCefBrowser().getURL(),
                0 
        );
    }


    public void setWebviewBackground() {
        // 提取当前主题设置颜色
        if (UIUtil.judgeBackgroudDarkTheme()) {
            setDarkBackground();
        } else {
            setLightBackground();
        }
    }

    /**
     * 设置暗色背景
     */
    private void setDarkBackground() {
        // 将webview的所属元素的类设置为'vscode-dark'
        this.getBrowser().getCefBrowser().executeJavaScript(
                "document.getElementById('app').setAttribute('class', 'vscode-dark');",
                this.getBrowser().getCefBrowser().getURL(),
                0
        );

        // 将背景颜色设置为深色
        this.getBrowser().getCefBrowser().executeJavaScript(
                "document.body.style.backgroundColor = 'rgb(37, 37, 38)'",
                this.getBrowser().getCefBrowser().getURL(),
                0
        );

        // 将轮廓颜色设置为深色
        this.getBrowser().getCefBrowser().executeJavaScript(
                "document.body.style.outlineColor = 'rgba(83, 89, 93, 0.5)'",
                this.getBrowser().getCefBrowser().getURL(),
                0
        );

        // 创建主题更改响应数据对象，设置主题为 "dark"
        PushThemeChangedResponseData pushThemeChangedResponseData = new PushThemeChangedResponseData("dark");

        // 创建主题更改响应对象，包含命令和响应数据
        PushThemeChangedResponse pushThemeChangedResponse = new PushThemeChangedResponse(
                WebViewRspCommand.PUSH_THEME_CHANGED,
                pushThemeChangedResponseData
        );

        // 发送到 WebView
        this.sentMessageToWebview(JsonUtil.getInstance().toJson(pushThemeChangedResponse));
    }

    /**
     * 设置亮色背景
     */
    private void setLightBackground() {
        // 将webview的所属元素的类设置为'vscode-light'
        this.getBrowser().getCefBrowser().executeJavaScript(
                "document.getElementById('app').setAttribute('class', 'vscode-light');",
                this.getBrowser().getCefBrowser().getURL(),
                0
        );

        // 将背景颜色设置为浅色
        this.getBrowser().getCefBrowser().executeJavaScript(
                "document.body.style.backgroundColor = 'rgb(243, 243, 243)'",
                this.getBrowser().getCefBrowser().getURL(),
                0
        );

        // 将轮廓颜色设置为浅色
        this.getBrowser().getCefBrowser().executeJavaScript(
                "document.body.style.outlineColor = 'rgba(38, 119, 203, 0.18)'",
                this.getBrowser().getCefBrowser().getURL(),
                0
        );

        // 创建主题更改响应数据对象，设置主题为 "light"
        PushThemeChangedResponseData pushThemeChangedResponseData = new PushThemeChangedResponseData("light");

        // 创建主题更改响应对象，包含命令和响应数据
        PushThemeChangedResponse pushThemeChangedResponse = new PushThemeChangedResponse(
                WebViewRspCommand.PUSH_THEME_CHANGED,
                pushThemeChangedResponseData
        );

        // 发送到WebView
        this.sentMessageToWebview(JsonUtil.getInstance().toJson(pushThemeChangedResponse));
    }

    /**
     * 获取JBCef浏览器实例
     *
     * @return JBCefBrowser实例
     */
    public JBCefBrowser getBrowser() {
        return browser;
    }

    public Project getProject() {
        return project;
    }

    @Override
    public void dispose() {
        this.browser.dispose();
    }
}
