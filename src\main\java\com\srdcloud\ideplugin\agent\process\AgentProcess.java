package com.srdcloud.ideplugin.agent.process;

import com.intellij.openapi.util.SystemInfo;
import com.srdcloud.ideplugin.agent.AgentManager;
import com.srdcloud.ideplugin.agent.config.AgentConfig;
import com.srdcloud.ideplugin.agent.config.AgentPath;
import com.srdcloud.ideplugin.agent.model.AgentVersion;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import com.srdcloud.ideplugin.general.utils.FileUtil;
import com.srdcloud.ideplugin.general.utils.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.attribute.*;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

abstract public class AgentProcess {
    private Process process;
    private final String name;
    private volatile boolean running;
    private final AgentConfig agentConfig;
    private final AgentPath agentPath;
    private final AgentManager agentManager;
    private final AgentVersion agentVersion;

    private static final Logger logger = LoggerFactory.getLogger(AgentProcess.class);

    public AgentProcess(String name, AgentConfig agentConfig, AgentManager agentManager, AgentVersion agentVersion) {
        this.name = name;
        this.agentConfig = agentConfig;
        this.agentPath = new AgentPath(agentConfig.getPluginType(), name, agentVersion.getVersion());
        this.running = false;
        this.agentManager = agentManager;
        this.agentVersion = agentVersion;
    }

    abstract public ProcessBuilder buildProcess();

    public Process start() throws IOException {
        ProcessBuilder processBuilder = buildProcess();

        // 添加环境变量，处理编码问题
        processBuilder.environment().put("LANG", "en_US.UTF-8");
        processBuilder.environment().put("LC_ALL", "en_US.UTF-8");
        processBuilder.environment().put("NODE_SKIP_PLATFORM_CHECK", "1");

        DebugLogUtil.println("[cf] Starting agent:" + name + ": " + JsonUtil.getInstance().toJson(agentConfig));

        // 设置各自的环境变量
        setUpEnvironment(agentConfig, processBuilder);

        try {
            setSystemExecutePermission();
            process = processBuilder.start();
            running = true;
            startMonitoring();

            logger.info("[cf ]Started process: {}", name);

            return process;
        } catch (IOException e) {
            throw new RuntimeException("[cf ]Failed to start agent: " + name, e);
        }

    }

    private void startMonitoring() {
        Thread monitorThread = new Thread(() -> {
            while (running) {
                if (!isProcessAlive()) {
                    try {
                        restart();
                        return;
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }
                try {
                    Thread.sleep(5000); // Check every 5 seconds
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        });
        monitorThread.setDaemon(true);
        monitorThread.start();
    }

    void setSystemExecutePermission() {
        FileUtil.setFilePermission(getAgentManager().getNodePath().getAgentFilePath());
        FileUtil.setFilePermission(getAgentPath().getAgentFilePath());
        FileUtil.setFilePermission(getAgentPath().getAgentBasePath());
    }

    private boolean isProcessAlive() {
        try {
            process.exitValue();
            return false;
        } catch (IllegalThreadStateException e) {
            return true;
        }
    }

    private void restart() throws IOException {
        stop();
        Process newProcess = start();
        agentManager.onAgentRestarted(name, newProcess);
    }

    public void stop() {
        running = false;
        if (process != null) {
            try {
                process.destroy();
                // 等待进程终止，最多等待5秒
                if (process.waitFor(5, TimeUnit.SECONDS)) {
                    logger.info("[cf] Agent process {} terminated normally", name);
                } else {
                    // 如果进程没有在5秒内终止，强制终止
                    process.destroyForcibly();
                    logger.warn("[cf] Agent process {} had to be forcibly terminated", name);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                process.destroyForcibly();
                logger.warn("[cf] Interrupted while waiting for agent process {} to terminate, forcibly terminated", name);
            }
            process = null;
        }
    }

    abstract protected void setUpEnvironment(AgentConfig agentConfig, ProcessBuilder processBuilder);

    public Process getProcess() {
        return process;
    }

    public AgentConfig getPluginConfig() {
        return agentConfig;
    }

    public AgentPath getAgentPath() {
        return agentPath;
    }

    public AgentManager getAgentManager() {
        return agentManager;
    }

    abstract public String getAgentName();
}