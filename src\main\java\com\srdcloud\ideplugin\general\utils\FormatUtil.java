package com.srdcloud.ideplugin.general.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import javax.swing.*;
import java.awt.*;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class FormatUtil {

    /**
     * 根据文本框内容计算自适应大小
     * @param component
     * @return
     */
    public static Dimension getTextPreferSize(JTextArea component) {
        Dimension dim = component.getPreferredSize();
        FontMetrics fm = component.getFontMetrics(component.getFont());
        int lineHeight = fm.getHeight();
        String text = component.getText();
        String[] lines = text.split("\n");
        int splitLineCount = lines.length;
        if (text.endsWith("\n")) {
            ++splitLineCount;
        }

        int lineCount = Math.max(component.getLineCount(), splitLineCount);
        int height = lineHeight * lineCount;
        Insets insets = component.getInsets();
        height += insets.top + insets.bottom;
        if (height < lineHeight) {
            height = lineHeight;
        }
        dim.height = Math.max(height, dim.height);
        return dim;
    }

    /**
     * 基于每行汉字数量处理换行(仅适用于纯文本输入，不带换行符的大段文字显示处理）
     */
    public static String wrapStringByCharacterCount(String input, int chineseCharCount) {
        StringBuilder wrappedText = new StringBuilder();
        int width = 0;

        for (char c : input.toCharArray()) {
            // 判断是否为汉字，Unicode范围：4E00-9FA5
            if (c >= '\u4e00' && c <= '\u9fa5') {
                width += 2;
            } else {
                width += 1;
            }

            wrappedText.append(c);

            // 当累计宽度达到18个汉字的宽度时插入换行
            if (width >= chineseCharCount * 2) {
                wrappedText.append('\n');
                width = 0; // 重置宽度计数器
            }
        }

        return wrappedText.toString();
    }


    /**
     * JSON->URL参数
     *
     * @param json          JSON
     * @param isUrlEncoding 是否进行URL编码
     * @param charset       字符编码集（可空，默认：UTF-8）
     * @return URL参数
     */
    public static String json2UrlParam(String json, boolean isUrlEncoding, String charset) {
        if (StringUtil.isEmpty(json)) {
            return "";
        }

        StringBuilder urlParamStrBuilder = new StringBuilder();
        JSONObject jsonObj = JSON.parseObject(json);
        dealWithJsonEmptyValue(jsonObj);
        for (String key : jsonObj.keySet()) {
            String value = jsonObj.getString(key);
            value = StringUtil.isEmpty(value) ? "" : value;
            if (isUrlEncoding && !StringUtil.isEmpty(value)) {
                value = StringUtil.getUrlEncodeStr(value);
            }
            urlParamStrBuilder.append("&");
            urlParamStrBuilder.append(key);
            urlParamStrBuilder.append("=");
            urlParamStrBuilder.append(value);
        }
        String urlParam = urlParamStrBuilder.toString();
        return urlParam.startsWith("&") ? urlParam.substring(1, urlParam.length()) : urlParam;
    }

    public static Map<String, String> parseQueryParameters(String uri) {
        Map<String, String> params = new HashMap<>();

        try {
            URI parsedUri = new URI(uri);
            String query = parsedUri.getQuery();

            if (query != null) {
                String[] queryParameters = query.split("&");

                for (String param : queryParameters) {
                    String[] keyValue = param.split("=");
                    if (keyValue.length == 2) {
                        String key = URLDecoder.decode(keyValue[0], StandardCharsets.UTF_8.name());
                        String value = URLDecoder.decode(keyValue[1], StandardCharsets.UTF_8.name());
                        params.put(key, value);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return params;
    }

    public static Map<String, Object> parseFormTypeResp(String encodedData) {
        Map<String, Object> formData = new HashMap<>();

        try {
            String decodedData = URLDecoder.decode(encodedData, "UTF-8");
            String[] keyValuePairs = decodedData.split("&");

            for (String pair : keyValuePairs) {
                String[] keyValue = pair.split("=");
                if (keyValue.length == 2) {
                    String key = keyValue[0];
                    Object value = keyValue[1];
                    formData.put(key, value);
                }
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        return formData;
    }

    /**
     * 处理JSON空值
     *
     * @param jsonObj JSON对象
     */
    private static void dealWithJsonEmptyValue(JSONObject jsonObj) {
        if (jsonObj == null) {
            return;
        }

        for (String key : jsonObj.keySet()) {
            if (StringUtil.isEmpty(jsonObj.getString(key))) {
                jsonObj.put(key, "");
            }
        }
    }
}
