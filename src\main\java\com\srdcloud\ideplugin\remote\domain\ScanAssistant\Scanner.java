package com.srdcloud.ideplugin.remote.domain.ScanAssistant;

/**
 * <AUTHOR>
 * @date 2024/9/10
 * @desc 扫描引擎信息
 */
public class Scanner {
    // 扫描引擎编码
    private String code;
    // 扫描引擎名称：sonar/fortify
    private String name;
    // 上传文件大小限制，字节为单位小于0表示不限制
    private Integer maxFileSize;
    // 不支持的语言列表，例如：fortify：不支持C/C++；sonar：如果java源码要带上对应的class，否则不支持
    private String unsupportedLanguages;
    // 引擎返回的最大问题列表限制
    private Integer maxIssuesLimit;

    public Scanner(String code, String name, Integer maxFileSize, String unsupportedLanguages, Integer maxIssuesLimit) {
        this.code = code;
        this.name = name;
        this.maxFileSize = maxFileSize;
        this.unsupportedLanguages = unsupportedLanguages;
        this.maxIssuesLimit = maxIssuesLimit;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getMaxFileSize() {
        return maxFileSize;
    }

    public void setMaxFileSize(Integer maxFileSize) {
        this.maxFileSize = maxFileSize;
    }

    public String getUnsupportedLanguages() {
        return unsupportedLanguages;
    }

    public void setUnsupportedLanguages(String unsupportedLanguages) {
        this.unsupportedLanguages = unsupportedLanguages;
    }

    public Integer getMaxIssuesLimit() {
        return maxIssuesLimit;
    }

    public void setMaxIssuesLimit(Integer maxIssuesLimit) {
        this.maxIssuesLimit = maxIssuesLimit;
    }
}
