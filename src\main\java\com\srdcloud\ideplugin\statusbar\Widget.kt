package com.srdcloud.ideplugin.statusbar

import com.intellij.ide.util.PropertiesComponent
import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.actionSystem.DataContext
import com.intellij.openapi.actionSystem.DefaultActionGroup
import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.popup.JBPopupFactory
import com.intellij.openapi.ui.popup.ListPopup
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.wm.StatusBar
import com.intellij.openapi.wm.StatusBarWidget
import com.intellij.openapi.wm.impl.status.EditorBasedStatusBarPopup
import com.intellij.ui.AnimatedIcon
import com.srdcloud.ideplugin.agent.AgentManager
import com.srdcloud.ideplugin.codecomplete.domain.CompletionType
import com.srdcloud.ideplugin.codecomplete.listener.CompletionDocumentListener.Companion.completionEnabled
import com.srdcloud.ideplugin.codecomplete.handle.CompletionHandler
import com.srdcloud.ideplugin.codeindex.CodeIndexService
import com.srdcloud.ideplugin.common.icons.MyIcons
import com.srdcloud.ideplugin.general.constants.Constants
import com.srdcloud.ideplugin.general.utils.EnvUtil
import com.srdcloud.ideplugin.general.utils.LocalStorageUtil
import com.srdcloud.ideplugin.service.LoginService
import javax.swing.Icon
import javax.swing.JPanel

class Widget(project: Project) : EditorBasedStatusBarPopup(project, true),
    IChangeStatusBarEventProcess {
    private var statusBarPanel: StatusBarPanel? = null

    private val logger = logger<Widget>()

    private var isStart = false

    private var startTime: Long = 0

    companion object {
        const val WIDGET_ID: String = "com.srdcloud.ideplugin.statusBar.Widget" // Widget::class.java.name
    }

    override fun ID(): String = WIDGET_ID

    override fun getEditor(): Editor? {
        val editor = super.getEditor()
        return if (editor?.document?.isWritable == false) null else editor
    }

    /**
     * 获取状态栏提示文案
     */
    override fun getWidgetState(file: VirtualFile?): WidgetState {
        val tooltip = EnvUtil.isSec("海云智码","研发云")

        // 未登录
        if (LoginService.getSecideaLoginStatus() == Constants.LoginStatus_NOK) {
            // 重置补全等待状态
            CompletionHandler.workingType.set(CompletionType.NULL.tokenSize)
            return MyWidgetState(tooltip, "未登录", MyIcons.NotLoggedIn)
        }

        // 具有特定原因的异常状态
        val notifyMsg = Notify.notifyMsg
        if (notifyMsg.isNotBlank()) {
            // 刷新提示文案
            Notify.notifyMsg = "";
            return MyWidgetState(tooltip, notifyMsg, MyIcons.Disconnect)
        }

        // 被远端关闭
        if (LocalStorageUtil.checkChannelDisconnectedByRemote()) {
            return MyWidgetState(tooltip, "连接异常,请重新登录", MyIcons.Disconnect)
        }

        // 服务不可达
        if (!LocalStorageUtil.checkChannelConnected()) {
            // 重置补全等待状态
            CompletionHandler.workingType.set(CompletionType.NULL.tokenSize)
            return MyWidgetState(tooltip, "服务不可达", MyIcons.Disconnect)
        }

        // agent相关状态
        when (AgentManager.getStatus(project)) {
            AgentManager.AgentStatus.DOWNLOADING -> {
                return MyWidgetState(tooltip, "同步服务中", AnimatedIcon.Default())
            }
            AgentManager.AgentStatus.FAILURE_DOWNLOAD -> {
                return MyWidgetState(tooltip, "同步服务失败", MyIcons.AgentSyncFailure)
            }
            AgentManager.AgentStatus.FAILURE_START -> {
                return MyWidgetState(tooltip, "服务启动失败", MyIcons.AgentStartFailure)
            }
            else -> {
                // do nothing 继续执行后面的代码
            }
        }

        // 记录agent索引时间消耗
        recordIndexTimeOverhead()
        
        // 代码补全相关状态
        val type = CompletionHandler.workingType.get()
        if (type == CompletionType.AUTO.tokenSize) {
            return MyWidgetState(tooltip, "自动获取代码中...", AnimatedIcon.Default())
        } else if (type == CompletionType.MANUAL.tokenSize) {
            return MyWidgetState(tooltip, "手动获取代码中...", AnimatedIcon.Default())
        }
        val errMsg = CompletionHandler.errMsg
        if (errMsg.isNotBlank()) {
            return MyWidgetState(tooltip, errMsg, MyIcons.CompletionError)
        }

        // 常亮状态
        return if (PropertiesComponent.getInstance().completionEnabled) MyWidgetState(
            tooltip,
            "自动补全启用",
            MyIcons.CompletionEnable
        ) else MyWidgetState(
            tooltip,
            "自动补全禁用",
            MyIcons.CompletionDisable
        )
    }

    /**
     * 记录agent索引时间消耗
     */
    private fun recordIndexTimeOverhead() {
        if(CodeIndexService.getIndexStatus(project) == CodeIndexService.IndexStatus.INDEXING) {
            if (!isStart) {
                logger.info("[cf] start count indexing time for project(ms)")
                isStart = true
                startTime = System.currentTimeMillis()
            }
        }

        if (CodeIndexService.getIndexStatus(project) == CodeIndexService.IndexStatus.IDLE) {
            if (isStart) {
                val endTime = System.currentTimeMillis()
                logger.info("[cf] triggerIndexing time for project(ms) :" + (endTime - startTime))
                isStart = false
            }
        }
    }

    /**
     * 状态栏popup菜单定义
     */
    override fun createPopup(context: DataContext): ListPopup {
        // 1、创建一个 ActionGroup ，用于添加Action菜单项
        val group = DefaultActionGroup()
        val actionManager = ActionManager.getInstance()

        // 添加登入/登出菜单
        group.add(actionManager.getAction("IDEPlugin.SignInOutAction"))

        // 如果是登录状态下，则继续添加其他能力菜单
        if (LoginService.getLoginStatus() == Constants.LoginStatus_OK) {
            group.add(actionManager.getAction("IDEPlugin.ToggleCompletionEnabled"))
            group.add(actionManager.getAction("com.srdcloud.ideplugin.codecomplete.actions.ManualCompleteAction"))
//            group.add(actionManager.getAction("com.srdcloud.ideplugin.actions.ToggleToolWindowAction"))
        }
        // 添加设置页面菜单
        if (EnvUtil.isSec()) group.add(actionManager.getAction("com.srdcloud.ideplugin.actions.OpenSettings"))

        // 添加 帮助 菜单
        group.add(actionManager.getAction("com.srdcloud.ideplugin.HelpAction"))

        return JBPopupFactory.getInstance().createActionGroupPopup(
            EnvUtil.isSec("海云智码","研发云"),
            group,
            context,
            JBPopupFactory.ActionSelectionAid.SPEEDSEARCH,
            true
        )
    }

    override fun install(statusBar: StatusBar) {
        super.install(statusBar)
        // 定义 Notify.TOPIC 消息，由本类 notifyUpdate() 方法进行响应处理
        myConnection.subscribe(Notify.STATUS_BAR_UPDATE_TOPIC, this)
    }

    /**
     * 处理 Notify.TOPIC 消息
     */
    override fun statusUpdate() {
        super.update()
    }

    override fun createInstance(project: Project): StatusBarWidget = Widget(project)

    override fun createComponent(): JPanel {
        statusBarPanel = StatusBarPanel()
        return statusBarPanel!!
    }

    override fun updateComponent(state: WidgetState) {
        statusBarPanel!!.setIcon(state.icon)
        state.text?.let { statusBarPanel!!.setText(it) }
        statusBarPanel!!.toolTipText = state.toolTip
//        logger.debug("[cf] StatusBar updateComponent,text:{${state.text}}")
    }

    private class MyWidgetState(tooltip: String, text: String, icon: Icon?) :
        WidgetState(tooltip, text, true) {
        init {
            if (icon != null) {
                this.icon = icon
            }
        }
    }


}