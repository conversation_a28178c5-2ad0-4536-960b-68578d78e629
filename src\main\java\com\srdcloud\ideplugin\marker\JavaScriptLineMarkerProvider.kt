package com.srdcloud.ideplugin.marker

import com.intellij.psi.PsiElement

class JavaScriptLineMarkerProvider : BaseLineMarkerProvider() {
    companion object {
        private const val ALLOW_LANGUAGE = "JavaScript"
        val ALLOW_LANGUAGE_SUFFIX = listOf("js", "ts", "jsx", "tsx")
        val ALLOW_IDE = listOf("WebStorm", "IntelliJ IDEA")
    }

    override fun isApplicable(element: PsiElement): Boolean {
        val fileSuffix = element.containingFile.name.substringAfterLast('.', "")
        return ALLOW_IDE.contains(getIdeType()) && ALLOW_LANGUAGE_SUFFIX.contains(fileSuffix) &&
                element.node.elementType.toString().equals("JS:FUNCTION_DECLARATION", ignoreCase = true)
    }

    override fun filterFunction(element: PsiElement, bean: LineMarkerFunctionBean) {
        bean.apply {
            isUtValid = true
            isAnnotateValid = true
            isCodeExplain = true
            isOptimization = true

            val elementInfo = LineMarkerFunctionBean.getFunctionLinesCount(element)
            elementInfo?.let {
                val lineCount = it.endLine - it.startLine
                val charCount = it.endOffset - it.startOffset
                isCodeSplit = lineCount >= 20 && charCount <= 4500
                isLineAnotateValid = charCount <= 4500
            }
        }
    }

    private fun getIdeType(): String {
        // Implement IDE type detection logic
        return "WebStorm"
    }
}
