package com.srdcloud.ideplugin.marker

import com.intellij.psi.PsiElement

class PythonLineMarkerProvider : BaseLineMarkerProvider() {
    companion object {
        private const val ALLOW_LANGUAGE = "Python"
        val ALLOW_LANGUAGE_SUFFIX = listOf("py")
        val ALLOW_IDE = listOf("PyCharm", "IntelliJ IDEA")
    }

    override fun isApplicable(element: PsiElement): Boolean {
        val fileSuffix = element.containingFile.name.substringAfterLast('.', "")
        return ALLOW_IDE.contains(getIdeType()) && ALLOW_LANGUAGE_SUFFIX.contains(fileSuffix) &&
                element.node.elementType.toString().equals("Py:FUNCTION_DECLARATION", ignoreCase = true)
    }

    override fun filterFunction(element: PsiElement, bean: LineMarkerFunctionBean) {
        bean.apply {
            isUtValid = isValidForUnitTest(element)
            isAnnotateValid = true
            isCodeExplain = true
            isOptimization = true

            val elementInfo = LineMarkerFunctionBean.Companion.getFunctionLinesCount(element)
            elementInfo?.let {
                val lineCount = it.endLine - it.startLine
                val charCount = it.endOffset - it.startOffset
                isCodeSplit = lineCount >= 20 && charCount <= 4500
                isLineAnotateValid = charCount <= 4500
            }
        }
    }

    private fun isValidForUnitTest(element: PsiElement): Boolean {
        // Implement Python-specific unit test validation logic
        return true
    }

    private fun getIdeType(): String {
        // Implement IDE type detection logic
        return "PyCharm"
    }
}
