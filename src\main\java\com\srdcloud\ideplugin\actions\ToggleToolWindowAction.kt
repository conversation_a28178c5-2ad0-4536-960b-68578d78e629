package com.srdcloud.ideplugin.actions

import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.wm.ToolWindowManager
import com.srdcloud.ideplugin.common.icons.MyIcons
import com.srdcloud.ideplugin.general.constants.Constants
import com.srdcloud.ideplugin.general.utils.EnvUtil
import com.srdcloud.ideplugin.general.utils.IdeUtil
import com.srdcloud.ideplugin.general.utils.MessageBalloonNotificationUtil
import com.srdcloud.ideplugin.general.utils.UIUtil
import com.srdcloud.ideplugin.service.LoginService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.awt.event.KeyEvent

/**
 * 打开/关闭 编程助手侧窗
 * 已废弃，改为 开始聊天 快捷Action
 */
class ToggleToolWindowAction : AnAction(), DumbAware {
    val logger: Logger = LoggerFactory.getLogger(ToggleToolWindowAction::class.java)
    // reload限流判断
    var webviewLoadTimeStamp = System.currentTimeMillis()
    val RELOAD_INTERVAL: Long = (1 * 1000).toLong()

    // 限流：两次reload不能超过RELOAD_INTERVAL
    fun checkReloadAble(): Boolean {

        // 时间限流也无法起效
//        val nowTimeStamp = System.currentTimeMillis()
//        return nowTimeStamp - webviewLoadTimeStamp >= RELOAD_INTERVAL

        // 只能根据版本来禁用
        if (IdeUtil.getBuildNumber() < 232) {
            return false;
        }
        return true;
    }

    /**
     * action执行逻辑
     */
    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return

        val inputEvent = e.inputEvent
        if ((inputEvent is KeyEvent) && !checkReloadAble()) {
            MessageBalloonNotificationUtil.showCommonNotificationWithConfirm(
                project,
                "IDE版本较低，暂不支持此快捷键"
            )
            return
        }

        // 更新最近一次加载时间
//        webviewLoadTimeStamp = System.currentTimeMillis();

        // 获取侧窗实例
        val toolWindow = ToolWindowManager.getInstance(project).getToolWindow(EnvUtil.isSec("海云智码","研发云CodeFree")) ?: return

        // 点击行为：如果当前可见，则隐藏；如果当前不可见，则变为可见
        if (toolWindow.isVisible) {
            toolWindow.hide(null);
        } else {
            toolWindow.show(null);
        }
    }

    /**
     * 菜单项动态变化
     */
    override fun update(e: AnActionEvent) {
        val project = e.project ?: return
        val toolWindow = ToolWindowManager.getInstance(project).getToolWindow(EnvUtil.isSec("海云智码","研发云CodeFree")) ?: return

        var offIcon = MyIcons.off
        if (UIUtil.judgeBackgroudDarkTheme()) {
            offIcon = MyIcons.offDark
        }

        // 动态控制菜单项显示
        if(toolWindow.isVisible){
            e.presentation.icon = offIcon
            e.presentation.text = "关闭编程助手"
        }else{
            e.presentation.icon = offIcon
            e.presentation.text = "展开编程助手"
        }

        // 动态控制菜单项是否可点击
        e.presentation.isEnabled = LoginService.getLoginStatus() == Constants.LoginStatus_OK
    }

    override fun getActionUpdateThread(): ActionUpdateThread {
        return ActionUpdateThread.BGT
    }

}