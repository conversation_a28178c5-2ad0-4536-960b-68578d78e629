package com.srdcloud.ideplugin.codecomplete.actions

import com.intellij.openapi.actionSystem.ActionPromoter
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.CommonDataKeys
import com.intellij.openapi.actionSystem.DataContext
import com.srdcloud.ideplugin.codecomplete.handle.CompletionContext.Companion.getInlineCompletionContextOrNull
import java.util.stream.Collectors

class ActionsPromoter : ActionPromoter {
    override fun promote(actions: List<AnAction>, context: DataContext): List<AnAction> {
        val editor = CommonDataKeys.EDITOR.getData(context)
        val ctx = editor?.getInlineCompletionContextOrNull()
        if (editor != null && ctx != null) {
            // 补全快捷键冲突时，本插件CompletionAction相关实例优于IDEA内置的补全或者其他插件的tab补全行为
            val acs =  actions.stream()
                .filter { action: AnAction? -> action is CompletionAction }
                .collect(Collectors.toList())
            return acs
        }
        return emptyList()
    }
}