package com.srdcloud.ideplugin.general.utils

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.project.DumbAwareRunnable
import com.srdcloud.ideplugin.general.utils.ChatColorUtil.SUCCESS_COLOR
import com.srdcloud.ideplugin.ui.SecIdeaSettingsDialog
import org.apache.http.client.config.RequestConfig
import org.apache.http.client.methods.HttpGet
import org.apache.http.conn.ssl.NoopHostnameVerifier
import org.apache.http.impl.client.CloseableHttpClient
import org.apache.http.impl.client.HttpClients
import org.apache.http.ssl.SSLContexts
import java.awt.Color
import java.net.URL
import java.util.concurrent.ExecutionException
import javax.swing.SwingWorker

object ConnectStatusForSecUtil {
    fun testConnection(address: String, dialog: SecIdeaSettingsDialog?) {
        val worker = object : SwingWorker<<PERSON><PERSON><PERSON>, Void>() {
            override fun doInBackground(): Boolean {
                val queryAddress = "$address/sca-api/sysinfo/sdlSiteManager/queryOne"
                pluginSettings().testConnection = connect(address) || connect(queryAddress)
                return pluginSettings().testConnection
            }

            override fun done() {
                try {
                    val testConnectionResult = get()
                    if (dialog != null) {
                        if (testConnectionResult) {
                            dialog.connectionLabel.text = "当前网络连接通畅"
                            dialog.connectionLabel.foreground = SUCCESS_COLOR
                        } else {
                            if(!address.contains("oscap")){
                                //地址：例如 http[s]://ip:port/oscap
                                dialog.connectionLabel.text = "请检查地址格式,地址格式中需包含oscap，如：http[s]://ip:port/oscap"
                                dialog.connectionLabel.foreground = Color.RED
                            }else{
                                dialog.connectionLabel.text = "当前网络连接不通，请检查地址格式，地址格式如：http[s]://ip:port/oscap"
                                dialog.connectionLabel.foreground = Color.RED
                            }
                        }
                    }
                } catch (_: InterruptedException) {
                } catch (_: ExecutionException) {
                }
            }
        }
        // 启动 SwingWorker
        ApplicationManager.getApplication().invokeLater(DumbAwareRunnable {
            worker.execute()
        })
    }

    private fun connect(address: String): Boolean {
        try {
            val urlOjb = URL(address)
            val oc = urlOjb.openConnection()
            oc.useCaches = false
            oc.connectTimeout = 15000
            oc.readTimeout = 15000
            val input = oc.getInputStream().bufferedReader().use { it.readText() }
            val status = Regex("\"code\":(.*?),").find(input)?.groups?.get(1)?.value
            return status == "200"
        } catch (_: Exception) {
        }

        try {
            val sslContext = SSLContexts.custom().loadTrustMaterial(null) { _, _ -> true }.build()
            val closeableHttpClient: CloseableHttpClient = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(NoopHostnameVerifier()).build()
            val httpGet = HttpGet(address)
            val requestConfig = RequestConfig.custom().setConnectTimeout(15000).setSocketTimeout(15000).build()
            httpGet.config = requestConfig
            val response = closeableHttpClient.execute(httpGet)
            if (response.statusLine.statusCode == 200) {
                return true
            }
        } catch (_: Exception) {
        }
        return false
    }
}
