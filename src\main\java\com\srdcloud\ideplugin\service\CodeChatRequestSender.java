package com.srdcloud.ideplugin.service;


import com.alibaba.fastjson.JSON;
import com.intellij.openapi.application.ApplicationInfo;
import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.constants.MessageNameConstant;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.general.enums.*;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import com.srdcloud.ideplugin.general.utils.EnvUtil;
import com.srdcloud.ideplugin.general.utils.GitUtil;
import com.srdcloud.ideplugin.general.utils.IdeUtil;
import com.srdcloud.ideplugin.general.utils.LocalStorageUtil;
import com.srdcloud.ideplugin.remote.CodeAICommHandler;
import com.srdcloud.ideplugin.remote.domain.ApiResponse;
import com.srdcloud.ideplugin.remote.domain.WorkItem.WorkItemInfo;
import com.srdcloud.ideplugin.service.domain.apigw.ApigwWebsocketRequestClient;
import com.srdcloud.ideplugin.service.domain.apigw.ApigwWebsocketRequestContext;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.*;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.DialogCondition;
import com.srdcloud.ideplugin.service.domain.apigw.useractivity.UserActivityNotifyMessage;
import com.srdcloud.ideplugin.service.domain.apigw.useractivity.UserActivityNotifyPayload;
import com.srdcloud.ideplugin.service.domain.codechat.CodeChatMessageBuilder;
import com.srdcloud.ideplugin.service.domain.codechat.CodeChatSystemPrompt;
import com.srdcloud.ideplugin.webview.codechat.relatedfile.RelatedFile;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

import static com.srdcloud.ideplugin.general.utils.PluginSettingsForSecUtilKt.pluginSettings;

/**
 * <AUTHOR> yangy
 * @create 2023/9/6 15:18
 * @description :AI对话请求发送类
 */
public class CodeChatRequestSender {

    private static final Logger logger = LoggerFactory.getLogger(CodeChatRequestSender.class);

    // 自然语言编程提问封装
    public static String getNLCPromptContent(final CodeChatSystemPrompt codeChatSystemPrompt) {
        StringBuilder promptBuilder = new StringBuilder("我的问题是：\"" + codeChatSystemPrompt.getContent() + "\",问题所指的优化、修改、生成代码等要求都是指下面具体代码内容中的“问题提问位置”的所在的方法，请回答适用于该位置的片段" + codeChatSystemPrompt.getLanguage() + "代码，具体代码内容（其中“问题提问位置”不是原有代码内容）:");
        if (codeChatSystemPrompt.getCodePrefix() != null) {
            promptBuilder.append(codeChatSystemPrompt.getCodePrefix());
        }
        promptBuilder.append(" “问题提问位置“ ");
        if (codeChatSystemPrompt.getCodeSuffix() != null) {
            promptBuilder.append(codeChatSystemPrompt.getCodeSuffix());
        }
        return promptBuilder.toString();
    }

    /**
     * 发送通道注册请求消息
     */
    public static ApiResponse SendRegisterChannel() {
        if (StringUtils.isBlank(LocalStorageUtil.getSessionId())) {
            return new ApiResponse(RtnCode.NOT_LOGIN, "");
        }
        String message = getCodeChatApigwWebsocketMessage(new CodeChatMessageBuilder(MessageNameConstant.MessageName_RegisterChannel,
                null, Constants.MESSAGE_APP_GID, null, null, null, null,
                null, null, null, null, null, null,
                IdeUtil.getPluginVersion(), null, null, null,
                null, null, null, null, null, null, null, null, null));
        return CodeAICommHandler.GetInstance(ConfigWrapper.getCodeAIServerUrl()).RegisterToCodeAI(message);
    }

    /**
     * 发送用户apiKey查询请求消息
     */
    public static ApiResponse SendGetUserApiKey() {

        Project project = IdeUtil.findCurrentProject();

        // 拼接消息
        String message = getCodeChatApigwWebsocketMessage(new CodeChatMessageBuilder(MessageNameConstant.MessageName_GetUserApiKey, UUID.randomUUID().toString(),
                null, IdeUtil.getIDEType(), null, null, null, null, null, null,
                null, null, null, IdeUtil.getPluginVersion(), null, null, null,
                null, null, null, null, null, null, project == null ? null : GitUtil.getGitUrls(project), null, null));

        // 调用通道实例，发出消息
        return CodeAICommHandler.GetInstance(ConfigWrapper.getCodeAIServerUrl()).SendMessageToCodeAI(message);
    }

    /**
     * 发送用户行为埋点上报消息
     */
    public static ApiResponse SendUserActivityReport(final String activityType, final Double lines, final Integer count, final String currentProjectName, final String currentProjectGitUrl, final List<String> gitUrls, final Boolean isAuto, final Long latency) {
        // 拼接消息
        String message = getUserActivityApigwWebsocketMessage(activityType, lines, count, currentProjectName, currentProjectGitUrl, gitUrls, isAuto, latency);
        // 调用通道实例，发出消息
        return CodeAICommHandler.GetInstance(ConfigWrapper.getCodeAIServerUrl()).SendMessageToCodeAI(message);
    }

    /**
     * 发送代码补全请求消息
     */
    public static ApiResponse SendCodeGenRequest(String reqId, String fileName, String prefix, String suffix, int maxNewTokens, List<String> stopWords, List<CodeMessage.ImportSnippets> importSnippets, List<String> gitUrls) {
        if (LoginService.getLoginStatus() != Constants.LoginStatus_OK) {
            return new ApiResponse(RtnCode.NOT_LOGIN, "");
        }
        // 拼接消息
        String message = getCodeChatApigwWebsocketMessage(new CodeChatMessageBuilder(MessageNameConstant.MessageName_CodeGenRequest, reqId,
                null, IdeUtil.getIDEType(), fileName, prefix, suffix,
                Language.Companion.detectLanguageName(fileName), null, null, null,
                maxNewTokens, stopWords, IdeUtil.getPluginVersion(), null, null, null,
                null, null, null, null, importSnippets, null, gitUrls, null, null));
        // 调用通道实例，发出消息
        return CodeAICommHandler.GetInstance(ConfigWrapper.getCodeAIServerUrl()).SendMessageToCodeAI(message);

    }

    /**
     * 发送编程助手对话消息
     */
    public static ApiResponse SendChatGenRequest(String reqId, String question, Integer kbId, int maxNewTokens, Integer manualType, QuestionType questionType,
                                                 String dialogId, String parentReqId, String promptTemplateId,
                                                 DialogCondition modelRouteCondition, List<CodeAIRequestPromptChat> prompts, QuoteItem quote, List<RelatedFile> relatedFiles, List<String> gitUrls, List<WorkItemInfo> workItemList) {
        if (LoginService.getLoginStatus() != Constants.LoginStatus_OK) {
            return new ApiResponse(RtnCode.NOT_LOGIN, "");
        }

        // 拼接消息
        String message = getCodeChatApigwWebsocketMessage(new CodeChatMessageBuilder(MessageNameConstant.MessageName_CodeChatRequest,
                reqId, null, IdeUtil.getIDEType(), null, null, null,
                null, question, kbId, manualType, maxNewTokens, null,
                IdeUtil.getPluginVersion(), questionType, dialogId, parentReqId, promptTemplateId, modelRouteCondition, prompts, quote, null, relatedFiles, gitUrls, null, workItemList));
        // 调用通道实例，发出消息
        return CodeAICommHandler.GetInstance(ConfigWrapper.getCodeAIServerUrl()).SendMessageToCodeAI(message);

    }

    /**
     * 发送自然语言编程对话消息
     */
    public static ApiResponse SendCodeAIByChatRequest(String reqId, String fileName, String prefix, String suffix, String question, Integer manualType, int maxNewTokens) {

        if (LoginService.getLoginStatus() != Constants.LoginStatus_OK) {
            return new ApiResponse(RtnCode.NOT_LOGIN, "");
        }
        // 拼接消息
        String message = getCodeChatApigwWebsocketMessage(new CodeChatMessageBuilder(MessageNameConstant.MessageName_CodeChatRequest,
                reqId, null, IdeUtil.getIDEType(), fileName, prefix, suffix,
                Language.Companion.detectLanguageName(fileName), question, null, manualType, maxNewTokens, null,
                IdeUtil.getPluginVersion(), null, null, null,
                null, null, null, null, null, null, null, null, null));
        // 调用通道实例，发出消息
        return CodeAICommHandler.GetInstance(ConfigWrapper.getCodeAIServerUrl()).SendMessageToCodeAI(message);

    }

    /**
     * 发送AI Commit对话消息
     */
    public static ApiResponse SendCommitChatRequest(String reqId, List<String> gitUrls, List<String> diffList, List<WorkItemInfo> workItemList) {
        if (LoginService.getLoginStatus() != Constants.LoginStatus_OK) {
            return new ApiResponse(RtnCode.NOT_LOGIN, "");
        }

        String message = getCodeChatApigwWebsocketMessage(new CodeChatMessageBuilder(MessageNameConstant.MessageName_CommitChatRequest, reqId,
                null, IdeUtil.getIDEType(), null, null, null,
                null, null, null, null,
                null, null, IdeUtil.getPluginVersion(), null, null, null,
                null, null, null, null, null, null, gitUrls, diffList, workItemList));

        // 调用通道实例，发出消息
        return CodeAICommHandler.GetInstance(ConfigWrapper.getCodeAIServerUrl()).SendMessageToCodeAI(message);

    }

    /**
     * 拼接用户行为埋点上报业务请求消息
     */
    @NotNull
    private static String getUserActivityApigwWebsocketMessage(final String activityType, final Double lines, final Integer count, final String currentProjectName, final String currentProjectGitUrl, final List<String> gitUrls, final Boolean isAuto, final Long latency) {
        UserActivityNotifyMessage requestMessage = new UserActivityNotifyMessage();

        // 1、设置消息体名称
        requestMessage.setMessageName(MessageNameConstant.MessageName_UserActivityNotify);

        // 2、设置消息通信上下文
        ApigwWebsocketRequestContext apigwWebsocketRequestContext = new ApigwWebsocketRequestContext();
        // 鉴权信息
        String apiKey = LocalStorageUtil.getApikey();
        // 兑换过apiKey后，则使用apiKey
        if (StringUtils.isNotBlank(apiKey)) {
            apigwWebsocketRequestContext.setApiKey(apiKey);
        } else {
            // 首次登录，使用sessionid鉴权
            apigwWebsocketRequestContext.setSessionId(LocalStorageUtil.getSessionId());
        }
        apigwWebsocketRequestContext.setInvokerId(LocalStorageUtil.getUserId());
        apigwWebsocketRequestContext.setReqId(UUID.randomUUID().toString());
        requestMessage.setContext(apigwWebsocketRequestContext);

        // 3、设置消息体具体内容
        // 填充client信息
        ApigwWebsocketRequestClient client = new ApigwWebsocketRequestClient();
        client.setType(IdeUtil.getIDEType());
        client.setVersion(ApplicationInfo.getInstance().getFullVersion());
        client.setPluginVersion(IdeUtil.getPluginVersion());
        // 0531 增加git地址与项目名上报
        client.setProjectName(currentProjectName);
        client.setGitUrl(currentProjectGitUrl);
        // 0320 git地址支持多个代码仓库
        client.setGitUrls(gitUrls);

        UserActivityNotifyPayload payload = new UserActivityNotifyPayload();
        payload.setClient(client);
        payload.setActivityType(activityType);

        if (Objects.nonNull(lines)) {
            payload.setLines(lines);
        }
        if (Objects.nonNull(count)) {
            payload.setCount(count);
        }
        if (Objects.nonNull(isAuto)) {
            payload.setIsAuto(isAuto);
        }
        if (Objects.nonNull(latency)) {
            payload.setLatency(latency);
        }
        requestMessage.setPayload(payload);
        // 4、拼接成websocket通信消息格式
        return Constants.WB_CHANNEL_START + JSON.toJSONString(requestMessage) + Constants.WB_CHANNEL_END;
    }

    /**
     * 拼接WebSocket通信业务请求消息
     */
    @NotNull
    private static String getCodeChatApigwWebsocketMessage(CodeChatMessageBuilder codeChatMessageBuilder) {
        CodeChatRequestMessage apigwWebsocketRequestMessage = new CodeChatRequestMessage();

        // 1、设置消息体名称
        apigwWebsocketRequestMessage.setMessageName(codeChatMessageBuilder.getMessageName());

        // 2、设置消息通信上下文
        ApigwWebsocketRequestContext apigwWebsocketRequestContext = new ApigwWebsocketRequestContext();
        apigwWebsocketRequestContext.setMessageName(codeChatMessageBuilder.getMessageName());
        if (StringUtils.isNotBlank(codeChatMessageBuilder.getAppGid())) {
            apigwWebsocketRequestContext.setAppGId(codeChatMessageBuilder.getAppGid());
        }
        if (StringUtils.isNotBlank(codeChatMessageBuilder.getReqId())) {
            apigwWebsocketRequestContext.setReqId(codeChatMessageBuilder.getReqId());
        }
        // 插件版本
        apigwWebsocketRequestContext.setVersion(IdeUtil.getPluginVersion());
        if (StringUtils.isNotBlank(codeChatMessageBuilder.getVersion())) {
            apigwWebsocketRequestContext.setVersion(codeChatMessageBuilder.getVersion());
        }
        // 鉴权信息
        String apiKey = LocalStorageUtil.getApikey();
        // 兑换过apiKey后，则使用apiKey
        if (StringUtils.isNotBlank(apiKey)) {
            apigwWebsocketRequestContext.setApiKey(apiKey);
        } else {
            // 首次登录，使用sessionid鉴权
            apigwWebsocketRequestContext.setSessionId(LocalStorageUtil.getSessionId());
        }
        apigwWebsocketRequestContext.setInvokerId(LocalStorageUtil.getUserId());
        apigwWebsocketRequestMessage.setContext(apigwWebsocketRequestContext);

        // 3、设置消息体具体内容
        CodeChatRequestPayload apigwWebsocketPayload = new CodeChatRequestPayload();
        if (StringUtils.isNotBlank(codeChatMessageBuilder.getClientType())) {
            apigwWebsocketPayload.setClientType(codeChatMessageBuilder.getClientType());
        }
        if (StringUtils.isNotBlank(codeChatMessageBuilder.getClientVersion())) {
            apigwWebsocketPayload.setClientVersion(codeChatMessageBuilder.getClientVersion());
        }
        if (StringUtils.isNotBlank(codeChatMessageBuilder.getClientPlatform())) {
            apigwWebsocketPayload.setClientPlatform(codeChatMessageBuilder.getClientPlatform());
        }
        String address = Objects.requireNonNullElse(pluginSettings().getAddress(), "");
        if (codeChatMessageBuilder.getGitUrls() != null) {
            apigwWebsocketPayload.setGitUrls(EnvUtil.isSec(List.of(address), codeChatMessageBuilder.getGitUrls()));
        } else {
            apigwWebsocketPayload.setGitUrls(EnvUtil.isSec(List.of(address), List.of("")));
        }

        // 根据不同的SubServiceType，拼接具体的codeMessage信息内容
        final CodeMessage codeMessage = buildCodeMessage(codeChatMessageBuilder);
            apigwWebsocketPayload.setMessages(codeMessage);
        apigwWebsocketPayload.setMessages(codeMessage);
        if (!JSON.toJSONString(apigwWebsocketPayload).isEmpty()) {
            apigwWebsocketRequestMessage.setPayload(apigwWebsocketPayload);
        }

        // 5、拼接成websocket通信消息格式
        return Constants.WB_CHANNEL_START + JSON.toJSONString(apigwWebsocketRequestMessage) + Constants.WB_CHANNEL_END;
    }

    /**
     * 根据不同对话场景，构造具体的codeMessage内容
     */
    private static CodeMessage buildCodeMessage(final CodeChatMessageBuilder codeChatMessageBuilder) {
        CodeMessage codeMessage = new CodeMessage();
        final Integer messageType = codeChatMessageBuilder.getMessageType();
        // 1、基础信息设置
        switch (codeChatMessageBuilder.getMessageName()) {
            // 对话类消息：设置对话业务场景SubServiceType
            case MessageNameConstant.MessageName_CodeChatRequest:
                codeMessage.setSub_service(SubServiceType.ASSISTANT.getName());
                if (messageType != null) {
                    codeMessage.setSub_service(ChatMessageType.getSubServiceTypeByMessageType(messageType).getName());
                }
                break;
            // 代码补全消息：设置文件名、推理上下文代码内容、代码语言
            case MessageNameConstant.MessageName_CodeGenRequest:
                if (codeChatMessageBuilder.getFileName() != null && !codeChatMessageBuilder.getFileName().isEmpty()) {
                    codeMessage.setFilename(codeChatMessageBuilder.getFileName());
                }
                if (codeChatMessageBuilder.getPrefix() != null && !codeChatMessageBuilder.getPrefix().isEmpty()) {
                    codeMessage.setPrefix(codeChatMessageBuilder.getPrefix());
                } else {
                    codeMessage.setPrefix("");
                }
                if (codeChatMessageBuilder.getSuffix() != null && !codeChatMessageBuilder.getSuffix().isEmpty()) {
                    codeMessage.setSuffix(codeChatMessageBuilder.getSuffix());
                } else {
                    codeMessage.setSuffix("");
                }
                if (codeChatMessageBuilder.getLanguage() != null && !codeChatMessageBuilder.getLanguage().isEmpty()) {
                    codeMessage.setLanguage(codeChatMessageBuilder.getLanguage());
                }
                break;
            default:
                break;
        }

        // 2、自然语言问答类业务消息
        if (codeChatMessageBuilder.getQuestion() != null) {
            CodeAIRequestPromptChat codeAIRequestPromptChat = new CodeAIRequestPromptChat();
            codeAIRequestPromptChat.setRole(PromptRoleType.USER.getType());

            // 自然语言编程提问特殊处理
            if (messageType != null && messageType == ChatMessageType.MANUAL_GENERATE.getType()) {
                LinkedList<CodeAIRequestPromptChat> payloadPrompts = new LinkedList<>();
                // 自然语言编程的对话背景prompt限定
                CodeAIRequestPromptChat codeAIRequestPromptChatFirst = new CodeAIRequestPromptChat();
                codeAIRequestPromptChatFirst.setRole(PromptRoleType.USER.getType());
                codeAIRequestPromptChatFirst.setContent("接下来的回答只需要返回问题答案的纯代码部分即可。");
                payloadPrompts.add(codeAIRequestPromptChatFirst);
                // 自然语言编程的业务prompt
                CodeChatSystemPrompt codeChatSystemPrompt = new CodeChatSystemPrompt();
                codeChatSystemPrompt.setCodePrefix(codeChatMessageBuilder.getPrefix());
                codeChatSystemPrompt.setCodeSuffix(codeChatMessageBuilder.getSuffix());
                codeChatSystemPrompt.setLanguage(codeChatMessageBuilder.getLanguage());
                codeChatSystemPrompt.setContent(codeChatMessageBuilder.getQuestion());
                codeAIRequestPromptChat.setContent(getNLCPromptContent(codeChatSystemPrompt));
                payloadPrompts.add(codeAIRequestPromptChat);
                // 设置codemessage提问prompt内容
                codeMessage.setPrompts(payloadPrompts);
            } else { //插件Action触发提问或编程助手自由对话
                // copy提问时，会话原有的PromptList
                ArrayList<CodeAIRequestPromptChat> payloadPrompts = new ArrayList<>(codeChatMessageBuilder.getPrompts());

                // 根据当前问题，新增一个Prompt，加入提问列表
                // 240531版本：由后端自行拼接业务场景前缀prompt，插件只传入问题本身作为content
                codeAIRequestPromptChat.setContent(codeChatMessageBuilder.getQuestion());

                // 250321版本：文件问答提问
                if (CollectionUtils.isNotEmpty(codeChatMessageBuilder.getRelatedFiles())) {
                    codeAIRequestPromptChat.setFiles(codeChatMessageBuilder.getRelatedFiles());
                }
                // 250530版本：关联工作项问答
                if (CollectionUtils.isNotEmpty(codeChatMessageBuilder.getWorkItemList())) {
                    codeAIRequestPromptChat.setWorkItems(codeChatMessageBuilder.getWorkItemList());
                }

                payloadPrompts.add(codeAIRequestPromptChat);

                // 设置本次提问codemessage的prompt内容
                codeMessage.setPrompts(payloadPrompts);
            }
        }

        //3、不同业务特有字段，按需设置
        if (codeChatMessageBuilder.getMaxNewTokens() != null) {
            codeMessage.setMax_new_tokens(codeChatMessageBuilder.getMaxNewTokens());
        }

        //4、设置本次对话要求AI停止作答的stop word判断词
        if (codeChatMessageBuilder.getStopWords() != null) {
            codeMessage.setStop_words(codeChatMessageBuilder.getStopWords());
        }

        //5、设置跨文件关联信息
        if (codeChatMessageBuilder.getImportSnippets() != null) {
            codeMessage.setImportSnippets(codeChatMessageBuilder.getImportSnippets());
        }

        // 6、设置提问类型
        if (codeChatMessageBuilder.getQuestionType() != null) {
            codeMessage.setQuestionType(codeChatMessageBuilder.getQuestionType().getName());
        }

        // 7、设置会话id
        if (codeChatMessageBuilder.getDialogId() != null) {
            codeMessage.setDialogId(codeChatMessageBuilder.getDialogId());
        }

        // 8、不是新会话首轮问答对，则设置上一次问答对的请求id
        if (codeChatMessageBuilder.getParentReqId() != null && (codeMessage.getPrompts() != null && codeMessage.getPrompts().size() != 2)) {
            codeMessage.setParentReqId(codeChatMessageBuilder.getParentReqId());
        }

        // 8、设置知识库id
        if (Objects.nonNull(codeChatMessageBuilder.getKbId())) {
            codeMessage.setKbId(codeChatMessageBuilder.getKbId());
        }

        // 设置知识库提问点击引用透传内容
        if (Objects.nonNull(codeChatMessageBuilder.getQuote())) {
            codeMessage.setQuote(Collections.singletonList(codeChatMessageBuilder.getQuote()));
        }

        // 9、设置会话路由规则条件
        if (Objects.nonNull(codeChatMessageBuilder.getModelRouteCondition()) && Objects.nonNull(codeChatMessageBuilder.getModelRouteCondition().getPayload()) && Objects.nonNull(codeChatMessageBuilder.getModelRouteCondition().getPayload().getTemplateId())) {
            codeMessage.setModelRouteCondition(codeChatMessageBuilder.getModelRouteCondition());
        }

        // 10、commit问答业务：diff列表、关联工作项
        if (Objects.nonNull(codeChatMessageBuilder.getDiffList())) {
            codeMessage.setDiffList(codeChatMessageBuilder.getDiffList());
            if (CollectionUtils.isNotEmpty(codeChatMessageBuilder.getWorkItemList())) {
                codeMessage.setWorkItemList(codeChatMessageBuilder.getWorkItemList());
            }
        }

        // debug用
        //DebugLogUtil.println(String.format("buildCodeMessage: %s", JsonUtil.getInstance().toJson(codeMessage)));

        return codeMessage;
    }


}
