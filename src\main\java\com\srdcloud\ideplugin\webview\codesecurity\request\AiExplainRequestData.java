package com.srdcloud.ideplugin.webview.codesecurity.request;

import com.srdcloud.ideplugin.remote.domain.ScanAssistant.Issue;
import com.srdcloud.ideplugin.webview.base.domain.WebViewReqType;

import java.util.List;

public class AiExplainRequestData  extends WebViewReqType {

    private String scannerEngine;

    private String filePath;

    private List<Issue> issueList;

    public AiExplainRequestData(String reqType, String scannerEngine, String filePath, List<Issue> issueList) {
        this.reqType = reqType;
        this.scannerEngine = scannerEngine;
        this.filePath = filePath;
        this.issueList = issueList;
    }

    public void setScannerEngine(String scannerEngine) {
        this.scannerEngine = scannerEngine;
    }

    public String getScannerEngine() {return scannerEngine;}

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFilePath() {return filePath;}

    public void setIssueList(List<Issue> issueList) {
        this.issueList = issueList;
    }

    public List<Issue> getIssueList() {
        return issueList;
    }

}
