package com.srdcloud.ideplugin.webview.workitem.request;

import com.srdcloud.ideplugin.webview.base.domain.WebViewCommand;

/**
 * <AUTHOR>
 * @date 2025/5/23
 */
public class WorkItemRequest extends WebViewCommand {
    private WorkItemCommonRequestData data;

    public WorkItemRequest(WorkItemCommonRequestData data) {
        this.data = data;
    }

    public WorkItemRequest(String command, WorkItemCommonRequestData data) {
        super(command);
        this.data = data;
    }

    public WorkItemCommonRequestData getData() {
        return data;
    }

    public void setData(WorkItemCommonRequestData data) {
        this.data = data;
    }
}
