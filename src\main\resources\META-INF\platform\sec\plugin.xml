<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin require-restart="true">
    <id>com.secIdea.secIdea-intellij-plugin-c10</id>
    <name>海云智码</name>
    <version>3.5.0</version>
    <vendor>secidea</vendor>

    <!-- Product and plugin compatibility requirements.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-compatibility.html -->
    <depends>com.intellij.modules.platform</depends>
    <depends>Git4Idea</depends>

    <depends optional="true" config-file="src/main/resources/META-INF/language-specific/javaPlugin.xml">com.intellij.modules.java</depends>

    <actions>
        <!-- 主菜单栏(已下线，改为statusBar弹出菜单) -->
        <!--        <group id="IDEPlugin.MainMenu" text="海云智码" description="海云智码" popup="true">-->
        <!--            <add-to-group group-id="MainMenu" anchor="last"/>-->
        <!--        </group>-->

        <!-- statusBar弹出菜单项Action注册 -->
        <action id="IDEPlugin.SignInOutAction"
                class="com.srdcloud.ideplugin.actions.SignInOutAction"
                text="登录/登出平台"
                description="Startup the SRD CodeFree plugin"/>
        <action class="com.srdcloud.ideplugin.codecomplete.actions.ToggleCompletionEnabledAction"
                id="IDEPlugin.ToggleCompletionEnabled"
                text="启用/禁用代码自动补全">
            <keyboard-shortcut keymap="$default" first-keystroke="ctrl shift alt O"/>
        </action>
        <action id="com.srdcloud.ideplugin.codecomplete.actions.ManualCompleteAction"
                class="com.srdcloud.ideplugin.codecomplete.actions.ManualCompleteAction"
                text="手动发起补全请求">
            <keyboard-shortcut keymap="$default" first-keystroke="ctrl ENTER"/>
        </action>
        <action id="com.srdcloud.ideplugin.HelpAction"
                class="com.srdcloud.ideplugin.actions.HelpAction"
                text="帮助"
                description="To docs"/>
        <!--        已下线，改为 右键开始聊天 Action-->
        <!--        <action id="com.srdcloud.ideplugin.actions.ToggleToolWindowAction"-->
        <!--                class="com.srdcloud.ideplugin.actions.ToggleToolWindowAction"-->
        <!--                text="打开编程助手"-->
        <!--                description="Open code chat">-->
        <!--            <keyboard-shortcut keymap="$default" first-keystroke="shift alt K"/>-->
        <!--        </action>-->
        <!--        <action id="com.srdcloud.ideplugin.actions.ToggleToolWindowAction"-->
        <!-- 设置页面 -->
        <action id="com.srdcloud.ideplugin.actions.OpenSettings"
                class="com.srdcloud.ideplugin.actions.OpenSettings"
                text="设置"
                description="打开设置页"/>
        <!-- 右键菜单 -->
        <group id="com.srdcloud.ideplugin.codechat.actions.RightMenuMainGroup" text="海云智码" popup="true"
               icon="/icons/sec/secidea_light_16.svg">
            <add-to-group group-id="EditorPopupMenu" anchor="first"/>
            <action id="RightMenuExplainAction"
                    class="com.srdcloud.ideplugin.webview.codechat.actions.rightmenu.RightMenuExplainAction"
                    popup="true" text="解释代码"/>
            <action id="RightMenuCommentAction"
                    class="com.srdcloud.ideplugin.webview.codechat.actions.rightmenu.RightMenuCommentAction"
                    popup="true" text="生成代码注释"/>
            <action id="RightMenuTestAction"
                    class="com.srdcloud.ideplugin.webview.codechat.actions.rightmenu.RightMenuTestAction"
                    popup="true" text="生成单元测试"/>
            <action id="RightMenuOptimizeAction"
                    class="com.srdcloud.ideplugin.webview.codechat.actions.rightmenu.RightMenuOptimizeAction"
                    popup="true" text="生成优化建议"/>
            <action id="RightMenuStartChatAction"
                    class="com.srdcloud.ideplugin.webview.codechat.actions.rightmenu.RightMenuStartChatAction"
                    popup="true" text="开始聊天">
                <keyboard-shortcut keymap="$default" first-keystroke="shift alt K"/>
            </action>
            <!--            <action id="RightMenuQuestionDialogAction"-->
            <!--                    class="com.srdcloud.ideplugin.codenatural.QuestionDialogAction"-->
            <!--                    popup="true" text="自然语言编程"/>-->
        </group>

        <!-- 自然语言编程相关 -->
        <!--        <action id="com.srdcloud.ideplugin.codenatural.QuestionDialogAction"-->
        <!--                class="com.srdcloud.ideplugin.codenatural.QuestionDialogAction" text="自然语言编程">-->
        <!--            <keyboard-shortcut keymap="$default" first-keystroke="shift alt A"/>-->
        <!--        </action>-->
        <!--        <group id="IDEPlugin.NLC" description="Srd CodeFree nlc">-->
        <!--            <action class="com.srdcloud.ideplugin.codenatural.NLCSelectAction"-->
        <!--                    id="com.srdcloud.ideplugin.codenatural.select"-->
        <!--                    text="确认生成代码">-->
        <!--                <keyboard-shortcut keymap="$default" first-keystroke="alt Y"/>-->
        <!--            </action>-->
        <!--            <action class="com.srdcloud.ideplugin.codenatural.NLCDeleteAction"-->
        <!--                    id="com.srdcloud.ideplugin.codenatural.delete"-->
        <!--                    text="删除高亮区域">-->
        <!--                <keyboard-shortcut keymap="$default" first-keystroke="alt K"/>-->
        <!--            </action>-->
        <!--        </group>-->

        <!-- 代码补全相关 -->
        <action class="com.srdcloud.ideplugin.codecomplete.actions.AcceptAction"
                id="IDEPlugin.InsertInlineCompletionAction"
                text="选择当前补全建议">
            <keyboard-shortcut keymap="$default" first-keystroke="TAB"/>
        </action>
        <action id="com.srdcloud.ideplugin.codecomplete.actions.PreviousAction"
                class="com.srdcloud.ideplugin.codecomplete.actions.PreviousAction" text="查看上一条补全建议">
            <keyboard-shortcut keymap="$default" first-keystroke="alt OPEN_BRACKET"/>
        </action>
        <action id="com.srdcloud.ideplugin.codecomplete.actions.NextAction"
                class="com.srdcloud.ideplugin.codecomplete.actions.NextAction" text="查看下一条补全建议">
            <keyboard-shortcut keymap="$default" first-keystroke="alt CLOSE_BRACKET"/>
        </action>

    </actions>

    <!-- Extension points defined by the plugin.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-extension-points.html -->
    <extensions defaultExtensionNs="com.intellij">
        <postStartupActivity implementation="com.srdcloud.ideplugin.service.StartupActivityService"/>
        <!--用于给不同的语言做marker，从而在不同语言编辑器左侧添加代码快捷操作-->
        <codeInsight.lineMarkerProvider language="JAVA" implementationClass="com.srdcloud.ideplugin.marker.JavaLineMarkerProvider"/>
        <codeInsight.lineMarkerProvider language="JavaScript"
                                        implementationClass="com.srdcloud.ideplugin.marker.JavaScriptLineMarkerProvider"/>
        <codeInsight.lineMarkerProvider language="Python"
                                        implementationClass="com.srdcloud.ideplugin.marker.PythonLineMarkerProvider"/>
        <codeInsight.lineMarkerProvider language="ObjectiveC"
                                        implementationClass="com.srdcloud.ideplugin.marker.CLionLineMarkerProvider"/>
        <codeInsight.lineMarkerProvider language="go" implementationClass="com.srdcloud.ideplugin.marker.GoLineMarkerProvider"/>
        <codeInsight.lineMarkerProvider language="kotlin"
                                        implementationClass="com.srdcloud.ideplugin.marker.KotlinLineMarkerProvider"/>
        <!--设置页面-->
        <projectConfigurable id="com.srdcloud.ideplugin.settings.SecIdeaProjectSettingsConfigurable"
                             instance="com.srdcloud.ideplugin.settings.SecIdeaProjectSettingsConfigurable"
                             displayName="海云智码"/>

        <!-- 右边侧边栏菜单注册 -->
        <toolWindow id="海云智码" anchor="right" icon="/icons/sec/secidea_dark_16.svg" canCloseContents="true"
                    factoryClass="com.srdcloud.ideplugin.assistant.AssistantToolWindow"/>

        <!-- 状态条注册-->
        <statusBarWidgetFactory implementation="com.srdcloud.ideplugin.statusbar.WidgetFactory"
                                id="com.srdcloud.ideplugin.statusbar.Widget" order="first"/>

        <!-- 右下角通知组注册 -->
        <notificationGroup id="com.srdcloud.IDEPlugin" displayType="BALLOON"/>

        <!-- project层级service托管 -->
        <projectService
                serviceImplementation="com.srdcloud.ideplugin.webview.treesitter.WebviewTreeSitter"/>
        <projectService serviceImplementation="com.srdcloud.ideplugin.codecomplete.rlcc.ProjectCodeIndexer"/>
        <projectService
                serviceImplementation="com.srdcloud.ideplugin.codecomplete.rlcc.CodeObjectIndexSystemUpdateTask"/>
        <projectService
                serviceImplementation="com.srdcloud.ideplugin.webview.codechat.login.LoginCheckTask"/>
        <!--        <projectService-->
        <!--                serviceImplementation="com.srdcloud.ideplugin.webview.treesitter.WebviewTreeSitterLoadTask"/>-->
        <!--        <projectService-->
        <!--                serviceImplementation="com.srdcloud.ideplugin.webview.codechat.CodeChatWebviewRefreshTask"/>-->

        <projectService
                serviceImplementation="com.srdcloud.ideplugin.agent.AgentManager"/>
        <projectService
                serviceImplementation="com.srdcloud.ideplugin.codeindex.CodeIndexService"/>
        <projectService
                serviceImplementation="com.srdcloud.ideplugin.composer.ComposerService"/>

        <projectService
                serviceImplementation="com.srdcloud.ideplugin.webview.codechat.CodeChatWebview"/>
        <projectService
                serviceImplementation="com.srdcloud.ideplugin.webview.codechat.actions.FixExceptionAction"/>
        <projectService
                serviceImplementation="com.srdcloud.ideplugin.webview.codechat.actions.rightmenu.RightMenuOptimizeAction"/>
        <projectService
                serviceImplementation="com.srdcloud.ideplugin.webview.codechat.actions.rightmenu.RightMenuTestAction"/>
        <projectService
                serviceImplementation="com.srdcloud.ideplugin.webview.codechat.actions.rightmenu.RightMenuCommentAction"/>
        <projectService
                serviceImplementation="com.srdcloud.ideplugin.webview.codechat.actions.rightmenu.RightMenuExplainAction"/>

        <!-- 编辑器工厂事件监听器 -->
        <editorFactoryListener
                implementation="com.srdcloud.ideplugin.codecomplete.listener.CompletionEditorFactoryListener"/>

        <!-- 编辑器行为处理器 -->
        <editorActionHandler
                id="com.srdcloud.completion.escape" action="EditorEscape"
                implementationClass="com.srdcloud.ideplugin.codecomplete.listener.EscapeHandler"
                order="before hide-hints"/>

        <!-- action快捷键冲突促进器 -->
        <actionPromoter implementation="com.srdcloud.ideplugin.codecomplete.actions.ActionsPromoter"/>

        <!-- 浏览器请求处理监听器 -->
        <httpRequestHandler implementation="com.srdcloud.ideplugin.service.BrowserRedirectListener"/>

    </extensions>

    <!-- 应用层监听器注册 -->
    <applicationListeners>
        <listener class="com.srdcloud.ideplugin.service.IDELifecycleService"
                  topic="com.intellij.ide.AppLifecycleListener"/>
    </applicationListeners>

    <!-- 项目层级监听器注册 -->
    <projectListeners>
        <listener class="com.srdcloud.ideplugin.service.ProjectLifecycleService"
                  topic="com.intellij.openapi.project.ProjectManagerListener"/>
    </projectListeners>
</idea-plugin>