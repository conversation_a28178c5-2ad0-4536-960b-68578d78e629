package com.srdcloud.ideplugin.codecomplete.render

import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.colors.EditorFontType
import com.intellij.openapi.util.SystemInfo
import com.intellij.ui.JBColor
import org.jetbrains.annotations.ApiStatus
import java.awt.Color
import java.awt.Font
import javax.swing.text.StyleContext


/**
 * 行内补全渲染字体效果
 */
@ApiStatus.Experimental
object RenderFontUtils {
    fun font(editor: Editor): Font {
        val font = editor.colorsScheme.getFont(EditorFontType.ITALIC)
        val fontWithFallback = if (SystemInfo.isMac) Font(
            font.getFamily(),
            font.getStyle(),
            font.getSize()
        ) else (StyleContext().getFont(font.getFamily(), font.getStyle(), font.getSize()))

        return fontWithFallback
    }

    val color: Color
        get() {
            return JBColor.GRAY
        }
}
