import{s as ut,g as rt,a as at,b as lt,c as F,x as ct,y as ot,j as v,A as ht,l as At,z as We,h as z,d as pt,as as Re}from"./index.js";var Ve=function(){var e=function(x,u,a,h){for(a=a||{},h=x.length;h--;a[x[h]]=u);return a},i=[1,17],r=[1,18],l=[1,19],o=[1,39],A=[1,40],g=[1,25],D=[1,23],B=[1,24],_=[1,31],fe=[1,32],de=[1,33],Ee=[1,34],Ce=[1,35],me=[1,36],be=[1,26],ge=[1,27],ke=[1,28],Te=[1,29],d=[1,43],Fe=[1,30],E=[1,42],<PERSON>=[1,44],m=[1,41],k=[1,45],ye=[1,9],c=[1,8,9],Y=[1,56],j=[1,57],Q=[1,58],X=[1,59],H=[1,60],De=[1,61],Be=[1,62],W=[1,8,9,39],Ge=[1,74],M=[1,8,9,12,13,21,37,39,42,59,60,61,62,63,64,65,70,72],q=[1,8,9,12,13,19,21,37,39,42,46,59,60,61,62,63,64,65,70,72,74,80,95,97,98],J=[13,74,80,95,97,98],G=[13,64,65,74,80,95,97,98],Ue=[13,59,60,61,62,63,74,80,95,97,98],_e=[1,93],Z=[1,110],$=[1,108],ee=[1,102],te=[1,103],se=[1,104],ie=[1,105],ne=[1,106],ue=[1,107],re=[1,109],Se=[1,8,9,37,39,42],ae=[1,8,9,21],ze=[1,8,9,78],S=[1,8,9,21,73,74,78,80,81,82,83,84,85],Ne={trace:function(){},yy:{},symbols_:{error:2,start:3,mermaidDoc:4,statements:5,graphConfig:6,CLASS_DIAGRAM:7,NEWLINE:8,EOF:9,statement:10,classLabel:11,SQS:12,STR:13,SQE:14,namespaceName:15,alphaNumToken:16,className:17,classLiteralName:18,GENERICTYPE:19,relationStatement:20,LABEL:21,namespaceStatement:22,classStatement:23,memberStatement:24,annotationStatement:25,clickStatement:26,styleStatement:27,cssClassStatement:28,noteStatement:29,direction:30,acc_title:31,acc_title_value:32,acc_descr:33,acc_descr_value:34,acc_descr_multiline_value:35,namespaceIdentifier:36,STRUCT_START:37,classStatements:38,STRUCT_STOP:39,NAMESPACE:40,classIdentifier:41,STYLE_SEPARATOR:42,members:43,CLASS:44,ANNOTATION_START:45,ANNOTATION_END:46,MEMBER:47,SEPARATOR:48,relation:49,NOTE_FOR:50,noteText:51,NOTE:52,direction_tb:53,direction_bt:54,direction_rl:55,direction_lr:56,relationType:57,lineType:58,AGGREGATION:59,EXTENSION:60,COMPOSITION:61,DEPENDENCY:62,LOLLIPOP:63,LINE:64,DOTTED_LINE:65,CALLBACK:66,LINK:67,LINK_TARGET:68,CLICK:69,CALLBACK_NAME:70,CALLBACK_ARGS:71,HREF:72,STYLE:73,ALPHA:74,stylesOpt:75,CSSCLASS:76,style:77,COMMA:78,styleComponent:79,NUM:80,COLON:81,UNIT:82,SPACE:83,BRKT:84,PCT:85,commentToken:86,textToken:87,graphCodeTokens:88,textNoTagsToken:89,TAGSTART:90,TAGEND:91,"==":92,"--":93,DEFAULT:94,MINUS:95,keywords:96,UNICODE_TEXT:97,BQUOTE_STR:98,$accept:0,$end:1},terminals_:{2:"error",7:"CLASS_DIAGRAM",8:"NEWLINE",9:"EOF",12:"SQS",13:"STR",14:"SQE",19:"GENERICTYPE",21:"LABEL",31:"acc_title",32:"acc_title_value",33:"acc_descr",34:"acc_descr_value",35:"acc_descr_multiline_value",37:"STRUCT_START",39:"STRUCT_STOP",40:"NAMESPACE",42:"STYLE_SEPARATOR",44:"CLASS",45:"ANNOTATION_START",46:"ANNOTATION_END",47:"MEMBER",48:"SEPARATOR",50:"NOTE_FOR",52:"NOTE",53:"direction_tb",54:"direction_bt",55:"direction_rl",56:"direction_lr",59:"AGGREGATION",60:"EXTENSION",61:"COMPOSITION",62:"DEPENDENCY",63:"LOLLIPOP",64:"LINE",65:"DOTTED_LINE",66:"CALLBACK",67:"LINK",68:"LINK_TARGET",69:"CLICK",70:"CALLBACK_NAME",71:"CALLBACK_ARGS",72:"HREF",73:"STYLE",74:"ALPHA",76:"CSSCLASS",78:"COMMA",80:"NUM",81:"COLON",82:"UNIT",83:"SPACE",84:"BRKT",85:"PCT",88:"graphCodeTokens",90:"TAGSTART",91:"TAGEND",92:"==",93:"--",94:"DEFAULT",95:"MINUS",96:"keywords",97:"UNICODE_TEXT",98:"BQUOTE_STR"},productions_:[0,[3,1],[3,1],[4,1],[6,4],[5,1],[5,2],[5,3],[11,3],[15,1],[15,2],[17,1],[17,1],[17,2],[17,2],[17,2],[10,1],[10,2],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,2],[10,2],[10,1],[22,4],[22,5],[36,2],[38,1],[38,2],[38,3],[23,1],[23,3],[23,4],[23,6],[41,2],[41,3],[25,4],[43,1],[43,2],[24,1],[24,2],[24,1],[24,1],[20,3],[20,4],[20,4],[20,5],[29,3],[29,2],[30,1],[30,1],[30,1],[30,1],[49,3],[49,2],[49,2],[49,1],[57,1],[57,1],[57,1],[57,1],[57,1],[58,1],[58,1],[26,3],[26,4],[26,3],[26,4],[26,4],[26,5],[26,3],[26,4],[26,4],[26,5],[26,4],[26,5],[26,5],[26,6],[27,3],[28,3],[75,1],[75,3],[77,1],[77,2],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[86,1],[86,1],[87,1],[87,1],[87,1],[87,1],[87,1],[87,1],[87,1],[89,1],[89,1],[89,1],[89,1],[16,1],[16,1],[16,1],[16,1],[18,1],[51,1]],performAction:function(u,a,h,n,f,t,U){var s=t.length-1;switch(f){case 8:this.$=t[s-1];break;case 9:case 11:case 12:this.$=t[s];break;case 10:case 13:this.$=t[s-1]+t[s];break;case 14:case 15:this.$=t[s-1]+"~"+t[s]+"~";break;case 16:n.addRelation(t[s]);break;case 17:t[s-1].title=n.cleanupLabel(t[s]),n.addRelation(t[s-1]);break;case 27:this.$=t[s].trim(),n.setAccTitle(this.$);break;case 28:case 29:this.$=t[s].trim(),n.setAccDescription(this.$);break;case 30:n.addClassesToNamespace(t[s-3],t[s-1]);break;case 31:n.addClassesToNamespace(t[s-4],t[s-1]);break;case 32:this.$=t[s],n.addNamespace(t[s]);break;case 33:this.$=[t[s]];break;case 34:this.$=[t[s-1]];break;case 35:t[s].unshift(t[s-2]),this.$=t[s];break;case 37:n.setCssClass(t[s-2],t[s]);break;case 38:n.addMembers(t[s-3],t[s-1]);break;case 39:n.setCssClass(t[s-5],t[s-3]),n.addMembers(t[s-5],t[s-1]);break;case 40:this.$=t[s],n.addClass(t[s]);break;case 41:this.$=t[s-1],n.addClass(t[s-1]),n.setClassLabel(t[s-1],t[s]);break;case 42:n.addAnnotation(t[s],t[s-2]);break;case 43:this.$=[t[s]];break;case 44:t[s].push(t[s-1]),this.$=t[s];break;case 45:break;case 46:n.addMember(t[s-1],n.cleanupLabel(t[s]));break;case 47:break;case 48:break;case 49:this.$={id1:t[s-2],id2:t[s],relation:t[s-1],relationTitle1:"none",relationTitle2:"none"};break;case 50:this.$={id1:t[s-3],id2:t[s],relation:t[s-1],relationTitle1:t[s-2],relationTitle2:"none"};break;case 51:this.$={id1:t[s-3],id2:t[s],relation:t[s-2],relationTitle1:"none",relationTitle2:t[s-1]};break;case 52:this.$={id1:t[s-4],id2:t[s],relation:t[s-2],relationTitle1:t[s-3],relationTitle2:t[s-1]};break;case 53:n.addNote(t[s],t[s-1]);break;case 54:n.addNote(t[s]);break;case 55:n.setDirection("TB");break;case 56:n.setDirection("BT");break;case 57:n.setDirection("RL");break;case 58:n.setDirection("LR");break;case 59:this.$={type1:t[s-2],type2:t[s],lineType:t[s-1]};break;case 60:this.$={type1:"none",type2:t[s],lineType:t[s-1]};break;case 61:this.$={type1:t[s-1],type2:"none",lineType:t[s]};break;case 62:this.$={type1:"none",type2:"none",lineType:t[s]};break;case 63:this.$=n.relationType.AGGREGATION;break;case 64:this.$=n.relationType.EXTENSION;break;case 65:this.$=n.relationType.COMPOSITION;break;case 66:this.$=n.relationType.DEPENDENCY;break;case 67:this.$=n.relationType.LOLLIPOP;break;case 68:this.$=n.lineType.LINE;break;case 69:this.$=n.lineType.DOTTED_LINE;break;case 70:case 76:this.$=t[s-2],n.setClickEvent(t[s-1],t[s]);break;case 71:case 77:this.$=t[s-3],n.setClickEvent(t[s-2],t[s-1]),n.setTooltip(t[s-2],t[s]);break;case 72:this.$=t[s-2],n.setLink(t[s-1],t[s]);break;case 73:this.$=t[s-3],n.setLink(t[s-2],t[s-1],t[s]);break;case 74:this.$=t[s-3],n.setLink(t[s-2],t[s-1]),n.setTooltip(t[s-2],t[s]);break;case 75:this.$=t[s-4],n.setLink(t[s-3],t[s-2],t[s]),n.setTooltip(t[s-3],t[s-1]);break;case 78:this.$=t[s-3],n.setClickEvent(t[s-2],t[s-1],t[s]);break;case 79:this.$=t[s-4],n.setClickEvent(t[s-3],t[s-2],t[s-1]),n.setTooltip(t[s-3],t[s]);break;case 80:this.$=t[s-3],n.setLink(t[s-2],t[s]);break;case 81:this.$=t[s-4],n.setLink(t[s-3],t[s-1],t[s]);break;case 82:this.$=t[s-4],n.setLink(t[s-3],t[s-1]),n.setTooltip(t[s-3],t[s]);break;case 83:this.$=t[s-5],n.setLink(t[s-4],t[s-2],t[s]),n.setTooltip(t[s-4],t[s-1]);break;case 84:this.$=t[s-2],n.setCssStyle(t[s-1],t[s]);break;case 85:n.setCssClass(t[s-1],t[s]);break;case 86:this.$=[t[s]];break;case 87:t[s-2].push(t[s]),this.$=t[s-2];break;case 89:this.$=t[s-1]+t[s];break}},table:[{3:1,4:2,5:3,6:4,7:[1,6],10:5,16:37,17:20,18:38,20:7,22:8,23:9,24:10,25:11,26:12,27:13,28:14,29:15,30:16,31:i,33:r,35:l,36:21,40:o,41:22,44:A,45:g,47:D,48:B,50:_,52:fe,53:de,54:Ee,55:Ce,56:me,66:be,67:ge,69:ke,73:Te,74:d,76:Fe,80:E,95:C,97:m,98:k},{1:[3]},{1:[2,1]},{1:[2,2]},{1:[2,3]},e(ye,[2,5],{8:[1,46]}),{8:[1,47]},e(c,[2,16],{21:[1,48]}),e(c,[2,18]),e(c,[2,19]),e(c,[2,20]),e(c,[2,21]),e(c,[2,22]),e(c,[2,23]),e(c,[2,24]),e(c,[2,25]),e(c,[2,26]),{32:[1,49]},{34:[1,50]},e(c,[2,29]),e(c,[2,45],{49:51,57:54,58:55,13:[1,52],21:[1,53],59:Y,60:j,61:Q,62:X,63:H,64:De,65:Be}),{37:[1,63]},e(W,[2,36],{37:[1,65],42:[1,64]}),e(c,[2,47]),e(c,[2,48]),{16:66,74:d,80:E,95:C,97:m},{16:37,17:67,18:38,74:d,80:E,95:C,97:m,98:k},{16:37,17:68,18:38,74:d,80:E,95:C,97:m,98:k},{16:37,17:69,18:38,74:d,80:E,95:C,97:m,98:k},{74:[1,70]},{13:[1,71]},{16:37,17:72,18:38,74:d,80:E,95:C,97:m,98:k},{13:Ge,51:73},e(c,[2,55]),e(c,[2,56]),e(c,[2,57]),e(c,[2,58]),e(M,[2,11],{16:37,18:38,17:75,19:[1,76],74:d,80:E,95:C,97:m,98:k}),e(M,[2,12],{19:[1,77]}),{15:78,16:79,74:d,80:E,95:C,97:m},{16:37,17:80,18:38,74:d,80:E,95:C,97:m,98:k},e(q,[2,112]),e(q,[2,113]),e(q,[2,114]),e(q,[2,115]),e([1,8,9,12,13,19,21,37,39,42,59,60,61,62,63,64,65,70,72],[2,116]),e(ye,[2,6],{10:5,20:7,22:8,23:9,24:10,25:11,26:12,27:13,28:14,29:15,30:16,17:20,36:21,41:22,16:37,18:38,5:81,31:i,33:r,35:l,40:o,44:A,45:g,47:D,48:B,50:_,52:fe,53:de,54:Ee,55:Ce,56:me,66:be,67:ge,69:ke,73:Te,74:d,76:Fe,80:E,95:C,97:m,98:k}),{5:82,10:5,16:37,17:20,18:38,20:7,22:8,23:9,24:10,25:11,26:12,27:13,28:14,29:15,30:16,31:i,33:r,35:l,36:21,40:o,41:22,44:A,45:g,47:D,48:B,50:_,52:fe,53:de,54:Ee,55:Ce,56:me,66:be,67:ge,69:ke,73:Te,74:d,76:Fe,80:E,95:C,97:m,98:k},e(c,[2,17]),e(c,[2,27]),e(c,[2,28]),{13:[1,84],16:37,17:83,18:38,74:d,80:E,95:C,97:m,98:k},{49:85,57:54,58:55,59:Y,60:j,61:Q,62:X,63:H,64:De,65:Be},e(c,[2,46]),{58:86,64:De,65:Be},e(J,[2,62],{57:87,59:Y,60:j,61:Q,62:X,63:H}),e(G,[2,63]),e(G,[2,64]),e(G,[2,65]),e(G,[2,66]),e(G,[2,67]),e(Ue,[2,68]),e(Ue,[2,69]),{8:[1,89],23:90,38:88,41:22,44:A},{16:91,74:d,80:E,95:C,97:m},{43:92,47:_e},{46:[1,94]},{13:[1,95]},{13:[1,96]},{70:[1,97],72:[1,98]},{21:Z,73:$,74:ee,75:99,77:100,79:101,80:te,81:se,82:ie,83:ne,84:ue,85:re},{74:[1,111]},{13:Ge,51:112},e(c,[2,54]),e(c,[2,117]),e(M,[2,13]),e(M,[2,14]),e(M,[2,15]),{37:[2,32]},{15:113,16:79,37:[2,9],74:d,80:E,95:C,97:m},e(Se,[2,40],{11:114,12:[1,115]}),e(ye,[2,7]),{9:[1,116]},e(ae,[2,49]),{16:37,17:117,18:38,74:d,80:E,95:C,97:m,98:k},{13:[1,119],16:37,17:118,18:38,74:d,80:E,95:C,97:m,98:k},e(J,[2,61],{57:120,59:Y,60:j,61:Q,62:X,63:H}),e(J,[2,60]),{39:[1,121]},{23:90,38:122,41:22,44:A},{8:[1,123],39:[2,33]},e(W,[2,37],{37:[1,124]}),{39:[1,125]},{39:[2,43],43:126,47:_e},{16:37,17:127,18:38,74:d,80:E,95:C,97:m,98:k},e(c,[2,70],{13:[1,128]}),e(c,[2,72],{13:[1,130],68:[1,129]}),e(c,[2,76],{13:[1,131],71:[1,132]}),{13:[1,133]},e(c,[2,84],{78:[1,134]}),e(ze,[2,86],{79:135,21:Z,73:$,74:ee,80:te,81:se,82:ie,83:ne,84:ue,85:re}),e(S,[2,88]),e(S,[2,90]),e(S,[2,91]),e(S,[2,92]),e(S,[2,93]),e(S,[2,94]),e(S,[2,95]),e(S,[2,96]),e(S,[2,97]),e(S,[2,98]),e(c,[2,85]),e(c,[2,53]),{37:[2,10]},e(Se,[2,41]),{13:[1,136]},{1:[2,4]},e(ae,[2,51]),e(ae,[2,50]),{16:37,17:137,18:38,74:d,80:E,95:C,97:m,98:k},e(J,[2,59]),e(c,[2,30]),{39:[1,138]},{23:90,38:139,39:[2,34],41:22,44:A},{43:140,47:_e},e(W,[2,38]),{39:[2,44]},e(c,[2,42]),e(c,[2,71]),e(c,[2,73]),e(c,[2,74],{68:[1,141]}),e(c,[2,77]),e(c,[2,78],{13:[1,142]}),e(c,[2,80],{13:[1,144],68:[1,143]}),{21:Z,73:$,74:ee,77:145,79:101,80:te,81:se,82:ie,83:ne,84:ue,85:re},e(S,[2,89]),{14:[1,146]},e(ae,[2,52]),e(c,[2,31]),{39:[2,35]},{39:[1,147]},e(c,[2,75]),e(c,[2,79]),e(c,[2,81]),e(c,[2,82],{68:[1,148]}),e(ze,[2,87],{79:135,21:Z,73:$,74:ee,80:te,81:se,82:ie,83:ne,84:ue,85:re}),e(Se,[2,8]),e(W,[2,39]),e(c,[2,83])],defaultActions:{2:[2,1],3:[2,2],4:[2,3],78:[2,32],113:[2,10],116:[2,4],126:[2,44],139:[2,35]},parseError:function(u,a){if(a.recoverable)this.trace(u);else{var h=new Error(u);throw h.hash=a,h}},parse:function(u){var a=this,h=[0],n=[],f=[null],t=[],U=this.table,s="",le=0,Ke=0,tt=2,Ye=1,st=t.slice.call(arguments,1),b=Object.create(this.lexer),I={yy:{}};for(var ve in this.yy)Object.prototype.hasOwnProperty.call(this.yy,ve)&&(I.yy[ve]=this.yy[ve]);b.setInput(u,I.yy),I.yy.lexer=b,I.yy.parser=this,typeof b.yylloc=="undefined"&&(b.yylloc={});var xe=b.yylloc;t.push(xe);var it=b.options&&b.options.ranges;typeof I.yy.parseError=="function"?this.parseError=I.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function nt(){var L;return L=n.pop()||b.lex()||Ye,typeof L!="number"&&(L instanceof Array&&(n=L,L=n.pop()),L=a.symbols_[L]||L),L}for(var T,R,y,Oe,P={},ce,N,je,oe;;){if(R=h[h.length-1],this.defaultActions[R]?y=this.defaultActions[R]:((T===null||typeof T=="undefined")&&(T=nt()),y=U[R]&&U[R][T]),typeof y=="undefined"||!y.length||!y[0]){var Ie="";oe=[];for(ce in U[R])this.terminals_[ce]&&ce>tt&&oe.push("'"+this.terminals_[ce]+"'");b.showPosition?Ie="Parse error on line "+(le+1)+`:
`+b.showPosition()+`
Expecting `+oe.join(", ")+", got '"+(this.terminals_[T]||T)+"'":Ie="Parse error on line "+(le+1)+": Unexpected "+(T==Ye?"end of input":"'"+(this.terminals_[T]||T)+"'"),this.parseError(Ie,{text:b.match,token:this.terminals_[T]||T,line:b.yylineno,loc:xe,expected:oe})}if(y[0]instanceof Array&&y.length>1)throw new Error("Parse Error: multiple actions possible at state: "+R+", token: "+T);switch(y[0]){case 1:h.push(T),f.push(b.yytext),t.push(b.yylloc),h.push(y[1]),T=null,Ke=b.yyleng,s=b.yytext,le=b.yylineno,xe=b.yylloc;break;case 2:if(N=this.productions_[y[1]][1],P.$=f[f.length-N],P._$={first_line:t[t.length-(N||1)].first_line,last_line:t[t.length-1].last_line,first_column:t[t.length-(N||1)].first_column,last_column:t[t.length-1].last_column},it&&(P._$.range=[t[t.length-(N||1)].range[0],t[t.length-1].range[1]]),Oe=this.performAction.apply(P,[s,Ke,le,I.yy,y[1],f,t].concat(st)),typeof Oe!="undefined")return Oe;N&&(h=h.slice(0,-1*N*2),f=f.slice(0,-1*N),t=t.slice(0,-1*N)),h.push(this.productions_[y[1]][0]),f.push(P.$),t.push(P._$),je=U[h[h.length-2]][h[h.length-1]],h.push(je);break;case 3:return!0}}return!0}},et=function(){var x={EOF:1,parseError:function(a,h){if(this.yy.parser)this.yy.parser.parseError(a,h);else throw new Error(a)},setInput:function(u,a){return this.yy=a||this.yy||{},this._input=u,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var u=this._input[0];this.yytext+=u,this.yyleng++,this.offset++,this.match+=u,this.matched+=u;var a=u.match(/(?:\r\n?|\n).*/g);return a?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),u},unput:function(u){var a=u.length,h=u.split(/(?:\r\n?|\n)/g);this._input=u+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-a),this.offset-=a;var n=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),h.length-1&&(this.yylineno-=h.length-1);var f=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:h?(h.length===n.length?this.yylloc.first_column:0)+n[n.length-h.length].length-h[0].length:this.yylloc.first_column-a},this.options.ranges&&(this.yylloc.range=[f[0],f[0]+this.yyleng-a]),this.yyleng=this.yytext.length,this},more:function(){return this._more=!0,this},reject:function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},less:function(u){this.unput(this.match.slice(u))},pastInput:function(){var u=this.matched.substr(0,this.matched.length-this.match.length);return(u.length>20?"...":"")+u.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var u=this.match;return u.length<20&&(u+=this._input.substr(0,20-u.length)),(u.substr(0,20)+(u.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var u=this.pastInput(),a=new Array(u.length+1).join("-");return u+this.upcomingInput()+`
`+a+"^"},test_match:function(u,a){var h,n,f;if(this.options.backtrack_lexer&&(f={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(f.yylloc.range=this.yylloc.range.slice(0))),n=u[0].match(/(?:\r\n?|\n).*/g),n&&(this.yylineno+=n.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:n?n[n.length-1].length-n[n.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+u[0].length},this.yytext+=u[0],this.match+=u[0],this.matches=u,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(u[0].length),this.matched+=u[0],h=this.performAction.call(this,this.yy,this,a,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),h)return h;if(this._backtrack){for(var t in f)this[t]=f[t];return!1}return!1},next:function(){if(this.done)return this.EOF;this._input||(this.done=!0);var u,a,h,n;this._more||(this.yytext="",this.match="");for(var f=this._currentRules(),t=0;t<f.length;t++)if(h=this._input.match(this.rules[f[t]]),h&&(!a||h[0].length>a[0].length)){if(a=h,n=t,this.options.backtrack_lexer){if(u=this.test_match(h,f[t]),u!==!1)return u;if(this._backtrack){a=!1;continue}else return!1}else if(!this.options.flex)break}return a?(u=this.test_match(a,f[n]),u!==!1?u:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){var a=this.next();return a||this.lex()},begin:function(a){this.conditionStack.push(a)},popState:function(){var a=this.conditionStack.length-1;return a>0?this.conditionStack.pop():this.conditionStack[0]},_currentRules:function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},topState:function(a){return a=this.conditionStack.length-1-Math.abs(a||0),a>=0?this.conditionStack[a]:"INITIAL"},pushState:function(a){this.begin(a)},stateStackSize:function(){return this.conditionStack.length},options:{},performAction:function(a,h,n,f){switch(n){case 0:return 53;case 1:return 54;case 2:return 55;case 3:return 56;case 4:break;case 5:break;case 6:return this.begin("acc_title"),31;case 7:return this.popState(),"acc_title_value";case 8:return this.begin("acc_descr"),33;case 9:return this.popState(),"acc_descr_value";case 10:this.begin("acc_descr_multiline");break;case 11:this.popState();break;case 12:return"acc_descr_multiline_value";case 13:return 8;case 14:break;case 15:return 7;case 16:return 7;case 17:return"EDGE_STATE";case 18:this.begin("callback_name");break;case 19:this.popState();break;case 20:this.popState(),this.begin("callback_args");break;case 21:return 70;case 22:this.popState();break;case 23:return 71;case 24:this.popState();break;case 25:return"STR";case 26:this.begin("string");break;case 27:return 73;case 28:return this.begin("namespace"),40;case 29:return this.popState(),8;case 30:break;case 31:return this.begin("namespace-body"),37;case 32:return this.popState(),39;case 33:return"EOF_IN_STRUCT";case 34:return 8;case 35:break;case 36:return"EDGE_STATE";case 37:return this.begin("class"),44;case 38:return this.popState(),8;case 39:break;case 40:return this.popState(),this.popState(),39;case 41:return this.begin("class-body"),37;case 42:return this.popState(),39;case 43:return"EOF_IN_STRUCT";case 44:return"EDGE_STATE";case 45:return"OPEN_IN_STRUCT";case 46:break;case 47:return"MEMBER";case 48:return 76;case 49:return 66;case 50:return 67;case 51:return 69;case 52:return 50;case 53:return 52;case 54:return 45;case 55:return 46;case 56:return 72;case 57:this.popState();break;case 58:return"GENERICTYPE";case 59:this.begin("generic");break;case 60:this.popState();break;case 61:return"BQUOTE_STR";case 62:this.begin("bqstring");break;case 63:return 68;case 64:return 68;case 65:return 68;case 66:return 68;case 67:return 60;case 68:return 60;case 69:return 62;case 70:return 62;case 71:return 61;case 72:return 59;case 73:return 63;case 74:return 64;case 75:return 65;case 76:return 21;case 77:return 42;case 78:return 95;case 79:return"DOT";case 80:return"PLUS";case 81:return 81;case 82:return 78;case 83:return 84;case 84:return 84;case 85:return 85;case 86:return"EQUALS";case 87:return"EQUALS";case 88:return 74;case 89:return 12;case 90:return 14;case 91:return"PUNCTUATION";case 92:return 80;case 93:return 97;case 94:return 83;case 95:return 83;case 96:return 9}},rules:[/^(?:.*direction\s+TB[^\n]*)/,/^(?:.*direction\s+BT[^\n]*)/,/^(?:.*direction\s+RL[^\n]*)/,/^(?:.*direction\s+LR[^\n]*)/,/^(?:%%(?!\{)*[^\n]*(\r?\n?)+)/,/^(?:%%[^\n]*(\r?\n)*)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:classDiagram-v2\b)/,/^(?:classDiagram\b)/,/^(?:\[\*\])/,/^(?:call[\s]+)/,/^(?:\([\s]*\))/,/^(?:\()/,/^(?:[^(]*)/,/^(?:\))/,/^(?:[^)]*)/,/^(?:["])/,/^(?:[^"]*)/,/^(?:["])/,/^(?:style\b)/,/^(?:namespace\b)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:[{])/,/^(?:[}])/,/^(?:$)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:\[\*\])/,/^(?:class\b)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:[}])/,/^(?:[{])/,/^(?:[}])/,/^(?:$)/,/^(?:\[\*\])/,/^(?:[{])/,/^(?:[\n])/,/^(?:[^{}\n]*)/,/^(?:cssClass\b)/,/^(?:callback\b)/,/^(?:link\b)/,/^(?:click\b)/,/^(?:note for\b)/,/^(?:note\b)/,/^(?:<<)/,/^(?:>>)/,/^(?:href\b)/,/^(?:[~])/,/^(?:[^~]*)/,/^(?:~)/,/^(?:[`])/,/^(?:[^`]+)/,/^(?:[`])/,/^(?:_self\b)/,/^(?:_blank\b)/,/^(?:_parent\b)/,/^(?:_top\b)/,/^(?:\s*<\|)/,/^(?:\s*\|>)/,/^(?:\s*>)/,/^(?:\s*<)/,/^(?:\s*\*)/,/^(?:\s*o\b)/,/^(?:\s*\(\))/,/^(?:--)/,/^(?:\.\.)/,/^(?::{1}[^:\n;]+)/,/^(?::{3})/,/^(?:-)/,/^(?:\.)/,/^(?:\+)/,/^(?::)/,/^(?:,)/,/^(?:#)/,/^(?:#)/,/^(?:%)/,/^(?:=)/,/^(?:=)/,/^(?:\w+)/,/^(?:\[)/,/^(?:\])/,/^(?:[!"#$%&'*+,-.`?\\/])/,/^(?:[0-9]+)/,/^(?:[\u00AA\u00B5\u00BA\u00C0-\u00D6\u00D8-\u00F6]|[\u00F8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377]|[\u037A-\u037D\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5]|[\u03F7-\u0481\u048A-\u0527\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA]|[\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE]|[\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA]|[\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0]|[\u08A2-\u08AC\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0977]|[\u0979-\u097F\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2]|[\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A]|[\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39]|[\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8]|[\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0B05-\u0B0C]|[\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C]|[\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99]|[\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0]|[\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C33\u0C35-\u0C39\u0C3D]|[\u0C58\u0C59\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3]|[\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10]|[\u0D12-\u0D3A\u0D3D\u0D4E\u0D60\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1]|[\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81]|[\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3]|[\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6]|[\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A]|[\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081]|[\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D]|[\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0]|[\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310]|[\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F4\u1401-\u166C]|[\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u1700-\u170C\u170E-\u1711]|[\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7]|[\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191C]|[\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19C1-\u19C7\u1A00-\u1A16]|[\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF]|[\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC]|[\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D]|[\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D]|[\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3]|[\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F]|[\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128]|[\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184]|[\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3]|[\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6]|[\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE]|[\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C]|[\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D]|[\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FCC]|[\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B]|[\uA640-\uA66E\uA67F-\uA697\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788]|[\uA78B-\uA78E\uA790-\uA793\uA7A0-\uA7AA\uA7F8-\uA801\uA803-\uA805]|[\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB]|[\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uAA00-\uAA28]|[\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA80-\uAAAF\uAAB1\uAAB5]|[\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4]|[\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E]|[\uABC0-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D]|[\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36]|[\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D]|[\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC]|[\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF]|[\uFFD2-\uFFD7\uFFDA-\uFFDC])/,/^(?:\s)/,/^(?:\s)/,/^(?:$)/],conditions:{"namespace-body":{rules:[26,32,33,34,35,36,37,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:!1},namespace:{rules:[26,28,29,30,31,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:!1},"class-body":{rules:[26,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:!1},class:{rules:[26,38,39,40,41,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:!1},acc_descr_multiline:{rules:[11,12,26,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:!1},acc_descr:{rules:[9,26,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:!1},acc_title:{rules:[7,26,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:!1},callback_args:{rules:[22,23,26,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:!1},callback_name:{rules:[19,20,21,26,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:!1},href:{rules:[26,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:!1},struct:{rules:[26,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:!1},generic:{rules:[26,48,49,50,51,52,53,54,55,56,57,58,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:!1},bqstring:{rules:[26,48,49,50,51,52,53,54,55,56,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:!1},string:{rules:[24,25,26,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,85,86,87,88,89,90,91,92,93,94,96],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,8,10,13,14,15,16,17,18,26,27,28,37,48,49,50,51,52,53,54,55,56,59,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96],inclusive:!0}}};return x}();Ne.lexer=et;function Le(){this.yy={}}return Le.prototype=Ne,Ne.Parser=Le,new Le}();Ve.parser=Ve;const zt=Ve,Qe=["#","+","~","-",""];class Xe{constructor(i,r){this.memberType=r,this.visibility="",this.classifier="";const l=pt(i,F());this.parseMember(l)}getDisplayDetails(){let i=this.visibility+Re(this.id);this.memberType==="method"&&(i+=`(${Re(this.parameters.trim())})`,this.returnType&&(i+=" : "+Re(this.returnType))),i=i.trim();const r=this.parseClassifier();return{displayText:i,cssStyle:r}}parseMember(i){let r="";if(this.memberType==="method"){const l=/([#+~-])?(.+)\((.*)\)([\s$*])?(.*)([$*])?/,o=i.match(l);if(o){const A=o[1]?o[1].trim():"";if(Qe.includes(A)&&(this.visibility=A),this.id=o[2].trim(),this.parameters=o[3]?o[3].trim():"",r=o[4]?o[4].trim():"",this.returnType=o[5]?o[5].trim():"",r===""){const g=this.returnType.substring(this.returnType.length-1);g.match(/[$*]/)&&(r=g,this.returnType=this.returnType.substring(0,this.returnType.length-1))}}}else{const l=i.length,o=i.substring(0,1),A=i.substring(l-1);Qe.includes(o)&&(this.visibility=o),A.match(/[$*]/)&&(r=A),this.id=i.substring(this.visibility===""?0:1,r===""?l:l-1)}this.classifier=r}parseClassifier(){switch(this.classifier){case"*":return"font-style:italic;";case"$":return"text-decoration:underline;";default:return""}}}const pe="classId-";let Pe=[],p={},he=[],He=0,O={},we=0,K=[];const V=e=>v.sanitizeText(e,F()),w=function(e){const i=v.sanitizeText(e,F());let r="",l=i;if(i.indexOf("~")>0){const o=i.split("~");l=V(o[0]),r=V(o[1])}return{className:l,type:r}},ft=function(e,i){const r=v.sanitizeText(e,F());i&&(i=V(i));const{className:l}=w(r);p[l].label=i},Ae=function(e){const i=v.sanitizeText(e,F()),{className:r,type:l}=w(i);if(Object.hasOwn(p,r))return;const o=v.sanitizeText(r,F());p[o]={id:o,type:l,label:o,cssClasses:[],methods:[],members:[],annotations:[],styles:[],domId:pe+o+"-"+He},He++},qe=function(e){const i=v.sanitizeText(e,F());if(i in p)return p[i].domId;throw new Error("Class not found: "+i)},dt=function(){Pe=[],p={},he=[],K=[],K.push(Ze),O={},we=0,ht()},Et=function(e){return p[e]},Ct=function(){return p},mt=function(){return Pe},bt=function(){return he},gt=function(e){At.debug("Adding relation: "+JSON.stringify(e)),Ae(e.id1),Ae(e.id2),e.id1=w(e.id1).className,e.id2=w(e.id2).className,e.relationTitle1=v.sanitizeText(e.relationTitle1.trim(),F()),e.relationTitle2=v.sanitizeText(e.relationTitle2.trim(),F()),Pe.push(e)},kt=function(e,i){const r=w(e).className;p[r].annotations.push(i)},Je=function(e,i){Ae(e);const r=w(e).className,l=p[r];if(typeof i=="string"){const o=i.trim();o.startsWith("<<")&&o.endsWith(">>")?l.annotations.push(V(o.substring(2,o.length-2))):o.indexOf(")")>0?l.methods.push(new Xe(o,"method")):o&&l.members.push(new Xe(o,"attribute"))}},Tt=function(e,i){Array.isArray(i)&&(i.reverse(),i.forEach(r=>Je(e,r)))},Ft=function(e,i){const r={id:`note${he.length}`,class:i,text:e};he.push(r)},yt=function(e){return e.startsWith(":")&&(e=e.substring(1)),V(e.trim())},Me=function(e,i){e.split(",").forEach(function(r){let l=r;r[0].match(/\d/)&&(l=pe+l),p[l]!==void 0&&p[l].cssClasses.push(i)})},Dt=function(e,i){e.split(",").forEach(function(r){i!==void 0&&(p[r].tooltip=V(i))})},Bt=function(e,i){return i?O[i].classes[e].tooltip:p[e].tooltip},_t=function(e,i,r){const l=F();e.split(",").forEach(function(o){let A=o;o[0].match(/\d/)&&(A=pe+A),p[A]!==void 0&&(p[A].link=We.formatUrl(i,l),l.securityLevel==="sandbox"?p[A].linkTarget="_top":typeof r=="string"?p[A].linkTarget=V(r):p[A].linkTarget="_blank")}),Me(e,"clickable")},St=function(e,i,r){e.split(",").forEach(function(l){Nt(l,i,r),p[l].haveCallback=!0}),Me(e,"clickable")},Nt=function(e,i,r){const l=v.sanitizeText(e,F());if(F().securityLevel!=="loose"||i===void 0)return;const A=l;if(p[A]!==void 0){const g=qe(A);let D=[];if(typeof r=="string"){D=r.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);for(let B=0;B<D.length;B++){let _=D[B].trim();_.charAt(0)==='"'&&_.charAt(_.length-1)==='"'&&(_=_.substr(1,_.length-2)),D[B]=_}}D.length===0&&D.push(g),K.push(function(){const B=document.querySelector(`[id="${g}"]`);B!==null&&B.addEventListener("click",function(){We.runFunc(i,...D)},!1)})}},Lt=function(e){K.forEach(function(i){i(e)})},vt={LINE:0,DOTTED_LINE:1},xt={AGGREGATION:0,EXTENSION:1,COMPOSITION:2,DEPENDENCY:3,LOLLIPOP:4},Ze=function(e){let i=z(".mermaidTooltip");(i._groups||i)[0][0]===null&&(i=z("body").append("div").attr("class","mermaidTooltip").style("opacity",0)),z(e).select("svg").selectAll("g.node").on("mouseover",function(){const o=z(this);if(o.attr("title")===null)return;const g=this.getBoundingClientRect();i.transition().duration(200).style("opacity",".9"),i.text(o.attr("title")).style("left",window.scrollX+g.left+(g.right-g.left)/2+"px").style("top",window.scrollY+g.top-14+document.body.scrollTop+"px"),i.html(i.html().replace(/&lt;br\/&gt;/g,"<br/>")),o.classed("hover",!0)}).on("mouseout",function(){i.transition().duration(500).style("opacity",0),z(this).classed("hover",!1)})};K.push(Ze);let $e="TB";const Ot=()=>$e,It=e=>{$e=e},Rt=function(e){O[e]===void 0&&(O[e]={id:e,classes:{},children:{},domId:pe+e+"-"+we},we++)},Vt=function(e){return O[e]},wt=function(){return O},Pt=function(e,i){if(O[e]!==void 0)for(const r of i){const{className:l}=w(r);p[l].parent=e,O[e].classes[l]=p[l]}},Mt=function(e,i){const r=p[e];if(!(!i||!r))for(const l of i)l.includes(",")?r.styles.push(...l.split(",")):r.styles.push(l)},Kt={setAccTitle:ut,getAccTitle:rt,getAccDescription:at,setAccDescription:lt,getConfig:()=>F().class,addClass:Ae,bindFunctions:Lt,clear:dt,getClass:Et,getClasses:Ct,getNotes:bt,addAnnotation:kt,addNote:Ft,getRelations:mt,addRelation:gt,getDirection:Ot,setDirection:It,addMember:Je,addMembers:Tt,cleanupLabel:yt,lineType:vt,relationType:xt,setClickEvent:St,setCssClass:Me,setLink:_t,getTooltip:Bt,setTooltip:Dt,lookUpDomId:qe,setDiagramTitle:ct,getDiagramTitle:ot,setClassLabel:ft,addNamespace:Rt,addClassesToNamespace:Pt,getNamespace:Vt,getNamespaces:wt,setCssStyle:Mt},Gt=e=>`g.classGroup text {
  fill: ${e.nodeBorder||e.classText};
  stroke: none;
  font-family: ${e.fontFamily};
  font-size: 10px;

  .title {
    font-weight: bolder;
  }

}

.nodeLabel, .edgeLabel {
  color: ${e.classText};
}
.edgeLabel .label rect {
  fill: ${e.mainBkg};
}
.label text {
  fill: ${e.classText};
}
.edgeLabel .label span {
  background: ${e.mainBkg};
}

.classTitle {
  font-weight: bolder;
}
.node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${e.mainBkg};
    stroke: ${e.nodeBorder};
    stroke-width: 1px;
  }


.divider {
  stroke: ${e.nodeBorder};
  stroke-width: 1;
}

g.clickable {
  cursor: pointer;
}

g.classGroup rect {
  fill: ${e.mainBkg};
  stroke: ${e.nodeBorder};
}

g.classGroup line {
  stroke: ${e.nodeBorder};
  stroke-width: 1;
}

.classLabel .box {
  stroke: none;
  stroke-width: 0;
  fill: ${e.mainBkg};
  opacity: 0.5;
}

.classLabel .label {
  fill: ${e.nodeBorder};
  font-size: 10px;
}

.relation {
  stroke: ${e.lineColor};
  stroke-width: 1;
  fill: none;
}

.dashed-line{
  stroke-dasharray: 3;
}

.dotted-line{
  stroke-dasharray: 1 2;
}

#compositionStart, .composition {
  fill: ${e.lineColor} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#compositionEnd, .composition {
  fill: ${e.lineColor} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#dependencyStart, .dependency {
  fill: ${e.lineColor} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#dependencyStart, .dependency {
  fill: ${e.lineColor} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#extensionStart, .extension {
  fill: transparent !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#extensionEnd, .extension {
  fill: transparent !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#aggregationStart, .aggregation {
  fill: transparent !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#aggregationEnd, .aggregation {
  fill: transparent !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#lollipopStart, .lollipop {
  fill: ${e.mainBkg} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#lollipopEnd, .lollipop {
  fill: ${e.mainBkg} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

.edgeTerminals {
  font-size: 11px;
  line-height: initial;
}

.classTitleText {
  text-anchor: middle;
  font-size: 18px;
  fill: ${e.textColor};
}
`,Yt=Gt;export{Kt as d,zt as p,Yt as s};
