package com.srdcloud.ideplugin.codecomplete.render

import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.Inlay
import com.intellij.openapi.util.Disposer
import com.srdcloud.ideplugin.codecomplete.domain.CompletionElement
import com.srdcloud.ideplugin.codecomplete.handle.ICompletion

/**
 * 编辑器补全提示渲染器
 */
class EditorCompletionRender(private val editor: Editor) : ICompletion {
    private var suffixInlay: Inlay<*>? = null
    private var blockInlay: Inlay<*>? = null
    override fun reset() {
        blockInlay?.let {
            Disposer.dispose(it)
            blockInlay = null
        }
        suffixInlay?.let {
            Disposer.dispose(it)
            suffixInlay = null
        }
    }

    /**
     * 渲染建议文本到编辑器中。
     */
    override fun render(proposal: CompletionElement, offset: Int) {
        var text = proposal.text
        if (text.isEmpty()) return
        val tabSize = getTabSize(editor)
        text = text.replace("\t".toRegex(), " ".repeat(tabSize))
        val lines = text.lines()
        val first = lines.first()
        if (first.isNotEmpty()) {
            renderSuffix(editor, first, offset)
        }
        if (lines.size > 1) {
            renderBlock(lines.stream().skip(1).toList(), editor, offset)
        }
    }

    override val offset: Int?
        get() = suffixInlay?.offset ?: blockInlay?.offset

    private fun renderBlock(lines: List<String>, editor: Editor, offset: Int) {
        val element =
            editor.inlayModel.addBlockElement(
                offset, true, false, 1, BlockRenderer(editor, lines)
            ) ?: return
        Disposer.tryRegister(this, element)
        blockInlay = element
    }

    private fun renderSuffix(editor: Editor, line: String, offset: Int) {
        val element = editor.inlayModel.addInlineElement(offset, true, InlineRenderer(editor, line)) ?: return
        Disposer.tryRegister(this, element)
        suffixInlay = element
    }

    override val isEmpty: Boolean
        get() = suffixInlay == null && blockInlay == null

    override fun dispose() {
        blockInlay?.let { Disposer.dispose(it) }
        suffixInlay?.let { Disposer.dispose(it) }
        reset()
    }

    private fun getTabSize(editor: Editor): Int {
        // deprecated:commonCodeStyleSettings的indentOptions、TAB_SIZE下面一直都是null
//        val commonCodeStyleSettings = editor.project
//            ?.let { PsiDocumentManager.getInstance(it).getPsiFile(editor.document) }
//            ?.let { CommonCodeStyleSettings(it.language) }
//        return commonCodeStyleSettings?.indentOptions?.TAB_SIZE ?: editor.settings.getTabSize(editor.project)

        return editor.settings.getTabSize(editor.project)
    }
}