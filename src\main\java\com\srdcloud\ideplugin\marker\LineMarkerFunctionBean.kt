package com.srdcloud.ideplugin.marker

import com.intellij.psi.PsiElement

class LineMarkerFunctionBean {
    var isUtValid: Boolean = false
    var isAnnotateValid: Boolean = false
    var isLineAnotateValid: Boolean = false
    var isCodeExplain: Boolean = false
    var isCodeSplit: Boolean = false
    var isOptimization: Boolean = false

    fun getFunctionNum(): Int {
        return listOf(isUtValid, isAnnotateValid, isLineAnotateValid, isCodeExplain, isCodeSplit, isOptimization)
            .count { it }
    }

    companion object {
        fun getFunctionLinesCount(element: PsiElement): ElementInfo? {
            // Implement logic to get function lines count
            // Return null if not applicable
            return null
        }
    }

    data class ElementInfo(
        val startOffset: Int,
        val endOffset: Int,
        val startLine: Int,
        val endLine: Int
    )
}
