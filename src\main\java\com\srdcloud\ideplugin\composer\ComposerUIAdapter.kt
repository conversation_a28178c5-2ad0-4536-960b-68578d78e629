package com.srdcloud.ideplugin.composer

import com.intellij.openapi.project.Project
import com.srdcloud.ideplugin.composer.ui.ComposerUIComponent

/**
 * Composer UI适配器接口
 * 用于抽象化Composer相关的UI操作
 */
interface ComposerUIAdapter {
    /**
     * 获取并激活UI组件
     */
    fun getAndActivateComponent(project: Project): ComposerUIComponent?

    /**
     * 验证当前窗口ID是否匹配
     */
    fun isCurrentWindow(project: Project, windowId: String): <PERSON><PERSON><PERSON>
}