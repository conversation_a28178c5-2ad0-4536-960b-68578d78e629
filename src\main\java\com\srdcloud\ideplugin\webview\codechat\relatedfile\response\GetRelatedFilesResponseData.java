package com.srdcloud.ideplugin.webview.codechat.relatedfile.response;

import com.srdcloud.ideplugin.webview.codechat.relatedfile.FileNode;
import com.srdcloud.ideplugin.webview.codechat.relatedfile.RecentlyUsedFile;

import java.util.List;

public class GetRelatedFilesResponseData {
    private String reqType;
    private int code;
    private int currentIndex;
    private List<RecentlyUsedFile> recentlyUsedFiles;
    private FileNode fileTree;


    public GetRelatedFilesResponseData(String reqType, int code, int currentIndex, List<RecentlyUsedFile> recentlyUsedFiles, FileNode fileTree) {
        this.reqType = reqType;
        this.code = code;
        this.currentIndex = currentIndex;
        this.recentlyUsedFiles = recentlyUsedFiles;
        this.fileTree = fileTree;
    }

    public String getReqType() {
        return reqType;
    }

    public void setReqType(String reqType) {
        this.reqType = reqType;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public List<RecentlyUsedFile> getRecentlyUsedFiles() {
        return recentlyUsedFiles;
    }

    public void setRecentlyUsedFiles(List<RecentlyUsedFile> recentlyUsedFiles) {
        this.recentlyUsedFiles = recentlyUsedFiles;
    }

    public FileNode getFileTree() {
        return fileTree;
    }

    public void setFileTree(FileNode fileTree) {
        this.fileTree = fileTree;
    }

    public int getCurrentIndex() {
        return currentIndex;
    }

    public void setCurrentIndex(int currentIndex) {
        this.currentIndex = currentIndex;
    }
}
