package com.srdcloud.ideplugin.codecomplete.render

import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.EditorCustomElementRenderer
import com.intellij.openapi.editor.Inlay
import com.intellij.openapi.editor.markup.TextAttributes
import java.awt.FontMetrics
import java.awt.Graphics
import java.awt.Rectangle

/**
 * 块级代码提示渲染器，用于显示多行信息。
 */
class BlockRenderer(private val editor: Editor, private val lines: List<String>) :
    EditorCustomElementRenderer {
    private val width =
        editor.contentComponent.getFontMetrics(RenderFontUtils.font(editor))
            .stringWidth(lines.maxByOrNull { it.length } ?: "")
    private val height = editor.contentComponent.getFontMetrics(RenderFontUtils.font(editor)).height * lines.size

    override fun calcWidthInPixels(inlay: Inlay<*>) = width

    override fun calcHeightInPixels(inlay: Inlay<*>) = height

    override fun paint(inlay: Inlay<*>, g: Graphics, targetRegion: Rectangle, textAttributes: TextAttributes) {
        // 补全字体颜色统一为灰色
        g.color = RenderFontUtils.color

        // 补全字体样式使用编辑器内置样式
        g.font = RenderFontUtils.font(editor)

        // 获取行高
        val fontMetrics: FontMetrics = g.fontMetrics
        val lineHeight: Int = fontMetrics.height
        var y: Int = targetRegion.y + editor.ascent

        // 绘制多行补全
        lines.forEach { line ->
            g.drawString(line, 0, y)
            y += lineHeight
        }
    }
}