package com.srdcloud.ideplugin.webview.codechat.workitem.response;

import com.srdcloud.ideplugin.remote.domain.WorkItem.WorkItemInfo;

import java.util.List;

public class WorkItemResponseData {

    private String reqType;

    private List<WorkItemInfo> workItemList;

    private String error;

    public WorkItemResponseData(String reqType, List<WorkItemInfo> workItemList, String error) {
        this.reqType = reqType;
        this.workItemList = workItemList;
        this.error = error;
    }

    public void setSeqType(String reqType) {
        this.reqType = reqType;
    }

    public String getReqType() {
        return reqType;
    }

    public void setWorkItemList(List<WorkItemInfo> workItemList) {
        this.workItemList = workItemList;
    }

    public List<WorkItemInfo> getWorkItemList() {
        return workItemList;
    }

    public void setError(String error) {
        this.error = error;
    }

    public String getError() {
        return error;
    }

}
