package com.srdcloud.ideplugin.codechat.domain;

import java.io.Serializable;
import java.util.List;

/**
 * @author: yangy
 * @date: 2024/5/13 16:40
 * @Desc
 */
public class ChatMessageSimple implements Serializable {
    /**
     * AI原子能力标识
     */
    private String subService;

    /**
     * 本对象的角色，可取值为：
     * system - 本对象为system prompt
     * user - 本对象为用户提问prompt
     * assistant - 本对象为模型回答prompt
     */
    private String role;

    /**
     * 本对象的内容，可携带多媒体内容的信息，当内容为纯文本时，数组只有一个元素，元素type取值为text
     */
    private List<MultiTypeContent> content;

    /**
     * 本轮问答的id号，用于关联提问和回答，以及后续关联统计等目的
     */
    private String reqId;

    /**
     * 产生本对话的时间，当role=user时为必选，格式为yyyy-MM-dd HH:mm:ss
     */
    private String reqTime;;

    /**
     * 承担本轮回答的模型名称，当role=assistant时为必选
     */
    private String modelName;

    /**
     * 用户对本轮回答的反馈评价，当role=assistant时为必选，可选值为：
     * none - 用户无反馈
     * like - 用户点赞
     * unlike - 用户点踩
     */
    private String feedback;

    /**
     * 该消息的子消息，若该消息role=assistant，则子消息role为user；若该消息role=user，则子消息role为assistant
     */
    private List<ChatMessageSimple> children;

    /**
     * 错误信息
     */
    private String errMsg;

    /**
     * 扩展数据
     */
    private Object customData;

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public List<MultiTypeContent> getContent() {
        return content;
    }

    public void setContent(List<MultiTypeContent> content) {
        this.content = content;
    }

    public String getReqId() {
        return reqId;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    public String getReqTime() {
        return reqTime;
    }

    public void setReqTime(String reqTime) {
        this.reqTime = reqTime;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getFeedback() {
        return feedback;
    }

    public void setFeedback(String feedback) {
        this.feedback = feedback;
    }

    public List<ChatMessageSimple> getChildren() {
        return children;
    }

    public void setChildren(List<ChatMessageSimple> children) {
        this.children = children;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public String getSubService() {
        return subService;
    }

    public void setSubService(String subService) {
        this.subService = subService;
    }

    public Object getCustomData() {
        return customData;
    }

    public void setCustomData(Object customData) {
        this.customData = customData;
    }
}
