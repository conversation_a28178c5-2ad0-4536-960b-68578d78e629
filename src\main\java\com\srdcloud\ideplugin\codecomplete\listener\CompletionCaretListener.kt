package com.srdcloud.ideplugin.codecomplete.listener

import com.intellij.openapi.editor.event.CaretEvent
import com.intellij.openapi.editor.event.CaretListener
import com.srdcloud.ideplugin.codecomplete.handle.CompletionContext.Companion.resetInlineCompletionContext

/**
 * <AUTHOR>
 * @date 2025/6/6
 * @desc 光标位置变动监听
 */
class CompletionCaretListener : CaretListener {
    override fun caretPositionChanged(event: CaretEvent) {
        // 同一行内光标右移，不触发任何行为
        if (event.oldPosition.line == event.newPosition.line &&
            event.oldPosition.column + 1 == event.newPosition.column
        ) return

        // 否则，重置补全提示
        event.editor.resetInlineCompletionContext()
    }
}