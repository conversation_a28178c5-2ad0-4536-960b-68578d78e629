package com.srdcloud.ideplugin.service;

import com.intellij.openapi.progress.EmptyProgressIndicator;
import com.intellij.openapi.progress.ProgressIndicator;
import com.intellij.openapi.progress.Task.Backgroundable;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.project.ProjectManager;
import com.intellij.openapi.updateSettings.impl.PluginDownloader;
import com.intellij.openapi.updateSettings.impl.UpdateChecker;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.utils.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * 更新检查后台线程
 *
 * <AUTHOR>
 */
public class VersionCheckerTask extends Backgroundable {
    private static final Logger logger = LoggerFactory.getLogger(VersionCheckerTask.class);

    private static AtomicInteger taskCounter = new AtomicInteger(0);

    private Project project;

    private String remoteVersion = "";

    private String remoteVersionId = "";

    private String remoteDownloadUrl = "";

    private String remoteVersionChange = "";

    /**
     * 网页端下载地址，已弃用
     */
    @Deprecated
    private String clientUrlSubPath;

    public VersionCheckerTask(Project project, String remoteVersion, String remoteVersionId, String remoteDownloadUrl, String remoteVersionChange, String clientUrlSubPath) {
        super(project, "Checking for updates", true);

        this.project = project;
        this.remoteVersion = remoteVersion;
        this.remoteVersionId = remoteVersionId;
        this.remoteDownloadUrl = remoteDownloadUrl;
        this.remoteVersionChange = remoteVersionChange;
        this.clientUrlSubPath = clientUrlSubPath;
    }


    @Override
    public void run(@NotNull ProgressIndicator indicator) {
        // 远端版本有更新时，才进行下一步操作，否则提前返回
        if (StringUtils.isBlank(this.remoteVersion) || BooleanUtils.isFalse(newVersionIsAvailable(this.remoteVersion))) {
            return;
        }

        // 发送更新通知
        MessageBalloonNotificationUtil.showUpdateBalloonNotification(this.project, this.remoteVersion, this.remoteVersionId, this.remoteVersionChange, this.remoteDownloadUrl);

        // 内外部版本隔离：外部版本不检查官方市场
        if (!ConfigWrapper.isInnerVersion) {
            return;
        }

        // 官方插件市场升级：静默下载安装
        ThreadPoolUtil.submit(() -> {
            try {
                // 全局计数，只运行一个更新检查任务
                if (taskCounter.get() > 0) {
                    logger.warn("[cf] VersionCheckerTask skip by taskCounter:{}", taskCounter.get());
                    return;
                }

                // 开始当前次任务
                taskCounter.addAndGet(1);

                logger.info("[cf] VersionCheckerTask update start,remoteVersion:{}", this.remoteVersion);

                String marketplaceVersion = "";
                Collection<PluginDownloader> updates = UpdateChecker.getPluginUpdates();
                if (CollectionUtils.isEmpty(updates)) {
                    logger.warn("[cf] auto update skip,getPluginUpdates fail.");
                    taskCounter.set(0);
                    return;
                }

                // -- 找到本插件的更新对象
                for (PluginDownloader update : updates) {
                    if (update == null) {
                        continue;
                    }
                    String pluginId = update.getId().getIdString();
                    if (!StringUtil.isEmpty(pluginId) && pluginId.equals("com.srdcloud.IDEPlugin")) {
                        marketplaceVersion = update.getPluginVersion();

                        // -- 判断官方市场版本是否有上架新版
                        if (newVersionIsAvailable(marketplaceVersion)) {
                            // -- 后台静默下载安装[右下角显示check for update后台任务,取决于网络]
                            update.prepareToInstall(new EmptyProgressIndicator());
                            // 进行安装
                            update.install();
                            logger.warn("[cf] auto update,new version:" + marketplaceVersion + " success!");
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                logger.warn("[cf] auto update fail,error:");
                e.printStackTrace();
            } finally {
                taskCounter.set(0);
            }
        });
    }

    /**
     * 判断远端版本是否为新版本
     *
     * @param remoteVersion 远端版本号
     * @return 结果
     */
    private boolean newVersionIsAvailable(String remoteVersion) {
        if (!StringUtil.isEmpty(IdeUtil.getPluginVersion()) &&
                compareVersionNumbers(IdeUtil.getPluginVersion(), remoteVersion) >= 0) {
            return false;
        }

        return true;
    }

    /**
     * 对比版本字段的数字大小
     *
     * @param srcVersion    本地版本号
     * @param remoteVersion 服务端版本号
     * @return 对比结果
     */
    private int compareVersionNumbers(String srcVersion, String remoteVersion) {
        String src = (srcVersion.indexOf("_") > 0) ?
                srcVersion.substring(0, srcVersion.indexOf("_")) : srcVersion;
        String remote = (remoteVersion.indexOf("_") > 0) ?
                remoteVersion.substring(0, remoteVersion.indexOf("_")) : remoteVersion;

        String[] srcParts = (src.startsWith("v")) ?
                src.substring(1).split("\\.") : src.split("\\.");
        String[] remoteParts = (remote.startsWith("v")) ?
                remote.substring(1).split("\\.") : remote.split("\\.");

        int len = Math.max(srcParts.length, remoteParts.length);

        for (int i = 0; i < len; i++) {
            int srcNum = (i < srcParts.length) ? Integer.parseInt(srcParts[i]) : 0;
            int remoteNum = (i < remoteParts.length) ? Integer.parseInt(remoteParts[i]) : 0;

            if (srcNum < remoteNum) {
                return -1;
            } else if (srcNum > remoteNum) {
                return 1;
            }
        }

        return 0;
    }
}

