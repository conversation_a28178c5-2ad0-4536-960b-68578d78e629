package com.srdcloud.ideplugin.assistant.codechatNative.logics;

import com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions.KnowledgeBaseAction;
import com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions.SendAction;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.Conversation;
import com.srdcloud.ideplugin.assistant.codechatNative.ui.CodeChatMainPanel;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.enums.QuestionType;
import com.srdcloud.ideplugin.general.utils.MessageBalloonNotificationUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.KeyEvent;
import java.awt.event.KeyListener;

/**
 * <AUTHOR> yangy
 * @create 2023/6/15 10:13
 * 开发问答输入框 交互事件监听器
 */
public class CodeChatOpsListener implements ActionListener, KeyListener {
    private static final Logger logger = LoggerFactory.getLogger(CodeChatOpsListener.class);

    private final CodeChatMainPanel codeChatMainPanel;

    public CodeChatOpsListener(CodeChatMainPanel codeChatMainPanel) {
        this.codeChatMainPanel = codeChatMainPanel;
    }

    /**
     * 鼠标点击事件
     */
    @Override
    public void actionPerformed(ActionEvent e) {
        // 获取输入框内容
        String text = codeChatMainPanel.getInputTextArea().
                getTextArea().getText();

        // PlaceHolder不作为提问内容发出
        if (Constants.placeholderText.equalsIgnoreCase(text)) {
            text = "";
        }

        // 如果代码选择器浮窗有内容，则拼接发送内容到输入内容的下方
        if (codeChatMainPanel.getMySelectionListener() != null && codeChatMainPanel.getFlowCodeViewPanelText() != null && !codeChatMainPanel.getFlowCodeViewPanelText().isEmpty()) {
            text = text + "\n" + codeChatMainPanel.getFlowCodeViewPanelText();
        }

        // 判空
        if (StringUtils.isBlank(text)) {
            MessageBalloonNotificationUtil.showBalloonNotificationByReason(codeChatMainPanel.getProject(), "请输入提问内容", RtnCode.RtnCode_Not_Select_Text);
            return;
        }

        // 获取当前会话选择的知识库id
        Conversation conv = CodeChatCompleteEngin.getCurrentConversation(codeChatMainPanel);
        Integer kbId = conv.getKbId();
        // 根据知识库id发送提问
        if (kbId == null) {
            SendAction sendAction = codeChatMainPanel.getProject().getService(SendAction.class);
            sendAction.doActionPerformed(codeChatMainPanel, text, null, QuestionType.NEW_ASK, null);
        } else {
            KnowledgeBaseAction knowledgeBaseAction = codeChatMainPanel.getProject().getService(KnowledgeBaseAction.class);
            knowledgeBaseAction.doActionPerformed(codeChatMainPanel, text, kbId, QuestionType.NEW_ASK, null);
        }

        // 注销代码选择浮窗
        if (codeChatMainPanel.getMySelectionListener() != null) {
            codeChatMainPanel.getMySelectionListener().flowPanelDisable();
        }
    }


    /**
     * 按下并释放一个按键，且该按键产生了一个有效输入时执行
     */
    @Override
    public void keyTyped(KeyEvent e) {

    }

    /**
     * 按下某个物理按键且未释放时执行
     */
    @Override
    public void keyPressed(KeyEvent e) {
        if (e.getKeyCode() == KeyEvent.VK_ENTER) {
            // 仅点击回车，则发送
            if (!e.isControlDown() && !e.isMetaDown()) {
                if (e.getModifiersEx() == 0) {
                    e.consume();
                    codeChatMainPanel.getSendButton().doClick();
                }
            } else {
                e.consume();
                // 光标位置换行
                int caretPosition = codeChatMainPanel.getInputTextArea().getTextArea().getCaretPosition();
                codeChatMainPanel.getInputTextArea().getTextArea().insert("\n", caretPosition);
            }
            codeChatMainPanel.getInputTextArea().revalidate();
        }
    }

    @Override
    public void keyReleased(KeyEvent e) {

    }
}
