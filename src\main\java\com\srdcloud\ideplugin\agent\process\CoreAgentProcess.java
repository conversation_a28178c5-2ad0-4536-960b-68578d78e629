package com.srdcloud.ideplugin.agent.process;

import com.intellij.openapi.util.SystemInfo;
import com.srdcloud.ideplugin.agent.AgentManager;
import com.srdcloud.ideplugin.agent.config.AgentConfig;
import com.srdcloud.ideplugin.agent.model.AgentVersion;
import com.srdcloud.ideplugin.general.constants.AgentNameConstant;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.attribute.PosixFilePermission;
import java.util.HashSet;
import java.util.Set;

public class CoreAgentProcess extends AgentProcess {

    public static String AGENT_NAME = AgentNameConstant.CORE_AGENT;

    private static final Logger logger = LoggerFactory.getLogger(CoreAgentProcess.class);

    public CoreAgentProcess(AgentConfig agentConfig, AgentManager agentManager, AgentVersion coreAgentVersion) {
        super(AGENT_NAME, agentConfig, agentManager, coreAgentVersion);
    }

    @Override
    public ProcessBuilder buildProcess() {

        // 针对node数据库在mac环境下进行文件隔离
        if (SystemInfo.isMac) {
            try {
                Process process = Runtime.getRuntime().exec(
                        new String[]{"xattr", "-dr", "com.apple.quarantine", getAgentPath().getAgentBasePath()}
                );

                // 等待命令执行完成
                int exitCode = process.waitFor();

                if (exitCode == 0) {
                    DebugLogUtil.println("隔离属性已移除");
                } else {
                    logger.warn("[cf] remove apple quarantine fai，with code: {}", exitCode);
                }
            } catch (Exception e) {
                logger.warn("[cf] execute apple quarantine error: {}", e.getMessage());
            }
        }

        return new ProcessBuilder(getAgentManager().getNodePath().getAgentFilePath(),
                getAgentPath().getAgentFilePath()
        ).directory(new File(getAgentPath().getAgentFilePath()).getParentFile());
    }

    @Override
    public String getAgentName() {
        return AGENT_NAME;
    }

    @Override
    protected void setUpEnvironment(AgentConfig agentConfig, ProcessBuilder processBuilder) {
        // 添加环境变量，启动参数
        processBuilder.environment().put("apiKey", agentConfig.getApiKey());
        processBuilder.environment().put("invokerId", agentConfig.getInvokerId());
        processBuilder.environment().put("pluginType", agentConfig.getPluginType());
        processBuilder.environment().put("pluginVersion", agentConfig.getPluginVersion());
        processBuilder.environment().put("clientType", agentConfig.getClientType());
        processBuilder.environment().put("clientVersion", agentConfig.getClientVersion());
        processBuilder.environment().put("serverType", agentConfig.getServerType());
        processBuilder.environment().put("serverBaseUrl", agentConfig.getServerBaseUrl() + agentConfig.getCoreApi());
        processBuilder.environment().put("embeddingSubservice", agentConfig.getEmbeddingSubservice());
        processBuilder.environment().put("rerankSubservice", agentConfig.getRerankSubservice());
        processBuilder.environment().put("composerSubservice", agentConfig.getComposerSubservice());
        processBuilder.environment().put("indexVersion", agentConfig.getIndexVersion());
        processBuilder.environment().put("env", agentConfig.getEnv());

    }
}
