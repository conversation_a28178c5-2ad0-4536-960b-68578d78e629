import{p as e,f as o}from"./flowDb-956e92f1.js";import{f as t,g as a}from"./styles-c10674c1.js";import{ar as i}from"./index.js";import"./graph.js";import"./layout.js";import"./index-3862675e.js";import"./clone.js";import"./edges-e0da2a9e.js";import"./createText-2e5e7dd3.js";import"./line.js";import"./array.js";import"./path.js";import"./channel.js";const M={parser:e,db:o,renderer:t,styles:a,init:r=>{r.flowchart||(r.flowchart={}),r.flowchart.arrowMarkerAbsolute=r.arrowMarkerAbsolute,i({flowchart:{arrowMarkerAbsolute:r.arrowMarkerAbsolute}}),t.setConf(r.flowchart),o.clear(),o.setGen("gen-2")}};export{M as diagram};
