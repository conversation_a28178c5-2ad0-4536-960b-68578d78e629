package com.srdcloud.ideplugin.assistant.codechatNative.ui;

import com.intellij.find.SearchTextArea;
import com.intellij.icons.AllIcons;
import com.intellij.ide.ui.laf.darcula.ui.DarculaButtonUI;
import com.intellij.openapi.Disposable;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.fileEditor.FileEditorManagerEvent;
import com.intellij.openapi.fileEditor.FileEditorManagerListener;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.popup.IPopupChooserBuilder;
import com.intellij.openapi.ui.popup.JBPopup;
import com.intellij.openapi.ui.popup.JBPopupFactory;
import com.intellij.openapi.util.Key;
import com.intellij.ui.OnePixelSplitter;
import com.intellij.ui.awt.RelativePoint;
import com.intellij.ui.components.JBLabel;
import com.intellij.ui.components.JBLoadingPanel;
import com.intellij.ui.components.JBTextArea;
import com.intellij.util.ui.JBUI;
import com.srdcloud.ideplugin.assistant.AssistantToolWindow;
import com.srdcloud.ideplugin.assistant.codechatNative.actions.KnowledgeAction;
import com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions.QuickHelpAction;
import com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions.SendAction;
import com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions.rightmenu.RightMenuCommentAction;
import com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions.rightmenu.RightMenuExplainAction;
import com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions.rightmenu.RightMenuOptimizeAction;
import com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions.rightmenu.RightMenuTestAction;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.CodeChatCompleteEngin;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.CodeChatOpsListener;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.ConversationManagerByAeBackend;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.EditorSelectionListener;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.Conversation;
import com.srdcloud.ideplugin.assistant.codechatNative.ui.history.CodeChatHistoryGroupComponent;
import com.srdcloud.ideplugin.assistant.codechatNative.ui.message.CodeChatMessageGroupComponent;
import com.srdcloud.ideplugin.assistant.codechatNative.ui.prompttemplate.CodeChatPromptTemplateGroupComponent;
import com.srdcloud.ideplugin.assistant.codechatNative.uicomponent.RoundedPanel;
import com.srdcloud.ideplugin.common.icons.MyIcons;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.constants.MessageNameConstant;
import com.srdcloud.ideplugin.general.enums.PromptRoleType;
import com.srdcloud.ideplugin.general.enums.QuestionType;
import com.srdcloud.ideplugin.general.enums.SubServiceType;
import com.srdcloud.ideplugin.general.icons.GPTIcons;
import com.srdcloud.ideplugin.general.utils.IdeUtil;
import com.srdcloud.ideplugin.general.utils.LocalStorageUtil;
import com.srdcloud.ideplugin.general.utils.MessageBalloonNotificationUtil;
import com.srdcloud.ideplugin.general.utils.UIUtil;
import com.srdcloud.ideplugin.remote.KnowledgeBaseCommHandler;
import com.srdcloud.ideplugin.remote.domain.KnowledgeBase.KnowledgeBaseDetailResponse;
import com.srdcloud.ideplugin.remote.domain.KnowledgeBase.KnowledgeBaseInfo;
import com.srdcloud.ideplugin.remote.domain.KnowledgeBase.KnowledgeBaseListResponse;
import com.srdcloud.ideplugin.service.CodeChatRequestSender;
import com.srdcloud.ideplugin.service.QuestionTask;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.CodeChatPrompt;
import okhttp3.Call;
import okhttp3.sse.EventSource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import javax.swing.border.Border;
import javax.swing.border.CompoundBorder;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;
import java.awt.*;
import java.awt.event.FocusEvent;
import java.awt.event.FocusListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR> yangy
 * @create 2023/6/15 10:14
 * 开发问答主面板
 */
public class CodeChatMainPanel implements Disposable {

    private static final Logger logger = LoggerFactory.getLogger(CodeChatMainPanel.class);

    // ========== 面板元素变量(从上到下依次对应)  ==========
    /**
     * 1 主窗体面板：由左右两部分组成，可自由调整大小比例
     */
    public OnePixelSplitter mainPanelContent;

    /**
     * 左侧展开面板
     */
    public JBLoadingPanel mainPanelContentLeft;

    /**
     * 左侧展开面板选项卡容器
     */
    public JPanel leftPanelContainer;

    /**
     * 当前选中展示的卡片
     */
    public static String currentLeftPanelCardName = "指令模板";

    /**
     * 左侧展开面板选项卡容器-历史会话
     */
    public CodeChatHistoryGroupComponent conversationListPanel;

    public static String conversationCardName = "历史会话";

    /**
     * 左侧展开面板选项卡容器-指令模板
     */
    public CodeChatPromptTemplateGroupComponent promptTemplatePanel;

    public static String promptTemplateCardName = "指令模板";

    /**
     * 1.2 主窗体右侧面板
     */
    private JPanel mainPanelContentRight;

    /**
     * 顶部header区域
     */
    private JPanel mainPanelContentRightHeader;

    /**
     * 顶部header区域-右侧操作区
     */
    private JPanel headerPanelRight;

    private JBLabel addJBLabel;


    /**
     * 中间区域：CardLayout
     */
    private JPanel mainPanelContentRightCenter;
    private CardLayout rightCentreLayout;

    /**
     * 当前展示的区域卡片
     */
    public static String codeChatMessageGroupCardName = "codeChatMessageGroup";
    public static String firstScreenCardName = "firstScreen";
    public static String currentRightCenterPanelCardName;

    /**
     * 问答窗口卡片
     */
    public CodeChatMessageGroupComponent messageAreaPanel = null;

    /**
     * 首屏UI卡片
     */
    private CodeChatFirstScreenComponent firstScreenPanel = null;


    /**
     * 底部区域
     */
    private JPanel mainPanelContentRightBottom;

    /**
     * 选中代码预览区域：mainPanelContentRightBottom-North
     */
    private JPanel flowCodeViewPanel;

    /**
     * 底部输入操作交互区域：mainPanelContentRightBottom-center
     */
    private JPanel askInputPanel;
    private JPanel knowledgeButton;
    private CardLayout knowledgeBaseLayout;
    private JLabel knowledgeChooseText;

    /**
     * askInputPanel-North：进度条
     */
    private JProgressBar actionPanelNorthProgressBar;


    /**
     * askInputPanel-Center：交互按钮区、输入框、发送按钮、停止按钮
     */
    private JPanel actionPanelCenter;
    /**
     * 输入交互区域：交互按钮
     */
    private JPanel askInputPanelOpsBar;
    // 左侧按钮组
    private JPanel askInputPanelOpsBarLeft;
    // 右侧发送按钮区域
    private JPanel askInputPanelOpsBarRight;


    /**
     * 自行封装的输入控件
     */
    private CodeChatInputTextArea inputTextArea;
    private JButton sendButton;
    public JButton stopAnswerButton;

    // ==========    面板逻辑变量   ==========
    // 当前项目
    private final Project myProject;

    // 当前编辑器
    private Editor editorLocal;

    // 线程池
    private ExecutorService executorService;

    // 请求管理器：用来停止流式应答
    private Object requestHolder;


    // 编辑器-代码选中事件监听器
    public EditorSelectionListener editorSelectionListener;

    // 编辑器-代码选中内容
    public String flowCodeViewPanelText = "";


    /**
     * 主窗体构造
     */
    public CodeChatMainPanel(@NotNull Project project) {
        myProject = project;

        // 一、创建水平排布的主面板，左侧部分占比40%
        mainPanelContent = new OnePixelSplitter(false, 0.35F);
        mainPanelContent.setDividerWidth(2);

        // 二、创建左侧面板UI，加入主面板左侧
        mainPanelContentLeft = buildLeftPart();
        mainPanelContent.setFirstComponent(mainPanelContentLeft);

        // 三、创建右侧问答区域面板UI，加入主面板右侧
        mainPanelContentRight = new JPanel(new BorderLayout());
        mainPanelContentRight.setFont(IdeUtil.getIDELabelFont());
        mainPanelContent.setSecondComponent(mainPanelContentRight);
        // -- 创建右侧面板顶部header区域
        mainPanelContentRightHeader = buildRightPartHeader();
        mainPanelContentRight.add(mainPanelContentRightHeader, BorderLayout.NORTH);

        // -- 创建右侧面板中间区域
        mainPanelContentRightCenter = buildRightPartCenter();
        mainPanelContentRight.add(mainPanelContentRightCenter, BorderLayout.CENTER);

        // -- 创建右侧面板底部区域
        mainPanelContentRightBottom = buildRightPartBottom();
        mainPanelContentRight.add(mainPanelContentRightBottom, BorderLayout.SOUTH);

        // 四、初始化一个新会话并应用到当前对话引擎
        initConversationContext();
    }

    /**
     * 构建左侧展开区域
     */
    public JBLoadingPanel buildLeftPart() {
        mainPanelContentLeft = new JBLoadingPanel(new BorderLayout(), this, 0);
        mainPanelContentLeft.setVisible(false);// 左侧窗默认不可见

        // == 创建CardLayout容器
        leftPanelContainer = new JPanel(new CardLayout());
        leftPanelContainer.setFont(IdeUtil.getIDELabelFont());
        leftPanelContainer.setVisible(true);
        mainPanelContentLeft.add(leftPanelContainer, BorderLayout.CENTER);

        // 内外部版本隔离：指令模板功能
        if (ConfigWrapper.isInnerVersion) {
            promptTemplatePanel = new CodeChatPromptTemplateGroupComponent(myProject, this, new BorderLayout(), this, 0);
            promptTemplatePanel.setVisible(true);
            leftPanelContainer.add(promptTemplateCardName, promptTemplatePanel);
        }

        // == 定义 会话历史 功能卡片
        conversationListPanel = new CodeChatHistoryGroupComponent(myProject, this, new BorderLayout(), this, 0);
        leftPanelContainer.add(conversationCardName, conversationListPanel);


        return mainPanelContentLeft;
    }

    /**
     * 构建右侧区域-Header区域
     *
     * @return
     */
    public JPanel buildRightPartHeader() {
        mainPanelContentRightHeader = new JPanel(new BorderLayout());
        mainPanelContentRightHeader.setBorder(JBUI.Borders.empty());
        UIUtil.setBackground(mainPanelContentRightHeader);

        // 窗体头部-左侧区域
        JPanel headerPanelLeft = new JPanel();
        headerPanelLeft.setOpaque(false);
        headerPanelLeft.setBorder(JBUI.Borders.empty(5, 5, 5, 0));
        headerPanelLeft.setLayout(new FlowLayout(FlowLayout.LEFT, 0, 0));
        UIUtil.setBackground(headerPanelLeft);

        // 窗体头部-右侧区域
        headerPanelRight = new JPanel();
        headerPanelRight.setOpaque(false);
        headerPanelRight.setBorder(JBUI.Borders.empty(15, 0, 0, 5));
        headerPanelRight.setLayout(new FlowLayout(FlowLayout.RIGHT, 0, 0));
        if (LocalStorageUtil.checkIsLogin()) {
            headerPanelRight.setEnabled(true);
            headerPanelRight.setVisible(true);
        } else {
            headerPanelRight.setEnabled(false);
            headerPanelRight.setVisible(false);
        }
        UIUtil.setBackground(headerPanelRight);

        // 窗体头部-左侧区域："新建会话"面板
        JPanel newChat = new JPanel();
        newChat.setBorder(JBUI.Borders.empty());
        UIUtil.setBackground(newChat);
        addJBLabel = new JBLabel(MyIcons.NewConversation);
        newChat.setOpaque(false);
        newChat.setBorder(BorderFactory.createEmptyBorder());
        newChat.setLayout(new FlowLayout(FlowLayout.LEFT, 0, 0));
        newChat.add(addJBLabel);
        newChat.add(Box.createHorizontalStrut(5));//分隔

        // 4.4 添加鼠标点击事件
        addJBLabel.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (addJBLabel.isEnabled()) {
                    super.mouseClicked(e);

                    // 判断是否用户被禁用
                    //if (LocalStorageUtil.checkIsBanedUser()) {
                    //    ApplicationManager.getApplication().invokeLater(() -> {
                    //        MessageDialogUtil.showUserBanDialog();
                    //    });
                    //    return;
                    //}

                    // 重置会话id
                    ConversationManagerByAeBackend.getInstance(myProject).setConversationId(null);

                    // 创建一个新的会话
                    conversationListPanel.addAndSelectNewConversation(myProject, Constants.NEW_CONVERSATION_NAME, SubServiceType.ASSISTANT.getName());
                }
            }

            @Override
            public void mouseEntered(MouseEvent e) {
                addJBLabel.setCursor(new Cursor(Cursor.HAND_CURSOR));
                addJBLabel.setToolTipText("新建会话");
            }
        });

        // 左侧填充
        headerPanelLeft.add(newChat);// 新建会话
        headerPanelLeft.add(Box.createHorizontalStrut(16));//分隔

        // 右侧功能操作区域按钮
        // -- 历史会话列表拉起按钮
        Icon conversationListIcon = null;
        if (UIUtil.judgeBackgroudDarkTheme()) {
            conversationListIcon = MyIcons.ConversationListDark;
        } else {
            conversationListIcon = MyIcons.ConversationList;
        }
        JBLabel conversationListSwitch = new JBLabel(conversationListIcon);
        conversationListSwitch.setOpaque(false);
        conversationListSwitch.setCursor(new Cursor(Cursor.HAND_CURSOR));
        conversationListSwitch.setToolTipText("历史会话");

        conversationListSwitch.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                // 动态改变左侧面板展开/隐藏
                showLeftWindowCard(CodeChatMainPanel.conversationCardName);
            }
        });
        headerPanelRight.add(conversationListSwitch);
        headerPanelRight.add(Box.createHorizontalStrut(16));//分隔

        // 内外部版本隔离：指令模板功能
        if (ConfigWrapper.isInnerVersion) {
            Icon promptListIcon = null;
            if (UIUtil.judgeBackgroudDarkTheme()) {
                promptListIcon = MyIcons.PromptListDark;
            } else {
                promptListIcon = MyIcons.PromptList;
            }
            JBLabel promptListSwitch = new JBLabel(promptListIcon);
            promptListSwitch.setOpaque(false);
            promptListSwitch.addMouseListener(new MouseAdapter() {
                @Override
                public void mouseClicked(MouseEvent e) {
                    // 动态改变左侧面板展开/隐藏,传激活哪个tab
                    showLeftWindowCard(CodeChatMainPanel.promptTemplateCardName);
                }

                @Override
                public void mouseEntered(MouseEvent e) {
                    promptListSwitch.setCursor(new Cursor(Cursor.HAND_CURSOR));
                    promptListSwitch.setToolTipText("指令模板");
                }
            });

            headerPanelRight.add(promptListSwitch);
            headerPanelRight.add(Box.createHorizontalStrut(20));//分隔
        }

        mainPanelContentRightHeader.add(headerPanelLeft, BorderLayout.WEST);
        mainPanelContentRightHeader.add(headerPanelRight, BorderLayout.EAST);
        return mainPanelContentRightHeader;
    }


    /**
     * 构建右侧区域-Center区域
     */
    public JPanel buildRightPartCenter() {
        rightCentreLayout = new CardLayout();
        mainPanelContentRightCenter = new JPanel(rightCentreLayout);
        mainPanelContentRightCenter.setVisible(true);

        // 首屏卡片
        firstScreenPanel = new CodeChatFirstScreenComponent(myProject, this);
        firstScreenPanel.setVisible(true);
        mainPanelContentRightCenter.add(firstScreenCardName, firstScreenPanel);
        currentRightCenterPanelCardName = firstScreenCardName;

        // 消息卡片
        messageAreaPanel = new CodeChatMessageGroupComponent(myProject, this, new BorderLayout(), this, 0);
        messageAreaPanel.setVisible(false);
        mainPanelContentRightCenter.add(codeChatMessageGroupCardName, messageAreaPanel);

        return mainPanelContentRightCenter;
    }

    /**
     * 切换右侧区域-Center区域
     */
    public void changeRightPartCenter(String cardName) {
        SwingUtilities.invokeLater(() -> {
            if (cardName.equalsIgnoreCase(firstScreenCardName) && messageAreaPanel.isNewConversation()) {
                // 切换到首屏卡片
                rightCentreLayout.show(mainPanelContentRightCenter, firstScreenCardName);
                firstScreenPanel.setVisible(true);
                firstScreenPanel.updateAction();
                messageAreaPanel.setVisible(false);
            } else {
                // 切换到消息卡片
                rightCentreLayout.show(mainPanelContentRightCenter, codeChatMessageGroupCardName);
                firstScreenPanel.setVisible(false);
                messageAreaPanel.setVisible(true);
            }
        });
    }

    /**
     * 构建右侧区域-Bottom区域
     */
    public JPanel buildRightPartBottom() {
        mainPanelContentRightBottom = new JPanel(new BorderLayout());

        //== North区域：选中代码预览区
        flowCodeViewPanel = buildFlowCodeView();
        mainPanelContentRightBottom.add(flowCodeViewPanel, BorderLayout.NORTH);

        //== Center区域：输入交互区域
        askInputPanel = buildAskInputPanel();
        mainPanelContentRightBottom.add(askInputPanel, BorderLayout.CENTER);

        return mainPanelContentRightBottom;
    }

    /**
     * 代码选中预览面板
     *
     * @return
     */
    public JPanel buildFlowCodeView() {
        // == 定义预览展示UI
        flowCodeViewPanel = new JPanel(new BorderLayout());
        flowCodeViewPanel.setBorder(BorderFactory.createEmptyBorder(2, 2, 2, 2));
        if (UIUtil.judgeBackgroudDarkTheme()) {
            flowCodeViewPanel.setBackground(Color.decode("#2E3338"));
        } else {
            flowCodeViewPanel.setBackground(Color.decode("#F5F7F9"));
        }
        flowCodeViewPanel.setMinimumSize(new Dimension(Integer.MIN_VALUE, 100));
        flowCodeViewPanel.setPreferredSize(new Dimension(Integer.MAX_VALUE, 200));
        flowCodeViewPanel.setMaximumSize(new Dimension(Integer.MAX_VALUE, 200));
        flowCodeViewPanel.setSize(new Dimension(Integer.MAX_VALUE, 100));
        flowCodeViewPanel.setVisible(false);
        flowCodeViewPanel.setAutoscrolls(true);
        flowCodeViewPanel.revalidate();

        // == 定义编辑器选中事件监听器，选中代码则展示预览面板
        editorSelectionListener = new EditorSelectionListener(flowCodeViewPanel, myProject, this, mainPanelContentRight);
        // ---- 获取当前编辑器
        editorLocal = FileEditorManager.getInstance(myProject).getSelectedTextEditor();
        // ---- 注册选择监听器
        if (editorLocal != null) {
            editorLocal.getSelectionModel().addSelectionListener(editorSelectionListener);
        }

        // -- 定义编辑器页面管理事件监听器
        myProject.getMessageBus().connect().subscribe(FileEditorManagerListener.FILE_EDITOR_MANAGER, new FileEditorManagerListener() {

            /**
             * 切换编辑器打开文件时
             */
            @Override
            public void selectionChanged(@NotNull FileEditorManagerEvent event) {
                // 移除旧的编辑器选中内容监听器
                if (editorLocal != null && editorLocal.getSelectionModel() != null) {
                    editorLocal.getSelectionModel().removeSelectionListener(editorSelectionListener);
                }
                // 针对新打开的文件页面添加代码选中监听器
                editorLocal = FileEditorManager.getInstance(myProject).getSelectedTextEditor();
                if (editorLocal != null) {
                    editorLocal.getSelectionModel().addSelectionListener(editorSelectionListener);
                }
            }
        });

        return flowCodeViewPanel;
    }


    /**
     * 底部输入交互区域：各种交互按钮、输入框、快捷指令popup等
     *
     * @return
     */
    public JPanel buildAskInputPanel() {
        askInputPanel = new JPanel(new BorderLayout());
        askInputPanel.setOpaque(false);

        // == North:动态显隐区域
        actionPanelNorthProgressBar = new JProgressBar();
        actionPanelNorthProgressBar.setVisible(false);
        askInputPanel.add(actionPanelNorthProgressBar, BorderLayout.NORTH);

        // == Center:输入、交互控件区域
        actionPanelCenter = buildAskInputOpsPanel();
        askInputPanel.add(actionPanelCenter, BorderLayout.SOUTH);

        return askInputPanel;
    }

    /**
     * 输入交互区域
     */
    public JPanel buildAskInputOpsPanel() {
        // - 定义开发问答交互事件监听器
        CodeChatOpsListener codeChatOpsListener = new CodeChatOpsListener(this);
        // - 创建底部文本带历史输入记忆功能的输入框
        inputTextArea = new CodeChatInputTextArea(new JBTextArea(), false, 100, 400);
        // -- 为文本输入区域添加按键事件监听：Enter换行，Enter+Ctrl发送
        inputTextArea.getTextArea().addKeyListener(codeChatOpsListener);

        // -- 设置输入框UI界面相关属性
        inputTextArea.setMinimumSize(new Dimension(inputTextArea.getWidth(), 500));
        inputTextArea.setMultilineEnabled(true);
        inputTextArea.getTextArea().setLineWrap(true);
        inputTextArea.getTextArea().setWrapStyleWord(true);
        inputTextArea.getTextArea().setAutoscrolls(true);
        inputTextArea.setAutoscrolls(true);
        inputTextArea.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        if (UIUtil.judgeBackgroudDarkTheme()) {
            inputTextArea.setBackground(Color.decode("#2E3338"));
        } else {
            inputTextArea.setBackground(Color.decode("#FFFFFF"));
        }
        // -- 设置输入框提示语
        setPlaceholder(Constants.placeholderText);

        // -- 为文本输入区域监听内容变化事件
        inputTextArea.getTextArea().getDocument().addDocumentListener(buildInputTextAreaDocumentListener());

        // -- 创建输入框旁边发送按钮
        sendButton = new JButton();
        Icon sendImageIcon = null;
        if (UIUtil.judgeBackgroudDarkTheme()) {
            sendImageIcon = GPTIcons.SEND_DARK;
        } else {
            sendImageIcon = GPTIcons.SEND;
        }
        sendButton.setIcon(sendImageIcon);
        // 为按钮绑定点击事件监听器
        sendButton.addActionListener(codeChatOpsListener);

        sendButton.setCursor(new Cursor(Cursor.HAND_CURSOR));
        sendButton.setBorder(BorderFactory.createEmptyBorder(0, 0, 0, 0));
        sendButton.setContentAreaFilled(false);
        sendButton.setFocusPainted(false);
        sendButton.setOpaque(false);
        sendButton.setVisible(true);
        sendButton.setToolTipText("发送");

        // - 创建输入框旁边停止回答按钮
        stopAnswerButton = new JButton("停止", AllIcons.Actions.Suspend);
        stopAnswerButton.addActionListener(e -> {
            checkAndStopCurrentQuestionAnswer();
        });
        stopAnswerButton.setUI(new DarculaButtonUI());
        stopAnswerButton.setVisible(false);
        stopAnswerButton.setCursor(new Cursor(Cursor.HAND_CURSOR));


        // --Center区域：输入框+发送按钮+停止按钮
        actionPanelCenter = new JPanel(new BorderLayout());
        Border topBorder = BorderFactory.createMatteBorder(1, 0, 0, 0, Color.decode("#F5F7F9"));
        if (UIUtil.judgeBackgroudDarkTheme()) {
            topBorder = BorderFactory.createMatteBorder(1, 0, 0, 0, Color.decode("#3D4349"));
        }
        Border emptyBorder = BorderFactory.createEmptyBorder(15, 15, 15, 15);
        CompoundBorder border = BorderFactory.createCompoundBorder(
                topBorder, emptyBorder
        );
        actionPanelCenter.setOpaque(true);
        actionPanelCenter.setBorder(border);
        UIUtil.setBackground(actionPanelCenter);

        // 登录状态影响输入框
        if (LocalStorageUtil.checkIsLogin()) {
            enableInputArea();
        } else {
            disableInputArea();
        }

        // --区域填充：添加交互按钮组、输入框、发送按钮
        actionPanelCenter.add(buildInputOpsBar(), BorderLayout.NORTH);
        actionPanelCenter.add(inputTextArea, BorderLayout.CENTER);
        actionPanelCenter.add(sendButton, BorderLayout.EAST);

        return actionPanelCenter;
    }

    public DocumentListener buildInputTextAreaDocumentListener() {
        return new DocumentListener() {

            // 弹窗事件
            private JBPopup cmdPopup;

            // action列表
            private List<SendAction> cmdList = new ArrayList<>();

            // 实例化单个Action
            private RightMenuCommentAction rightMenuCommentAction = myProject.getService(RightMenuCommentAction.class);
            private RightMenuExplainAction rightMenuExplainAction = myProject.getService(RightMenuExplainAction.class);
            private RightMenuOptimizeAction rightMenuOptimizeAction = myProject.getService(RightMenuOptimizeAction.class);
            private RightMenuTestAction rightMenuTestAction = myProject.getService(RightMenuTestAction.class);
            private QuickHelpAction quickHelpAction = myProject.getService(QuickHelpAction.class);

            @Override
            public void insertUpdate(DocumentEvent e) {
                textContentChanged();
            }

            @Override
            public void removeUpdate(DocumentEvent e) {
                textContentChanged();
            }

            @Override
            public void changedUpdate(DocumentEvent e) {
                textContentChanged();
            }

            private void textContentChanged() {
                String newText = inputTextArea.getTextArea().getText();
                // 清空内容，则重置当前点选模板ID
                if (StringUtils.isBlank(newText)) {
                    CodeChatCompleteEngin.currUsePromptTemplateId = null;
                }

                // 当前关键字匹配到的action
                ArrayList<SendAction> sendActions = new ArrayList<>();

                if (!newText.isEmpty() && newText.charAt(0) == '/') {
                    if ("/help".contains(newText)) {
                        sendActions.add(quickHelpAction);
                    }

                    if ("/explain code".contains(newText)) {
                        sendActions.add(rightMenuExplainAction);
                    }

                    if ("/generate unit test".contains(newText)) {
                        sendActions.add(rightMenuTestAction);
                    }

                    if ("/generate comment".contains(newText)) {
                        sendActions.add(rightMenuCommentAction);
                    }

                    if ("/generate optimization".contains(newText)) {
                        sendActions.add(rightMenuOptimizeAction);
                    }
                }

                // 匹配到相对应的action
                if (!sendActions.isEmpty()) {
                    // 检测搜索到的action发送改变
                    if (!cmdList.equals(sendActions)) {
                        // 根据Action List生成对应的popup builder
                        IPopupChooserBuilder<SendAction> builder = JBPopupFactory.getInstance().createPopupChooserBuilder(sendActions);
                        // 为每一个Action设置点击回调
                        builder.setItemChosenCallback(action -> {


                            // 展示侧边栏
                            AssistantToolWindow.toolWindowVisible(myProject);

                            // 清空快捷指令
                            inputTextArea.getTextArea().setText("");

                            // 检查mainPanel
                            Key key = AssistantToolWindow.CODE_CHAT_TAB_KEY_MAP.get(myProject.getLocationHash());
                            CodeChatMainPanel mainPanel = (CodeChatMainPanel) myProject.getUserData(key);
                            if (mainPanel == null) {
                                MessageBalloonNotificationUtil.showBalloonNotificationByReason(myProject, "插件组件加载中，请稍后", Constants.RtnCode_Main_Panel_Is_Not_Loaded);
                                return;
                            }

                            // 获取代码，执行提问
                            if (editorLocal != null) {
                                String data = editorLocal.getSelectionModel().getSelectedText();
                                action.doActionPerformed(mainPanel, data, null, QuestionType.NEW_ASK, null);
                            } else {
                                action.doActionPerformed(mainPanel, "", null, QuestionType.NEW_ASK, null);
                            }
                        });

                        // 默认选中取消
                        builder.setAutoSelectIfEmpty(false);

                        builder.setRenderer((list, value, index, isSelected, cellHasFocus) -> {

                            JPanel border = new JPanel();
                            JPanel panel = new JPanel();
                            panel.setLayout(new BorderLayout());
                            border.setLayout(new BorderLayout());

                            JLabel label = new JLabel(value.toString());
                            JLabel cmd = new JLabel(value.getCMD());


                            if (isSelected) {
                                label.setForeground(UIUtil.getTextHoverColor());
                                cmd.setForeground(UIUtil.getTextHoverColor());
                            }

                            panel.add(label, BorderLayout.EAST);
                            panel.add(cmd, BorderLayout.WEST);

                            border.add(panel);

                            // 空挡预埋，可使用空像素块填充，注意调整show的位置
                            border.add(new JLabel("   "), BorderLayout.EAST);
                            border.add(new JLabel("   "), BorderLayout.WEST);
                            border.add(new JLabel(" "), BorderLayout.NORTH);
                            border.add(new JLabel(" "), BorderLayout.SOUTH);

                            return border;
                        });

                        // 创建弹窗
                        JBPopup popup = builder.createPopup();
                        popup.setMinimumSize(new Dimension(inputTextArea.getWidth(), 0));

                        // 关闭上一次开启的弹窗
                        if (cmdPopup != null) {
                            cmdPopup.cancel();
                        }

                        // 开启当前创建的弹窗
                        cmdList = sendActions;
                        cmdPopup = popup;

                        cmdPopup.show(new RelativePoint(inputTextArea, new Point(0, -(5 + 48 * (sendActions.size())))));
                        inputTextArea.getTextArea().requestFocus();
                    }
                } else {
                    if (cmdPopup != null) {
                        cmdPopup.cancel();
                        cmdPopup = null;
                        cmdList = new ArrayList<>();
                    }
                }
            }
        };
    }


    /**
     * ops操作交互区域：知识库、关联文件等
     *
     * @return
     */
    public JPanel buildInputOpsBar() {
        askInputPanelOpsBar = new JPanel(new BorderLayout());

        // 内外部版本隔离：ops操作区不对外开放，如：知识库等
        if (!ConfigWrapper.isInnerVersion) {
            askInputPanelOpsBar.setEnabled(false);
            askInputPanelOpsBar.setVisible(false);
            return askInputPanelOpsBar;
        }

        askInputPanelOpsBarLeft = new JPanel();
        askInputPanelOpsBarLeft.setOpaque(false);
        askInputPanelOpsBarLeft.setLayout(new FlowLayout(FlowLayout.LEFT, 0, 0));

        askInputPanelOpsBarLeft.add(createKnowledgeButton());
        askInputPanelOpsBar.add(askInputPanelOpsBarLeft, BorderLayout.WEST);
        askInputPanelOpsBar.add(new JLabel(" "), BorderLayout.SOUTH);

        // == 右侧：预埋，用于其他功能按钮扩充
        //askInputPanelOpsBarRight = new JPanel();
        //askInputPanelOpsBarRight.setOpaque(false);
        //JButton leftButton = new JButton("左");
        //JButton rightButton = new JButton("右");
        //askInputPanelOpsBarRight.add(leftButton);
        //askInputPanelOpsBarRight.add(rightButton);
        //askInputPanelOpsBar.add(askInputPanelOpsBarRight, BorderLayout.EAST);

        askInputPanelOpsBar.setOpaque(false);

        // 根据初始登录状态，决定可见性
        if (LocalStorageUtil.checkIsLogin()) {
            askInputPanelOpsBar.setEnabled(true);
            askInputPanelOpsBar.setVisible(true);
        } else {
            askInputPanelOpsBar.setEnabled(false);
            askInputPanelOpsBar.setVisible(false);
        }

        return askInputPanelOpsBar;
    }

    /**
     * 知识库操作区域：popup、回显
     *
     * @return
     */
    public JPanel createKnowledgeButton() {
        CodeChatMainPanel parent = this;
        knowledgeBaseLayout = new CardLayout();
        knowledgeButton = new JPanel(knowledgeBaseLayout);

        JLabel knowledgeIcon = new JLabel(UIUtil.judgeBackgroudDarkTheme() ? MyIcons.Knowledge_Base_Dark : MyIcons.Knowledge_Base);
        knowledgeIcon.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));
        knowledgeIcon.setOpaque(false);

        // 设置按钮边框和样式
        knowledgeButton.add(knowledgeIcon);
        knowledgeButton.setOpaque(false);

        // 用于显示选中的知识库名称和取消按钮
        RoundedPanel knowledgeChoosePanel = RoundedPanel.createDefaultPanel();
        knowledgeChoosePanel.setLayout(new BorderLayout());
        knowledgeChoosePanel.setBackgroundColor(UIUtil.getCardBackgroundColor());
        JPanel knowledgeChoose = new JPanel();
        knowledgeChooseText = new JLabel("");
        JLabel knowledgeChooseIcon = new JLabel(UIUtil.judgeBackgroudDarkTheme() ? MyIcons.CancelDark : MyIcons.Cancel);
        knowledgeChoose.setOpaque(false);

        knowledgeChooseText.setOpaque(false);
        knowledgeChooseIcon.setOpaque(false);
        knowledgeChooseIcon.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));


        knowledgeChooseIcon.addMouseListener(new MouseAdapter() {
            public void mouseClicked(MouseEvent e) {
                CodeChatCompleteEngin.getCurrentConversation(parent).setKbId(null);
                changeKnowledgeButton();
            }
        });

        knowledgeChoose.add(knowledgeChooseText);
        knowledgeChoose.add(knowledgeChooseIcon);

        // 填空
        JPanel space1 = new JPanel();
        space1.setOpaque(false);
        space1.setPreferredSize(new Dimension(1, 2));

        JPanel space2 = new JPanel();
        space2.setOpaque(false);
        space2.setPreferredSize(new Dimension(1, 2));

        knowledgeChoosePanel.add(space1, BorderLayout.NORTH);
        knowledgeChoosePanel.add(space2, BorderLayout.WEST);
        knowledgeChoosePanel.add(knowledgeChoose, BorderLayout.CENTER);

        // 添加鼠标监听器
        knowledgeButton.addMouseListener(new MouseAdapter() {

            // 存储知识库操作
            private ArrayList<KnowledgeAction> knowledgeActions = new ArrayList<>();
            private IPopupChooserBuilder<KnowledgeAction> builder = JBPopupFactory.getInstance().createPopupChooserBuilder(knowledgeActions);
            private JBPopup knowledgePopup = builder.createPopup();

            // 鼠标点击操作
            @Override
            public void mouseClicked(MouseEvent e) {
                knowledgeActions.clear();

                KnowledgeBaseListResponse knowledgeBaseListResponse = KnowledgeBaseCommHandler.getKnowledgeBaseList();
                if (knowledgeBaseListResponse.getOptResult() != RtnCode.SUCCESS || Objects.isNull(knowledgeBaseListResponse.getData())) {
                    MessageBalloonNotificationUtil.showBalloonNotificationByReason(Objects.requireNonNull(IdeUtil.findCurrentProject()), "知识库获取失败，请稍后重试。", knowledgeBaseListResponse.getOptResult());
                    return;
                }
                List<KnowledgeBaseInfo> info = knowledgeBaseListResponse.getData().getRecords();
                if (CollectionUtils.isEmpty(info)) {
                    logger.warn("[cf] knowledgeBaseList is empty.");
                    return;
                }

                for (KnowledgeBaseInfo item : info) {
                    knowledgeActions.add(new KnowledgeAction(item));
                }

                // 创建弹窗
                builder = JBPopupFactory.getInstance().createPopupChooserBuilder(knowledgeActions);

                // 设置渲染器
                builder.setRenderer((list, value, index, isSelected, cellHasFocus) -> {
                    JPanel border = new JPanel();
                    JPanel panel = new JPanel();
                    panel.setOpaque(false);
                    panel.setLayout(new BorderLayout());
                    border.setLayout(new BorderLayout());

                    // 选中效果
                    if (isSelected) {
                        border.setBackground(UIUtil.getCardBackgroundColor());
                    } else {
                        border.setBackground(UIUtil.getBackground());
                    }

                    JLabel kbName = new JLabel(value.getKnowledgeBaseName());
                    border.setToolTipText(value.getOrgName());
                    panel.add(kbName, BorderLayout.WEST);

                    // 右侧文字预留
//                    JLabel kbOrg = new JLabel(value.getOrgName());
//                    panel.add(kbOrg, BorderLayout.EAST);

                    // 空挡预埋，可使用空像素块填充，注意调整show的位置
                    border.add(panel);
                    border.add(new JLabel("   "), BorderLayout.EAST);
                    border.add(new JLabel("   "), BorderLayout.WEST);
                    border.add(new JLabel(" "), BorderLayout.NORTH);
                    border.add(new JLabel(" "), BorderLayout.SOUTH);

                    return border;
                });

                // 设置点击触发回调
                builder.setItemChosenCallback(knowledgeAction -> {
                    CodeChatCompleteEngin.getCurrentConversation(parent).setKbId(knowledgeAction.getKnowledgeBaseInfo().getKbId());
                    knowledgeAction.execute();
                    changeKnowledgeButton();
                });

                knowledgePopup = builder.createPopup();

                // 设置弹窗大小
                int height = 48 * knowledgeActions.size();
                knowledgePopup.setMinimumSize(new Dimension(inputTextArea.getWidth(), 0));

                // 滚动条
                height = Math.min(height, 240);
                knowledgePopup.setSize(new Dimension(inputTextArea.getWidth(), height));

                knowledgePopup.show(new RelativePoint(knowledgeIcon, new Point(-5, -(height + 10))));
            }
        });

        knowledgeButton.add(knowledgeChoosePanel);
        changeKnowledgeButton();

        return knowledgeButton;
    }

    public void changeKnowledgeButton() {
        // 内外部版本隔离：不回显知识库
        if (!ConfigWrapper.isInnerVersion) {
            return;
        }

        Conversation conversation = CodeChatCompleteEngin.getCurrentConversation(this);
        Integer kbId = conversation.getKbId();

        if (kbId != null) {

            KnowledgeBaseDetailResponse response = KnowledgeBaseCommHandler.getKnowledgeBaseDetail(kbId);

            if (response.getData() == null) {
                // 反查失败，需要重置知识库，走回大模型链路
                conversation.setKbId(null);
                logger.warn("[cf] Knowledge base detail response is empty.");
                return;
            }

            knowledgeChooseText.setText(response.getData().getKbName());
            knowledgeBaseLayout.last(knowledgeButton);
        } else {
            knowledgeChooseText.setText("");
            knowledgeBaseLayout.first(knowledgeButton);
        }

        knowledgeButton.revalidate();
        knowledgeButton.repaint();
    }


    //=======;==============逻辑函数=======================

    /**
     * 初始化对话引擎环境
     */
    public void initConversationContext() {
        Conversation newConversation = this.conversationListPanel.createNewConversation(myProject, Constants.NEW_CONVERSATION_NAME, SubServiceType.ASSISTANT.getName());
        CodeChatCompleteEngin.setCurrentConversation(newConversation);

        // 重设当前会话id
        ConversationManagerByAeBackend.getInstance(myProject).setConversationId(newConversation.getId());
        // 重设对话引擎会话模型路由条件
        CodeChatCompleteEngin.currModelRouteCondition = newConversation.getModelRouteCondition();
        // 重设对话引擎点选模板
        CodeChatCompleteEngin.currConversationConditionTemplateId = null;

        // 重设对话引擎问答轮
        CodeChatCompleteEngin.payloadPromptsChats.clear();
        // -- 设置system prompt，限定对话背景
        CodeChatPrompt codeChatPromptFirst = new CodeChatPrompt();
        codeChatPromptFirst.setContent(CodeChatRequestSender.systemRoleText);
        codeChatPromptFirst.setRole(PromptRoleType.SYSTEM.getType());
        CodeChatCompleteEngin.payloadPromptsChats.add(codeChatPromptFirst);
    }

    /**
     * 停止当前回答
     */
    public void checkAndStopCurrentQuestionAnswer() {
        if (CodeChatCompleteEngin.isWaitResp || CodeChatCompleteEngin.onAnswerStatus) {
            // 停止线程
            if (executorService != null) {
                executorService.shutdownNow();
            }

            // 发送停止回答请求
            if (requestHolder instanceof EventSource) {
                ((EventSource) requestHolder).cancel();
            } else if (requestHolder instanceof Call) {
                ((Call) requestHolder).cancel();
            } else if (requestHolder instanceof QuestionTask) {
                ((QuestionTask) requestHolder).Cancel(MessageNameConstant.MessageName_CodeCancelChatRequest);
            }

            // 停止UI等待效果、刷新会话列表
            aroundRequest(false);
        }

        // 修改回答等待状态
        CodeChatCompleteEngin.isWaitResp = false;
        CodeChatCompleteEngin.onAnswerStatus = false;
    }

    /**
     * 收起左侧扩展窗
     */
    public void closeLeftWindow() {
        mainPanelContentLeft.setVisible(false);
    }

    /**
     * 左侧扩展窗相关功能按钮点击事件
     */
    public void showLeftWindowCard(String cardName) {
        // 侧边栏可见性判断计算
        boolean visible = false;
        if (cardName.equalsIgnoreCase(currentLeftPanelCardName)) {
            visible = !mainPanelContentLeft.isVisible();
        } else {
            visible = true;
        }

        // 刷新卡片数据
        if (promptTemplateCardName.equalsIgnoreCase(cardName)) {
            promptTemplatePanel.refreshPromptCategory();
            promptTemplatePanel.refreshPromptTemplateList();
        }

        if (conversationCardName.equalsIgnoreCase(cardName)) {
            conversationListPanel.refreshConversationListFromMainPanel();
        }

        // 切换左侧展开卡片
        changeLeftPanelCard(cardName);

        // 控制侧边栏可见性
        mainPanelContentLeft.setVisible(visible);
    }

    private void changeLeftPanelCard(String cardName) {
        currentLeftPanelCardName = cardName;
        // 切换卡片展示
        ((CardLayout) leftPanelContainer.getLayout()).show(leftPanelContainer, cardName);
    }

    /**
     * 设置输入框提示语
     */
    public void setPlaceholder(String placeholder) {
        boolean isDark = UIUtil.judgeBackgroudDarkTheme();
        inputTextArea.getTextArea().setText(placeholder);
        inputTextArea.getTextArea().setForeground(UIUtil.getTextPlaceHolderColor());
        inputTextArea.getTextArea().addFocusListener(new FocusListener() {
            @Override
            public void focusGained(FocusEvent e) {
                if (inputTextArea.getTextArea().getText().equals(placeholder)) {
                    inputTextArea.getTextArea().setText(null);
                    inputTextArea.setForeground(null);
                    inputTextArea.getTextArea().setForeground(null);
                }
            }

            @Override
            public void focusLost(FocusEvent e) {
                if (inputTextArea.getTextArea().getText().isEmpty()) {
                    inputTextArea.getTextArea().setText(placeholder);
                    inputTextArea.setForeground(UIUtil.getTextPlaceHolderColor());
                    inputTextArea.getTextArea().setForeground(UIUtil.getTextPlaceHolderColor());
                }
            }
        });
    }

    public void aroundRequest(boolean answering) {
        SwingUtilities.invokeLater(() -> {
            actionPanelNorthProgressBar.setIndeterminate(answering);
            actionPanelNorthProgressBar.setVisible(answering);
            sendButton.setEnabled(!answering);
            if (answering) {
                // 通信中
                messageAreaPanel.addScrollListener();
                sendButton.setVisible(false);
                actionPanelCenter.add(stopAnswerButton, BorderLayout.EAST);
                actionPanelCenter.remove(sendButton);
                stopAnswerButton.setVisible(true);
            } else {
                // 通信完毕
                enableAddNewConversation();
                messageAreaPanel.removeScrollListener();
                stopAnswerButton.setVisible(false);
                actionPanelCenter.add(sendButton, BorderLayout.EAST);
                actionPanelCenter.remove(stopAnswerButton);
                sendButton.setVisible(true);
            }
            askInputPanel.updateUI();
            messageAreaPanel.updateLayoutAndAutoScroll();
        });

        // 停止后，刷新会话列表
        if (!answering) {
            conversationListPanel.refreshConversationList(null);
        }
    }

    // ==================getter/setter================

    /**
     * 获取单线程的串行执行线程池
     * - 线程池：不阻塞UI
     * - 单线程：可以保证由UI层发起的耗时操作按串行顺序执行
     *
     * @return
     */
    public ExecutorService getSerialThreadPool() {
        executorService = Executors.newFixedThreadPool(1);
        return executorService;
    }

    @Override
    public void dispose() {
    }

    public Project getProject() {
        return myProject;
    }

    public SearchTextArea getInputTextArea() {
        return inputTextArea;
    }

    public CodeChatMessageGroupComponent getMessageAreaPanel() {
        return messageAreaPanel;
    }

    public JButton getSendButton() {
        return sendButton;
    }

    public void setRequestHolder(Object eventSource) {
        this.requestHolder = eventSource;
    }

    public String getFlowCodeViewPanelText() {
        return flowCodeViewPanelText;
    }

    public void setFlowCodeViewPanelText(String flowCodeViewPanelText) {
        this.flowCodeViewPanelText = flowCodeViewPanelText;
    }

    public EditorSelectionListener getMySelectionListener() {
        return editorSelectionListener;
    }

    public JButton getStopAnswerButton() {
        return stopAnswerButton;
    }

    public OnePixelSplitter getMainPanelContent() {
        return mainPanelContent;
    }

    public JPanel getMainPanelContentRightCenter() {
        return mainPanelContentRightCenter;
    }

    public CodeChatFirstScreenComponent getFirstScreenPanel() {
        return firstScreenPanel;
    }

    public void disableInputArea() {
        inputTextArea.getTextArea().setEnabled(false);
        sendButton.setEnabled(false);

        inputTextArea.revalidate();
        inputTextArea.repaint();
        sendButton.revalidate();
        sendButton.repaint();
    }

    public void enableAskInputPanelOpsBar() {
        askInputPanelOpsBar.setEnabled(true);
        askInputPanelOpsBar.setVisible(true);

        askInputPanelOpsBar.repaint();
        actionPanelCenter.repaint();
    }

    public void disableAskInputPanelOpsBar() {
        askInputPanelOpsBar.setEnabled(false);
        askInputPanelOpsBar.setVisible(false);

        askInputPanelOpsBar.repaint();
        actionPanelCenter.repaint();
    }

    public void enableInputArea() {
        inputTextArea.getTextArea().setEnabled(true);
        sendButton.setEnabled(true);

        inputTextArea.revalidate();
        sendButton.revalidate();
        inputTextArea.repaint();
        sendButton.repaint();
    }

    public void enableAddNewConversation() {
        addJBLabel.setEnabled(true);
        addJBLabel.revalidate();
        addJBLabel.revalidate();
        conversationListPanel.enableAddNewConversation();
    }

    public void disableAddNewConversation() {
        addJBLabel.setEnabled(false);
        addJBLabel.revalidate();
        addJBLabel.revalidate();
        conversationListPanel.disableAddNewConversation();
    }

    public void enableHeaderRight() {
        headerPanelRight.setEnabled(true);
        headerPanelRight.setVisible(true);
        headerPanelRight.revalidate();
        headerPanelRight.revalidate();
    }

    public void disableHeaderRight() {
        mainPanelContentLeft.setVisible(false);

        headerPanelRight.setEnabled(false);
        headerPanelRight.setVisible(false);
        headerPanelRight.revalidate();
        headerPanelRight.revalidate();
    }
}
