package com.srdcloud.ideplugin.codecomplete.listener

import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.ex.FocusChangeListener
import com.srdcloud.ideplugin.codecomplete.handle.CompletionContext.Companion.resetInlineCompletionContext

/**
 * <AUTHOR>
 * @date 2025/6/6
 * @desc 光标焦点变化监听
 */
class CompletionFocusListener : FocusChangeListener {
    override fun focusGained(editor: Editor) {}

    // 失去焦点，重置补全提示
    override fun focusLost(editor: Editor) {
        editor.resetInlineCompletionContext()
    }
}