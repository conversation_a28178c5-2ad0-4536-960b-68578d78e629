package com.srdcloud.ideplugin.general.utils;

import com.intellij.ide.util.PropertiesComponent;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.remote.CodeAICommHandler;
import com.srdcloud.ideplugin.remote.domain.User.UserInfo;
import com.srdcloud.ideplugin.service.LoginService;
import com.srdcloud.ideplugin.service.domain.oauth.AuthResponse;

/**
 * <AUTHOR>
 * @date 2024/4/18
 * @description 本地数据存储工具
 */
public final class LocalStorageUtil {

    /**
     * 查询当前登录状态是否处于已登录
     */
    public static boolean checkIsLogin() {
        if (LoginService.getSecideaLoginStatus() == Constants.LoginStatus_OK) {
            return true;
        }
        return false;
    }

    /**
     * 查询当前用户是否被禁用
     */
    public static boolean checkIsBanedUser() {
        if (LoginService.getUserStatus() == Constants.UserStatus_NOK) {
            return true;
        }
        return false;
    }

    /**
     * 查询当前通道状态是否连接
     */
    public static boolean checkChannelConnected() {
        if (CodeAICommHandler.GetInstance(ConfigWrapper.getCodeAIServerUrl()).GetChannelStatus() == Constants.Channel_Connected) {
            return true;
        }
        return false;
    }

    /**
     * 查询当前通道是否被远端关闭
     */
    public static boolean checkChannelDisconnectedByRemote() {
        return CodeAICommHandler.GetInstance(ConfigWrapper.getCodeAIServerUrl()).GetIsRemoteClose();
    }

    /**
     * 检查通信条件：如果不具备，则业务http请求不发起
     *
     * @return
     */
    public static boolean checkNetCondition() {
        return checkIsLogin() && checkChannelConnected();
    }

    /**
     * 查询登录会话id
     *
     * @return
     */
    public static String getSessionId() {
        return getAuthProperty(Constants.SessionPropKey);
    }

    /**
     * 查询用户id
     *
     * @return
     */
    public static String getUserId() {
        return getAuthProperty(Constants.UserIdPropKey);
    }

    /**
     * 查询用户账户
     *
     * @return
     */
    public static String getUserAccount() {
        return getAuthProperty(Constants.UserAccountPropKey);
    }

    /**
     * 查询网关层apiKey
     *
     * @return
     */
    public static String getApikey() {
        return getAuthProperty(Constants.ApiKeyPropKey);
    }

    public static String getIndexVersion() {
        return getAuthProperty(Constants.IndexVersion);
    }


    /**
     * 查询当前环境服务端通信url
     */
    public static String getServerUrl() {
        return ConfigWrapper.getServerUrl();
    }

    public static String getRemoteVersion(){
        return getAuthProperty(Constants.RemoteVersion);
    }

    public static String getClientUrlSubPath(){
        return getAuthProperty(Constants.ClientUrlSubPath);
    }

    /**
     * 获取存储的oauth认证凭据，用于业务侧发起通信鉴权
     */
    public static String getAuthProperty(final String key) {
        final String propertyValue = PropertiesComponent.getInstance().getValue(key, "");
        return propertyValue;
    }

    /**
     * 存储鉴权验证成功后的信息
     */
    public static void saveAuthProperties(AuthResponse authRespons) {
        PropertiesComponent.getInstance().setValue(Constants.AuthTokenPropKey, authRespons.getAccess_token());
        PropertiesComponent.getInstance().setValue(Constants.SessionPropKey, authRespons.getOri_session_id());
        PropertiesComponent.getInstance().setValue(Constants.UserIdPropKey, authRespons.getUid());
        PropertiesComponent.getInstance().setValue(Constants.LoginStatusPropKey, String.valueOf(Constants.LoginStatus_OK));
    }

    /**
     * 存储用户信息
     */
    public static void saveUserInfo(UserInfo userInfo) {
        PropertiesComponent.getInstance().setValue(Constants.UserNamePropKey, userInfo.getName());
        PropertiesComponent.getInstance().setValue(Constants.UserAccountPropKey, userInfo.getUserAccount());
    }

    /**
     * 清除oauth认证凭据与用户信息
     */
    public static void clearAuthPropertiesAndUserInfo() {
        PropertiesComponent.getInstance().setValue(Constants.AuthTokenPropKey, null);
        PropertiesComponent.getInstance().setValue(Constants.SessionPropKey, null);
        PropertiesComponent.getInstance().setValue(Constants.UserIdPropKey, null);
        PropertiesComponent.getInstance().setValue(Constants.ApiKeyPropKey, null);
        PropertiesComponent.getInstance().setValue(Constants.LoginStatusPropKey, String.valueOf(Constants.LoginStatus_NOK));
        PropertiesComponent.getInstance().setValue(Constants.UserNamePropKey, null);
        PropertiesComponent.getInstance().setValue(Constants.UserAccountPropKey, null);
        PropertiesComponent.getInstance().setValue(Constants.AgentVersionInfoKey, null);
    }
}
