package com.srdcloud.ideplugin.codecomplete.handle.codeprovider.prompt

import com.srdcloud.ideplugin.service.domain.apigw.codechat.CodeMessage.ImportSnippets

/**
 * <AUTHOR>
 * @date 2025/6/9
 * @desc 代码补全参考信息
 */
data class Prompt(
    val fileName: String?,
    val line: Int,
    val col: Int,
    val prefix: String,
    val suffix: String?,
    val stopWords: String?,
    val importSnippets: List<ImportSnippets>
)
