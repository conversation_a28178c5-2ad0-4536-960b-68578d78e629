package com.srdcloud.ideplugin.assistant.codechatNative.ui.message;

import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.ConversationMessageQuoteGuide;
import com.srdcloud.ideplugin.general.utils.BrowseUtil;
import com.srdcloud.ideplugin.general.utils.UIUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/21
 * @desc CodeChatMessageQuoteGuidePanel 类用于显示对话中引用指南链接
 */
public class CodeChatMessageQuoteGuidePanel extends JPanel {

    /**
     * 构造函数，初始化面板布局和内容
     *
     * @param list ConversationMessageQuoteGuide 的列表，用于展示的引用指南数据
     */
    public CodeChatMessageQuoteGuidePanel(List<ConversationMessageQuoteGuide> list) {

        // 使用BorderLayout布局管理器
        setLayout(new BorderLayout());

        // 创建一个内容面板，设置为透明背景
        JPanel contentPanel = new JPanel();
        contentPanel.setOpaque(false);

        // 创建一个提示标签，指示用户更多开发说明的位置
        JLabel hintText = new JLabel("更多开发说明请参考:");
        hintText.setForeground(UIUtil.disableTextColor); // 设置提示文本的颜色

        // 将提示标签添加到内容面板
        contentPanel.add(hintText);

        // 遍历数据列表，为每个知音创建一个标签，并关联外部链接
        for (int i = 0; i < list.size(); i++) {
            ConversationMessageQuoteGuide item = list.get(i);

            // 创建标题标签，如果不是第一个，则在标题前加上逗号
            JLabel title = new JLabel(i > 0 ? ", " + item.getFullTittle() : " " + item.getFullTittle());
            title.setForeground(UIUtil.textHoverFrontColor); // 设置标题文本的颜色

            // 设置鼠标悬停时的光标为手形，指示可点击
            title.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));

            // 添加鼠标点击事件监听器，点击时打开对应链接
            title.addMouseListener(new MouseAdapter() {
                @Override
                public void mouseClicked(MouseEvent e) {
                    BrowseUtil.Companion.browse(item.getLink());
                }
            });

            // 将标题标签添加到内容面板
            contentPanel.add(title);
        }

        // 将内容面板添加到左边区域
        this.add(contentPanel, BorderLayout.WEST);
        // 设置面板为透明背景
        this.setOpaque(false);
    }
}