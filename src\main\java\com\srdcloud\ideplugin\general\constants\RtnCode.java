package com.srdcloud.ideplugin.general.constants;

/**
 * <AUTHOR>
 * @date 2025/1/13
 */
public class RtnCode {
    public static final int SUCCESS = 0;
    public static final int NO_CHANNEL = 1;
    public static final int NOT_LOGIN = 2;
    public static final int INVALID_USER = 3;
    public static final int SEND_ERROR = 4;
    public static final int RECV_TIMEOUT = 5;
    public static final int USER_FORBIDDEN = 6;
    public static final int INSUFFICIENT_RESOURCE = 7;
    public static final int MODEL_ERROR = 8;
    public static final int SERVER_DOWN = 9;
    public static final int CANCEL = 10;
    public static final int INVALID_SESSION_ID = 11;
    public static final int LOGOUT = 12;
    public static final int INVALID_QUESTION = 13;
    public static final int INVALID_ANSWER = 14;
    public static final int KNOWLEDGE_BASE_DELETED = 15;

    // vscode客户端自定义返回码
    public static final int OFFLINE = 1;
    public static final int OAUTH2_ERROR = 16;
    public static final int CONNECTED_ERROR = 17;
    public static final int UPLOAD_FAIL = 18;
    public static final int HTTP_CLIENT_ERROR = 19;
    public static final int HTTP_REQUEST_ERROR = 20;
    public static final int INSERT_ERROR = 21;
    public static final int STOP_ANSWER = 22;
    public static final int NO_CHANNEL_CHAT = 23;

    // jetbrains客户端自定义返回码
    public static final int RtnCode_Not_Select_Text = 101;
    public static final int RtnCode_Not_Select_Editor = 102;
    public static final int RtnCode_Right_Chat_RUNNING = 103;
    public static final int RtnCode_Main_Panel_Is_Not_Loaded = 104;
}
