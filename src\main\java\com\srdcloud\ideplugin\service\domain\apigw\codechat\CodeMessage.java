package com.srdcloud.ideplugin.service.domain.apigw.codechat;

import com.srdcloud.ideplugin.remote.domain.WorkItem.WorkItemInfo;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.DialogCondition;

import java.util.List;

/**
 * @author: yangy
 * @date: 2023/7/23 20:23
 * @Desc 承载AI Chat通信中的具体业务内容
 */
public class CodeMessage {

    private Integer kbId;

    private String dialogId;

    private String questionType;

    private String parentReqId;

    private String sub_service;

    private DialogCondition modelRouteCondition;

    private List<CodeAIRequestPromptChat> prompts;

    /**
     * 用户选择的用于提问的具体知识库引用
     */
    private List<QuoteItem> quote;

    private String language;

    private String filename;

    private String prefix;

    private String suffix;

    private Integer max_new_tokens;

    private List<String> stop_words;

    private List<ImportSnippets> importSnippets;

    private List<String> diffList;

    private List<WorkItemInfo> workItemList;

    public static class ImportSnippets {
        private String filePath;
        private String snippet;

        public ImportSnippets(String filePath, String snippet) {
            this.filePath = filePath;
            this.snippet = snippet;
        }

        public String getFilePath() {
            return filePath;
        }

        public void setFilePath(String filePath) {
            this.filePath = filePath;
        }

        public String getSnippet() {
            return snippet;
        }

        public void setSnippet(String snippet) {
            this.snippet = snippet;
        }
    }


    public CodeMessage() {
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public String getSuffix() {
        return suffix;
    }

    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }

    public Integer getMax_new_tokens() {
        return max_new_tokens;
    }

    public void setMax_new_tokens(Integer max_new_tokens) {
        this.max_new_tokens = max_new_tokens;
    }

    public List<String> getStop_words() {
        return stop_words;
    }

    public void setStop_words(List<String> stop_words) {
        this.stop_words = stop_words;
    }

    public String getSub_service() {
        return sub_service;
    }

    public void setSub_service(String sub_service) {
        this.sub_service = sub_service;
    }

    public List<CodeAIRequestPromptChat> getPrompts() {
        return prompts;
    }

    public void setPrompts(List<CodeAIRequestPromptChat> prompts) {
        this.prompts = prompts;
    }

    public Integer getKbId() {
        return kbId;
    }

    public void setKbId(Integer kbId) {
        this.kbId = kbId;
    }

    public String getDialogId() {
        return dialogId;
    }

    public void setDialogId(String dialogId) {
        this.dialogId = dialogId;
    }

    public String getQuestionType() {
        return questionType;
    }

    public void setQuestionType(String questionType) {
        this.questionType = questionType;
    }

    public String getParentReqId() {
        return parentReqId;
    }

    public void setParentReqId(String parentReqId) {
        this.parentReqId = parentReqId;
    }

    public DialogCondition getModelRouteCondition() {
        return modelRouteCondition;
    }

    public void setModelRouteCondition(DialogCondition modelRouteCondition) {
        this.modelRouteCondition = modelRouteCondition;
    }

    public List<QuoteItem> getQuote() {
        return quote;
    }

    public void setQuote(List<QuoteItem> quote) {
        this.quote = quote;
    }

    public List<ImportSnippets> getImportSnippets() {
        return importSnippets;
    }

    public void setImportSnippets(List<ImportSnippets> importSnippets) {
        this.importSnippets = importSnippets;
    }

    public List<String> getDiffList() {
        return diffList;
    }

    public void setDiffList(List<String> diffList) {
        this.diffList = diffList;
    }

    public List<WorkItemInfo> getWorkItemList() {
        return workItemList;
    }

    public void setWorkItemList(List<WorkItemInfo> workItemList) {
        this.workItemList = workItemList;
    }
}
