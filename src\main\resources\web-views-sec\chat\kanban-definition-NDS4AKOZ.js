import{_ as c,l as ne,d as z,aE as fe,bh as ye,bi as be,bj as me,bb as Ee,F as J,i as G,q as _e,J as ke,bc as Se,bd as le,be as ce}from"./index.js";var ee=function(){var e=c(function(p,s,t,a){for(t=t||{},a=p.length;a--;t[p[a]]=s);return t},"o"),d=[1,4],f=[1,13],r=[1,12],m=[1,15],k=[1,16],u=[1,20],y=[1,19],D=[6,7,8],g=[1,26],O=[1,24],L=[1,25],E=[6,7,11],I=[1,31],i=[6,7,11,24],V=[1,6,13,16,17,20,23],j=[1,35],M=[1,36],w=[1,6,7,11,13,16,17,20,23],U=[1,38],B={trace:c(function(){},"trace"),yy:{},symbols_:{error:2,start:3,mindMap:4,spaceLines:5,SPACELINE:6,NL:7,KANBAN:8,document:9,stop:10,EOF:11,statement:12,SPACELIST:13,node:14,shapeData:15,ICON:16,CLASS:17,nodeWithId:18,nodeWithoutId:19,NODE_DSTART:20,NODE_DESCR:21,NODE_DEND:22,NODE_ID:23,SHAPE_DATA:24,$accept:0,$end:1},terminals_:{2:"error",6:"SPACELINE",7:"NL",8:"KANBAN",11:"EOF",13:"SPACELIST",16:"ICON",17:"CLASS",20:"NODE_DSTART",21:"NODE_DESCR",22:"NODE_DEND",23:"NODE_ID",24:"SHAPE_DATA"},productions_:[0,[3,1],[3,2],[5,1],[5,2],[5,2],[4,2],[4,3],[10,1],[10,1],[10,1],[10,2],[10,2],[9,3],[9,2],[12,3],[12,2],[12,2],[12,2],[12,1],[12,2],[12,1],[12,1],[12,1],[12,1],[14,1],[14,1],[19,3],[18,1],[18,4],[15,2],[15,1]],performAction:c(function(s,t,a,l,h,n,T){var o=n.length-1;switch(h){case 6:case 7:return l;case 8:l.getLogger().trace("Stop NL ");break;case 9:l.getLogger().trace("Stop EOF ");break;case 11:l.getLogger().trace("Stop NL2 ");break;case 12:l.getLogger().trace("Stop EOF2 ");break;case 15:l.getLogger().info("Node: ",n[o-1].id),l.addNode(n[o-2].length,n[o-1].id,n[o-1].descr,n[o-1].type,n[o]);break;case 16:l.getLogger().info("Node: ",n[o].id),l.addNode(n[o-1].length,n[o].id,n[o].descr,n[o].type);break;case 17:l.getLogger().trace("Icon: ",n[o]),l.decorateNode({icon:n[o]});break;case 18:case 23:l.decorateNode({class:n[o]});break;case 19:l.getLogger().trace("SPACELIST");break;case 20:l.getLogger().trace("Node: ",n[o-1].id),l.addNode(0,n[o-1].id,n[o-1].descr,n[o-1].type,n[o]);break;case 21:l.getLogger().trace("Node: ",n[o].id),l.addNode(0,n[o].id,n[o].descr,n[o].type);break;case 22:l.decorateNode({icon:n[o]});break;case 27:l.getLogger().trace("node found ..",n[o-2]),this.$={id:n[o-1],descr:n[o-1],type:l.getType(n[o-2],n[o])};break;case 28:this.$={id:n[o],descr:n[o],type:0};break;case 29:l.getLogger().trace("node found ..",n[o-3]),this.$={id:n[o-3],descr:n[o-1],type:l.getType(n[o-2],n[o])};break;case 30:this.$=n[o-1]+n[o];break;case 31:this.$=n[o];break}},"anonymous"),table:[{3:1,4:2,5:3,6:[1,5],8:d},{1:[3]},{1:[2,1]},{4:6,6:[1,7],7:[1,8],8:d},{6:f,7:[1,10],9:9,12:11,13:r,14:14,16:m,17:k,18:17,19:18,20:u,23:y},e(D,[2,3]),{1:[2,2]},e(D,[2,4]),e(D,[2,5]),{1:[2,6],6:f,12:21,13:r,14:14,16:m,17:k,18:17,19:18,20:u,23:y},{6:f,9:22,12:11,13:r,14:14,16:m,17:k,18:17,19:18,20:u,23:y},{6:g,7:O,10:23,11:L},e(E,[2,24],{18:17,19:18,14:27,16:[1,28],17:[1,29],20:u,23:y}),e(E,[2,19]),e(E,[2,21],{15:30,24:I}),e(E,[2,22]),e(E,[2,23]),e(i,[2,25]),e(i,[2,26]),e(i,[2,28],{20:[1,32]}),{21:[1,33]},{6:g,7:O,10:34,11:L},{1:[2,7],6:f,12:21,13:r,14:14,16:m,17:k,18:17,19:18,20:u,23:y},e(V,[2,14],{7:j,11:M}),e(w,[2,8]),e(w,[2,9]),e(w,[2,10]),e(E,[2,16],{15:37,24:I}),e(E,[2,17]),e(E,[2,18]),e(E,[2,20],{24:U}),e(i,[2,31]),{21:[1,39]},{22:[1,40]},e(V,[2,13],{7:j,11:M}),e(w,[2,11]),e(w,[2,12]),e(E,[2,15],{24:U}),e(i,[2,30]),{22:[1,41]},e(i,[2,27]),e(i,[2,29])],defaultActions:{2:[2,1],6:[2,2]},parseError:c(function(s,t){if(t.recoverable)this.trace(s);else{var a=new Error(s);throw a.hash=t,a}},"parseError"),parse:c(function(s){var t=this,a=[0],l=[],h=[null],n=[],T=this.table,o="",H=0,W=0,ue=2,re=1,ge=n.slice.call(arguments,1),_=Object.create(this.lexer),R={yy:{}};for(var q in this.yy)Object.prototype.hasOwnProperty.call(this.yy,q)&&(R.yy[q]=this.yy[q]);_.setInput(s,R.yy),R.yy.lexer=_,R.yy.parser=this,typeof _.yylloc=="undefined"&&(_.yylloc={});var Q=_.yylloc;n.push(Q);var de=_.options&&_.options.ranges;typeof R.yy.parseError=="function"?this.parseError=R.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function pe(N){a.length=a.length-2*N,h.length=h.length-N,n.length=n.length-N}c(pe,"popStack");function ae(){var N;return N=l.pop()||_.lex()||re,typeof N!="number"&&(N instanceof Array&&(l=N,N=l.pop()),N=t.symbols_[N]||N),N}c(ae,"lex");for(var S,P,x,Z,F={},K,C,oe,Y;;){if(P=a[a.length-1],this.defaultActions[P]?x=this.defaultActions[P]:((S===null||typeof S=="undefined")&&(S=ae()),x=T[P]&&T[P][S]),typeof x=="undefined"||!x.length||!x[0]){var $="";Y=[];for(K in T[P])this.terminals_[K]&&K>ue&&Y.push("'"+this.terminals_[K]+"'");_.showPosition?$="Parse error on line "+(H+1)+`:
`+_.showPosition()+`
Expecting `+Y.join(", ")+", got '"+(this.terminals_[S]||S)+"'":$="Parse error on line "+(H+1)+": Unexpected "+(S==re?"end of input":"'"+(this.terminals_[S]||S)+"'"),this.parseError($,{text:_.match,token:this.terminals_[S]||S,line:_.yylineno,loc:Q,expected:Y})}if(x[0]instanceof Array&&x.length>1)throw new Error("Parse Error: multiple actions possible at state: "+P+", token: "+S);switch(x[0]){case 1:a.push(S),h.push(_.yytext),n.push(_.yylloc),a.push(x[1]),S=null,W=_.yyleng,o=_.yytext,H=_.yylineno,Q=_.yylloc;break;case 2:if(C=this.productions_[x[1]][1],F.$=h[h.length-C],F._$={first_line:n[n.length-(C||1)].first_line,last_line:n[n.length-1].last_line,first_column:n[n.length-(C||1)].first_column,last_column:n[n.length-1].last_column},de&&(F._$.range=[n[n.length-(C||1)].range[0],n[n.length-1].range[1]]),Z=this.performAction.apply(F,[o,W,H,R.yy,x[1],h,n].concat(ge)),typeof Z!="undefined")return Z;C&&(a=a.slice(0,-1*C*2),h=h.slice(0,-1*C),n=n.slice(0,-1*C)),a.push(this.productions_[x[1]][0]),h.push(F.$),n.push(F._$),oe=T[a[a.length-2]][a[a.length-1]],a.push(oe);break;case 3:return!0}}return!0},"parse")},X=function(){var p={EOF:1,parseError:c(function(t,a){if(this.yy.parser)this.yy.parser.parseError(t,a);else throw new Error(t)},"parseError"),setInput:c(function(s,t){return this.yy=t||this.yy||{},this._input=s,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:c(function(){var s=this._input[0];this.yytext+=s,this.yyleng++,this.offset++,this.match+=s,this.matched+=s;var t=s.match(/(?:\r\n?|\n).*/g);return t?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),s},"input"),unput:c(function(s){var t=s.length,a=s.split(/(?:\r\n?|\n)/g);this._input=s+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-t),this.offset-=t;var l=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),a.length-1&&(this.yylineno-=a.length-1);var h=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:a?(a.length===l.length?this.yylloc.first_column:0)+l[l.length-a.length].length-a[0].length:this.yylloc.first_column-t},this.options.ranges&&(this.yylloc.range=[h[0],h[0]+this.yyleng-t]),this.yyleng=this.yytext.length,this},"unput"),more:c(function(){return this._more=!0,this},"more"),reject:c(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:c(function(s){this.unput(this.match.slice(s))},"less"),pastInput:c(function(){var s=this.matched.substr(0,this.matched.length-this.match.length);return(s.length>20?"...":"")+s.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:c(function(){var s=this.match;return s.length<20&&(s+=this._input.substr(0,20-s.length)),(s.substr(0,20)+(s.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:c(function(){var s=this.pastInput(),t=new Array(s.length+1).join("-");return s+this.upcomingInput()+`
`+t+"^"},"showPosition"),test_match:c(function(s,t){var a,l,h;if(this.options.backtrack_lexer&&(h={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(h.yylloc.range=this.yylloc.range.slice(0))),l=s[0].match(/(?:\r\n?|\n).*/g),l&&(this.yylineno+=l.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:l?l[l.length-1].length-l[l.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+s[0].length},this.yytext+=s[0],this.match+=s[0],this.matches=s,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(s[0].length),this.matched+=s[0],a=this.performAction.call(this,this.yy,this,t,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),a)return a;if(this._backtrack){for(var n in h)this[n]=h[n];return!1}return!1},"test_match"),next:c(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var s,t,a,l;this._more||(this.yytext="",this.match="");for(var h=this._currentRules(),n=0;n<h.length;n++)if(a=this._input.match(this.rules[h[n]]),a&&(!t||a[0].length>t[0].length)){if(t=a,l=n,this.options.backtrack_lexer){if(s=this.test_match(a,h[n]),s!==!1)return s;if(this._backtrack){t=!1;continue}else return!1}else if(!this.options.flex)break}return t?(s=this.test_match(t,h[l]),s!==!1?s:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:c(function(){var t=this.next();return t||this.lex()},"lex"),begin:c(function(t){this.conditionStack.push(t)},"begin"),popState:c(function(){var t=this.conditionStack.length-1;return t>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:c(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:c(function(t){return t=this.conditionStack.length-1-Math.abs(t||0),t>=0?this.conditionStack[t]:"INITIAL"},"topState"),pushState:c(function(t){this.begin(t)},"pushState"),stateStackSize:c(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:c(function(t,a,l,h){switch(l){case 0:return this.pushState("shapeData"),a.yytext="",24;case 1:return this.pushState("shapeDataStr"),24;case 2:return this.popState(),24;case 3:const n=/\n\s*/g;return a.yytext=a.yytext.replace(n,"<br/>"),24;case 4:return 24;case 5:this.popState();break;case 6:return t.getLogger().trace("Found comment",a.yytext),6;case 7:return 8;case 8:this.begin("CLASS");break;case 9:return this.popState(),17;case 10:this.popState();break;case 11:t.getLogger().trace("Begin icon"),this.begin("ICON");break;case 12:return t.getLogger().trace("SPACELINE"),6;case 13:return 7;case 14:return 16;case 15:t.getLogger().trace("end icon"),this.popState();break;case 16:return t.getLogger().trace("Exploding node"),this.begin("NODE"),20;case 17:return t.getLogger().trace("Cloud"),this.begin("NODE"),20;case 18:return t.getLogger().trace("Explosion Bang"),this.begin("NODE"),20;case 19:return t.getLogger().trace("Cloud Bang"),this.begin("NODE"),20;case 20:return this.begin("NODE"),20;case 21:return this.begin("NODE"),20;case 22:return this.begin("NODE"),20;case 23:return this.begin("NODE"),20;case 24:return 13;case 25:return 23;case 26:return 11;case 27:this.begin("NSTR2");break;case 28:return"NODE_DESCR";case 29:this.popState();break;case 30:t.getLogger().trace("Starting NSTR"),this.begin("NSTR");break;case 31:return t.getLogger().trace("description:",a.yytext),"NODE_DESCR";case 32:this.popState();break;case 33:return this.popState(),t.getLogger().trace("node end ))"),"NODE_DEND";case 34:return this.popState(),t.getLogger().trace("node end )"),"NODE_DEND";case 35:return this.popState(),t.getLogger().trace("node end ...",a.yytext),"NODE_DEND";case 36:return this.popState(),t.getLogger().trace("node end (("),"NODE_DEND";case 37:return this.popState(),t.getLogger().trace("node end (-"),"NODE_DEND";case 38:return this.popState(),t.getLogger().trace("node end (-"),"NODE_DEND";case 39:return this.popState(),t.getLogger().trace("node end (("),"NODE_DEND";case 40:return this.popState(),t.getLogger().trace("node end (("),"NODE_DEND";case 41:return t.getLogger().trace("Long description:",a.yytext),21;case 42:return t.getLogger().trace("Long description:",a.yytext),21}},"anonymous"),rules:[/^(?:@\{)/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^\"]+)/i,/^(?:[^}^"]+)/i,/^(?:\})/i,/^(?:\s*%%.*)/i,/^(?:kanban\b)/i,/^(?::::)/i,/^(?:.+)/i,/^(?:\n)/i,/^(?:::icon\()/i,/^(?:[\s]+[\n])/i,/^(?:[\n]+)/i,/^(?:[^\)]+)/i,/^(?:\))/i,/^(?:-\))/i,/^(?:\(-)/i,/^(?:\)\))/i,/^(?:\))/i,/^(?:\(\()/i,/^(?:\{\{)/i,/^(?:\()/i,/^(?:\[)/i,/^(?:[\s]+)/i,/^(?:[^\(\[\n\)\{\}@]+)/i,/^(?:$)/i,/^(?:["][`])/i,/^(?:[^`"]+)/i,/^(?:[`]["])/i,/^(?:["])/i,/^(?:[^"]+)/i,/^(?:["])/i,/^(?:[\)]\))/i,/^(?:[\)])/i,/^(?:[\]])/i,/^(?:\}\})/i,/^(?:\(-)/i,/^(?:-\))/i,/^(?:\(\()/i,/^(?:\()/i,/^(?:[^\)\]\(\}]+)/i,/^(?:.+(?!\(\())/i],conditions:{shapeDataEndBracket:{rules:[],inclusive:!1},shapeDataStr:{rules:[2,3],inclusive:!1},shapeData:{rules:[1,4,5],inclusive:!1},CLASS:{rules:[9,10],inclusive:!1},ICON:{rules:[14,15],inclusive:!1},NSTR2:{rules:[28,29],inclusive:!1},NSTR:{rules:[31,32],inclusive:!1},NODE:{rules:[27,30,33,34,35,36,37,38,39,40,41,42],inclusive:!1},INITIAL:{rules:[0,6,7,8,11,12,13,16,17,18,19,20,21,22,23,24,25,26],inclusive:!0}}};return p}();B.lexer=X;function A(){this.yy={}}return c(A,"Parser"),A.prototype=B,B.Parser=A,new A}();ee.parser=ee;var Ne=ee,v=[],se=[],te=0,ie={},xe=c(()=>{v=[],se=[],te=0,ie={}},"clear"),ve=c(e=>{if(v.length===0)return null;const d=v[0].level;let f=null;for(let r=v.length-1;r>=0;r--)if(v[r].level===d&&!f&&(f=v[r]),v[r].level<d)throw new Error('Items without section detected, found section ("'+v[r].label+'")');return e===(f==null?void 0:f.level)?null:f},"getSection"),he=c(function(){return se},"getSections"),De=c(function(){var m,k;const e=[],d=[],f=he(),r=z();for(const u of f){const y={id:u.id,label:G((m=u.label)!=null?m:"",r),isGroup:!0,ticket:u.ticket,shape:"kanbanSection",level:u.level,look:r.look};d.push(y);const D=v.filter(g=>g.parentId===u.id);for(const g of D){const O={id:g.id,parentId:u.id,label:G((k=g.label)!=null?k:"",r),isGroup:!1,ticket:g==null?void 0:g.ticket,priority:g==null?void 0:g.priority,assigned:g==null?void 0:g.assigned,icon:g==null?void 0:g.icon,shape:"kanbanItem",level:g.level,rx:5,ry:5,cssStyles:["text-align: left"]};d.push(O)}}return{nodes:d,edges:e,other:{},config:z()}},"getData"),Le=c((e,d,f,r,m)=>{var g,O,L,E;const k=z();let u=(O=(g=k.mindmap)==null?void 0:g.padding)!=null?O:J.mindmap.padding;switch(r){case b.ROUNDED_RECT:case b.RECT:case b.HEXAGON:u*=2}const y={id:G(d,k)||"kbn"+te++,level:e,label:G(f,k),width:(E=(L=k.mindmap)==null?void 0:L.maxNodeWidth)!=null?E:J.mindmap.maxNodeWidth,padding:u,isGroup:!1};if(m!==void 0){let I;m.includes(`
`)?I=m+`
`:I=`{
`+m+`
}`;const i=_e(I,{schema:ke});if(i.shape&&(i.shape!==i.shape.toLowerCase()||i.shape.includes("_")))throw new Error(`No such shape: ${i.shape}. Shape names should be lowercase.`);(i==null?void 0:i.shape)&&i.shape==="kanbanItem"&&(y.shape=i==null?void 0:i.shape),i!=null&&i.label&&(y.label=i==null?void 0:i.label),i!=null&&i.icon&&(y.icon=i==null?void 0:i.icon.toString()),i!=null&&i.assigned&&(y.assigned=i==null?void 0:i.assigned.toString()),i!=null&&i.ticket&&(y.ticket=i==null?void 0:i.ticket.toString()),i!=null&&i.priority&&(y.priority=i==null?void 0:i.priority)}const D=ve(e);D?y.parentId=D.id||"kbn"+te++:se.push(y),v.push(y)},"addNode"),b={DEFAULT:0,NO_BORDER:0,ROUNDED_RECT:1,RECT:2,CIRCLE:3,CLOUD:4,BANG:5,HEXAGON:6},Oe=c((e,d)=>{switch(ne.debug("In get type",e,d),e){case"[":return b.RECT;case"(":return d===")"?b.ROUNDED_RECT:b.CLOUD;case"((":return b.CIRCLE;case")":return b.CLOUD;case"))":return b.BANG;case"{{":return b.HEXAGON;default:return b.DEFAULT}},"getType"),Ie=c((e,d)=>{ie[e]=d},"setElementForId"),Ce=c(e=>{if(!e)return;const d=z(),f=v[v.length-1];e.icon&&(f.icon=G(e.icon,d)),e.class&&(f.cssClasses=G(e.class,d))},"decorateNode"),we=c(e=>{switch(e){case b.DEFAULT:return"no-border";case b.RECT:return"rect";case b.ROUNDED_RECT:return"rounded-rect";case b.CIRCLE:return"circle";case b.CLOUD:return"cloud";case b.BANG:return"bang";case b.HEXAGON:return"hexgon";default:return"no-border"}},"type2Str"),Ae=c(()=>ne,"getLogger"),Te=c(e=>ie[e],"getElementById"),Re={clear:xe,addNode:Le,getSections:he,getData:De,nodeType:b,getType:Oe,setElementForId:Ie,decorateNode:Ce,type2Str:we,getLogger:Ae,getElementById:Te},Pe=Re,Ve=c(async(e,d,f,r)=>{var j,M,w,U,B,X,A;ne.debug(`Rendering kanban diagram
`+e);const k=r.db.getData(),u=z();u.htmlLabels=!1;const y=fe(d),D=y.append("g");D.attr("class","sections");const g=y.append("g");g.attr("class","items");const O=k.nodes.filter(p=>p.isGroup);let L=0;const E=10,I=[];let i=25;for(const p of O){const s=((j=u==null?void 0:u.kanban)==null?void 0:j.sectionWidth)||200;L=L+1,p.x=s*L+(L-1)*E/2,p.width=s,p.y=0,p.height=s*3,p.rx=5,p.ry=5,p.cssClasses=p.cssClasses+" section-"+L;const t=await ye(D,p);i=Math.max(i,(M=t==null?void 0:t.labelBBox)==null?void 0:M.height),I.push(t)}let V=0;for(const p of O){const s=I[V];V=V+1;const t=((w=u==null?void 0:u.kanban)==null?void 0:w.sectionWidth)||200,a=-t*3/2+i;let l=a;const h=k.nodes.filter(o=>o.parentId===p.id);for(const o of h){if(o.isGroup)throw new Error("Groups within groups are not allowed in Kanban diagrams");o.x=p.x,o.width=t-1.5*E;const W=(await be(g,o,{config:u})).node().getBBox();o.y=l+W.height/2,await me(o),l=o.y+W.height/2+E/2}const n=s.cluster.select("rect"),T=Math.max(l-a+3*E,50)+(i-25);n.attr("height",T)}Ee(void 0,y,(B=(U=u.mindmap)==null?void 0:U.padding)!=null?B:J.kanban.padding,(A=(X=u.mindmap)==null?void 0:X.useMaxWidth)!=null?A:J.kanban.useMaxWidth)},"draw"),Be={draw:Ve},Fe=c(e=>{let d="";for(let r=0;r<e.THEME_COLOR_LIMIT;r++)e["lineColor"+r]=e["lineColor"+r]||e["cScaleInv"+r],Se(e["lineColor"+r])?e["lineColor"+r]=le(e["lineColor"+r],20):e["lineColor"+r]=ce(e["lineColor"+r],20);const f=c((r,m)=>e.darkMode?ce(r,m):le(r,m),"adjuster");for(let r=0;r<e.THEME_COLOR_LIMIT;r++){const m=""+(17-3*r);d+=`
    .section-${r-1} rect, .section-${r-1} path, .section-${r-1} circle, .section-${r-1} polygon, .section-${r-1} path  {
      fill: ${f(e["cScale"+r],10)};
      stroke: ${f(e["cScale"+r],10)};

    }
    .section-${r-1} text {
     fill: ${e["cScaleLabel"+r]};
    }
    .node-icon-${r-1} {
      font-size: 40px;
      color: ${e["cScaleLabel"+r]};
    }
    .section-edge-${r-1}{
      stroke: ${e["cScale"+r]};
    }
    .edge-depth-${r-1}{
      stroke-width: ${m};
    }
    .section-${r-1} line {
      stroke: ${e["cScaleInv"+r]} ;
      stroke-width: 3;
    }

    .disabled, .disabled circle, .disabled text {
      fill: lightgray;
    }
    .disabled text {
      fill: #efefef;
    }

  .node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${e.background};
    stroke: ${e.nodeBorder};
    stroke-width: 1px;
  }

  .kanban-ticket-link {
    fill: ${e.background};
    stroke: ${e.nodeBorder};
    text-decoration: underline;
  }
    `}return d},"genSections"),Ge=c(e=>`
  .edge {
    stroke-width: 3;
  }
  ${Fe(e)}
  .section-root rect, .section-root path, .section-root circle, .section-root polygon  {
    fill: ${e.git0};
  }
  .section-root text {
    fill: ${e.gitBranchLabel0};
  }
  .icon-container {
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .edge {
    fill: none;
  }
  .cluster-label, .label {
    color: ${e.textColor};
    fill: ${e.textColor};
    }
  .kanban-label {
    dy: 1em;
    alignment-baseline: middle;
    text-anchor: middle;
    dominant-baseline: middle;
    text-align: center;
  }
`,"getStyles"),je=Ge,Ue={db:Pe,renderer:Be,parser:Ne,styles:je};export{Ue as diagram};
