package com.srdcloud.ideplugin.general.utils;

import com.intellij.openapi.vfs.VirtualFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * <AUTHOR>
 * @date 2025/1/9
 */
public class GoUtil {
    /**
     * 获取当前操作系统的根目录
     *
     * @return 操作系统的根目录
     */
    public static String getRootDirectory() {
        // 获取当前操作系统的根目录
        String osName = System.getProperty("os.name").toLowerCase();
        if (osName.contains("windows")) {
            // Windows 系统的根目录通常是 C:\
            return "C:.";
        } else {
            // 其他系统（如 Linux、macOS）的根目录通常是 /
            return "/";
        }
    }

    /**
     * 从 VirtualFile 中提取 module 名
     *
     * @param file VirtualFile 对象
     * @return module 名
     * @throws IOException 如果读取文件时发生错误
     */
    public static String extractModuleName(VirtualFile file) throws IOException {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.startsWith("module")) {
                    // 去掉 "module" 关键字和前后多余的空格
                    return line.replaceFirst("module", "").trim();
                }
            }
        }
        throw new IOException("Module name not found in go.mod file");
    }
}
