package com.srdcloud.ideplugin.webview.codechat.actions;

import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.assistant.AssistantToolWindow;
import com.srdcloud.ideplugin.general.enums.ChatMessageType;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2025/1/8
 */
public class FixExceptionAction extends AnAction {
    private static final Logger logger = LoggerFactory.getLogger(FixExceptionAction.class);

    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {
    }

    public void doActionPerformed(Project project, String selectCode, String errDesc) {
        if (StringUtils.isBlank(selectCode)) {
            logger.warn("[cf] FixExceptionAction skip,Exception info is null.");
            return;
        }

        // 展开编程助手对话窗
        AssistantToolWindow.toolWindowVisible(project);

        CodeChatWebview.getInstance(project).getActionHandler().fixExceptionAction(selectCode, ChatMessageType.FIX_EXCEPTION.getType(), errDesc);
    }
}
