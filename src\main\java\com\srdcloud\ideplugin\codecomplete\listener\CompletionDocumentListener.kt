package com.srdcloud.ideplugin.codecomplete.listener

import com.intellij.codeInsight.completion.CompletionUtil
import com.intellij.ide.util.PropertiesComponent
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.editor.event.DocumentEvent
import com.intellij.openapi.editor.event.DocumentListener
import com.intellij.openapi.editor.impl.EditorImpl
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.util.TextRange
import com.srdcloud.ideplugin.codecomplete.handle.CompletionContext.Companion.resetInlineCompletionContext
import com.srdcloud.ideplugin.codecomplete.handle.CompletionHandler
import com.srdcloud.ideplugin.codecomplete.domain.CompletionState.Companion.getInlineCompletionState
import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.DefaultCodeProvider
import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.ICompletionProvider
import com.srdcloud.ideplugin.general.constants.Constants
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.debounce
import java.util.regex.Pattern

/**
 * <AUTHOR>
 * @date 2025/6/6
 * @desc 编辑器内document内容变动监听器，用于监听编辑器的文档变化事件，触发自动补全
 */
private const val PROPERTIES_KEY = "srdcloud.inline.completion.enabled"

@OptIn(FlowPreview::class)
class CompletionDocumentListener(private val editor: EditorImpl, private val scope: CoroutineScope) :
    DocumentListener, Disposable {

    // 协程任务
    private var jobCall: Job? = null
    private var flow =
        MutableSharedFlow<CompletionEvent>(replay = 1, onBufferOverflow = BufferOverflow.DROP_OLDEST)

    private val END_OF_LINE_VALID_PATTERN = Pattern.compile("^\\s*[)}\\]\"'`]*\\s*[:{;,]?\\s*$")
    private val logger = logger<CompletionDocumentListener>()

    // 自定义事件对象，包括事件本身与其关联的内联补全提供程序列表
    private data class CompletionEvent(val event: DocumentEvent, val providers: List<ICompletionProvider>)

    // 补全处理器
    private val completionHandler = CompletionHandler(scope)

    // 自动补全的启用/禁用状态
    companion object {
        //        private val KEY = Key.create<InlineCompletionDocumentListener>("inline.completion.listener")
        var PropertiesComponent.completionEnabled: Boolean
            get() = getBoolean(PROPERTIES_KEY, true)
            set(value) = setValue(PROPERTIES_KEY, value, !value)
    }


    /**
     * 监听编辑器文档变化
     */
    @OptIn(InternalCoroutinesApi::class)
    fun listenForChanges() {
        Disposer.register(editor.disposable, this)
        editor.document.addDocumentListener(this, this)

        // 补全任务协程启动
        jobCall = scope.launch(CoroutineName("inline.completion.call")) {
            // 事件降噪：Constants.Code_completion_debounce ms内没有新的事件触发，才执行documentChangedDebounced逻辑
            flow.debounce(Constants.Code_completion_debounce)
                .collect(::documentChangedDebounced)
        }
    }

    /**
     * 当前编辑器内文档对象被修改（输入、换行等等行为）
     */
    override fun documentChanged(event: DocumentEvent) {
        // 判断不满足自动补全条件时，重置内联完成上下文并返回
        if (CompletionHandler.isMuted.get() || !isEnabled(event)) {
            editor.resetInlineCompletionContext()
            return
        }

        // 1、获取当前编辑器的内联补全状态
        val state = editor.getInlineCompletionState()
        // 如果用户输入与提示内容一致，则直接显示当前提示，无需重新获取
        if (state != null && state.checkUserInput(event.newFragment)) {
            completionHandler.showInlineSuggestion(editor, state, event.offset + event.newLength, false, 0, false)
            return
        }
        ApplicationManager.getApplication().invokeLater {
            editor.resetInlineCompletionContext()
        }

        // 用DefaultCodeProvider创建一个自动补全任务事件
        val completionEvent = CompletionEvent(event, listOf(DefaultCodeProvider.instance))

        // 如果当前处于单元测试模式，则调用 documentChangedDebounced 方法直接处理事件
        if (ApplicationManager.getApplication().isUnitTestMode) {
            documentChangedDebounced(completionEvent)
        } else {
            // 否则，尝试将 inlineCompletionEvent 发送到 flow（一个协程流），进行后续处理
            flow.tryEmit(completionEvent)
        }
    }

    /**
     * 自动补全条件判断
     */
    private fun isEnabled(event: DocumentEvent): Boolean {
        val document = event.document
        val offset = event.offset + event.newLength
        val lineIndex = document.getLineNumber(offset)
        val suffix = document.getText(
            TextRange.create(offset, document.getLineEndOffset(lineIndex))
        )
        val matches = END_OF_LINE_VALID_PATTERN.matcher(suffix).matches()
        return event.newFragment != CompletionUtil.DUMMY_IDENTIFIER && event.newLength >= 1 &&
                matches && PropertiesComponent.getInstance().completionEnabled
    }

    /**
     * 文档变化事件降噪处理
     */
    private fun documentChangedDebounced(completionEvent: CompletionEvent) {
        val (event, providers) = completionEvent

        // 文档批量更新行为不会触发自动补全
        if (event.document.isInBulkUpdate) {
            logger.warn("[cf] Skipping inline completion due to bulk update.")
            return
        }

        // 入口：补全入口策略触发(在此处进行策略分配)
        completionHandler.invoke(event, editor, providers.first())
        // handler.invoke(event, editor, providers.last())
    }


    override fun dispose() {
//        editor.putUserData(KEY, null)
        jobCall?.cancel()
    }

}