package com.srdcloud.ideplugin.webview.codesecurity.request;

import com.srdcloud.ideplugin.webview.base.domain.WebViewReqType;

/**
 * WebView安全扫描请求数据
 */
public class RunScanRequestData extends WebViewReqType {

    // 安全扫描文件大小
    private int fileSize;
    // 安全扫描引擎类型
    private String scannerEngine;
    // 安全扫描文件列表
    private String[] scanFiles;

    public RunScanRequestData(String reqType, int fileSize, String scannerEngine, String[] scanFiles) {
        this.reqType = reqType;
        this.fileSize = fileSize;
        this.scannerEngine = scannerEngine;
        this.scanFiles = scanFiles;
    }

    public void setFileSize(int fileSize) {
        this.fileSize = fileSize;
    }

    public int getFileSize() {
        return fileSize;
    }

    public void setScannerEngine(String scannerEngine) {
        this.scannerEngine = scannerEngine;
    }

    public String getScannerEngine() {
        return scannerEngine;
    }

    public void setScanFiles(String[] scanFiles) {
        this.scanFiles = scanFiles;
    }

    public String[] getScanFiles() {
        return scanFiles;
    }

}
