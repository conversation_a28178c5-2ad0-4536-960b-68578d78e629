package com.srdcloud.ideplugin.service;

import com.intellij.ide.AppLifecycleListener;
import com.srdcloud.ideplugin.general.utils.EnvUtil;
import com.srdcloud.ideplugin.general.utils.LocalStorageUtil;
import com.srdcloud.ideplugin.general.utils.ThreadPoolUtil;
import com.srdcloud.ideplugin.login.LoginUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * <AUTHOR>
 * IDE生命周期挂载点
 */
public class IDELifecycleService implements AppLifecycleListener{
    private static final Logger logger = LoggerFactory.getLogger(IDELifecycleService.class);

    /**
     * IDE启动hook挂载
     */
    @Override
    public void appFrameCreated(@NotNull List<String> commandLineArgs) {
        AppLifecycleListener.super.appFrameCreated(commandLineArgs);
        logger.info("[cf] ideStarted...");
        // 初始化AI对话通信通道
        initAiChannel();

        // 其他需要启动时执行的逻辑预埋...

    }

    /**
     * 初始化AI对话通信通道
     */
    private void initAiChannel() {
        logger.info("[cf] initAiChannel...");
        if (EnvUtil.isSec()) {
            LoginUtils.INSTANCE.initLoginStatus();
        }
        try {
            if (StringUtils.isNotEmpty(LocalStorageUtil.getSessionId())) {
                // 异步执行，不要阻塞插件启动
                ThreadPoolUtil.submit(() -> LoginService.sendAiRegisterChannel());
            } else {
                logger.info("[cf] StartupService initAiChannel sendAiRegisterChannel skip,sessionId is null cause no login.");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
