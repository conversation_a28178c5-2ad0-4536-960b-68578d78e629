package com.srdcloud.ideplugin.webview.base.domain;

import com.srdcloud.ideplugin.webview.codechat.common.ChatTips;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCode;

public class ErrorResponse extends WebViewCommand {

    private ErrorResponseCode data;

    public ErrorResponse(String command, ErrorResponseCode data) {
        this.command = command;
        this.data = data;
    }

    public void setData(ErrorResponseCode data) {
        this.data = data;
    }

    public ErrorResponseCode getData() {
        return data;
    }


    /**
     * 获取未登录回传消息
     * @return
     */
    public static ErrorResponse getNoLoginResponse(String command){
        ErrorResponseCode errorResponseCode = new ErrorResponseCode(WebViewRspCode.NOT_LOGIN, ChatTips.NOT_LOGIN);
        return new ErrorResponse(command, errorResponseCode);
    }

}
