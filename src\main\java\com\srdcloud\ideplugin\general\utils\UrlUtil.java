package com.srdcloud.ideplugin.general.utils;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


public final class UrlUtil {
    // 定义合法文件类型的正则表达式
    private static final String REGEX_VALID_FILE_TYPE = "^[a-zA-Z0-9]{1,20}$";
    private static final Pattern FILE_TYPE_PATTERN = Pattern.compile(REGEX_VALID_FILE_TYPE);

    public UrlUtil() {
    }

    /**
     * 校验文件类型名是否合法
     *
     * @param fileType 文件类型名
     * @return 如果文件类型名合法返回true，否则返回false
     */
    public static boolean isValidFileType(String fileType) {
        // 创建Matcher对象
        Matcher matcher = FILE_TYPE_PATTERN.matcher(fileType);

        // 使用Matcher对象进行匹配
        return matcher.matches();
    }

    /**
     * 根据下载链接，获取文件类型
     */
    public static String getUrlFileType(String fileUrl) {
        int dotIndex = fileUrl.lastIndexOf('.');
        String fileType = "";
        if (dotIndex != -1) {
            fileType = fileUrl.substring(dotIndex + 1); // 不包含点号的文件扩展名
        }
        if (isValidFileType(fileType)) {
            return fileType;
        } else {
            return ""; // 如果没有找到点号，返回空字符串
        }
    }

    /**
     * 根据条件拼装url
     */
    public static String buildUrlWithParams(String baseUrl, Map<String, String> params) {
        StringBuilder urlBuilder = new StringBuilder(baseUrl);
        if (params != null && !params.isEmpty()) {
            urlBuilder.append('?');
            for (Map.Entry<String, String> entry : params.entrySet()) {
                String name = entry.getKey();
                String value = URLEncoder.encode(entry.getValue(), StandardCharsets.UTF_8);
                urlBuilder.append(name).append('=').append(value).append('&');
            }
            urlBuilder.setLength(urlBuilder.length() - 1); // 移除最后一个'&'
        }
        return urlBuilder.toString();
    }
}