package com.srdcloud.ideplugin.service.domain.apigw;

import java.io.Serializable;

/**
 * <AUTHOR> yangy
 * @create 2023/7/23 20:21
 */
public class ApigwWebsocketRespMessage implements Serializable {

    /**
     * 消息体名称
     */
    private String messageName;
    /**
     * context
     */
    private ApigwWebsocketRespContext context;
    /**
     * payload
     */
    private ApigwWebsocketRespPayload payload;

    public String getMessageName() {
        return messageName;
    }

    public void setMessageName(String messageName) {
        this.messageName = messageName;
    }

    public ApigwWebsocketRespContext getContext() {
        return context;
    }

    public void setContext(ApigwWebsocketRespContext context) {
        this.context = context;
    }

    public ApigwWebsocketRespPayload getPayload() {
        return payload;
    }

    public void setPayload(ApigwWebsocketRespPayload payload) {
        this.payload = payload;
    }
}
