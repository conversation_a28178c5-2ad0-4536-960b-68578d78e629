package com.srdcloud.ideplugin.general.utils;

import com.intellij.ide.util.PropertiesComponent;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.constants.IgnoreRules;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2025/6/11
 * @desc 扫描忽略规则
 */
public class IgnoreUtils {

    public static String[] getIgnoreRule() {
        // 优先取后端下发
        String ruleStr = PropertiesComponent.getInstance().getValue(Constants.IgnoreList, "");
        if (StringUtils.isNotBlank(ruleStr)) {
            return JsonUtil.getInstance().fromJson(ruleStr, String[].class);
        }

        // 其次取本地兜底
        return IgnoreRules.DEFAULT_IGNORE_LIST;
    }

}
