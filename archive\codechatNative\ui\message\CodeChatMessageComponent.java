package com.srdcloud.ideplugin.assistant.codechatNative.ui.message;


import com.intellij.icons.AllIcons;
import com.intellij.openapi.project.Project;
import com.intellij.ui.components.JBLabel;
import com.intellij.ui.components.JBPanel;
import com.intellij.ui.components.panels.VerticalLayout;
import com.intellij.util.ui.JBUI;
import com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions.SendAction;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.CodeChatCompleteEngin;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.ConversationMessage;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.ConversationMessageQuoteGuide;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.ConversationMessageQuoteInterface;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.ConversationMessageQuoteLink;
import com.srdcloud.ideplugin.assistant.codechatNative.ui.CodeChatMainPanel;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.enums.ChatMessageType;
import com.srdcloud.ideplugin.general.enums.ConversationMessageType;
import com.srdcloud.ideplugin.general.enums.MessageNodeType;
import com.srdcloud.ideplugin.general.icons.GPTIcons;
import com.srdcloud.ideplugin.general.utils.ThreadPoolUtil;
import com.srdcloud.ideplugin.general.utils.TooltipUtil;
import com.srdcloud.ideplugin.general.utils.UIUtil;
import com.srdcloud.ideplugin.remote.FeedbackAnswerCommHandler;
import com.srdcloud.ideplugin.remote.domain.FeedbackAnswerResponse;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.CodeChatQuote;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.QuoteItem;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;
import java.awt.datatransfer.Clipboard;
import java.awt.datatransfer.StringSelection;
import java.awt.datatransfer.Transferable;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;

/**
 * <AUTHOR> yangy
 * @create 2023/6/15 14:43
 * 会话消息UI组件：一条消息一个MessageComponent
 */
public class CodeChatMessageComponent extends JBPanel<CodeChatMessageComponent> {

    private static final Logger logger = LoggerFactory.getLogger(CodeChatMessageComponent.class);

    // 左侧：头像区域
    private final JPanel leftPanel;

    // 中间：昵称+消息体
    private final JPanel centerPanel;

    // 右侧：边框
    private final JPanel rightPanel;

    //--消息内容
    public final CodeChatMessageContentPanel codeChatMessageContentPanel = new CodeChatMessageContentPanel();

    // 操作区域
    public JPanel actionPanel;

    // 操作区域左侧
    public JPanel actionLeftPanel;

    // 操作区域右侧
    public JPanel actionRightPanel;

    // 消息数据内容
    // -- 问题
    private final String question;

    // -- 答案
    private String answer;

    // -- 对话Id
    private String dialogId;

    // -- 回答Id
    private String reqId;

    // -- 回答反馈
    private final String feedback;

    // -- 已经点击点赞或点踩
    private boolean clickedAnswer = false;

    private Project project;

    private boolean isMe;

    private CodeChatMainPanel codeChatMainPanel;

    private ConversationMessage message;

    /**
     * 构造消息UI组件
     */
    public CodeChatMessageComponent(CodeChatMainPanel codeChatMainPanel, ConversationMessage message) {
        this.codeChatMainPanel = codeChatMainPanel;
        this.message = message;

        if (message.getMessageType().equals(ConversationMessageType.USER)) {
            isMe = true;
        } else {
            isMe = false;
        }

        // 设置样式
        boolean isDark = UIUtil.judgeBackgroudDarkTheme();
        setDoubleBuffered(true);
        setOpaque(true);
        setBorder(JBUI.Borders.empty(10, 15, 0, 0));
        setLayout(new BorderLayout(JBUI.scale(7), 0));
        setAutoscrolls(true);
        if (!isDark) {
            setBackground(isMe ? Color.decode("#FFFFFF") : Color.decode("#F5F7F9"));
        } else {
            setBackground(isMe ? Color.decode("#24282C") : Color.decode("#2E3338"));
        }

        project = codeChatMainPanel.getProject();
        question = message.getContent();

        //记录初始点赞点踩状态
        feedback = message.getFeedback();

        // 左侧：头像
        leftPanel = new JPanel(new BorderLayout());
        leftPanel.setOpaque(false);
        // 不同对话角色，展示不同头像
        Icon imageIcon = isMe ? GPTIcons.ME : GPTIcons.AI;
        JBLabel imageJBLabel = new JBLabel(imageIcon);
        imageJBLabel.setOpaque(false);
        leftPanel.add(imageJBLabel, BorderLayout.NORTH);
        leftPanel.setOpaque(false);
        add(leftPanel, BorderLayout.LINE_START);

        // 右侧：空边框
        rightPanel = new JPanel(new BorderLayout());
        rightPanel.setOpaque(false);
        rightPanel.setBorder(JBUI.Borders.empty(0, 0, 0, 20));
        add(rightPanel, BorderLayout.EAST);

        // 中间：昵称+消息内容
        centerPanel = new JPanel(new VerticalLayout(JBUI.scale(8)));
        centerPanel.setOpaque(false);
        centerPanel.setBorder(JBUI.Borders.emptyBottom(5));
        centerPanel.setAutoscrolls(true);
        add(centerPanel, BorderLayout.CENTER);

        // 昵称渲染
        JPanel headerPanel = new JPanel();
        headerPanel.setLayout(new FlowLayout(FlowLayout.LEFT));
        headerPanel.setOpaque(false);
        headerPanel.setBorder(JBUI.Borders.empty(0, 0, 0, 0));
        JBLabel titleLabel = new JBLabel(isMe ? "ME" : "研发云CodeFree");
        Font font = titleLabel.getFont();
        Font boldFont = font.deriveFont(Font.BOLD);
        titleLabel.setFont(boldFont);
        headerPanel.add(titleLabel);
        if (isMe) { // 如果是用户消息，增加时间戳展示
            DateTimeFormatter fmt = DateTimeFormatter.ofPattern("HH:mm:ss");
            LocalDateTime now = LocalDateTime.now();
            String time = now.format(fmt);
            if (message.getDateTime() != null) {
                time = message.getDateTime();
            }
            JBLabel timeLabel = new JBLabel(time);
            timeLabel.setBorder(JBUI.Borders.empty(0, 5, 0, 0));
            headerPanel.add(timeLabel);
        }
        centerPanel.add(headerPanel);

        // 消息内容渲染
        centerPanel.add(createMessagePanel(message, message.getChatMessageType(), isMe));

        // 底部:操作按钮区域渲染,包括问候语、历史消息、新建提问和回答
        if (message.getNodeType() != MessageNodeType.INVALID_ERROR) {
            displayQuote();
            SwingUtilities.invokeLater(this::displayActionPanel);
        }
    }

    //============逻辑函数==============

    /**
     * 设置消息内容
     */
    public void setMessageContent(String content, ChatMessageType chatMessageType, boolean me) {
        SwingUtilities.invokeLater(() -> {
            this.codeChatMessageContentPanel.paintMessage(content, chatMessageType, me);
        });
    }

    // 渲染消息内容，并返回UI元素
    private Component createMessagePanel(ConversationMessage message, ChatMessageType chatMessageType, boolean me) {
        SwingUtilities.invokeLater(() -> {
            this.codeChatMessageContentPanel.paintMessage(message, chatMessageType, me);
        });
        return this.codeChatMessageContentPanel;
    }

    /**
     * 更新点赞、点踩、复制等操作区
     * me: true,自己的提问; false,AI回答
     * invalidContent: false,有效的问题和答案; true,可能是系统提示信息或者错误信息
     */
    public void displayActionPanel() {
        boolean isDark = UIUtil.judgeBackgroudDarkTheme();

        if (actionPanel != null) {
            remove(actionPanel);
        }
        actionPanel = new JPanel(new BorderLayout());
        actionPanel.setOpaque(false);

        actionRightPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 10, 0));
        actionRightPanel.setOpaque(false);
        actionRightPanel.setBorder(JBUI.Borders.empty(0, 0, 10, 20));

        actionLeftPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 10, 0));
        actionLeftPanel.setOpaque(false);
        actionLeftPanel.setBorder(JBUI.Borders.empty(0, 0, 10, 20));

        ConversationMessage parent = message.getParent();

        //1、点赞点踩按钮：不是用户提问、是有效的内容、点赞字段不为空
        if (message.getNodeType() == MessageNodeType.ANSWER) {
            if (feedback.equals("like")) {
                Icon likeIcon = GPTIcons.LIKE_CLICKED;
                JLabel likeAction = new JLabel(likeIcon);
                actionRightPanel.add(likeAction);
                likeAction.setCursor(new Cursor(Cursor.HAND_CURSOR));
                likeAction.setToolTipText("已点赞！");

                Icon dislikeIcon = null;
                if (isDark) {
                    dislikeIcon = GPTIcons.DISLIKE_DARK;
                } else {
                    dislikeIcon = GPTIcons.DISLIKE;
                }
                JLabel dislikeAction = new JLabel(dislikeIcon);
                actionRightPanel.add(dislikeAction);
                dislikeAction.setCursor(new Cursor(Cursor.HAND_CURSOR));
                dislikeAction.setToolTipText("已点赞！");
            } else if (feedback.equals("unlike")) {
                Icon likeIcon = null;
                if (isDark) {
                    likeIcon = GPTIcons.LIKE_DARK;
                } else {
                    likeIcon = GPTIcons.LIKE;
                }
                JLabel likeAction = new JLabel(likeIcon);
                actionRightPanel.add(likeAction);
                likeAction.setCursor(new Cursor(Cursor.HAND_CURSOR));
                likeAction.setToolTipText("已点踩！");

                Icon dislikeIcon = GPTIcons.DISLIKE_CLICKED;
                JLabel dislikeAction = new JLabel(dislikeIcon);
                actionRightPanel.add(dislikeAction);
                dislikeAction.setCursor(new Cursor(Cursor.HAND_CURSOR));
                dislikeAction.setToolTipText("已点踩！");
            } else {
                Icon likeIcon = null;
                if (isDark) {
                    likeIcon = GPTIcons.LIKE_DARK;
                } else {
                    likeIcon = GPTIcons.LIKE;
                }
                JLabel likeAction = new JLabel(likeIcon);
                actionRightPanel.add(likeAction);
                likeAction.setCursor(new Cursor(Cursor.HAND_CURSOR));
                likeAction.setToolTipText("点赞");
                Icon finalLikeIcon = GPTIcons.LIKE_CLICKED;

                Icon dislikeIcon = null;
                if (isDark) {
                    dislikeIcon = GPTIcons.DISLIKE_DARK;
                } else {
                    dislikeIcon = GPTIcons.DISLIKE;
                }
                JLabel dislikeAction = new JLabel(dislikeIcon);
                actionRightPanel.add(dislikeAction);
                dislikeAction.setCursor(new Cursor(Cursor.HAND_CURSOR));
                dislikeAction.setToolTipText("点踩");
                Icon finalDislikeIcon = GPTIcons.DISLIKE_CLICKED;

                likeAction.addMouseListener(new MouseAdapter() { //点赞按钮点击事件
                    public void mouseClicked(MouseEvent e) {
                        if (!clickedAnswer) {
                            clickedAnswer = true;
                            try {
                                ThreadPoolUtil.execute(() -> {
                                    // 发送点赞
                                    final FeedbackAnswerResponse feedbackAnswerResponse = FeedbackAnswerCommHandler.feedback(CodeChatMessageComponent.this.dialogId, CodeChatMessageComponent.this.reqId, "like");
                                    if (RtnCode.NOT_LOGIN == feedbackAnswerResponse.getCode()) {
                                        clickedAnswer = false;
                                        return;
                                    }
                                    if (RtnCode.SUCCESS == feedbackAnswerResponse.getCode()) {
                                        likeAction.setIcon(finalLikeIcon);
                                        likeAction.setToolTipText("已点赞！");
                                        dislikeAction.setToolTipText("已点赞！");
                                        message.setFeedback("like");
                                    } else {
                                        clickedAnswer = false;
                                    }
                                });
                            } catch (Exception ex) {
                                throw new RuntimeException(ex);
                            }
                        }
                    }
                });

                dislikeAction.addMouseListener(new MouseAdapter() { //点踩按钮点击事件
                    public void mouseClicked(MouseEvent e) {
                        if (!clickedAnswer) {
                            clickedAnswer = true;
                            try {
                                ThreadPoolUtil.execute(() -> {
                                    // 发送点踩
                                    final FeedbackAnswerResponse feedbackAnswerResponse = FeedbackAnswerCommHandler.feedback(CodeChatMessageComponent.this.dialogId, CodeChatMessageComponent.this.reqId, "unlike");
                                    if (RtnCode.NOT_LOGIN == feedbackAnswerResponse.getCode()) {
                                        clickedAnswer = false;
                                        return;
                                    }
                                    if (RtnCode.SUCCESS == feedbackAnswerResponse.getCode()) {
                                        dislikeAction.setIcon(finalDislikeIcon);
                                        dislikeAction.setToolTipText("已点踩！");
                                        likeAction.setToolTipText("已点踩！");
                                        message.setFeedback("unlike");
                                    } else {
                                        clickedAnswer = false;
                                    }
                                });
                            } catch (Exception ex) {
                                throw new RuntimeException(ex);
                            }
                        }
                    }
                });
            }

            //2、重新回答按钮：不是用户提问、是有效的内容、没有子节点
            if (parent != null && message.getChildren().isEmpty()) {
                Icon regenerationIcon = null;
                if (isDark) {
                    regenerationIcon = GPTIcons.REGENERATION_DARK;
                } else {
                    regenerationIcon = GPTIcons.REGENERATION;
                }
                JLabel regenerationAction = new JLabel(regenerationIcon);
                actionRightPanel.add(regenerationAction);
                regenerationAction.setCursor(new Cursor(Cursor.HAND_CURSOR));
                regenerationAction.setToolTipText("重新回答");
                Icon finalRegenerationIcon = regenerationIcon;
                Icon finalRegenerationTipIcon = GPTIcons.REGENERATION_TIP;
                regenerationAction.addMouseListener(new MouseAdapter() { //重新回答按钮点击事件
                    public void mouseClicked(MouseEvent e) {
                        SendAction sendAction = project.getService(SendAction.class);
                        // 0831版本：重新回答 支持知识库提问
                        Integer kbId = parent.getKbId();
                        if (kbId == null) {
                            sendAction.doRegenerationPerformed(codeChatMainPanel, parent.getContent(), parent.getReqId(), null, ChatMessageType.CHAT_GENERATE, CodeChatMessageComponent.this, null);
                        } else {
                            sendAction.doRegenerationPerformed(codeChatMainPanel, parent.getContent(), parent.getReqId(), kbId, ChatMessageType.KB_ASSISTANT, CodeChatMessageComponent.this, parent.getQuoteItem());
                        }
                    }

                    public void mouseEntered(MouseEvent e) {
                        regenerationAction.setIcon(finalRegenerationTipIcon);
                    }

                    public void mouseExited(MouseEvent e) {
                        regenerationAction.setIcon(finalRegenerationIcon);
                    }
                });
            }
        }

        //3、翻页按钮：不是用户提问、有兄弟节点
        if (message.getNodeType() != MessageNodeType.QUESTION && parent != null && parent.getChildren().size() > 1) {
            //往前翻前一个按钮
            if (parent.getSelectedIndex() > 0) {
                Icon previousIcon = isDark ? GPTIcons.PREVIOUS_DARK : GPTIcons.PREVIOUS;

                JLabel previousAction = new JLabel(previousIcon);
                actionLeftPanel.add(previousAction);
                previousAction.setCursor(new Cursor(Cursor.HAND_CURSOR));
                previousAction.setToolTipText("前一个");
                Icon finalPreviousTipIcon = GPTIcons.PREVIOUS_TIP;
                previousAction.addMouseListener(new MouseAdapter() { //前一个按钮点击事件
                    public void mouseClicked(MouseEvent e) {
                        if (!CodeChatCompleteEngin.onAnswerStatus) {
                            parent.resetSelectedIndex(parent.getSelectedIndex() - 1);
                            codeChatMainPanel.enableInputArea();
                            codeChatMainPanel.messageAreaPanel.applyConversationToCurrent(project, CodeChatCompleteEngin.getCurrentConversation(codeChatMainPanel));
                            // 切换后自动跳到最底部展示对应分支最新答案（Jetbrains无法做到记录切换前焦点位置并在切换后固定在点击前位置）
                            codeChatMainPanel.messageAreaPanel.updateLayoutAndAutoScroll();
                        }
                    }

                    public void mouseEntered(MouseEvent e) {
                        previousAction.setIcon(finalPreviousTipIcon);
                    }

                    public void mouseExited(MouseEvent e) {
                        previousAction.setIcon(previousIcon);
                    }
                });
            } else {
                Icon previousIcon = isDark ? GPTIcons.PREVIOUS_DISABLE_DARK : GPTIcons.PREVIOUS_DISABLE;

                JLabel previousAction = new JLabel(previousIcon);
                actionLeftPanel.add(previousAction);
            }

            actionLeftPanel.add(new JLabel((parent.getSelectedIndex() + 1) + "/" + (parent.getChildren().size())));

            //往后翻下一个按钮
            if (parent.getSelectedIndex() < parent.getChildren().size() - 1) {
                Icon nextIcon = isDark ? GPTIcons.NEXT_DARK : GPTIcons.NEXT;

                JLabel nextAction = new JLabel(nextIcon);
                actionLeftPanel.add(nextAction);
                nextAction.setCursor(new Cursor(Cursor.HAND_CURSOR));
                nextAction.setToolTipText("下一个");
                Icon finalNextTipIcon = GPTIcons.NEXT_TIP;
                nextAction.addMouseListener(new MouseAdapter() { //下一个按钮点击事件
                    public void mouseClicked(MouseEvent e) {

                        if (!CodeChatCompleteEngin.onAnswerStatus) {
                            parent.resetSelectedIndex(parent.getSelectedIndex() + 1);
                            codeChatMainPanel.enableInputArea();
                            codeChatMainPanel.messageAreaPanel.applyConversationToCurrent(project, CodeChatCompleteEngin.getCurrentConversation(codeChatMainPanel));
                            // 切换后自动跳到最底部展示对应分支最新答案（Jetbrains无法做到记录切换前焦点位置并在切换后固定在点击前位置）
                            codeChatMainPanel.messageAreaPanel.updateLayoutAndAutoScroll();
                        }
                    }

                    public void mouseEntered(MouseEvent e) {
                        nextAction.setIcon(finalNextTipIcon);
                    }

                    public void mouseExited(MouseEvent e) {
                        nextAction.setIcon(nextIcon);
                    }
                });
            } else {
                Icon nextIcon = isDark ? GPTIcons.NEXT_DISABLE_DARK : GPTIcons.NEXT_DISABLE;
                JLabel nextAction = new JLabel(nextIcon);
                actionLeftPanel.add(nextAction);
            }
            actionPanel.add(actionLeftPanel, BorderLayout.WEST);
        }

        //4、复制按钮：是有效的内容
        if (message.getNodeType() == MessageNodeType.QUESTION || message.getNodeType() == MessageNodeType.ANSWER) {
            Icon copyIcon = null;
            if (isDark) {
                copyIcon = GPTIcons.COPY_DARK;
            } else {
                copyIcon = GPTIcons.COPY;
            }
            JLabel copyAction = new JLabel(copyIcon);
            actionRightPanel.add(copyAction);
            copyAction.setCursor(new Cursor(Cursor.HAND_CURSOR));
            copyAction.setToolTipText("复制");
            Icon finalCopyIcon = copyIcon;
            Icon finalCopyTipIcon = GPTIcons.COPY_TIP;
            copyAction.addMouseListener(new MouseAdapter() { //复制按钮点击事件
                public void mouseClicked(MouseEvent e) {
                    Clipboard clipboard = Toolkit.getDefaultToolkit().getSystemClipboard();
                    Transferable transferable = new StringSelection(isMe ? CodeChatMessageComponent.this.question : CodeChatMessageComponent.this.answer);
                    clipboard.setContents(transferable, null);
                    (new TooltipUtil(copyAction, finalCopyIcon, AllIcons.Actions.Commit)).showCopiedToolTip(e);
                }

                public void mouseEntered(MouseEvent e) {
                    copyAction.setIcon(finalCopyTipIcon);
                }

                public void mouseExited(MouseEvent e) {
                    copyAction.setIcon(finalCopyIcon);
                }
            });
            actionPanel.add(actionRightPanel, BorderLayout.EAST);
        }

        add(actionPanel, BorderLayout.SOUTH);
    }

    /**
     * 展示知识库应用内容：需要根据customData中的不同响应格式，提前不同的quote内容进行展示
     */
    public void displayQuote() {
        CodeChatQuote quote = message.getQuote();
        if (quote == null) {
            return;
        }

        // 文档类知识库引用内容展示：Link
        if (CollectionUtils.isNotEmpty(quote.getDocument())) {
            ArrayList<ConversationMessageQuoteLink> list = new ArrayList<>();
            for (QuoteItem item : quote.getDocument()) {
                list.add(new ConversationMessageQuoteLink(item));
            }
            centerPanel.add(new CodeChatMessageQuoteLinkPanel(list));
        }

        // API知识库引用内容展示
        if (CollectionUtils.isNotEmpty(quote.getApi())) {
            ArrayList<ConversationMessageQuoteInterface> list = new ArrayList<>();
            for (QuoteItem item : quote.getApi()) {
                list.add(new ConversationMessageQuoteInterface(item));
            }
            centerPanel.add(new CodeChatMessageQuoteInterfacePanel(list, codeChatMainPanel, message));
        }

        // API知识库指南内容展示
        if (CollectionUtils.isNotEmpty(quote.getGuide())) {
            ArrayList<ConversationMessageQuoteGuide> list = new ArrayList<>();
            for (QuoteItem item : quote.getGuide()) {
                list.add(new ConversationMessageQuoteGuide(item));
            }
            centerPanel.add(new CodeChatMessageQuoteGuidePanel(list));
        }

        // 刷新组件布局与绘制
        revalidate();
        repaint();

    }

    //======getter/setter============
    public void setAnswer(String source) {
        answer = source;
    }

    public void setDialogId(String source) {
        dialogId = source;
    }

    public void setReqId(String source) {
        reqId = source;
    }

    public String getQuestion() {
        return question;
    }

    public String getReqId() {
        return reqId;
    }

    public String getAnswer() {
        return answer;
    }

    public ConversationMessage getMessage() {
        return message;
    }

    public void setMessage(ConversationMessage message) {
        this.message = message;
    }
}
