package com.srdcloud.ideplugin.general.utils;


import com.intellij.openapi.util.text.StringUtil;
import com.vladsch.flexmark.ext.tables.TablesExtension;
import com.vladsch.flexmark.html.HtmlRenderer;
import com.vladsch.flexmark.parser.Parser;
import com.vladsch.flexmark.util.data.MutableDataHolder;
import com.vladsch.flexmark.util.data.MutableDataSet;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * Html处理工具类
 */
public class HtmlUtil {

    /**
     * 通知内容格式化
     *
     * @param str
     * @return
     */
    public static String wrapHtmlNotification(String str) {
        if (StringUtils.isBlank(str)) {
            return str;
        } else {
            str = str.replaceAll("(?<=\\d)\\.", "\\\\.");
            MutableDataHolder options = new MutableDataSet();
            options.set(Parser.EXTENSIONS, List.of(TablesExtension.create()));
            Parser parser = Parser.builder(options).build();
            HtmlRenderer renderer = HtmlRenderer.builder(options).build();
            com.vladsch.flexmark.util.ast.Document document = parser.parse(str);
            String html = renderer.render(document);
            return "<html>" + "<body>" + html + "</body></html>";
        }
    }

    /**
     * ToolTip内容格式化，将内容转换为html格式、最大截断256个字符，超出用...缺省
     *
     * @param str
     * @return
     */
    public static String wrapHtmlTooltip(String str) {
        if (StringUtils.isBlank(str)) {
            return str;
        } else {
            if (str.length() > 256) {
                str = str.substring(0, 256) + " ...";
            }
            str = StringUtil.escapeXmlEntities(str);
            str = str.replace(" ", "&nbsp;");
            str = str.replace("\t", "&nbsp;&nbsp;");
            str = str.replace("\r\n", "<br/>");
            str = str.replace("\n", "<br/>");
            return "<html><body>" + str + "</body></html>";
        }
    }

    /**
     * Markdown文本渲染html
     */
    public static String md2html(String message) {
        message = message.replaceAll("(?<=\\d)\\.", "\\\\.");
        MutableDataHolder options = new MutableDataSet();
        options.set(Parser.EXTENSIONS, List.of(TablesExtension.create()));
        Parser parser = Parser.builder(options).build();
        HtmlRenderer renderer = HtmlRenderer.builder(options).build();
        com.vladsch.flexmark.util.ast.Document document = parser.parse(message);
        String html = renderer.render(document);
        return addHtmlBodyTag(html);
    }

    public static String html(String children) {
        return "<html>" + children + "</html>";
    }

    public static String getTableStyle() {
        String color = UIUtil.judgeBackgroudDarkTheme() ? "white" : "black";
        return "<style>table {border-collapse: separate;border-spacing: 0;}  table th, table td {border: 1px solid " + color + "; padding: 10px;}</style>";
    }

    public static String addHtmlBodyTag(String content) {
        return "<html>" + getTableStyle() + "<body>" + content + "</body></html>";
    }

    public static String removeHtmlBodyTag(String content) {
        return content
                .replace("<html>", "")
                .replace(getTableStyle(), "")
                .replace("<body>", "")
                .replace("</body>", "")
                .replace("</html>", "");
    }

    public static String removePreCodeTag(String content) {
        return content
                .replace("<pre>", "")
                .replace("<code>", "")
                .replace("</code>", "")
                .replace("</pre>", "");
    }

    public static String removePTag(String content) {
        return content
                .replace("<p>", "")
                .replace("</p>", "");
    }

    public static @NotNull String stripHtml(@NotNull String html, @Nullable String breaks) {
        if (breaks != null) {
            html = html.replaceAll("<br/?>", breaks);
        }

        return html.replaceAll("<(.|\n)*?>", "");
    }
}
