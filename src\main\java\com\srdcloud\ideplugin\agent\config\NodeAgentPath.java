package com.srdcloud.ideplugin.agent.config;

public class Node<PERSON>gentPath extends AgentPath {

    public NodeAgentPath(String pluginName) {
        super(pluginName,"node");
    }

    public NodeAgentPath(String pluginName, String agentVersion) {
        super(pluginName,"node", agentVersion);
    }

    @Override
    public String getAgentFilePath() {

        String os = System.getProperty("os.name").toLowerCase();
        String arch = System.getProperty("os.arch").toLowerCase();

        String nodePath;
        if (os.contains("mac") && arch.contains("aarch64")) {
            nodePath = "/node-" + agentVersion + "-darwin-arm64/bin/node";
        } else if (os.contains("mac") && arch.contains("x86_64")) {
            nodePath = "/node-" + agentVersion + "-darwin-x64/bin/node";
        } else if (os.contains("linux") && arch.contains("aarch64")) {
            nodePath = "/node-" + agentVersion + "-linux-arm64/bin/node";
        } else if (os.contains("linux") && arch.contains("amd64")) {
            nodePath = "/node-" + agentVersion + "-linux-x64/bin/node";
        } else if (os.contains("win") && arch.contains("amd64")) {
            nodePath = "\\node-" + agentVersion + "-win32-x64\\node.exe";
            return getAgentBasePath() + "\\agent\\node" + nodePath;
        } else {
            throw new IllegalStateException("Unsupported OS/architecture combination: OS=" + os + ", Arch=" + arch);
        }

        return getAgentBasePath() + "/agent/node" + nodePath;
    }
}
