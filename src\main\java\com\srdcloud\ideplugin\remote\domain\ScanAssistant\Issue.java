package com.srdcloud.ideplugin.remote.domain.ScanAssistant;

/**
 * <AUTHOR>
 * @date 2024/9/10
 * @desc 扫描问题
 */
public class Issue {
    // 扫描问题id
    private String issueId;
    // 扫描问题的规则id
    private String rule;
    // 问题所在文件名
    private String fileName;
    // 问题所在代码行
    private Integer line;
    // 问题简单描述
    private String message;
    // 严重程度
    private Integer severity;
    // 问题详情描述，如果detail字段为空，ide通过调用查详情接口获取问题详情
    private String detail;

    public Issue(String issueId, String rule, String fileName, Integer line, String message, Integer severity, String detail) {
        this.issueId = issueId;
        this.rule = rule;
        this.fileName = fileName;
        this.line = line;
        this.message = message;
        this.severity = severity;
        this.detail = detail;
    }

    public String getIssueId() {
        return issueId;
    }

    public void setIssueId(String issueId) {
        this.issueId = issueId;
    }

    public String getRule() {
        return rule;
    }

    public void setRule(String rule) {
        this.rule = rule;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Integer getLine() {
        return line;
    }

    public void setLine(Integer line) {
        this.line = line;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getSeverity() {
        return severity;
    }

    public void setSeverity(Integer severity) {
        this.severity = severity;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }
}
