package com.srdcloud.ideplugin.marker


import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.editor.Document
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.ProjectManager
import com.intellij.openapi.util.TextRange
import com.intellij.openapi.vfs.VirtualFileManager
import com.intellij.psi.*
import com.intellij.psi.codeStyle.CodeStyleManager
import java.nio.file.Paths

@Service
class SplitService {
    companion object {
        private val LOG = Logger.getInstance(SplitService::class.java)
        const val CODE_MAX_LENGTH = 4500
        const val CODE_MIN_LINES = 20

        fun getInstance(project: Project): SplitService = project.getService(SplitService::class.java)
    }

    fun checkCodeSize(charCount: Int, lineCount: Int): Boolean {
        return charCount <= CODE_MAX_LENGTH && lineCount >= CODE_MIN_LINES
    }

    fun isValidForSplit(element: PsiElement): Boolean {
        val elementText = element.text
        return checkCodeMinLines(elementText) && checkCodeMaxLength(elementText)
    }

    private fun checkCodeMinLines(code: String): Boolean {
        return code.lines().count { it.isNotBlank() } >= CODE_MIN_LINES
    }

    private fun checkCodeMaxLength(code: String): Boolean {
        return code.length <= CODE_MAX_LENGTH
    }

    fun getElementIndent(element: PsiElement): String {
        val file = element.containingFile
        val document = PsiDocumentManager.getInstance(element.project).getDocument(file) ?: return ""
        val lineNumber = document.getLineNumber(element.textRange.startOffset)
        return document.getText(TextRange(document.getLineStartOffset(lineNumber), element.textRange.startOffset))
    }

    fun splitCode(element: PsiElement, project: Project, file: PsiFile) {
        try {
            val document = PsiDocumentManager.getInstance(project).getDocument(file) ?: return
            val textRange = element.textRange
            val startLine = document.getLineNumber(textRange.startOffset) + 1
            val endLine = document.getLineNumber(textRange.endOffset) + 1

            LOG.info("[Comate] Split startOffset: ${textRange.startOffset} endOffset: ${textRange.endOffset} startLine: $startLine endLine: $endLine")

            val code = document.getText(textRange)
            val indent = getElementIndent(element)

            // Here you would typically call your AI service to perform the actual code splitting
            val splitResult = performAISplit(code, indent)

            updateDocument(document, splitResult, startLine, endLine, project)
        } catch (e: Exception) {
            LOG.error("[Comate] Error splitting code", e)
        }
    }

    private fun performAISplit(code: String, indent: String): String {
        // This is where you'd integrate with your AI service
        // For now, we'll just return a mock split
        return """
            $indent// First part of the split function
            ${code.substringBefore("\n")}
            $indent// ...

            $indent// Second part of the split function
            ${code.substringAfterLast("\n")}
        """.trimIndent()
    }

    private fun updateDocument(document: Document, splitResult: String, startLine: Int, endLine: Int, project: Project) {
        val startOffset = document.getLineStartOffset(startLine - 1)
        val endOffset = document.getLineEndOffset(endLine - 1)

        WriteCommandAction.runWriteCommandAction(project) {
            document.replaceString(startOffset, endOffset, splitResult)
            PsiDocumentManager.getInstance(project).commitDocument(document)
            val psiFile = PsiDocumentManager.getInstance(project).getPsiFile(document)
            if (psiFile != null) {
                CodeStyleManager.getInstance(project).reformatText(
                    psiFile,
                    startOffset,
                    startOffset + splitResult.length
                )
            }
        }
    }
    private fun getProjectFromDocument(document: Document): Project {
        val virtualFile = FileDocumentManager.getInstance().getFile(document)
        if (virtualFile != null) {
            ProjectManager.getInstance().openProjects.forEach { project ->
                if (project.isInitialized && !project.isDisposed && virtualFile.isInLocalFileSystem) {
                    val projectFile = VirtualFileManager.getInstance().findFileByNioPath(project.basePath?.let { Paths.get(it) } ?: return@forEach)
                    if (projectFile != null && virtualFile.path.startsWith(projectFile.path)) {
                        return project
                    }
                }
            }
        }
        throw IllegalStateException("Could not find project for document")
    }
}
