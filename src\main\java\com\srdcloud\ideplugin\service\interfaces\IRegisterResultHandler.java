package com.srdcloud.ideplugin.service.interfaces;

import com.srdcloud.ideplugin.service.domain.apigw.ApigwWebsocketRespConfig;
import com.srdcloud.ideplugin.service.domain.apigw.ApigwWebsocketRespPayload;

public interface IRegisterResultHandler {

    /**
     * 注册通道结果查询回调处理接口
     * @param result 请求结果
     */
    public void OnRegisterResult(int result);

    /**
     * 用户Session异常等事件回调处理接口
     * @param eventCode 事件id
     */
    void onUserSessionEvent(int eventCode);

    /**
     * 用户Apikey查询结果回调处理接口
     * @param result 请求结果值
     * @param apiKey apikey值
     */
    void onUserApiKeyResult(int result, String apiKey, ApigwWebsocketRespPayload respPayload);

}
