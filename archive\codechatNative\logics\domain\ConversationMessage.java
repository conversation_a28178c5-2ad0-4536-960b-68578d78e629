package com.srdcloud.ideplugin.assistant.codechatNative.logics.domain;

import com.alibaba.fastjson2.util.DateUtils;
import com.srdcloud.ideplugin.general.enums.ChatMessageType;
import com.srdcloud.ideplugin.general.enums.ConversationMessageType;
import com.srdcloud.ideplugin.general.enums.MessageNodeType;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.CodeChatQuote;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.QuoteItem;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/11
 * @description 会话消息基类
 */
public class ConversationMessage {

    /**
     * 消息业务场景
     */
    private ChatMessageType chatMessageType;

    /**
     * 消息角色类型
     */
    private final ConversationMessageType messageType;

    /**
     * 消息顺序
     */
    private int order;

    /**
     * 创建时间
     */
    private String dateTime;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息内容
     */
    private String errMsg;

    /**
     * 本轮问答的id号
     */
    private String reqId;

    /**
     * 用户对本轮回答的反馈评价，当role=assistant时为必选，可选值为：
     * none - 用户无反馈
     * like - 用户点赞
     * unlike - 用户点踩
     */
    private String feedback;

    /**
     * 树连接器，用于连接父消息和子消息组
     */
    private ConversationMessageNode connector = new ConversationMessageNode();

    private MessageNodeType nodeType;


    /**
     * 当前答案来自哪个知识库提问
     */
    private Integer kbId;

    /**
     * 知识库提问返回引用响应
     */
    private CodeChatQuote quote;

    /**
     * 是否禁止点击知识库引用消息：来自历史会话加载的、问答过程中已经点击过的，就不允许
     */
    private boolean quoteBanned = false;

    /**
     * APIList引用列表被点选提问的具体引用
     */
    private QuoteItem quoteItem;

    //============业务逻辑===============

    /**
     * 工厂方法：构建一条用户提问消息
     */
    public static ConversationMessage buildQuestionMessage(String content, ChatMessageType chatMessageType) {
        return new ConversationMessage(content, "",ConversationMessageType.USER, chatMessageType, MessageNodeType.QUESTION);
    }


    /**
     * 工厂方法：构建一条AI回答消息
     */
    public static ConversationMessage buildAnswerMessage(String text, ChatMessageType chatMessageType) {
        return new ConversationMessage(text, "", ConversationMessageType.ASSISTANT, chatMessageType, MessageNodeType.ANSWER);
    }

    /**
     * 工厂方法：构建一条系统错误消息
     */
    public static ConversationMessage buildErrorMessage(String error, ChatMessageType chatMessageType) {
        return new ConversationMessage("",error, ConversationMessageType.ASSISTANT, chatMessageType, MessageNodeType.VALID_ERROR);
    }

    /**
     * 工厂方法：构建一条无效错误消息
     */
    public static ConversationMessage buildInvalidMessage(String error, ChatMessageType chatMessageType) {
        return new ConversationMessage("",error, ConversationMessageType.ASSISTANT, chatMessageType, MessageNodeType.INVALID_ERROR);
    }


    /**
     * 判断本条消息是否由用户发出
     */
    public boolean me() {
        return this.messageType == ConversationMessageType.USER;
    }

    /**
     * 判断本条消息是否为插件tip消息
     */
    public boolean tip() {
        return this.messageType == ConversationMessageType.TIP;
    }

    //============非业务逻辑==============


    private ConversationMessage(String content, String errorMsg, ConversationMessageType messageType, ChatMessageType chatMessageType, MessageNodeType nodeType) {
        this(content,errorMsg,messageType,chatMessageType,"", nodeType);
    }

    public ConversationMessage(String content, String errMsg, ConversationMessageType messageType, ChatMessageType chatMessageType, String dateTime, MessageNodeType nodeType) {
        this.content = content;
        this.messageType = messageType;
        this.chatMessageType = chatMessageType;
        this.dateTime = dateTime;
        this.errMsg = errMsg;
        this.nodeType = nodeType;
    }

    public ConversationMessageType getMessageType() {
        return this.messageType;
    }

    // 根据错误和内容判断输出内容
    public String getContent() {
        if (!content.isEmpty()) {
            return content;
        }else {
            return errMsg;
        }
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getOrder() {
        return this.order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public ChatMessageType getChatMessageType() {
        if (chatMessageType == null) {
            return ChatMessageType.CHAT_GENERATE;
        }
        return chatMessageType;
    }

    public void setChatMessageType(ChatMessageType chatMessageType) {
        this.chatMessageType = chatMessageType;
    }

    public String getDateTime() {
        if (dateTime == null || dateTime.isEmpty()) {
            return DateUtils.format(new Date());
        }
        return dateTime;
    }

    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
    }


    public String getReqId() {
        return reqId;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    public String getFeedback() {
        return feedback;
    }

    public void setFeedback(String feedback) {
        this.feedback = feedback;
    }

    public ConversationMessage getParent() {
        return connector.getParent();
    }

    public List<ConversationMessage> getChildren() {
        return connector.getChildren();
    }

    public int getSelectedIndex() {
        return connector.getSelectedIndex();
    }

    public void setSelectedIndex(int selectedIndex) {
        connector.setSelectedIndex(selectedIndex);
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setNodeType(MessageNodeType nodeType) {
        this.nodeType = nodeType;
    }

    public MessageNodeType getNodeType() {
        return nodeType;
    }

    public ConversationMessageNode getTreeNode() {
        return connector;
    }


    public void setConnector(ConversationMessageNode connector) {
        this.connector = connector;
    }

    public void resetSelectedIndex(int selectedIndex) {
        connector.resetSelectedIndex(selectedIndex);
    }

    public CodeChatQuote getQuote() {
        return quote;
    }

    public void setQuote(CodeChatQuote quote) {
        this.quote = quote;
    }

    public boolean isQuoteBanned() {
        return quoteBanned;
    }

    public void setQuoteBanned(boolean quoteBanned) {
        this.quoteBanned = quoteBanned;
    }

    public Integer getKbId() {
        return kbId;
    }

    public void setKbId(Integer kbId) {
        this.kbId = kbId;
    }

    public QuoteItem getQuoteItem() {
        return quoteItem;
    }

    public void setQuoteItem(QuoteItem quoteItem) {
        this.quoteItem = quoteItem;
    }
}
