package com.srdcloud.ideplugin.codechat.domain;

import com.srdcloud.ideplugin.webview.codechat.relatedfile.RelatedFile;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/26
 */
public class ChatMessage {
    /**
     * 提问消息id，插件自定义生成，规则为：[reqId]_[PromptRoleType.USER], 回答消息id:[reqId]_[PromptRoleType.ASSISTANT]
     */
    private String id;

    private String role;
    private String content;
    private String createTime;
    private int type;
    private boolean isErrMsg;
    private List<RelatedFile> files;

    public ChatMessage() {
    }

    public ChatMessage(String id, String role, String content, String createTime, int type, boolean isErrMsg, List<RelatedFile> files) {
        this.id = id;
        this.role = role;
        this.content = content;
        this.createTime = createTime;
        this.type = type;
        this.isErrMsg = isErrMsg;
        this.files = files;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public boolean isErrMsg() {
        return isErrMsg;
    }

    public void setErrMsg(boolean errMsg) {
        isErrMsg = errMsg;
    }

    public List<RelatedFile> getFiles() {
        return files;
    }

    public void setFiles(List<RelatedFile> files) {
        this.files = files;
    }
}

