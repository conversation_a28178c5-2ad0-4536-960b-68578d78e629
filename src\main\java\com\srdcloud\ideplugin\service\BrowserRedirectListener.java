package com.srdcloud.ideplugin.service;

import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.utils.EnvUtil;
import com.srdcloud.ideplugin.general.utils.FormatUtil;
import com.srdcloud.ideplugin.general.utils.StringUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.http.*;
import io.netty.util.CharsetUtil;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jetbrains.ide.RestService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * 浏览器重定向请求监听器。
 * 响应及执行浏览器登录研发云成功后，插件所需要处理的业务。
 *
 * <AUTHOR>
 */
public class BrowserRedirectListener extends RestService {

    private static final Logger logger = LoggerFactory.getLogger(BrowserRedirectListener.class);

    /**
     * 处理Oauth登录回调
     * 回调请求示例：GET http://127.0.0.1:63343/api/oauth-redirect?code=XXXX
     *  6334x端口是IDEA自带的服务端口，启动IDEA就会监听该端口，可从settings->Build->Debugger->Builtin servers查看
     */
    @Nullable
    @Override
    public String execute(@NotNull QueryStringDecoder queryStringDecoder, @NotNull FullHttpRequest fullHttpRequest, @NotNull ChannelHandlerContext channelHandlerContext) throws IOException {
        logger.info("[cf] BrowserRedirectListener Redirect begin...");
        // query参数提取
        Map<String, String> queryParams = FormatUtil.parseQueryParameters(fullHttpRequest.uri());

        // code兑换token
        boolean authResult = true;
        if (!queryParams.isEmpty() && !StringUtil.isEmpty(queryParams.get("code"))) {
            if(EnvUtil.isSec()){
                authResult = LoginService.handleCode(queryParams.get("code"));
            }else {
                authResult = LoginService.getAuthTokenByCode(queryParams.get("code"));
            }
        } else {
            authResult = false;
        }

        // 返回结果页面
        ByteBuf buf = Unpooled.wrappedBuffer(getHtmlContent(authResult).getBytes(CharsetUtil.UTF_8));
        HttpResponse response = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.OK, buf);
        response.headers().set(HttpHeaderNames.CONTENT_TYPE, "text/html");
        sendResponse(fullHttpRequest, channelHandlerContext, response);

        return authResult ? Constants.LoginMsg_OK : Constants.LoginMsg_NOK;
    }

    /**
     * 定义浏览器重向路径
     *
     * @return
     */
    @NotNull
    @Override
    protected String getServiceName() {
        return "oauth-redirect";
    }

    @Override
    protected boolean isMethodSupported(@NotNull HttpMethod httpMethod) {
        return httpMethod == HttpMethod.GET;
    }

    @Override
    protected boolean isHostTrusted(@NotNull FullHttpRequest request) throws InterruptedException, InvocationTargetException {
        return true;
    }

    /**
     * 获取resources下html文件进行重定向相应的返回
     *
     * @return
     */
    private String getHtmlContent(final boolean isLoginSuccess) {
        final String fileName = isLoginSuccess ?
                EnvUtil.isSec( "secidea-page/index.html", "aicode-page/index.html") :
                EnvUtil.isSec( "secidea-page/error.html", "aicode-page/error.html");
        try {
            // 读取 HTML 文件内容
            ClassLoader classLoader = BrowserRedirectListener.class.getClassLoader();
            InputStream inputStream = classLoader.getResourceAsStream(fileName);

            return new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
        } catch (IOException e) {
            e.printStackTrace();
            return "Error loading HTML content";
        }
    }
}
