package com.srdcloud.ideplugin.general.utils;

import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.config.PipelineBundle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2025/2/27
 * @desc 环境与调试模式控制
 */
public class EnvUtil {
    private static final Logger logger = LoggerFactory.getLogger(EnvUtil.class);

    /**
     * 是否打开调试模式
     */
    public static boolean checkDebugAble() {
        return ConfigWrapper.isDebugMode;
    }

    /**
     * 是否打开webview日志打印
     */
    public static boolean checkWebviewLogAble() {
        return checkDebugAble() && ConfigWrapper.isWebviewLog;
    }

    /**
     * 是否挂载TreeSitter调试入口
     */
    public static boolean checkTreeSitterMountAble() {
        return checkDebugAble() && ConfigWrapper.isTreeSitterMount;
    }

    /**
     * 判断是 sec 还是 srd的
     * @return
     */
    public static boolean isSec() { return PipelineBundle.message("isSec").equals("true");}




    /**
     * 根据环境选择返回值，取代三元表达式
     * @param <T> 返回值的类型
     * @param secValue 如果是 sec 环境，返回此值
     * @param srdValue 如果是 srd 环境，返回此值
     * @return 根据环境选择的返回值
     */
    public static <T> T isSec(T secValue, T srdValue) {
        return isSec() ? secValue : srdValue;
    }
}
