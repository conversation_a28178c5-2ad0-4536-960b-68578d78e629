package com.srdcloud.ideplugin.general.constants

/**
 * <AUTHOR>
 * @date 2025/6/11
 * @desc 扫描规则
 */
object IgnoreRules {

    @JvmField
    val DEFAULT_IGNORE_FILETYPES = setOf(
        ".DS_Store",
        "-lock.json",
        ".lock",
        ".log",
        ".ttf",
        ".png",
        ".jpg",
        ".jpeg",
        ".gif",
        ".mp4",
        ".svg",
        ".ico",
        ".pdf",
        ".zip",
        ".gz",
        ".tar",
        ".dmg",
        ".tgz",
        ".rar",
        ".7z",
        ".exe",
        ".dll",
        ".obj",
        ".o",
        ".o.d",
        ".a",
        ".lib",
        ".so",
        ".dylib",
        ".ncb",
        ".sdf",
        ".woff",
        ".woff2",
        ".eot",
        ".cur",
        ".avi",
        ".mpg",
        ".mpeg",
        ".mov",
        ".mp3",
        ".mp4",
        ".mkv",
        ".mkv",
        ".webm",
        ".jar",
        ".onnx",
        ".parquet",
        ".pqt",
        ".wav",
        ".webp",
        ".db",
        ".sqlite",
        ".wasm",
        ".plist",
        ".profraw",
        ".gcda",
        ".gcno",
        ".gitignore",
        "go.sum",
    )

    @JvmField
    val DEFAULT_IGNORE_DIRS = setOf(
        // 基础版本控制
        ".git",
        ".svn",
        ".hg",
        ".bzr",
        ".fossil",
        ".repo",
        ".cvs",
        ".cvsignore",

        // IDE和编辑器
        ".vscode",
        ".idea",
        ".vs",
        ".eclipse",
        ".netbeans",
        ".kate-swp",
        ".komodotools",
        ".komodoproject",
        ".visualstudio",
        ".vscodium",
        ".webstorm",
        ".pycharm",
        ".rubymine",
        ".phpstorm",
        ".rider",
        ".goland",
        ".clion",
        ".androidstudio",
        ".xcode",

        // Python相关
        "venv",
        ".venv",
        "env",
        ".env",
        "__pycache__",
        ".pytest_cache",
        ".coverage",
        ".tox",
        ".pytype",
        ".mypy_cache",
        ".pytest_cache",
        ".python-version",
        ".pyenv",
        "pip-wheel-metadata",
        ".ipynb_checkpoints",
        ".spyderproject",
        ".ropeproject",
        ".pyre",

        // Node.js相关
        "node_modules",
        ".npm",
        ".yarn",
        ".pnpm",
        ".node-gyp",
        ".node_repl_history",
        ".v8flags",
        ".babel",
        ".next",
        ".nuxt",
        ".gatsby",
        ".remix",
        ".deno",
        ".bun",

        // Java相关
        "target",
        ".m2",
        ".mvn",
        ".gradle",
        "build",
        "out",
        ".settings",
        ".project",
        ".classpath",
        ".factorypath",
        "classes",
        "META-INF",
        "WEB-INF",
        ".springBeans",
        ".sts4-cache",
        ".grails",

        // Ruby相关
        ".bundle",
        "vendor/bundle",
        ".gem",
        ".rvm",
        ".rbenv",
        ".ruby-version",
        ".ruby-gemset",
        "coverage",
        ".solargraph",
        ".byebug_history",

        // Go相关
        "pkg",
        "bin",
        ".gvm",
        ".glide",
        ".dep",
        "vendor",
        ".go-version",
        "testdata",
        ".gonvim",
        ".gore",

        // Rust相关
        "target",
        ".cargo",
        ".rustup",
        ".cargo-cache",
        "Cargo.lock",
        ".rust-version",
        ".rustfmt",
        ".clippy",

        // PHP相关
        "vendor",
        ".composer",
        ".php_cs.cache",
        ".phpunit",
        ".php-version",
        ".phpdoc",
        ".phalcon",
        ".phpbrew",
        ".php-cs-fixer",

        // 前端构建和依赖
        "dist",
        "build",
        ".cache",
        ".parcel-cache",
        ".vite",
        ".rollup",
        ".webpack",
        ".browserify",
        ".esbuild",
        ".swc",
        ".turbo",
        ".storybook",
        ".stylelint",
        ".sass-cache",

        // 移动开发
        ".gradle",
        ".idea",
        "build",
        "captures",
        ".externalNativeBuild",
        ".cxx",
        "*.apk",
        "*.ipa",
        ".pods",
        "Pods",
        "xcuserdata",
        ".playground",
        ".flutter-plugins",
        ".pub-cache",
        ".android",
        ".ios",

        // 容器和云服务
        ".docker",
        ".kubernetes",
        ".helm",
        ".terraform",
        ".vagrant",
        ".chef",
        ".puppet",
        ".ansible",
        ".salt",
        ".aws",
        ".azure",
        ".gcloud",
        ".digitalocean",
        ".heroku",
        ".vercel",
        ".netlify",
        ".firebase",

        // 数据库和缓存
        ".mysql",
        ".postgresql",
        ".mongodb",
        ".redis",
        ".sqlite",
        ".cassandra",
        ".couchdb",
        ".elasticsearch",
        ".neo4j",
        ".influxdb",
        ".clickhouse",
        ".memcached",

        // CI/CD和部署
        ".github",
        ".gitlab",
        ".circleci",
        ".jenkins",
        ".travis",
        ".drone",
        ".teamcity",
        ".bamboo",
        ".buildkite",
        ".appveyor",
        ".codeship",
        ".wercker",
        ".semaphore",
        ".bitrise",

        // 文档和静态站点
        "docs",
        "_site",
        ".jekyll-cache",
        ".hugo_build.lock",
        ".vuepress",
        ".docusaurus",
        ".mkdocs",
        ".sphinx-build",
        ".gitbook",
        ".docz",

        // 工具和实用程序
        ".tmp",
        ".temp",
        ".cache",
        ".local",
        ".config",
        ".history",
        ".log",
        "logs",
        "backup",
        ".backup",
        "archive",
        ".archive",
        ".trash",
        ".recycle",

        // 操作系统特定
        ".DS_Store",
        ".Spotlight-V100",
        ".Trashes",
        ".Trash",
        ".fseventsd",
        "Thumbs.db",
        "Desktop.ini",
        "\$RECYCLE.BIN",
        "System Volume Information",

        // 安全和认证
        ".ssh",
        ".gnupg",
        ".gpg",
        ".cert",
        ".credentials",
        ".secrets",
        ".vault",
        ".kube",
        ".keystore",
        ".truststore",

        // 监控和分析
        ".newrelic",
        ".datadog",
        ".sentry",
        ".grafana",
        ".prometheus",
        ".elastic",
        ".splunk",
        ".nagios",
        ".zabbix",

        // 测试和质量保证
        "coverage",
        ".nyc_output",
        ".jest",
        ".cypress",
        ".selenium",
        ".testcafe",
        ".karma",
        ".playwright",
        ".puppeteer",
        ".webdriver",
        ".lighthouse",
        ".axe",
        ".sonar",
        ".codecov",

        // 多媒体和资源
        "assets",
        "media",
        "uploads",
        "downloads",
        "public/uploads",
        "storage/app",
        ".thumbs",
        ".previews",
        ".miniatures",
        ".compressed",

        // 本地化和国际化
        "locale",
        "locales",
        "i18n",
        "translations",
        ".translations",
        ".locale-data",
        ".messages",
        ".intl",

        // 旧文件和备份
        ".old",
        ".bak",
        ".backup",
        ".save",
        ".swp",
        ".swap",
        ".orig",
        ".rej",
        ".previous",
        ".historic",

        // 其他工具和框架
        ".meteor",
        ".sails",
        ".yeoman",
        ".bower",
        ".grunt",
        ".gulp",
        ".brunch",
        ".middleman",
        ".eleventy",
        ".gatsby",
        ".phenomic",
        ".metalsmith",
    )

    @JvmField
    val DEFAULT_IGNORE_LIST = arrayOf(
        //Files
        "*.min.js",
        "*.js.map",
        "*.DS_Store",
        "*-lock.json",
        "*.lock",
        "*.log",
        "*.ttf",
        "*.png",
        "*.jpg",
        "*.jpeg",
        "*.gif",
        "*.mp4",
        "*.svg",
        "*.ico",
        "*.pdf",
        "*.zip",
        "*.gz",
        "*.tar",
        "*.dmg",
        "*.tgz",
        "*.rar",
        "*.7z",
        "*.exe",
        "*.dll",
        "*.obj",
        "*.o",
        "*.o.d",
        "*.a",
        "*.lib",
        "*.so",
        "*.dylib",
        "*.ncb",
        "*.sdf",
        "*.woff",
        "*.woff2",
        "*.eot",
        "*.cur",
        "*.avi",
        "*.mpg",
        "*.mpeg",
        "*.mov",
        "*.mp3",
        "*.mp4",
        "*.mkv",
        "*.webm",
        "*.jar",
        "*.onnx",
        "*.parquet",
        "*.pqt",
        "*.wav",
        "*.webp",
        "*.db",
        "*.sqlite",
        "*.wasm",
        "*.plist",
        "*.profraw",
        "*.gcda",
        "*.gcno",
        "go.sum",
        ".env",
        "app.*.*.js",
        "chunk-*.js",

        //dirs
        // 基础版本控制
        ".git",
        ".svn",
        ".hg",
        ".bzr",
        ".fossil",
        ".repo",
        ".cvs",
        ".cvsignore",

        // IDE和编辑器
        ".vscode",
        ".idea",
        ".vs",
        ".eclipse",
        ".netbeans",
        ".kate-swp",
        ".komodotools",
        ".komodoproject",
        ".visualstudio",
        ".vscodium",
        ".webstorm",
        ".pycharm",
        ".rubymine",
        ".phpstorm",
        ".rider",
        ".goland",
        ".clion",
        ".androidstudio",
        ".xcode",

        // Python相关
        "venv",
        ".venv",
        "env",
        ".env",
        "__pycache__",
        ".pytest_cache",
        ".coverage",
        ".tox",
        ".pytype",
        ".mypy_cache",
        ".pytest_cache",
        ".python-version",
        ".pyenv",
        "pip-wheel-metadata",
        ".ipynb_checkpoints",
        ".spyderproject",
        ".ropeproject",
        ".pyre",
        "node",

        // Node.js相关
        "node_modules",
        ".npm",
        ".yarn",
        ".pnpm",
        ".node-gyp",
        ".node_repl_history",
        ".v8flags",
        ".babel",
        ".next",
        ".nuxt",
        ".gatsby",
        ".remix",
        ".deno",
        ".bun",

        // Java相关
        "target",
        ".m2",
        ".mvn",
        ".gradle",
        "build",
        "out",
        ".settings",
        ".project",
        ".classpath",
        ".factorypath",
        "classes",
        "META-INF",
        "WEB-INF",
        ".springBeans",
        ".sts4-cache",
        ".grails",

        // Ruby相关
        ".bundle",
        "vendor/bundle",
        ".gem",
        ".rvm",
        ".rbenv",
        ".ruby-version",
        ".ruby-gemset",
        "coverage",
        ".solargraph",
        ".byebug_history",

        // Go相关
        "pkg",
        "bin",
        ".gvm",
        ".glide",
        ".dep",
        "vendor",
        ".go-version",
        "testdata",
        ".gonvim",
        ".gore",

        // Rust相关
        "target",
        ".cargo",
        ".rustup",
        ".cargo-cache",
        "Cargo.lock",
        ".rust-version",
        ".rustfmt",
        ".clippy",

        // PHP相关
        "vendor",
        ".composer",
        ".php_cs.cache",
        ".phpunit",
        ".php-version",
        ".phpdoc",
        ".phalcon",
        ".phpbrew",
        ".php-cs-fixer",

        // 前端构建和依赖
        "dist",
        "build",
        ".cache",
        ".parcel-cache",
        ".vite",
        ".rollup",
        ".webpack",
        ".browserify",
        ".esbuild",
        ".swc",
        ".turbo",
        ".storybook",
        ".stylelint",
        ".sass-cache",

        // 移动开发
        ".gradle",
        ".idea",
        "build",
        "captures",
        ".externalNativeBuild",
        ".cxx",
        "*.apk",
        "*.ipa",
        ".pods",
        "Pods",
        "xcuserdata",
        ".playground",
        ".flutter-plugins",
        ".pub-cache",
        ".android",
        ".ios",

        // 容器和云服务
        ".docker",
        ".kubernetes",
        ".helm",
        ".terraform",
        ".vagrant",
        ".chef",
        ".puppet",
        ".ansible",
        ".salt",
        ".aws",
        ".azure",
        ".gcloud",
        ".digitalocean",
        ".heroku",
        ".vercel",
        ".netlify",
        ".firebase",

        // 数据库和缓存
        ".mysql",
        ".postgresql",
        ".mongodb",
        ".redis",
        ".sqlite",
        ".cassandra",
        ".couchdb",
        ".elasticsearch",
        ".neo4j",
        ".influxdb",
        ".clickhouse",
        ".memcached",

        // CI/CD和部署
        ".github",
        ".gitlab",
        ".circleci",
        ".jenkins",
        ".travis",
        ".drone",
        ".teamcity",
        ".bamboo",
        ".buildkite",
        ".appveyor",
        ".codeship",
        ".wercker",
        ".semaphore",
        ".bitrise",

        // 文档和静态站点
        "docs",
        "_site",
        ".jekyll-cache",
        ".hugo_build.lock",
        ".vuepress",
        ".docusaurus",
        ".mkdocs",
        ".sphinx-build",
        ".gitbook",
        ".docz",

        // 工具和实用程序
        ".tmp",
        ".temp",
        ".cache",
        ".local",
        ".config",
        ".history",
        ".log",
        "logs",
        "backup",
        ".backup",
        "archive",
        ".archive",
        ".trash",
        ".recycle",

        // 操作系统特定
        ".DS_Store",
        ".Spotlight-V100",
        ".Trashes",
        ".Trash",
        ".fseventsd",
        "Thumbs.db",
        "Desktop.ini",
        "\$RECYCLE.BIN",
        "System Volume Information",

        // 安全和认证
        ".ssh",
        ".gnupg",
        ".gpg",
        ".cert",
        ".credentials",
        ".secrets",
        ".vault",
        ".kube",
        ".keystore",
        ".truststore",

        // 监控和分析
        ".newrelic",
        ".datadog",
        ".sentry",
        ".grafana",
        ".prometheus",
        ".elastic",
        ".splunk",
        ".nagios",
        ".zabbix",

        // 测试和质量保证
        "coverage",
        ".nyc_output",
        ".jest",
        ".cypress",
        ".selenium",
        ".testcafe",
        ".karma",
        ".playwright",
        ".puppeteer",
        ".webdriver",
        ".lighthouse",
        ".axe",
        ".sonar",
        ".codecov",

        // 多媒体和资源
        "assets",
        "media",
        "uploads",
        "downloads",
        "public/uploads",
        "storage/app",
        ".thumbs",
        ".previews",
        ".miniatures",
        ".compressed",

        // 本地化和国际化
        "locale",
        "locales",
        "i18n",
        "translations",
        ".translations",
        ".locale-data",
        ".messages",
        ".intl",

        // 旧文件和备份
        ".old",
        ".bak",
        ".backup",
        ".save",
        ".swp",
        ".swap",
        ".orig",
        ".rej",
        ".previous",
        ".historic",

        // 其他工具和框架
        ".meteor",
        ".sails",
        ".yeoman",
        ".bower",
        ".grunt",
        ".gulp",
        ".brunch",
        ".middleman",
        ".eleventy",
        ".gatsby",
        ".phenomic",
        ".metalsmith",
    )

}