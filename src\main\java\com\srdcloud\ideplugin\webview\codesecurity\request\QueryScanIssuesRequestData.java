package com.srdcloud.ideplugin.webview.codesecurity.request;

import com.srdcloud.ideplugin.webview.base.domain.WebViewReqType;

public class QueryScanIssuesRequestData  extends WebViewReqType {

    // 安全扫描任务ID
    private String taskId;
    // 安全扫描页码
    private int page;
    // 安全扫描页码大小
    private int pageSize;

    public QueryScanIssuesRequestData(String reqType, String taskId, int page, int pageSize) {
        this.reqType = reqType;
        this.taskId = taskId;
        this.page = page;
        this.pageSize = pageSize;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskId() {return taskId;}

    public void setPage(int page) {
        this.page = page;
    }

    public int getPage() {
        return page;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPageSize() {
        return pageSize;
    }

}
