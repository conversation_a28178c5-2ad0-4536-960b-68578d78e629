package com.srdcloud.ideplugin.webview.codechat.conversation.request;

import com.srdcloud.ideplugin.webview.base.domain.WebViewCommand;

public class ConversationFeedbackRequest extends WebViewCommand {

    private ConversationFeedbackRequestData data;

    public ConversationFeedbackRequest(String command, ConversationFeedbackRequestData data) {
        this.command = command;
        this.data = data;
    }

    public void setData(ConversationFeedbackRequestData data) {
        this.data = data;
    }

    public ConversationFeedbackRequestData getData() {
        return data;
    }

}
