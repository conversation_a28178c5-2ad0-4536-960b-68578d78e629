package com.srdcloud.ideplugin.webview.workitem.response;

import com.srdcloud.ideplugin.remote.domain.WorkItem.WorkItemInfo;
import com.srdcloud.ideplugin.webview.base.domain.WebViewReqType;

import java.util.List;

public class SearchworkitemsResponseData extends WebViewReqType {

    private List<WorkItemInfo> workItemList;

    private String error;

    private int isEnd;

    public SearchworkitemsResponseData() {
    }

    public SearchworkitemsResponseData(String reqType) {
        super(reqType);
    }

    public SearchworkitemsResponseData(String reqType, int isEnd) {
        super(reqType);
        this.isEnd = isEnd;
    }

    public SearchworkitemsResponseData(List<WorkItemInfo> workItemList, String error) {
        this.workItemList = workItemList;
        this.error = error;
    }

    public SearchworkitemsResponseData(String reqType, List<WorkItemInfo> workItemList, String error) {
        super(reqType);
        this.workItemList = workItemList;
        this.error = error;
    }

    public List<WorkItemInfo> getWorkItemList() {
        return workItemList;
    }

    public void setWorkItemList(List<WorkItemInfo> workItemList) {
        this.workItemList = workItemList;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }
}
