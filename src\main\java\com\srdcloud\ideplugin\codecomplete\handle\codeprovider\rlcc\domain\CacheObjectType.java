package com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc.domain;

/**
 * <AUTHOR>
 * @date 2025/1/1
 * @description 缓存的数据类型，对应treesitter中的Parser.SyntaxNode的type类型值
 * 注：不同语言解析出来的SyntaxNode的type值枚举范围有所差别
 * 此处仅提取 声明 相关类型
 */
public enum CacheObjectType {
    CLASS("class_declaration"),
    INTERFACE("interface_declaration"),
    ENUM("enum_declaration"),

    /**
     * 独立方法
     */
    FUNCTION("function_declaration"),

    /**
     * go语言结构体定义
     */
    STRUCT("struct_type"),

    ;

    private String name;

    CacheObjectType(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
