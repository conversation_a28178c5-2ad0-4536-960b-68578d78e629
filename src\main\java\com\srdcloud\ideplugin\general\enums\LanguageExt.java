package com.srdcloud.ideplugin.general.enums;

import java.util.Arrays;

/**
 * @author: yangy
 * @date: 2023/7/14 17:21
 * @Desc
 */
public enum LanguageExt {

    JAVA("java", "java"),
    PYTHON("python", "py"),
    JAVASCRIPT("javascript", "js"),
    COMMONJAVASCRIPT("javascript", "cjs"),
    TYPESCRIPT("typescript", "ts"),
    C("c", "c"),
    CPP("c++", "cpp"),
    GO("go", "go"),
    RUST("rust", "rs"),
    SWIFT("swift", "swift"),
    KOTLIN("kotlin", "kt"),
    GROOVY("groovy", "groovy"),
    SCALA("scala", "scala"),
    CLOJURE("clojure", "clj"),
    LUA("lua", "lua"),
    R("r", "r"),
    DART("dart", "dart"),
    ELIXIR("elixir", "ex"),
    ERLANG("erlang", "erl");


    private String type;
    private String ext;

    LanguageExt(String type, String ext) {
        this.type = type;
        this.ext = ext;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getExt() {
        return ext;
    }

    public void setExt(String ext) {
        this.ext = ext;
    }


    public static String getTypeByExt(String ext) {
        return Arrays.stream(values())
                .filter(v -> v.ext.equalsIgnoreCase(ext))
                .findFirst()
                .map(lang -> lang.type)
                .orElse(null);
    }

    public static String getExtByType(String type) {
        return Arrays.stream(values())
                .filter(v -> v.type.equalsIgnoreCase(type))
                .findFirst()
                .map(lang -> lang.ext)
                .orElse(null);
    }

}
