package com.srdcloud.ideplugin.general.enums

import com.srdcloud.ideplugin.general.constants.Constants

enum class Language(val extensions: List<String>) {
    Java(listOf("java")),
    Kotlin(listOf("kt")),
    Cpp(listOf("cpp", "cxx", "cc")),
    C(listOf("c")),
    CHeader(listOf("h")),
    Python(listOf("py")),
    JavaScript(listOf("js")),
    TypeScript(listOf("ts")),
    PHP(listOf("php")),
    Swift(listOf("swift")),
    Ruby(listOf("rb")),
    Scala(listOf("scala")),
    Go(listOf("go")),
    Rust(listOf("rs")),
    Dart(listOf("dart")),
    HTML(listOf("html")),
    CSS(listOf("css")),
    Sass(listOf("scss")),
    Less(listOf("less")),
    XML(listOf("xml")),
    JSON(listOf("json")),
    YAML(listOf("yaml")),
    SQL(listOf("sql")),
    ShellScript(listOf("sh")),
    BatchScript(listOf("bat")),
    PowerShellScript(listOf("ps1")),
    Groovy(listOf("groovy")),
    Perl(listOf("pl"));

    companion object {
        fun detectLanguageName(fileName: String): String =
            values().find { it.extensions.any { ext -> fileName.endsWith(".$ext") } }?.name ?: Constants.UNKNOWN
    }
}