package com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc;


import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc.domain.RelativeCodeObject;

/**
 * 跨文件代码关联查找器接口，为不同代码语言类型提供方法抽象
 * 实现功能：
 * 1. 针对文件级别进行抽象语法树解析，解析文件内所有该语言的主要语法对象，如java的class等，并将结果存入全局缓存索引中
 * 2. 针对代码文件中光标位置进行语法树遍历，查找并返回光标所在位置的所有跨文件关联对象信息
 */
public interface IRelativeCodeFinder {

    /**
     * 解析指定代码文件中的代码对象信息
     * @param fileName 需要解析的代码文件路径，为文件在代码工程中的完整路径名，如:/src/main/java/com/example/main.java
     * @param codeFileContent 代码文件内容，字符串形式的源代码内容
     */
    void ParseFile(String fileName, String codeFileContent);


    /**
     * 按指定的行列号，所搜当前位置代码在整个代码项目中的相关代码对象
     * @param codeFilePath 当前代码文件在代码项目中的路径
     * @param codeFileContent 当前最新代码文件内容
     * @param line 指定位置的行号，0表示文件开始的第一行，依此类推
     * @param column 指定位置的列号，0表示行的开始位置，依此类推
     * @return 搜索到的关联代码对象
     */
    RelativeCodeObject FindRelativeObject(String codeFilePath, String codeFileContent,
                                          int line, int column);


}
