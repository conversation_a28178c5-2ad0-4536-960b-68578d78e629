package com.srdcloud.ideplugin.codenatural

import com.intellij.codeInsight.hint.HintManagerImpl.ActionToIgnore
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.DataContext
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.editor.Caret
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.actionSystem.EditorAction
import com.intellij.openapi.editor.actionSystem.EditorWriteActionHandler
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiDirectory
import com.intellij.psi.PsiFile
import com.intellij.psi.PsiFileFactory
import com.intellij.psi.PsiManager
import com.srdcloud.ideplugin.codenatural.HighlightState.Companion.getHighlightText
import com.srdcloud.ideplugin.codenatural.HighlightState.Companion.initOrGetHighlightState
import com.srdcloud.ideplugin.common.icons.MyIcons
import com.srdcloud.ideplugin.general.enums.ActivityType
import com.srdcloud.ideplugin.service.UserActivityReportService

class QuestionDialogAction : EditorAction(QuestionHandler()), ActionToIgnore {
    class QuestionHandler : EditorWriteActionHandler() {

        override fun executeWriteAction(editor: Editor, caret: Caret?, dataContext: DataContext?) {
            ApplicationManager.getApplication().invokeLater {

                val document = editor.document
                val project = editor.project
                if (project != null && document.text.isNotBlank()) {
                    if (NotifyDialog().showAndGet()) {
                        val currentFile = FileDocumentManager.getInstance().getFile(document) ?: return@invokeLater
                        val directory =
                            PsiManager.getInstance(project).findDirectory(currentFile.parent) ?: return@invokeLater
                        WriteCommandAction.runWriteCommandAction(project) {
                            val file = createEmptyFile(
                                project,
                                currentFile,
                                directory,
                                "NewFile-${System.currentTimeMillis() / 1000}.${currentFile.extension}"
                            )
                            FileEditorManager.getInstance(project).openFile(file, true)
                        }
                        val newEditor = FileEditorManager.getInstance(project).selectedTextEditor ?: editor
                        val dialog = QuestionDialog(newEditor)
                        dialog.show()
                    }
                    return@invokeLater
                }

                val dialog = QuestionDialog(editor)
                dialog.show()
            }
        }

        override fun isEnabledForCaret(editor: Editor, caret: Caret, dataContext: DataContext?): Boolean {
            return editor.initOrGetHighlightState().getCurrentHighlight(caret) == null
        }

        private fun createEmptyFile(
            project: Project?,
            currentFile: VirtualFile,
            directory: PsiDirectory,
            name: String
        ): VirtualFile {
            val file = PsiFileFactory.getInstance(project).createFileFromText(name, currentFile.fileType, "")
            val newFile = directory.add(file) as PsiFile
            return newFile.virtualFile
        }
    }
}

class NLCSelectAction : NLCAction(true)
class NLCDeleteAction : NLCAction(false)

open class NLCAction(private val confirm: Boolean) : EditorAction(HighlightHandler(confirm)), ActionToIgnore {
    class HighlightHandler(private val confirm: Boolean) : EditorWriteActionHandler() {
        override fun executeWriteAction(editor: Editor, caret: Caret?, dataContext: DataContext?) {
            val state = editor.initOrGetHighlightState()
            state.getCurrentHighlight(editor.caretModel.primaryCaret)?.let { highlight ->
                if (!confirm) {
                    editor.document.deleteString(highlight.startOffset, highlight.endOffset)
                } else {
                    UserActivityReportService.codeActivityReport(
                        ActivityType.CODE_CHAT_ACCEPTED,
                        editor.project,
                        editor.getHighlightText(highlight),
                        null
                    );
                }
                editor.markupModel.removeHighlighter(highlight)
                state.highlights -= highlight
            }
        }
    }

    override fun update(e: AnActionEvent) {
        val presentation = e.presentation
        if (confirm) {
            presentation.setIcon(MyIcons.NLC_Select)
        } else {
            presentation.setIcon(MyIcons.NLC_Delete)
        }
        super.update(e)
    }
}