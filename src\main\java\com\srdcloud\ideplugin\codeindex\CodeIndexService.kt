package com.srdcloud.ideplugin.codeindex

import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.srdcloud.ideplugin.agent.AgentManager
import com.srdcloud.ideplugin.general.constants.AgentNameConstant
import com.srdcloud.ideplugin.general.utils.DebugLogUtil
import com.srdcloud.ideplugin.general.utils.IdeUtil
import com.srdcloud.ideplugin.statusbar.Notify.Companion.updateStatusNotify
import kotlinx.datetime.LocalTime
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicReference
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.locks.ReentrantLock
import javax.swing.SwingWorker
import kotlin.concurrent.withLock

class CodeIndexService(private val project: Project) {
    private val logger = Logger.getInstance(CodeIndexService::class.java)
    private val status = AtomicReference<IndexStatus>(IndexStatus.IDLE)
    private val indexProgress = AtomicInteger(0)
    private val scheduledExecutor: ScheduledExecutorService = Executors.newSingleThreadScheduledExecutor()
    private var reindexInterval: Long = 0
    private val lock = ReentrantLock()

    enum class IndexStatus {
        IDLE,
        INDEXING,
        DONE
    }

    fun startIndexing(intervalMinutes: Long) {
        lock.withLock {
            reindexInterval = intervalMinutes
            if (status.get() == IndexStatus.IDLE) {
                // 防止占用ui线程
                val worker = object : SwingWorker<String, Void>() {
                    override fun doInBackground(): String {
                        triggerIndexing()
                        return "Task Completed"
                    }
                }
                worker.execute()
            }
        }
    }

    private fun triggerIndexing() {
        DebugLogUtil.info("[cf] triggerIndexing")
        if (status.get() == IndexStatus.INDEXING) {
            return
        }

        status.set(IndexStatus.INDEXING)
        indexProgress.set(0)
        updateStatusNotify()

        val agentCommClient = AgentManager.getInstance(project).getAgentCommClient(AgentNameConstant.CORE_AGENT)
            ?: throw IllegalStateException("Core agent communication client not found")

        logger.info("[cf] request:index/forceReIndex")

        agentCommClient.request(
            "index/forceReIndex",
             project.basePath,
            null
        ) { response ->
            handleIndexProgress(response)
        }
    }

    private fun handleIndexProgress(response: Any?) {
        if (response !is Map<*, *>) {
            return
        }

        DebugLogUtil.info("[cf] handleIndexProgress")

//        val progress = (response["progress"] as? String)?.toDoubleOrNull() ?: return
//        val desc = response["desc"] as? String ?: return
        val status = response["status"] as? String ?: return

        DebugLogUtil.info("[cf]status:${status}")

        if ("done" == status || "failed" == status) {
            this.status.set(IndexStatus.IDLE)
            scheduleNextIndexing()
        }
    }

    private fun scheduleNextIndexing() {
        logger.info("[cf] scheduleNextIndexing")
        if (reindexInterval > 0) {
            scheduledExecutor.schedule(
                { triggerIndexing() },
                reindexInterval,
                TimeUnit.MINUTES
            )
        }
    }

    fun shutdown() {
        scheduledExecutor.shutdown()
        try {
            if (!scheduledExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduledExecutor.shutdownNow()
            }
        } catch (e: InterruptedException) {
            scheduledExecutor.shutdownNow()
            Thread.currentThread().interrupt()
        }
    }

    companion object {
        fun getInstance(project: Project): CodeIndexService {
            return project.getService(CodeIndexService::class.java)
        }

        fun getIndexStatus(project: Project): IndexStatus {
            return getInstance(project).status.get()
        }

        fun getIndexProgress(project: Project): Int {
            return getInstance(project).indexProgress.get()
        }

        fun setIndexStatus(project: Project, status: IndexStatus) {
            getInstance(project).status.set(status)
        }
        
        fun setIndexProgress(project: Project, progress: Int) {
            val validProgress = progress.coerceIn(0, 100) // 确保进度值在0-100范围内
            getInstance(project).indexProgress.set(validProgress)
        }
    }
}