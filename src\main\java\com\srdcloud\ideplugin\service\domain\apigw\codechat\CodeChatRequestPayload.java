package com.srdcloud.ideplugin.service.domain.apigw.codechat;

import com.intellij.openapi.application.ApplicationInfo;
import com.intellij.openapi.application.ApplicationNamesInfo;

import java.io.Serializable;
import java.util.List;

/**
 * @author: yangy
 * @date: 2023/7/23 20:23
 * @Desc
 */
public class CodeChatRequestPayload implements Serializable {

    private String clientType;
    private String clientVersion;
    private String clientPlatform;
    private List<String> gitUrls;
    private CodeMessage messages;

    public String getClientType() {
        return clientType;
    }

    public void setClientType(String clientType) {
        this.clientType = ApplicationNamesInfo.getInstance().getProductName();
        this.clientVersion = ApplicationInfo.getInstance().getFullVersion();
    }

    public CodeMessage getMessages() {
        return messages;
    }

    public void setMessages(CodeMessage messages) {
        this.messages = messages;
    }

    public String getClientVersion() {
        return clientVersion;
    }

    public void setClientVersion(String clientVersion) {
        this.clientVersion = clientVersion;
    }

    public String getClientPlatform() {
        return clientPlatform;
    }

    public void setClientPlatform(String clientPlatform) {
        this.clientPlatform = clientPlatform;
    }

    public List<String> getGitUrls() {
        return gitUrls;
    }

    public void setGitUrls(List<String> gitUrls) {
        this.gitUrls = gitUrls;
    }
}
