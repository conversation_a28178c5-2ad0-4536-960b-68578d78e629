package com.srdcloud.ideplugin.webview.treesitter;

import com.intellij.openapi.Disposable;
import com.intellij.openapi.project.Project;
import com.intellij.ui.jcef.JBCefApp;
import com.intellij.ui.jcef.JBCefBrowser;
import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc.CodeObjectIndexSystem;
import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc.CodeObjectIndexSystemUpdateTask;
import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc.ProjectCodeIndexer;
import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc.domain.RelativeCodeObject;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.utils.*;
import com.srdcloud.ideplugin.webview.base.WebView;
import org.apache.commons.lang3.StringUtils;
import org.cef.CefSettings;
import org.cef.browser.CefBrowser;
import org.cef.browser.CefFrame;
import org.cef.browser.CefMessageRouter;
import org.cef.callback.CefQueryCallback;
import org.cef.handler.CefDisplayHandlerAdapter;
import org.cef.handler.CefLoadHandler;
import org.cef.handler.CefMessageRouterHandlerAdapter;
import org.cef.network.CefRequest;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * WebviewTreeSitter类用于通过Web视图与JavaScript交互，
 * 该类用于交互Web-Tree-Sitter和Java之间的调用
 */
@Deprecated
public class WebviewTreeSitter implements Disposable {
    private static final Logger logger = LoggerFactory.getLogger(WebviewTreeSitter.class);

    private Project project;

    // 持有的webview实例
    private WebView webView = null;

    // 资源载入路径
    private final static String RESOURCE_DOMAIN_NAME = "tree-sitter";
    private final static String RESOURCE_URL = "http://" + RESOURCE_DOMAIN_NAME + "/index.html";
//    private final static String TREE_SITTER_URL = "http://localhost:5001";

    // 通道配置，管理通讯通道命名
    private static final CefMessageRouter.CefMessageRouterConfig config = new CefMessageRouter.CefMessageRouterConfig("treeSitterQuery", "cancel");

    // webview实例是否已经被加载成功，加载成功后才可以通信：2020版本需要展开ToolWindow才能触发懒加载；2022之后会自动触发加载
    private boolean loaded = false;

    // 静态资源是否已加载成功，可以开始进行业务消息通信
    private boolean webviewContentLoaded = false;


    // 跨文件缓存系统
    private CodeObjectIndexSystem codeObjectIndexSystem;

    // 关联文件对象
    private RelativeCodeObject relativeCodeObject;
    private volatile boolean isNewObject = false;

    // 文件解析计数器（性能统计）
    private AtomicInteger parseFileCounter = new AtomicInteger(0);

    private WebviewTreeSitter(Project project) {
        // 判断是否支持JCEF
        if (!JBCefApp.isSupported()) {
            loaded = false;
            logger.error("[cf]WebviewTreeSitter init fail,JCEF is not supported.");
            return;
        }

        this.project = project;
    }

    /**
     * 获取WebviewTreeSitter的项目级别单例实例
     */
    public static WebviewTreeSitter getInstance(@NotNull Project project) {
        return project.getService(WebviewTreeSitter.class);
    }

    /**
     * 进行跨文件关联webview资源加载
     */
    public void loadWebview() {
        logger.info("load WebviewTreeSitter...");
        DebugLogUtil.println("load WebviewTreeSitter...");

        // 判断是否支持JCEF
        if (!JBCefApp.isSupported()) {
            loaded = false;
            logger.error("[cf]WebviewTreeSitter loadWebview skip,JCEF is not supported.");
            return;
        }

        if (webView == null) {
            DebugLogUtil.println("WebviewTreeSitter createBrowser and loadURL.");

            // 创建业务webview实例
            webView = new WebView(RESOURCE_DOMAIN_NAME);

            //---进行自定义设置---
            // 注册浏览器加载生命周期监听
            addLoadListener();

            // 浏览器console日志监听
            addConsoleListener();

            // 注册业务消息监听与处理逻辑
            addMessageListener();


            DebugLogUtil.println("load WebviewTreeSitter Resource...");
            logger.info("load WebviewTreeSitter Resource...");

            // 触发webview即时创建，保证可以在项目启动后派上用场
            webView.getBrowser().getCefBrowser().createImmediately();
            // 加载webview资源
            webView.getBrowser().loadURL(RESOURCE_URL);
        }
    }

    /**
     * 获取Web组件，调试用
     */
    public JComponent getTreeSitterWebViewComponent() {
        if (webView == null) {
            return null;
        }
        JComponent webViewComponent = webView.getBrowser().getComponent();
        webViewComponent.setPreferredSize(new Dimension(1, 1));
        return webViewComponent;
    }

    /**
     * 消息监听器，处理来自浏览器回调的消息
     */
    private void addMessageListener() {
        JBCefBrowser browser = webView.getBrowser();
        codeObjectIndexSystem = ProjectCodeIndexer.getInstance(project).getCodeObjectIndexSystem();

        // 创建对应路由消息控制器
        CefMessageRouter messageRouter = CefMessageRouter.create(config, new CefMessageRouterHandlerAdapter() {

            // 处理收到的JavaScript消息
            @Override
            public boolean onQuery(CefBrowser browser, CefFrame frame, long queryId, String request, boolean persistent, CefQueryCallback callback) {
                if (StringUtils.isBlank(request)) {
                    return true;
                }

                try {
                    //fixme：调试用
                    //DebugLogUtil.println(String.format("WebviewTreeSitter receive from webview: %s", request));

//                    DebugLogUtil.info(String.format("WebviewTreeSitter receive from webview: %s", request));

                    WebviewTreeSitterMessage requestMessage = JsonUtil.getInstance().fromJson(request, WebviewTreeSitterMessage.class);
                    if (Objects.isNull(requestMessage)) {
                        logger.warn("[cf rlcc] onQuery error: WebMessage decode is null,request: {}", request);
                        return true;
                    }

                    switch (requestMessage.getType()) {
                        // webviewTreeSitter初始化完成消息
                        case "webviewTreeSitterLoaded":
                            loaded = true;
                            webviewContentLoaded = true;
                            DebugLogUtil.println("WebviewTreeSitter webviewContentLoaded.");
                            logger.info("WebviewTreeSitter webviewContentLoaded.");

                            // 设置webview主题颜色
                            if (UIUtil.judgeBackgroudDarkTheme()) {
                                getWebView().sentMessageToWebview("{type: 'backgroundColor', color: 'dark'}");
                            } else {
                                getWebView().sentMessageToWebview("{type: 'backgroundColor', color: 'white'}");
                            }

                            // Go跨文件关联：第一遍遍历，建立文件路径与go moudle的映射关系
                            CodeObjectIndexSystemUpdateTask.getInstance(project).buildGoModuleMapping(project);
                            // 遍历项目文件，建立缓存
                            CodeObjectIndexSystemUpdateTask.getInstance(project).initCodeObjectIndexSystem(project);
                            break;
                        // 协助Webview侧解析工作：查询某包下是否有目标对象（已废弃，改为走Webview侧本地查询）
                        case "queryObjectInPackage":
                            if (codeObjectIndexSystem.queryObjectInPackage(requestMessage.getPackagePath(), requestMessage.getObjectName())) {
                                callback.success("true");
                            } else {
                                callback.success("false");
                            }
                            return true;

                        // 更新文件解析缓存
                        case "updateCodeObjectIndexSystem":
                            codeObjectIndexSystem.updateByFile(requestMessage.getPackagePath(), requestMessage.getFileName(), requestMessage.getObjectsToUpdate());
                            // 计数
                            if (EnvUtil.checkDebugAble()) {
//                                DebugLogUtil.info("updateFileCache,file count:" + parseFileCounter.incrementAndGet());
                            }
                            break;

                        // 找到关联文件，回传关联对象信息，拼接缓存系统查找key
                        case "findRelativeObjectReturned":
                            isNewObject = true;
                            // 从缓存系统中反查关联对象内容
                            relativeCodeObject = codeObjectIndexSystem.queryRelativeCodeObjectByPkgObject(requestMessage.getLanguage(), requestMessage.getPackagePath(), requestMessage.getObjectName());
                            callback.success(JsonUtil.getInstance().toJson(relativeCodeObject));
                            return true;

                        // 未找到关联文件对象
                        case "findRelativeObjectNull":
                            isNewObject = true;
                            relativeCodeObject = null;
                            callback.success("true");
                            return true;

                        // webview侧逻辑执行出错
                        case "error":
//                            logger.warn("[cf rlcc] web tree-sitter error:" + requestMessage.getErrorMessage());
                    }

                    // 回复js端：success 或者 failure
                    callback.success("query success");
                } catch (Exception e) {
                    callback.failure(0, "query fail");
//                    logger.warn("[cf rlcc] Web Tree-Sitter Query error: can not process query message");
                }

                return true;
            }

            // Web视图信息回传通道关闭消息
            @Override
            public void onQueryCanceled(CefBrowser browser, CefFrame frame, long queryId) {
                logger.info("[cf rlcc] WebviewTreeSitter onQueryCanceled canceled.");

                loaded = false;
            }
        });

        browser.getCefBrowser().getClient().addMessageRouter(messageRouter);
    }

    /**
     * 添加webview browser实例加载过程监听器，监控加载状态
     */
    private void addLoadListener() {
        JBCefBrowser browser = webView.getBrowser();
        browser.getJBCefClient().addLoadHandler(new CefLoadHandler() {

            // Web视图状态改变
            @Override
            public void onLoadingStateChange(CefBrowser browser, boolean isLoading, boolean canGoBack, boolean canGoForward) {
            }

            // Web视图开始进入加载状态，该状态下Web视图还无法处理数据
            @Override
            public void onLoadStart(CefBrowser browser, CefFrame frame, CefRequest.TransitionType transitionType) {
            }

            // Web视图加载完成
            @Override
            public void onLoadEnd(CefBrowser browser, CefFrame frame, int httpStatusCode) {
                DebugLogUtil.println("WebviewTreeSitter loaded,httpStatusCode:" + httpStatusCode);
                logger.info("WebviewTreeSitter loaded,httpStatusCode:{}", httpStatusCode);
                // browser加载成功，可以进行后续通信
                loaded = true;
            }

            // Web视图加载发生错误
            @Override
            public void onLoadError(CefBrowser browser, CefFrame frame, ErrorCode errorCode, String errorText, String failedUrl) {
                logger.warn("[cf rlcc] WebviewTreeSitter error,failedUrl:{},errorCode:{},errorText:{}", failedUrl, errorCode.name(), errorText);
            }
        }, browser.getCefBrowser());
    }

    /**
     * 控制台输出监听器，处理来自webview的控制台输出信息，用于调试使用
     */
    private void addConsoleListener() {
        JBCefBrowser browser = webView.getBrowser();
        browser.getJBCefClient().getCefClient().addDisplayHandler(new CefDisplayHandlerAdapter() {
            @Override
            public boolean onConsoleMessage(CefBrowser browser, CefSettings.LogSeverity level, String message, String source, int line) {
                if (CefSettings.LogSeverity.LOGSEVERITY_WARNING == level || CefSettings.LogSeverity.LOGSEVERITY_ERROR == level || CefSettings.LogSeverity.LOGSEVERITY_FATAL == level || CefSettings.LogSeverity.LOGSEVERITY_DISABLE == level) {
//                    logger.warn("[cf] WebviewTreeSitter console log,{}:{}", level.name(), message);
                } else {
                    if (EnvUtil.checkWebviewLogAble()) {
                        DebugLogUtil.info("WebviewTreeSitter console log: " + message);
                    }
                }
                return super.onConsoleMessage(browser, level, message, source, line);
            }
        });
    }

    /**
     * 发送消息到webview，并伴随加载检查
     *
     * @param message
     */
    public boolean sentMessageToWebviewWithLoadCheck(String message) {
        if (Objects.nonNull(webView) && isLoaded()) {
            webView.sentMessageToWebview(message);
            return true;
        }
//        logger.warn("[cf] WebviewTreeSitter sentMessageToWebviewWithLoadCheck fail,webview not loaded,message:{}", message);
        return false;
    }

    /**
     * 建立go mod映射缓存
     */
    public void sendGoModuleMappingMessageToWebview(String moduleName, String moduleDirPath) {
        final WebviewTreeSitterMessage message = new WebviewTreeSitterMessage("goModMapping", UUID.randomUUID().toString());
        message.setModuleName(moduleName);
        message.setModuleDirPath(moduleDirPath);

        // 向Web视图发送数据
        sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(message));
    }

    /**
     * 发送消息到Web视图，
     * 该方法为双向信息通道，用于向Web发送解析代码请求
     *
     * @param data 消息内容
     */
    private void sendParseCodeMessageToWebview(String path, String data, String languageExt) {
        String language = FileUtil.getLanguageTypeByExt(languageExt);

        final WebviewTreeSitterMessage message = new WebviewTreeSitterMessage("parseCodeFileRequest", path, data, UUID.randomUUID().toString(), language);
        // 向Web视图发送数据
        sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(message));
    }

    // 该方法为双向信息通道，用于向Web发送寻找相关实例请求
    private synchronized void sendFindRelativeObjectMessageToWebview(String path, String data, int row, int col, String languageExt, String goModMapJson) {
        try {
            String language = FileUtil.getLanguageTypeByExt(languageExt);

            final WebviewTreeSitterMessage message = new WebviewTreeSitterMessage("findRelativeObjectRequest", row, col, path, data, UUID.randomUUID().toString(), language, goModMapJson);
            // 向Web视图发送数据
            webView.sentMessageToWebview(JsonUtil.getInstance().toJson(message));

            // 阻塞等待解析结果返回，模拟同步返回效果
            int count = 0;
            int maxCount = Constants.Webview_msg_timeout / Constants.Webview_msg_interval;
            while (count < maxCount && !isNewObject) {
                Thread.sleep(Constants.Webview_msg_interval);
                count += 1;
            }
            // 超时没返回的，打log监听
            if (count >= maxCount) {
                logger.warn("[cf rlcc] sendFindRelativeObjectMessageToWebview timeout:{} ms", count * Constants.Webview_msg_interval);
            }
            DebugLogUtil.println(String.format("[cf rlcc debug] FindRelativeObject in: %s ms,relativeObject:%s,filePath:%s,row:%s,col:%s", count * Constants.Webview_msg_interval, JsonUtil.getInstance().toJson(relativeCodeObject), path, row, col));
        } catch (Exception e) {
            logger.warn("[cf rlcc] sendFindRelativeObjectMessageToWebview error:");
            e.printStackTrace();
        }
    }


    /**
     * 解析代码文件
     */
    public void ParseFile(String filePath, String code, String languageExt) {
        sendParseCodeMessageToWebview(filePath, code, languageExt);
    }

    /**
     * 查找关联文件
     */
    public RelativeCodeObject findRelativeObject(String fileName, String code, int row, int col, String languageExt, String goModMapJson) {

        sendFindRelativeObjectMessageToWebview(fileName, code, row, col, languageExt, goModMapJson);

        // 获取得到的relative object
        if (isNewObject) {
            isNewObject = false;
            return relativeCodeObject;
        } else {
            return null;
        }
    }

    public WebView getWebView() {
        return webView;
    }

    public Project getProject() {
        return project;
    }

    public boolean isLoaded() {
        return loaded;
    }

    @Override
    public void dispose() {
        DebugLogUtil.println("WebviewTreeSitter disposed.");
        if (webView != null) {
            webView.dispose();
        }
        project = null;
        webView = null;
        loaded = false;
    }
}
