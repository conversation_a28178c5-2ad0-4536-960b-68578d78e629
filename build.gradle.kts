import java.io.FileOutputStream
import java.io.OutputStreamWriter
import java.nio.charset.StandardCharsets
import org.jetbrains.changelog.markdownToHTML
import java.util.*

plugins {
    id("java")
    id("org.jetbrains.kotlin.jvm") version "1.8.21"
    id("org.jetbrains.intellij") version "1.17.3"
    id("org.sonarqube") version "3.0"
    id("org.jetbrains.changelog") version "2.2.0"

}

group = "com.srdcloud"
// 构建版本号: _beta 是开发环境、_test是测试环境、不带后缀是生产环境
version = "1.5.3_test"

// 流水线构建脚本指令：./gradlew injectPipelineProperties buildPlugin -PinnerVersion=${innerVersion} -PserverDomain=${serverDomain} -PoauthClientId=${oauthClientId} -PoauthClientSecret=${oauthClientSecret} -PbuildVersion=${version}  -Dorg.gradle.internal.http.socketTimeout=1800000 -Dorg.gradle.internal.http.connectionTimeout=1800000
// 内外部版本标识
var innerVersion = "true"
// 调试模式标识:默认关闭（仅针对报障用户，一对一排查问题时构建调试模式包）
var debugMode = "false"
var webviewLog = "false"

// TreeSitter挂载
var treeSitterMount = "false"

// 鉴权ClientId
var oauthClientId = "2220476020a3tfk6llee"
// 鉴权秘钥
var oauthClientSecret = "2220476020b1qOplkzHnKCsRJHPUjZldR5c9oxvY"
// 后端通信网关域名：不同环境注入不同域名
var serverDomain = "www.srdcloud.cn"

// 加载流水线属性
val pipelineProps = Properties().apply {
    file("$projectDir/src/main/resources/bundles/pipeline.properties").inputStream().use {
        load(it)
    }
}
// true 为secidea，false为 srd
val isSec = if (pipelineProps["isSec"] == "true") true else false

// 存储 srd / secidea区分的根目录
val metaInfoDir = "$projectDir/src/main/resources/META-INF"
val platformSpecificDir = "$projectDir/src/main/resources/META-INF/platform"
val secConfigInfo = "$platformSpecificDir/sec"
val srdConfigInfo = "$platformSpecificDir/srd"

// 构建版本号
version = if (isSec) "3.5.0-c10" else "1.5.0_beta"

if (project.hasProperty("buildVersion")) {
    version = project.property("buildVersion").toString()
}

repositories {
    maven {
        setUrl("https://gz01-srdart.srdcloud.cn/maven/srdcloud/srdcloud-aicode-release-maven-virtual")
        credentials {
            username = "yegj"
            password = "4bc6c2f8251689ef97828ed6b66a0e36"
        }
    }
    mavenCentral()
}

// Configure Gradle IntelliJ Plugin
// Read more: https://plugins.jetbrains.com/docs/intellij/tools-gradle-intellij-plugin.html
intellij {
    // 上下限版本调试，需要搭配切换JDK
    version.set("2022.3")
//    version.set("2023.2")
//    version.set("2024.2")

    type.set("IC") // Target IDE Platform
    updateSinceUntilBuild.set(false)
    plugins.set(listOf("com.intellij.java","Git4Idea","terminal"))
}

dependencies {
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.1")
    implementation("com.vladsch.flexmark:flexmark-all:0.64.8")
    implementation("org.apache.commons:commons-text:1.10.0")
    implementation("org.java-websocket:Java-WebSocket:1.3.8") // 不能直接升级到1.5.0版本，会有依赖冲突导致插件无法加载
    implementation("com.squareup.okhttp3:okhttp:4.12.0")
    implementation("com.squareup.okhttp3:okhttp-sse:4.12.0")
    implementation("com.alibaba:fastjson:2.0.40")
    implementation("org.junit.jupiter:junit-jupiter:5.8.1")
    testImplementation("org.junit.jupiter:junit-jupiter:5.8.1")
}

kotlin {
    jvmToolchain(17)
}

tasks.register<DefaultTask>("injectPipelineProperties") {
    // 从运行参数获取住入值（流水线打包，注入指令）
    if (project.hasProperty("serverDomain")) {
        serverDomain = project.property("serverDomain").toString()
    }
    if (project.hasProperty("oauthClientId")) {
        oauthClientId = project.property("oauthClientId").toString()
    }
    if (project.hasProperty("oauthClientSecret")) {
        oauthClientSecret = project.property("oauthClientSecret").toString()
    }
    if (project.hasProperty("innerVersion")) {
        innerVersion = project.property("innerVersion").toString()
    }
    if (project.hasProperty("debugMode")) {
        debugMode = project.property("debugMode").toString()
    }
    if (project.hasProperty("webviewLog")) {
        webviewLog = project.property("webviewLog").toString()
    }
    if (project.hasProperty("treeSitterMount")) {
        treeSitterMount = project.property("treeSitterMount").toString()
    }

    val propertiesFile = File("src/main/resources/bundles/pipeline.properties")
    // 如果文件不存在，则先创建
    if (!propertiesFile.exists()) {
        propertiesFile.createNewFile()
    }

    // 写入配置内容
    FileOutputStream(propertiesFile).use { fos ->
        OutputStreamWriter(fos, StandardCharsets.UTF_8).use { writer ->
            writer.write("serverDomain=$serverDomain")
            writer.write("\n")
            writer.write("oauthClientId=$oauthClientId")
            writer.write("\n")
            writer.write("oauthClientSecret=$oauthClientSecret")
            writer.write("\n")
            writer.write("innerVersion=$innerVersion")
            writer.write("\n")
            writer.write("debugMode=$debugMode")
            writer.write("\n")
            writer.write("webviewLog=$webviewLog")
            writer.write("\n")
            writer.write("treeSitterMount=$treeSitterMount")
            writer.write("\n")
            writer.write("isSec=$isSec")
            writer.write("\n")
        }
    }
}

tasks.named("buildPlugin").configure {
    dependsOn("injectPipelineProperties")
}

tasks.named("processResources").configure {
    notCompatibleWithConfigurationCache("cannot serialize")
}

tasks {
    // Set the JVM compatibility versions
    withType<JavaCompile> {
        sourceCompatibility = "17"
        targetCompatibility = "17"
        options.encoding = "UTF-8"
    }

    processResources {
        // 定义需要拷贝的图标列表
        val iconFiles = listOf("pluginIcon.svg", "pluginIcon_dark.svg")

        // 根据环境选择源目录
        val sourceDir = if (isSec) {
            file(secConfigInfo)
        } else {
            file(srdConfigInfo)
        }

        // 将指定的文件复制到 META-INF 目录
        doFirst {
            val targetDir = file(metaInfoDir)

            // 逐个处理图标文件
            iconFiles.forEach { fileName ->
                val sourceFile = file("$sourceDir/$fileName")
                val targetFile = file("$targetDir/$fileName")

                // 检查源文件是否存在
                if (!sourceFile.exists()) {
                    println("Warning: File $sourceFile does not exist and will be skipped.")
                    return@forEach
                }

                // 比较文件内容,防止文件相同的时候仍然删除原有存在的meta-inf下的文件
                val sourceHash = sourceFile.length()
                val targetHash = if (targetFile.exists()) targetFile.length() else ""

                if (sourceHash == targetHash) {
                    println("Skipping $fileName: Source and target files are identical.")
                    return@forEach
                }

                // 内容不同，删除旧文件并复制新文件
                if (targetFile.exists()) {
                    targetFile.delete()
                    println("Deleted outdated $targetFile")
                }
                sourceFile.copyTo(targetFile, overwrite = true)
                println("Copied $sourceFile to $targetFile")
            }
        }
    }

    patchPluginXml {
        sinceBuild.set("223")  // 最低支持版本：2022.3
        untilBuild.set("251.*") // 最高支持版本：2025.1.*

        // 区分不同配置文件 plugin.xml,changelog,readme介绍 等的存放目录
        if (isSec) {
            pluginXmlFiles.set(listOf(file("$secConfigInfo/plugin.xml")))
            pluginDescription.set(
                File("$secConfigInfo/README.md").readText().lines().run {
                    val start = "<!-- Plugin description start -->"
                    val end = "<!-- Plugin description end -->"
                    if (!containsAll(listOf(start, end))) {
                        throw Throwable("Plugin description section not found in README.md file:\n$start ... $end")
                    }
                    subList(indexOf(start) + 1, indexOf(end))
                }.joinToString("\n").run { markdownToHTML(this) }
            )
            changeNotes.set(File("$secConfigInfo/CHANGELOG.md")
                .readText()
                .lines()
                .joinToString("\n")
                .run { markdownToHTML(this) })
        } else {
            pluginXmlFiles.set(listOf(file("$srdConfigInfo/plugin.xml")))
        }
    }

    signPlugin {
        certificateChain.set(System.getenv("CERTIFICATE_CHAIN"))
        privateKey.set(System.getenv("PRIVATE_KEY"))
        password.set(System.getenv("PRIVATE_KEY_PASSWORD"))
    }

    publishPlugin {
        token.set(System.getenv("PUBLISH_TOKEN"))
    }

    buildSearchableOptions {
        enabled = false
    }
}
