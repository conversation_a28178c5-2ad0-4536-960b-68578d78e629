package com.srdcloud.ideplugin.webview.codechat.composer.request;

import com.srdcloud.ideplugin.webview.base.domain.WebViewCommand;

public class ComposerChatRequest extends WebViewCommand {

    private ComposerChatRequestData data;

    public ComposerChatRequest(String command, ComposerChatRequestData data) {
        this.command = command;
        this.data = data;
    }

    public void setData(ComposerChatRequestData data) {
        this.data = data;
    }

    public ComposerChatRequestData getData() {
        return data;
    }

}
