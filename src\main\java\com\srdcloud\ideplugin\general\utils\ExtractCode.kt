package com.srdcloud.ideplugin.general.utils

/**
 * @author: yangy
 * @date: 2023/9/7 14:17
 * @Desc 从字符串中，提取代码部分内容
 */
class ExtractCode {

    /**
     * 提取第一个代码块
     */
    fun extractFirstCodeBlock(text: String?): String {
        if(text.isNullOrEmpty()){
            return "";
        }
//        thisLogger().info("extractFirstCodeBlock text=====> $text")
        var textBuilder  = StringBuilder(text)
        var count = countCodeBlocks(text)
        if(count ==  0){
            return "";
        }else if(count==1){
            textBuilder.append("```")
        }
        val codeBlockPattern = """```(.*?)```""".toRegex(RegexOption.DOT_MATCHES_ALL)
        val match = codeBlockPattern.find(textBuilder.toString())
        val codeBlockContent = match?.groupValues?.get(1) ?: ""
//        thisLogger().info("extractFirstCodeBlock =====> $codeBlockContent")

        // 使用换行符分割代码块，并移除第一行（语言标识）
        val lines = codeBlockContent.split("\n")
        if (lines.size > 1) {
            return lines.subList(1, lines.size).joinToString("\n")
        } else {
            return ""
        }
    }

    /**
     * 计算文本中包含多少个代码块
     */
    fun countCodeBlocks(text: String): Int {
        val codeBlockPattern = """```""".toRegex()
        val matches = codeBlockPattern.findAll(text)
        return matches.count()
    }

}