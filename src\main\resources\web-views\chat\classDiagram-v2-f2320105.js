import{p as z,d as C,s as P}from"./styles-9a916d00.js";import{l as d,c,h as k,z as q,u as F,p as $,t as I,o as M,j as R}from"./index.js";import{G as H}from"./graph.js";import{r as V}from"./index-3862675e.js";import"./layout.js";import"./clone.js";import"./edges-e0da2a9e.js";import"./createText-2e5e7dd3.js";import"./line.js";import"./array.js";import"./path.js";const T=s=>R.sanitizeText(s,c());let S={dividerMargin:10,padding:5,textHeight:10,curve:void 0};const W=function(s,o,f,a){const t=Object.keys(s);d.info("keys:",t),d.info(s),t.forEach(function(i){var p;var l,r;const e=s[i],y={shape:"rect",id:e.id,domId:e.domId,labelText:T(e.id),labelStyle:"",style:"fill: none; stroke: black",padding:(p=(l=c().flowchart)==null?void 0:l.padding)!=null?p:(r=c().class)==null?void 0:r.padding};o.setNode(e.id,y),B(e.classes,o,f,a,e.id),d.info("setNode",y)})},B=function(s,o,f,a,t){const i=Object.keys(s);d.info("keys:",i),d.info(s),i.filter(l=>s[l].parent==t).forEach(function(l){var x,m;var r,e;const n=s[l],y=n.cssClasses.join(" "),p=$(n.styles),v=(x=n.label)!=null?x:n.id,b=0,h="class_box",u={labelStyle:p.labelStyle,shape:h,labelText:T(v),classData:n,rx:b,ry:b,class:y,style:p.style,id:n.id,domId:n.domId,tooltip:a.db.getTooltip(n.id,t)||"",haveCallback:n.haveCallback,link:n.link,width:n.type==="group"?500:void 0,type:n.type,padding:(m=(r=c().flowchart)==null?void 0:r.padding)!=null?m:(e=c().class)==null?void 0:e.padding};o.setNode(n.id,u),t&&o.setParent(n.id,t),d.info("setNode",u)})},J=function(s,o,f,a){d.info(s),s.forEach(function(t,i){var m;var l,r;const e=t,n="",y={labelStyle:"",style:""},p=e.text,v=0,b="note",h={labelStyle:y.labelStyle,shape:b,labelText:T(p),noteData:e,rx:v,ry:v,class:n,style:y.style,id:e.id,domId:e.id,tooltip:"",type:"note",padding:(m=(l=c().flowchart)==null?void 0:l.padding)!=null?m:(r=c().class)==null?void 0:r.padding};if(o.setNode(e.id,h),d.info("setNode",h),!e.class||!(e.class in a))return;const u=f+i,x={id:`edgeNote${u}`,classes:"relation",pattern:"dotted",arrowhead:"none",startLabelRight:"",endLabelLeft:"",arrowTypeStart:"none",arrowTypeEnd:"none",style:"fill:none",labelStyle:"",curve:I(S.curve,M)};o.setEdge(e.id,e.class,x,u)})},K=function(s,o){const f=c().flowchart;let a=0;s.forEach(function(t){var r;var i;a++;const l={classes:"relation",pattern:t.relation.lineType==1?"dashed":"solid",id:`id_${t.id1}_${t.id2}_${a}`,arrowhead:t.type==="arrow_open"?"none":"normal",startLabelRight:t.relationTitle1==="none"?"":t.relationTitle1,endLabelLeft:t.relationTitle2==="none"?"":t.relationTitle2,arrowTypeStart:A(t.relation.type1),arrowTypeEnd:A(t.relation.type2),style:"fill:none",labelStyle:"",curve:I(f==null?void 0:f.curve,M)};if(d.info(l,t),t.style!==void 0){const e=$(t.style);l.style=e.style,l.labelStyle=e.labelStyle}t.text=t.title,t.text===void 0?t.style!==void 0&&(l.arrowheadStyle="fill: #333"):(l.arrowheadStyle="fill: #333",l.labelpos="c",((r=(i=c().flowchart)==null?void 0:i.htmlLabels)!=null?r:c().htmlLabels)?(l.labelType="html",l.label='<span class="edgeLabel">'+t.text+"</span>"):(l.labelType="text",l.label=t.text.replace(R.lineBreakRegex,`
`),t.style===void 0&&(l.style=l.style||"stroke: #333; stroke-width: 1.5px;fill:none"),l.labelStyle=l.labelStyle.replace("color:","fill:"))),o.setEdge(t.id1,t.id2,l,a)})},Q=function(s){S={...S,...s}},U=async function(s,o,f,a){var m,L,_,N;d.info("Drawing class - ",o);const t=(m=c().flowchart)!=null?m:c().class,i=c().securityLevel;d.info("config:",t);const l=(L=t==null?void 0:t.nodeSpacing)!=null?L:50,r=(_=t==null?void 0:t.rankSpacing)!=null?_:50,e=new H({multigraph:!0,compound:!0}).setGraph({rankdir:a.db.getDirection(),nodesep:l,ranksep:r,marginx:8,marginy:8}).setDefaultEdgeLabel(function(){return{}}),n=a.db.getNamespaces(),y=a.db.getClasses(),p=a.db.getRelations(),v=a.db.getNotes();d.info(p),W(n,e,o,a),B(y,e,o,a),K(p,e),J(v,e,p.length+1,y);let b;i==="sandbox"&&(b=k("#i"+o));const h=i==="sandbox"?k(b.nodes()[0].contentDocument.body):k("body"),u=h.select(`[id="${o}"]`),x=h.select("#"+o+" g");if(await V(x,e,["aggregation","extension","composition","dependency","lollipop"],"classDiagram",o),q.insertTitle(u,"classTitleText",(N=t==null?void 0:t.titleTopMargin)!=null?N:5,a.db.getDiagramTitle()),F(e,u,t==null?void 0:t.diagramPadding,t==null?void 0:t.useMaxWidth),!(t!=null&&t.htmlLabels)){const D=i==="sandbox"?b.nodes()[0].contentDocument:document,G=D.querySelectorAll('[id="'+o+'"] .edgeLabel .label');for(const w of G){const E=w.getBBox(),g=D.createElementNS("http://www.w3.org/2000/svg","rect");g.setAttribute("rx",0),g.setAttribute("ry",0),g.setAttribute("width",E.width),g.setAttribute("height",E.height),w.insertBefore(g,w.firstChild)}}};function A(s){let o;switch(s){case 0:o="aggregation";break;case 1:o="extension";break;case 2:o="composition";break;case 3:o="dependency";break;case 4:o="lollipop";break;default:o="none"}return o}const X={setConf:Q,draw:U},it={parser:z,db:C,renderer:X,styles:P,init:s=>{s.class||(s.class={}),s.class.arrowMarkerAbsolute=s.arrowMarkerAbsolute,C.clear()}};export{it as diagram};
