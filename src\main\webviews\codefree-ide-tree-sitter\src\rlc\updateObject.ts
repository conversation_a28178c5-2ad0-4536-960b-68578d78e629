export class UpdateObject {
    constructor(
      public packageSuffix: string, // Java内部类需要额外拼接的包路径：一般是某个外部类名
      public objectType: string, // 对应treesitter中的Parser.SyntaxNode的type类型，需要原值透传
      public objectName: string, // 具体的关联对象名，如：类名、方法名
      public simpleText: string // 关联对象的内容
    ) {}
  
    getPackageSuffix(): string {
      return this.packageSuffix;
    }

    getObjectType(): string {
        return this.objectType;
    }
  
    getObjectName(): string {
      return this.objectName;
    }
  
    getSimpleText(): string {
      return this.simpleText;
    }
  }