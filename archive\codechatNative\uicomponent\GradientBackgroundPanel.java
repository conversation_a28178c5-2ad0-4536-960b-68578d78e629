package com.srdcloud.ideplugin.assistant.codechatNative.uicomponent;

import javax.swing.*;
import java.awt.*;
import java.awt.geom.Point2D;

/**
 * @author: yangy
 * @date: 2023/8/30 10:13
 * @Desc 渐变背景panel包装器
 */
public class GradientBackgroundPanel extends JPanel {

    public Color startColor = null;

    public Color endColor = null;

    public float startColorFraction = 0.0f;

    public float endColorFraction = 1.0f;

    public GradientBackgroundPanel(LayoutManager layout,String startColorDecode,String endColorDecode,float startColorFraction,float endColorFraction) {
        super(layout, true);
        if(startColorDecode!=null){
            this.startColor = Color.decode(startColorDecode);
        }
        if(endColorDecode!=null){
            this.endColor = Color.decode(endColorDecode);
        }
        this.startColorFraction = startColorFraction;
        this.endColorFraction = endColorFraction;
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        if(startColor !=null && endColor!=null){
            int width = getWidth();
            int height = getHeight();

            // 创建一个渐变画笔
            LinearGradientPaint gradientPaint = new LinearGradientPaint(
                    new Point2D.Double(0, 0),
                    new Point2D.Double(0, height),
                    new float[]{startColorFraction, endColorFraction},
                    new Color[]{startColor, endColor}
            );

            Graphics2D g2d = (Graphics2D) g;
            g2d.setPaint(gradientPaint);

            // 填充整个面板
            g2d.fillRect(0, 0, width, height);
        }

    }
}
