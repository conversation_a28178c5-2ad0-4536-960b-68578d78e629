package com.srdcloud.ideplugin.codecomplete.handle.codeprovider.strategy;

import com.srdcloud.ideplugin.codecomplete.domain.CompletionRequest;
import kotlin.Unit;
import kotlin.coroutines.Continuation;

public abstract class CodeCompletionStrategy {

    abstract public boolean isEnabled();

    abstract public String getCodeCompletion(CompletionRequest request, Continuation<Unit> continuation);

    abstract public void cancel();

}
