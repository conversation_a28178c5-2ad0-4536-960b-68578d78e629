package com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc

import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.newvfs.BulkFileListener
import com.intellij.openapi.vfs.newvfs.events.*
import com.srdcloud.ideplugin.agent.AgentManager
import com.srdcloud.ideplugin.agent.commclient.AgentCommClient
import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc.RlccFile.Companion.getFileInfo
import com.srdcloud.ideplugin.general.constants.AgentNameConstant
import com.srdcloud.ideplugin.general.utils.DebugLogUtil
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import java.util.function.Consumer
import java.lang.ref.WeakReference

data class FileChangeEvent(
    val filePath: String,
    val updateType: String,
    val projectName: String,
    val isDirectory: Boolean
)

/**
 * 实现 BulkFileListener 来监听文件变更
 * 文件事件推送tabby agent处理逻辑：
 */
class FileChangeListener(project: Project) : BulkFileListener {

    private val logger = logger<FileChangeListener>()
    private val projectRef = WeakReference(project) // 使用弱引用避免循环引用
    private val tabbyAgentClient by lazy {
        projectRef.get()?.let { AgentManager.getInstance(it).getAgentCommClient(AgentNameConstant.TABBY_AGENT) }
    }

    // 用于跟踪已处理的文件，防止重复处理
    private val processedFiles = ConcurrentHashMap<String, Long>()

    // 用于在一次事件中收集待发送的事件
    private val pendingEvents = ConcurrentHashMap<String, FileChangeEvent>()

    // 批量事件大小上限
    private val BATCH_SIZE_LIMIT = 50

    // 缓存清理间隔(毫秒)
    private val CACHE_CLEANUP_INTERVAL_MS = 30_000L // 30秒

    // 缓存过期时间(毫秒)
    private val CACHE_EXPIRY_MS = 10_000L // 10秒

    // 上次缓存清理时间
    @Volatile
    private var lastCacheCleanupTime = System.currentTimeMillis()

    // 后台线程池，用于异步处理大量文件
    private val fileProcessorExecutor = Executors.newSingleThreadExecutor { r ->
        Thread(r, "FileChangeProcessorThread").apply {
            isDaemon = true
            priority = Thread.MIN_PRIORITY // 新增：降低线程优先级避免影响主线程
        }
    }

    override fun after(events: MutableList<out VFileEvent>) {
        val project = projectRef.get() ?: return // 如果项目已被回收，则不处理

        // 清空前一次事件的待发送队列
        pendingEvents.clear()

        // 收集所有需要处理的文件
        val filesToProcess = mutableMapOf<String, Pair<VirtualFile, String>>()
        val deleteEvents = mutableListOf<VFileDeleteEvent>()

        // 定期清理缓存
        cleanupCacheIfNeeded()

        // 第一遍：收集所有文件事件
        events.forEach { event ->
            when (event) {
                is VFileContentChangeEvent -> {
                    event.file.let {
                        filesToProcess[it.path] = Pair(it, "change")
                    }
                }

                is VFileCreateEvent -> {
                    event.file?.let {
                        filesToProcess[it.path] = Pair(it, "create")
                    }
                }

                is VFileCopyEvent -> {
                    event.file.let {
                        filesToProcess[it.path] = Pair(it, "create")
                    }
                }

                is VFileDeleteEvent -> {
                    // 收集删除事件，单独处理
                    deleteEvents.add(event)
                }

                is VFileMoveEvent -> {
                    // 处理移动事件（包含新路径和旧路径）
                    event.file.let {
                        // 新位置的文件被视为新创建的文件，而不是移动的文件
                        filesToProcess[it.path] = Pair(it, "create")
                    }
                    event.oldPath.let { oldPath ->
                        // 记录文件被移走的信息
                        val fileKey = "$oldPath|delete"
                        if (!processedFiles.containsKey(fileKey)) {
                            processedFiles[fileKey] = System.currentTimeMillis()
                            // 添加到待发送队列
                            queueEvent(
                                FileChangeEvent(
                                    filePath = oldPath,
                                    updateType = "delete",
                                    projectName = project.name,
                                    isDirectory = false
                                )
                            )
                        }
                    }
                }

                is VFilePropertyChangeEvent -> {
                    // 处理重命名事件
                    if (event.propertyName == VirtualFile.PROP_NAME && event.oldValue != null) {
                        event.file.let {
                            // 将重命名视为创建新文件
                            filesToProcess[it.path] = Pair(it, "create")

                            // 处理旧名称的文件删除
                            val parent = it.parent?.path ?: ""
                            val oldName = event.oldValue.toString()
                            val oldPath = "$parent/$oldName"

                            val fileKey = "$oldPath|delete"
                            if (!processedFiles.containsKey(fileKey)) {
                                processedFiles[fileKey] = System.currentTimeMillis()
                                // 添加到待发送队列
                                queueEvent(
                                    FileChangeEvent(
                                        filePath = oldPath,
                                        updateType = "delete",
                                        projectName = project.name,
                                        isDirectory = it.isDirectory
                                    )
                                )
                            }
                        }
                    }
                }

                else -> {
                    // 其他未处理的事件
                    logger.debug("[cf]Unhandled event type: ${event.javaClass.simpleName}")
                }
            }
        }

        // 处理删除事件
        if (deleteEvents.isNotEmpty()) {
            processDeleteEvents(deleteEvents)
        }

        // 第二遍：同步处理普通文件变更
        if (filesToProcess.isNotEmpty()) {
            filesToProcess.forEach { (path, fileInfo) ->
                val (file, updateType) = fileInfo
                val fileKey = "$path|$updateType"

                if (!processedFiles.containsKey(fileKey)) {
                    processedFiles[fileKey] = System.currentTimeMillis()
                    processFileChange(file, updateType)
                }
            }
        }

        // 事件处理完成后立即发送收集的所有事件
        if (pendingEvents.isNotEmpty()) {
            // 使用后台线程发送，以避免可能的阻塞
            val eventsToSend = ArrayList(pendingEvents.values)
            pendingEvents.clear() // 立即清空，避免内存占用
            // sendEvents(eventsToSend)

            fileProcessorExecutor.submit {
                sendEvents(eventsToSend)
            }
        }
    }

    override fun before(events: MutableList<out VFileEvent>) {
        // 在after中统一处理，before不执行任何操作
    }

    // 定期清理缓存
    private fun cleanupCacheIfNeeded() {
        val currentTime = System.currentTimeMillis()
        // 检查是否需要清理，避免频繁检查影响性能
        if (currentTime - lastCacheCleanupTime < CACHE_CLEANUP_INTERVAL_MS) {
            return
        }
        // 使用CAS更新最后清理时间，避免多线程并发清理
        if (!compareAndSetLastCleanupTime(lastCacheCleanupTime, currentTime)) {
            return // 另一个线程已经在清理
        }
        try {
            // 清理过期记录
            val expireTime = currentTime - CACHE_EXPIRY_MS
            var removedCount = 0
            // 分批处理，避免长时间阻塞
            val iterator = processedFiles.entries.iterator()
            while (iterator.hasNext()) {
                val entry = iterator.next()
                if (entry.value < expireTime) {
                    iterator.remove()
                    removedCount++
                    // 每处理100条记录检查一次是否需要让出CPU
                    if (removedCount % 100 == 0 && Thread.interrupted()) {
                        break
                    }
                }
            }
        } catch (e: Exception) {
            logger.error("[cf] Error cleaning up cache", e)
        }
    }

    // 使用CAS更新最后清理时间
    private fun compareAndSetLastCleanupTime(expected: Long, update: Long): Boolean {
        synchronized(this) {
            if (lastCacheCleanupTime == expected) {
                lastCacheCleanupTime = update
                return true
            }
            return false
        }
    }

    // 将事件添加到待发送队列
    private fun queueEvent(event: FileChangeEvent) {
        val key = "${event.filePath}|${event.updateType}"
        pendingEvents[key] = event
    }

    private fun sendEvents(events: List<FileChangeEvent>) {
        if (events.isEmpty()) return

        val client = tabbyAgentClient
        if (client?.isEnabled != true) {
            DebugLogUtil.warn("[cf] TabbyAgentClient is not enabled or unavailable, cannot send events")
            return
        }

        try {
            // 如果事件较多，按批次发送
            if (events.size > BATCH_SIZE_LIMIT) {
                events.chunked(BATCH_SIZE_LIMIT).forEachIndexed { index, batch ->
                    DebugLogUtil.info("[cf] Sending batch ${index + 1}/${(events.size + BATCH_SIZE_LIMIT - 1) / BATCH_SIZE_LIMIT}")
                    sendEventBatch(batch, client)
                }
            } else {
                sendEventBatch(events, client)
            }
        } catch (e: Exception) {
            logger.error("[cf]Failed to send events", e)
        }
    }

    // 发送单批事件
    private fun sendEventBatch(events: List<FileChangeEvent>, client: AgentCommClient) {
        try {
            // 按照指定的数据结构组装参数：[[events数组], {"signal": true}]
            val requestParams = arrayOf(events, mapOf("signal" to true))
            // 使用组装好的参数发送请求
            client.request(
                "updateFiles",
                requestParams,
                null,
                Consumer { response ->
                    // 只记录错误响应，减少日志量
                    if (response != null && response.toString().contains("error")) {
                        logger.warn("[cf]Error response for file events batch: $response")
                    }
                }
            )

            // 记录批量发送成功的日志
            logger.warn("[cf]Sent batch of ${events.size} events successfully")
        } catch (e: Exception) {
            logger.error("[cf]Failed to send events batch of size ${events.size}", e)
            // 记录失败的事件路径，便于排查
            events.take(5).forEach { event ->
                logger.warn("[cf]Failed event example: ${event.filePath} (${event.updateType})")
            }
            if (events.size > 5) {
                logger.warn("[cf]... and ${events.size - 5} more events")
            }
        }
    }

    // 专门处理删除事件，不依赖磁盘上的文件存在
    private fun processDeleteEvents(deleteEvents: List<VFileDeleteEvent>) {
        val project = projectRef.get() ?: return
        val currentTime = System.currentTimeMillis()

        // 分组处理：先处理普通文件，再处理目录
        val directoryEvents = mutableListOf<VFileDeleteEvent>()
        val fileEvents = mutableListOf<VFileDeleteEvent>()

        deleteEvents.forEach { event ->
            event.file.let { file ->
                if (file.isDirectory) {
                    directoryEvents.add(event)
                } else {
                    fileEvents.add(event)
                }
            }
        }

        // 处理普通文件删除
        fileEvents.forEach { event ->
            val file = event.file
            val fileKey = "${file.path}|delete"

            if (!processedFiles.containsKey(fileKey)) {
                processedFiles[fileKey] = currentTime

                // 直接创建删除事件
                queueEvent(
                    FileChangeEvent(
                        filePath = file.path,
                        updateType = "delete",
                        projectName = project.name,
                        isDirectory = false
                    )
                )

                // 尝试使用getFileInfo获取更多信息（如果文件还存在）
                file.getFileInfo(project)?.let { fileInfo ->
                    // 使用相对路径更新事件
                    queueEvent(
                        FileChangeEvent(
                            filePath = fileInfo.filePath,
                            updateType = "delete",
                            projectName = project.name,
                            isDirectory = false
                        )
                    )
                }
            }
        }

        // 处理目录删除
        directoryEvents.forEach { event ->
            val directory = event.file
            val dirKey = "${directory.path}|delete"

            if (!processedFiles.containsKey(dirKey)) {
                processedFiles[dirKey] = currentTime

                try {
                    // 尝试获取目录下的文件（可能会失败，因为目录可能已被删除）
                    val childFiles = getAllFilesInDirectory(directory)

                    if (childFiles.size > BATCH_SIZE_LIMIT) {
                        // 大目录处理
                        logger.warn("[cf] Large directory deletion detected: ${directory.path} with ${childFiles.size} files")

                        // 使用单独线程处理
                        val projectName = project.name
                        val dirPath = directory.path
                        val isDirectory = directory.isDirectory

                        fileProcessorExecutor.submit {
                            // 标记所有子文件为已处理
                            childFiles.forEach { childFile ->
                                val childKey = "${childFile.path}|delete"
                                processedFiles[childKey] = currentTime
                            }

                            // 只发送一个目录删除事件
                            val dirEvent = FileChangeEvent(
                                filePath = dirPath,
                                updateType = "directory_delete",
                                projectName = projectName,
                                isDirectory = isDirectory
                            )

                            // 立即发送，不经过队列
                            sendEvents(listOf(dirEvent))
                        }
                    } else {
                        // 小目录处理：为每个文件生成删除事件
                        childFiles.forEach { childFile ->
                            val childKey = "${childFile.path}|delete"
                            if (!processedFiles.containsKey(childKey)) {
                                processedFiles[childKey] = currentTime
                                queueEvent(
                                    FileChangeEvent(
                                        filePath = childFile.path,
                                        updateType = "delete",
                                        projectName = project.name,
                                        isDirectory = false
                                    )
                                )
                            }
                        }

                        // 也发送目录本身的删除事件
                        queueEvent(
                            FileChangeEvent(
                                filePath = directory.path,
                                updateType = "delete",
                                projectName = project.name,
                                isDirectory = true
                            )
                        )
                    }
                } catch (e: Exception) {
                    // 如果获取子文件失败（目录已不存在），只发送目录本身的删除事件
                    DebugLogUtil.warn("[cf] Cannot access deleted directory: ${directory.path}, sending directory event only")
                    queueEvent(
                        FileChangeEvent(
                            filePath = directory.path,
                            updateType = "delete",
                            projectName = project.name,
                            isDirectory = true
                        )
                    )
                }
            }
        }
    }

    // 处理文件变更
    private fun processFileChange(file: VirtualFile, updateType: String) {
        val project = projectRef.get() ?: return

        // 发送文件变更事件
        val fileInfo = file.getFileInfo(project)
        fileInfo?.let {
            queueEvent(
                FileChangeEvent(
                    filePath = it.filePath,
                    updateType = updateType,
                    projectName = project.name,
                    isDirectory = file.isDirectory
                )
            )
        }
    }

    // 获取文件夹下的所有文件
    private fun getAllFilesInDirectory(directory: VirtualFile): List<VirtualFile> {
        val files = mutableListOf<VirtualFile>()
        try {
            directory.children.forEach { child ->
                if (child.isDirectory) {
                    files.addAll(getAllFilesInDirectory(child)) // 递归处理子文件夹
                } else {
                    files.add(child)
                }
            }
        } catch (e: Exception) {
            logger.warn("[cf] Error accessing directory ${directory.path}: ${e.message}")
        }
        return files
    }

    // 释放资源防止内存泄漏
    fun dispose() {
        try {
            // 清空所有缓存
            pendingEvents.clear()
            processedFiles.clear()
            fileProcessorExecutor.shutdown()
            // 增加优雅关闭检查
            if (!fileProcessorExecutor.awaitTermination(2, TimeUnit.SECONDS)) {
                fileProcessorExecutor.shutdownNow().also {
                    logger.warn("[cf] Forced shutdown of file processor thread")
                }
            }
        } catch (e: InterruptedException) {
            Thread.currentThread().interrupt() // 恢复中断状态
            logger.error("[cf] Thread interrupted during shutdown", e)
        }
    }
}
