package com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions;

import com.google.common.collect.Lists;
import com.intellij.openapi.util.SystemInfo;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.ConversationMessage;
import com.srdcloud.ideplugin.assistant.codechatNative.ui.CodeChatMainPanel;
import com.srdcloud.ideplugin.assistant.codechatNative.ui.message.CodeChatMessageComponent;
import com.srdcloud.ideplugin.assistant.codechatNative.ui.message.CodeChatMessageGroupComponent;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.enums.QuestionType;
import com.srdcloud.ideplugin.general.enums.SubServiceType;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.QuoteItem;
import org.jetbrains.annotations.Nls;

import java.util.ArrayList;

public class QuickHelpAction extends SendAction {

    private static final String BLUE_POINT = "<font face='黑体' color='0081FF'  size=4>•</font>";

    private static final String MAC_CMD =
            BLUE_POINT + " ⌥ ⇧ K：打开/关闭问答框\n" +
                    BLUE_POINT + " Tab：采纳代码建议\n" +
                    BLUE_POINT + " ⌥ [：查看上一条补全建议\n" +
                    BLUE_POINT + " ⌥ ]：查看下一条补全建议\n" +
                    BLUE_POINT + " ⌘ ↩\uFE0E：手动发起补全请求\n";

    private static final String WIN_CMD =
            BLUE_POINT + " Alt + Shift + K：打开/关闭问答框\n" +
                    BLUE_POINT + " Tab：采纳代码建议\n" +
                    BLUE_POINT + " Alt + [：查看上一条补全建议\n" +
                    BLUE_POINT + " Alt + ]：查看下一条补全建议\n" +
                    BLUE_POINT + " Ctrl + Enter：手动发起补全请求\n";

    private static final String HELP_MESSAGE =
            "您好，我是研发云CodeFree，您的智能开发助手。让我和您一起更高效地完成编码工作。\n" +
                    "您可以：\n" +
                    BLUE_POINT + " 在这里向我提问，建议精准详细表达问题，亦可以选中代码后输入问题\n" +
                    BLUE_POINT + " 在编辑器中获得代码补全建议\n" +
                    BLUE_POINT + " 选中代码后<font face='黑体' color='F7B66B' size=4>右键</font>或在对话框中<font face='黑体' color='F7B66B' size=4>使用/</font>触发快捷指令\n" +
                    BLUE_POINT + " 查看和使用指令模板\n" +
                    " <br>" +
                    " 快捷键：\n" +
                    (SystemInfo.isMac ? MAC_CMD : WIN_CMD) +
                    " <br>" +
                    "快捷指令：\n" +
                    BLUE_POINT + " /help：了解CodeFree\n" +
                    BLUE_POINT + " /explain code：解释选中的代码\n" +
                    BLUE_POINT + " /generate unit test：为选中的代码生成单元测试\n" +
                    BLUE_POINT + " /generate comment：为选中的代码生成方法注释或行间注释\n" +
                    BLUE_POINT + " /generate optimization：为选中的代码生成代码优化建议及相关代码\n" +
                    " <br>" +
                    "更多：\n" +
                    "[CodeFree帮助文档](" + ConfigWrapper.HelpDocsPageUrl + ")\n" +
                    "[问题反馈](" + ConfigWrapper.FeedBackPageUrl + ")\n";

    // 外部版本/help文案
    private static final String HELP_MESSAGE_PUBLIC =
            "您好，我是研发云CodeFree，您的智能开发助手。让我和您一起更高效地完成编码工作。\n" +
                    "您可以：\n" +
                    BLUE_POINT + " 在这里向我提问，建议精准详细表达问题，亦可以选中代码后输入问题\n" +
                    BLUE_POINT + " 在编辑器中获得代码补全建议\n" +
                    BLUE_POINT + " 选中代码后<font face='黑体' color='F7B66B' size=4>右键</font>或在对话框中<font face='黑体' color='F7B66B' size=4>使用/</font>触发快捷指令\n" +
                    " <br>" +
                    " 快捷键：\n" +
                    (SystemInfo.isMac ? MAC_CMD : WIN_CMD) +
                    " <br>" +
                    "快捷指令：\n" +
                    BLUE_POINT + " /help：了解CodeFree\n" +
                    BLUE_POINT + " /explain code：解释选中的代码\n" +
                    BLUE_POINT + " /generate unit test：为选中的代码生成单元测试\n" +
                    BLUE_POINT + " /generate comment：为选中的代码生成方法注释或行间注释\n" +
                    BLUE_POINT + " /generate optimization：为选中的代码生成代码优化建议及相关代码\n" +
                    " <br>";

    public void doActionPerformed(CodeChatMainPanel codeChatMainPanel, String data, Integer kbId, QuestionType questionType, QuoteItem quote) {

        codeChatMainPanel.conversationListPanel.addAndSelectNewConversation(codeChatMainPanel.getProject(), Constants.NEW_CONVERSATION_NAME, SubServiceType.ASSISTANT.getName());

        ArrayList<CodeChatMessageComponent> batchMessageComponentList = Lists.newArrayListWithExpectedSize(2);
        CodeChatMessageGroupComponent contentPanel = codeChatMainPanel.getMessageAreaPanel();

        ConversationMessage questionMessage = ConversationMessage.buildQuestionMessage(getCMD(), getChatMessageType());
        CodeChatMessageComponent questionComponent = new CodeChatMessageComponent(codeChatMainPanel, questionMessage);

        String helpMsg = HELP_MESSAGE;
        if (!ConfigWrapper.isInnerVersion) {
            helpMsg = HELP_MESSAGE_PUBLIC;
        }
        ConversationMessage answerMessage = ConversationMessage.buildInvalidMessage(helpMsg, getChatMessageType());
        CodeChatMessageComponent answerComponent = new CodeChatMessageComponent(codeChatMainPanel, answerMessage);

        batchMessageComponentList.add(questionComponent);
        batchMessageComponentList.add(answerComponent);

        contentPanel.batchAddNewMessageComponentToUI(batchMessageComponentList, false);
        codeChatMainPanel.changeRightPartCenter(codeChatMainPanel.codeChatMessageGroupCardName);
    }

    @Override
    public @Nls String toString() {
        return "帮助指引";
    }

    @Override
    public String getCMD() {
        return "/help";
    }
}
