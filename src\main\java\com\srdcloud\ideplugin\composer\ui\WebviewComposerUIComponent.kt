package com.srdcloud.ideplugin.composer.ui

import com.intellij.openapi.project.Project
import com.srdcloud.ideplugin.general.utils.IdeUtil
import com.srdcloud.ideplugin.general.utils.JsonUtil
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview
import com.srdcloud.ideplugin.webview.codechat.composer.response.*

/**
 * WebviewComposerUIComponent实现类
 * 将ComposerUIComponent接口的消息发送到Webview
 */
class WebviewComposerUIComponent(val project: Project) : ComposerUIComponent {
    
    /**
     * Composer Chat回答消息回调
     */
    override fun onChatResponseChunk(chunk: ComposerResponse) {
        val jsonMessage = JsonUtil.getInstance().toJson(chunk)
        CodeChatWebview.getInstance(project).sentMessageToWebviewWithContentLoadCheck(jsonMessage)
    }

    /**
     * 停止Composer Chat响应消息回调
     */
    override fun sendStopChatResponse(response: ComposerResponse) {
        val jsonMessage = JsonUtil.getInstance().toJson(response)
        CodeChatWebview.getInstance(project).sentMessageToWebviewWithContentLoadCheck(jsonMessage)
    }

    /**
     * 向UI发送diff文件列表消息
     */
    override fun sendDiffFiles(diffFiles: ComposerResponse) {
        val jsonMessage = JsonUtil.getInstance().toJson(diffFiles)
        CodeChatWebview.getInstance(project).sentMessageToWebviewWithContentLoadCheck(jsonMessage)
    }

    /**
     * 拉起Diff窗口响应消息
     */
    override fun sendShowDiffViewsResponse(response: ShowDiffViewResponseData) {
        val jsonMessage = JsonUtil.getInstance().toJson(response)
        CodeChatWebview.getInstance(project).sentMessageToWebviewWithContentLoadCheck(jsonMessage)
    }

    /**
     * 接受diff文件响应消息
     */
    override fun sendAcceptFileDiffResponse(response: AccpetFileDiffResponseData) {
        val jsonMessage = JsonUtil.getInstance().toJson(response)
        CodeChatWebview.getInstance(project).sentMessageToWebviewWithContentLoadCheck(jsonMessage)
    }

    /**
     * 拒绝diff文件响应消息
     */
    override fun sendRejectFileDiffResponse(response: RejectFileDiffResponseData) {
        val jsonMessage = JsonUtil.getInstance().toJson(response)
        CodeChatWebview.getInstance(project).sentMessageToWebviewWithContentLoadCheck(jsonMessage)
    }

    /**
     * 推送DiffFile变更状态
     */
    override fun sendDiffStatusChangedResponse(response: DiffStatusChangedResponse) {
        val jsonMessage = JsonUtil.getInstance().toJson(response)
        CodeChatWebview.getInstance(project).sentMessageToWebviewWithContentLoadCheck(jsonMessage)
    }

    /**
     * 加载历史对话记录消息响应
     */
    override fun sendLoadHistoryChatResponse(response: ComposerResponse) {
        val jsonMessage = JsonUtil.getInstance().toJson(response)
        CodeChatWebview.getInstance(project).sentMessageToWebviewWithContentLoadCheck(jsonMessage)
    }

    /**
     * 删除历史对话记录消息响应
     */
    override fun sendDeleteHistoryChatResponse(response: ComposerResponse) {
        val jsonMessage = JsonUtil.getInstance().toJson(response)
        CodeChatWebview.getInstance(project).sentMessageToWebviewWithContentLoadCheck(jsonMessage)
    }
}