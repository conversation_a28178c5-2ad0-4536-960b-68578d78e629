package com.srdcloud.ideplugin.webview.codesecurity.response;

import com.srdcloud.ideplugin.remote.domain.ScanAssistant.Issue;
import com.srdcloud.ideplugin.webview.base.domain.WebViewCode;

import java.util.List;

public class QueryScanIssuesResponseData extends WebViewCode {

    private String msg;

    private List<Issue> issueList;

    public QueryScanIssuesResponseData(int code, String msg, List<Issue> issueList) {
        this.code = code;
        this.msg = msg;
        this.issueList = issueList;
    }

}
