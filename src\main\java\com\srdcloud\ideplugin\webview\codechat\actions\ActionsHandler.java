package com.srdcloud.ideplugin.webview.codechat.actions;

import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.general.utils.JsonUtil;
import com.srdcloud.ideplugin.general.utils.MessageBalloonNotificationUtil;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;
import com.srdcloud.ideplugin.webview.codechat.actions.response.CodeSelectionParams;
import com.srdcloud.ideplugin.webview.codechat.actions.response.CodeSelectionResponse;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCommand;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2025/1/8
 */
public class ActionsHandler {
    private static final Logger logger = LoggerFactory.getLogger(ActionsHandler.class);

    private final Project project;

    private final CodeChatWebview parent;

    public ActionsHandler(Project project, CodeChatWebview parent) {
        this.project = project;
        this.parent = parent;
    }


    private void sendResponseWithToolWindowVisible(CodeSelectionResponse response) {
        if (CodeChatWebview.getInstance(project).isLoaded() && CodeChatWebview.getInstance(project).isWebviewContentLoaded()) {
            // 发送到CodeChatWebview进行处理
            parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(response));
        } else {
            // 如果Webview尚未装载，则提示用户稍后再试（首次拉起窗口会加载大量消息，此时提问会界面样式错乱）
            MessageBalloonNotificationUtil.showCommonNotificationWithConfirm(project, "问答窗口装载中，请稍后重试...");

            // 以下代码废弃：延迟发送消息也不行
            //try {
            //    Thread.sleep(1000);
            //} catch (Exception ex) {
            //    throw new RuntimeException(ex);
            //}
            //
            // 然后把消息加到队列里
            //CodeChatWebview.getInstance(project).getCachedCodeSelectionResponseQueue().add(response);
        }
    }


    public boolean checkIsChating() {
        return parent.getCodeChatEngin().isChating();
    }

    /**
     * 右键菜单
     *
     * @param code
     * @param type
     */
    public void rightSelectionAction(String code, int type, String filePath, Integer startLine, Integer endLine) {
        // 检查是否回答中
        if (checkIsChating()) {
            MessageBalloonNotificationUtil.showCommonNotificationWithConfirm(project, "正在回答中，请稍后重试...");
            return;
        }

        CodeSelectionParams params = new CodeSelectionParams(type, code, null, true, checkIsChating(), filePath, startLine, endLine);
        CodeSelectionResponse response = new CodeSelectionResponse(WebViewRspCommand.CODE_SELECTION_ASKED, params);
        sendResponseWithToolWindowVisible(response);
    }

    /**
     * 异常解析
     */
    public void fixExceptionAction(String code, int type, String errDesc) {
        if (checkIsChating()) {
            MessageBalloonNotificationUtil.showCommonNotificationWithConfirm(project, "正在回答中，请稍后重试...");
            return;
        }
        CodeSelectionParams params = new CodeSelectionParams(type, code, errDesc, true, checkIsChating(), null);
        CodeSelectionResponse response = new CodeSelectionResponse(WebViewRspCommand.CODE_SELECTION_ASKED, params);
        sendResponseWithToolWindowVisible(response);
    }

}
