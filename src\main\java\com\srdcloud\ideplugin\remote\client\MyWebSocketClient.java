package com.srdcloud.ideplugin.remote.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.constants.MessageNameConstant;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import com.srdcloud.ideplugin.general.utils.EnvUtil;
import com.srdcloud.ideplugin.general.utils.IdeUtil;
import com.srdcloud.ideplugin.general.utils.LocalStorageUtil;
import com.srdcloud.ideplugin.general.utils.ThreadPoolUtil;
import com.srdcloud.ideplugin.remote.domain.ApiResponse;
import com.srdcloud.ideplugin.service.LoginService;
import com.srdcloud.ideplugin.statusbar.Notify;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.webview.codechat.common.StatusEventType;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCode;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.java_websocket.WebSocket;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft_6455;
import org.java_websocket.handshake.ServerHandshake;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.*;
import java.net.Socket;
import java.net.URI;
import java.net.URISyntaxException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.Objects;
import java.util.Queue;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR> yangy
 * @create 2023/9/6 15:20
 */
public class MyWebSocketClient {

    private static final Logger logger = LoggerFactory.getLogger(MyWebSocketClient.class);

    private final Lock initLock = new ReentrantLock();

    private final URI url;

    private final ICommChannelEvent commChannelEvent;

    private int channelStatus;

    private boolean disconnectedByRemoteClose = false;

    private boolean remoteClose = false;

    private WebSocketClient websocketClient;

    // 当前连接终止标识：若终止，则进行重连等操作；不终止，则进行消息发送、收取、心跳等操作
    private boolean isTerminated = false;

    private int websocketCloseAndChannelConnecting = 0;

    private int reconnectBy1006Count = 0;


    /**
     * 是否手动关闭websocket
     */
    private boolean closeManual = false;

    /**
     * 下行消息队列类
     * 用于解耦websocket客户端接收消息线程和下行消息消费线程
     */
    class MessageConcurrentQueue {
        private Queue<Object> elements;

        public MessageConcurrentQueue() {
            elements = new ConcurrentLinkedQueue<>();
        }

        public void enqueue(Object element) {
            elements.add(element);
        }

        public Object dequeue() {
            return elements.poll();
        }

        public void clear() {
            elements.clear();
        }
    }

    private MessageConcurrentQueue recvMessageQueue;

    /**
     * 下行消息消费线程，用于将下行消息处理从websocket接收线程中解耦出来，提升websocket通道消息处理效率
     */
    class MessageConsumeThread extends Thread {

        public MessageConsumeThread() {
        }

        @Override
        public void run() {
            // 当前连接实例状态为未终止，则不断提取下行消息队列，进行消费处理
            while (!isTerminated) {
                String message = (String) recvMessageQueue.dequeue();
                if (message != null && message.length() > 0) {
                    if (message.contains("apiKey")) {
                        try {
                            ObjectMapper mapper = new ObjectMapper();
                            String messageJson = message.replace(Constants.WB_CHANNEL_START, "").replace(Constants.WB_CHANNEL_END, "");
                            JsonNode node = mapper.readTree(messageJson);
                            if (node != null) {
                                JsonNode payload = node.get("payload");
                                if (payload != null && payload.get("apiKey") != null) {
                                    ((ObjectNode) payload).put("apiKey", "***");
                                }
                                logger.debug("[cf] 收到消息={}", Constants.WB_CHANNEL_START + mapper.writeValueAsString(node) + Constants.WB_CHANNEL_END);
                            }
                        } catch (Exception e) {
                            logger.warn("[cf] onMessage json", e);
                        }
                    }

                    //提交消息处理
                    if (Objects.nonNull(commChannelEvent)) {
                        commChannelEvent.OnCommChannelIncomMessage(message);
                    }
                } else {
                    // 避免cpu空耗
                    try {
                        Thread.sleep(10);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
            }

            //清空队列
            recvMessageQueue.clear();
        }

    }

    private MessageConsumeThread messageConsumeThread;

    //Websocket服务端心跳请求和响应消息字串，用于心跳处理
    private final String HBREQ = "<WBChannel>{\"messageName\":\"ServerHeartbeat\"}</WBChannel>";
    private final String HBRES = "<WBChannel>{\"messageName\":\"" + MessageNameConstant.MessageName_ServerHeartbeatResponse + "\"}</WBChannel>";

    //Websocket客户端心跳请求和响应小资字串，用于客户端心跳处理
    private final String CLIHBREQ = "<WBChannel>{\"messageName\":\"ClientHeartbeat\"}</WBChannel>";
    private final String CLIHBRES = "<WBChannel>{\"messageName\":\"ClientHeartbeatResponse\"}</WBChannel>";

    //客户端心跳超时定时器
    private Timer cliHbTimeoutTimer;

    //客户端心跳间隔定时器
    private Timer cliHbIntervalTimer;

    // websocket通道周期性重连任务线程对象
    private ScheduledExecutorService executorService;

    public MyWebSocketClient(String urlStr, ICommChannelEvent commChannelEvent) {
        URI url1 = null;
        try {
            url1 = new URI(urlStr);
        } catch (URISyntaxException ex) {
            logger.error("[cf] MyWebSocketClient Raise URISyntaxException, exception=" + ex.getMessage());
        }
        this.url = url1;
        this.commChannelEvent = commChannelEvent;
        this.channelStatus = Constants.Channel_Disconnected;
        this.remoteClose = false;
        this.disconnectedByRemoteClose = false;
    }

    public int ConnectToServer() {

        closeManual = false;
        remoteClose = false;
        disconnectedByRemoteClose = false;
        isTerminated = false;

        //创建下行消息队列
        recvMessageQueue = new MessageConcurrentQueue();

        //创建并启动下行消息消费线程
        messageConsumeThread = new MessageConsumeThread();
        messageConsumeThread.start();

        //创建WebsocketClient对象
        try {
            initWebsocketClient();
            logger.info("[cf] new websocket connecting……");
            channelStatus = Constants.Channel_Connecting;
            websocketClient.connectBlocking();
        } catch (Exception e) {
            logger.error("[cf] WebSocketClient connect exception: ", e);
            channelStatus = Constants.Channel_Disconnected;
        }


        //启动通道重连任务
        executorService = new ScheduledThreadPoolExecutor(1,
                new BasicThreadFactory.Builder().namingPattern("websocket-schedule-pool-%d").daemon(true).build());
        executorService.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                // 当前实例没有被终止，无需进行重连操作
                if (!isTerminated) {
//                    DebugLogUtil.info("[cf] websocket reconnect skip,isTerminated is false.");
                    return;
                }
                // 当前持有的websocketClient已经释放，无需进行重连操作
                if (websocketClient == null) {
                    logger.warn("[cf] websocket reconnect skip,websocketClient is null.");
                    return;
                }
                try {
                    // 当前socket状态不正常，而且不是处于连接中的状态，则尝试建立新的连接
                    if (remoteClose || (websocketClient.getReadyState() != WebSocket.READYSTATE.OPEN && channelStatus != Constants.Channel_Connecting)) {
                        logger.info("[cf] websocket reconnect schedule closeAndReconnectWebsocket,websocketClientState:{},channelStatus:{},remoteClose:{}", websocketClient.getReadyState(), channelStatus, remoteClose);
                        closeAndReconnectWebsocket();
                    } else if (websocketClient != null && websocketClient.getReadyState() == WebSocket.READYSTATE.CLOSED && channelStatus == Constants.Channel_Connecting) {
                        // 当前socket正在连接中，则等待5次定时检查
                        websocketCloseAndChannelConnecting++;

                        // 5次后还没连接成功，断开重新建立新的连接，并且重置计数
                        if (websocketCloseAndChannelConnecting > 5) {
                            logger.info("[cf] websocket reconnect schedule closeAndReconnectWebsocket,websocketClientState:{},channelStatus:{}", websocketClient.getReadyState(), channelStatus);
                            closeAndReconnectWebsocket();
                            websocketCloseAndChannelConnecting = 0;
                        } else {
                            logger.warn("[cf] websocket reconnect schedule skip,websocketClientState:{},channelStatus:{},websocketCloseAndChannelConnecting:{}", websocketClient.getReadyState(), channelStatus, websocketCloseAndChannelConnecting);
                        }
                    } else {
                        logger.warn("[cf] websocket reconnect schedule skip,websocketClientState:{},channelStatus:{}", websocketClient.getReadyState(), channelStatus);
                    }
                } catch (Exception e) {
                    logger.warn("[cf] schedule websocket reconnect raise exception:{}", e.getMessage());
                    channelStatus = Constants.Channel_Disconnected;
                }
            }
        }, 0, Constants.Channel_Reconn_Interval, TimeUnit.SECONDS);

        return RtnCode.SUCCESS;
    }

    private void closeAndReconnectWebsocket() {
        try {
            // 重连控制：1006 3次之后，重新注册通道
            if (reconnectBy1006Count < Constants.WS_1006_LIMIT) {
                // 1006 引起重连累加次数
                if (this.remoteClose) {
                    reconnectBy1006Count++;
                }

                // 关闭旧连接
                closeManual = true;
                websocketClient.closeBlocking();
                this.remoteClose = false;

                // 建立新连接
                websocketClient = null;
                channelStatus = Constants.Channel_Connecting;
                initWebsocketClient();
                websocketClient.connectBlocking();
                this.closeManual = false;
                // 等2s，确保连接建立
                Thread.sleep(2000);

                if (channelStatus != Constants.Channel_Connected) {
                    logger.warn("[cf] closeAndReconnectWebsocket  fail,channelStatus:{}", channelStatus);
                    // 状态变化
                    Notify.Companion.updateStatusNotify();
                    // 推送到Webview
                    sendStatusChangeMsgToWebview(StatusEventType.WSSERVER_ERROR, RtnCode.NO_CHANNEL);
                } else {
                    logger.info("[cf] closeAndReconnectWebsocket success,channelStatus:{}", channelStatus);
                    websocketClient.send(HBRES);

                    isTerminated = false;
                    messageConsumeThread = new MessageConsumeThread();
                    messageConsumeThread.start();
                    // 状态变化
                    Notify.Companion.updateStatusNotify();
                    // 推送到Webview
                    sendStatusChangeMsgToWebview(StatusEventType.WSSERVER_RECONNECT, WebViewRspCode.SUCCESS);
                }
            } else {
                disconnectAndRegisterChannel();
            }
        } catch (Exception e) {
            logger.warn("[cf] WebSocketClient closeAndReconnectWebsocket exception: ", e);
            channelStatus = Constants.Channel_Disconnected;
            Notify.Companion.updateStatusNotify();
        }
    }

    private void initWebsocketClient() {
        logger.info("[cf] initWebsocketClient begin");
        // 加锁，防止并发初始化
        if (!initLock.tryLock()) {
            logger.info("[cf] initWebsocketClient initLock tryLock failed");
            return;
        }
        try {
            //建立socket通信实例
            websocketClient = new WebSocketClient(url, new Draft_6455(), null, ConfigWrapper.SyncMessageRecvTimeout * 1000) {
                //连接服务端时触发
                @Override
                public void onOpen(ServerHandshake handshakedata) {
                    logger.info("[cf] websocket channel open succeed.");
                    channelStatus = Constants.Channel_Connected;
                    commChannelEvent.OnCommChannelEvent(RtnCode.SUCCESS, "");

                    //如果需要客户端心跳，则发送客户端心跳
                    if (ConfigWrapper.isNeedWsClientHeartbeat) {
                        sendClientHeartbeat();
                    }
                    isTerminated = false;
                    closeManual = false;
                }

                //收到服务端消息时触发
                @Override
                public void onMessage(String message) {
                    //判断接收到的消息是否为心跳请求消息，是的话直接返回心跳响应，不压入消息队列
                    JSONObject messageJsonObj = msgVerify(message);
                    if (messageJsonObj != null && messageJsonObj.get("messageName").equals(MessageNameConstant.MessageName_ServerHeartbeat)) {
                        //返回心跳响应
                        websocketClient.send(HBRES);
                    } else if (CLIHBRES.equals(message)) {    //收到客户端心跳响应消息
                        if (cliHbTimeoutTimer != null) {
                            cliHbTimeoutTimer.cancel();
                            cliHbTimeoutTimer = null;
                        }

                        sendClientHeartbeat();
                    } else {
                        //将收到的消息压入消息队列，减轻消息处理对websocket接收消息线程的阻塞
                        recvMessageQueue.enqueue(message);
                    }
                }

                //和服务端断开连接时触发
                @Override
                public void onClose(int code, String reason, boolean remote) {
                    logger.warn("[cf] websocket channel closed, code={}, reason={}, remote={},reconnectBy1006Count:{},CountcloseManual:{},channelStatus:{},loginStatus:{},u:{},session:{}", code, reason, remote, reconnectBy1006Count, closeManual, channelStatus, LoginService.getLoginStatus(), LocalStorageUtil.getUserId(), LocalStorageUtil.getSessionId());
                    if (!closeManual) {
                        // 更新状态标志
                        channelStatus = Constants.Channel_Disconnected;
                        // 如果没有退出登录但是被close掉，则标志为异常终止，在后续进行重连
                        if (LoginService.getLoginStatus() != Constants.LoginStatus_NOK) {
                            isTerminated = true;
                        }

                        // 更新状态
                        Notify.Companion.updateStatusNotify();
                        onTaskErrorOrClose(isTerminated);
                    }

                    // 掐断连接
                    if (remote) {
                        remoteClose = true;
                    }
                }

                //连接异常时触发
                @Override
                public void onError(Exception ex) {
                    logger.warn("[cf] websocket channel raise exception:", ex);
                    channelStatus = Constants.Channel_Disconnected;
                    Notify.Companion.updateStatusNotify();
                    onTaskErrorOrClose(isTerminated);
                }
            };
            // 忽略证书
            if (EnvUtil.isSec() && url.toString().startsWith("wss://")) {
                try {
                    SSLContext sslContext = SSLContext.getInstance("TLS");
                    sslContext.init(null, new TrustManager[]{new X509TrustManager() {
                        @Override
                        public void checkClientTrusted(X509Certificate[] chain, String authType) {}
                        @Override
                        public void checkServerTrusted(X509Certificate[] chain, String authType) {}
                        @Override
                        public X509Certificate[] getAcceptedIssuers() {
                            return new X509Certificate[0];
                        }
                    }}, new SecureRandom());

                    SSLSocketFactory factory = sslContext.getSocketFactory();
                    Socket socket = factory.createSocket(); // 创建忽略证书验证的 socket
                    websocketClient.setSocket(socket);      // 设置 socket 给 websocketClient
                } catch (Exception ignored) {}
            }
        } finally {
            initLock.unlock();
        }
        logger.info("[cf] initWebsocketClient end");
    }

    private void onTaskErrorOrClose(boolean isTerminate) {
        if (!isTerminate) {
            sendStatusChangeMsgToWebview(StatusEventType.WSSERVER_ERROR, RtnCode.NO_CHANNEL);
        }
    }

    private void sendStatusChangeMsgToWebview(int eventType, int code) {
        Project project = IdeUtil.findCurrentProject();
        if (Objects.isNull(project)) {
            return;
        }
        CodeChatWebview codeChatWebview = CodeChatWebview.getInstance(project);
        if (Objects.isNull(codeChatWebview)) {
            return;
        }
        codeChatWebview.getPushStatusHandler().onStatusChanged(eventType, code);
    }

    //add by Jiangh 2023.8.17
    //发送客户端心跳消息
    private void sendClientHeartbeat() {
        cliHbIntervalTimer = new Timer();
        cliHbIntervalTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (!isTerminated) {
                    return;
                }
                websocketClient.send(CLIHBREQ);

                //启动心跳响应超时定时器
                cliHbTimeoutTimer = new Timer();
                cliHbTimeoutTimer.schedule(new TimerTask() {
                    @Override
                    public void run() {
                        logger.warn("[cf] websocket client heartbeat timeout, close to reconnect.");
                        websocketClient.close();
                    }
                }, ConfigWrapper.WebsocketClientHeartBeatTimeout);
            }
        }, ConfigWrapper.WebsocketClientHeartBeatInterval);
    }

    public int ReConnectToServer() {
        if (!isTerminated || websocketClient == null) {
            return RtnCode.NO_CHANNEL;
        }

        logger.info("[cf] 客户端重连开始,当前连接状态：{}", websocketClient.getReadyState());

        //如果通道已处于连接状态，或者正在重连状态，直接返回
        if (websocketClient != null) {
            if (websocketClient.getReadyState() == WebSocket.READYSTATE.OPEN ||
                    channelStatus == Constants.Channel_Connecting) {
                return RtnCode.SUCCESS;
            }
        }

        try {
            if (websocketClient.getReadyState() != WebSocket.READYSTATE.OPEN) {
                if (websocketClient.getReadyState() == WebSocket.READYSTATE.NOT_YET_CONNECTED) {
                    if (websocketClient.isClosed()) {
                        logger.info("[cf] websocket closed, reconnecting……");
                        websocketClient.reconnect();
                    } else {
                        logger.info("[cf] websocket connecting……");
                        channelStatus = Constants.Channel_Connecting;
                        websocketClient.connectBlocking();
                    }
                } else if (websocketClient.getReadyState() == WebSocket.READYSTATE.CLOSED) {
                    logger.info("[cf] websocket closed, reconnecting……");
                    channelStatus = Constants.Channel_Connecting;
                    websocketClient.reconnect();
                }
            }
            return RtnCode.SUCCESS;
        } catch (Exception e) {
            logger.error("[cf] websocket channel connect exception: " + e.getMessage());
            channelStatus = Constants.Channel_Disconnected;
        }
        return RtnCode.NO_CHANNEL;

    }

    public void disconnectAndRegisterChannel() {
        try {
            // 手动停止
            closeManual = true;
            // 设置停止标志位
            isTerminated = true;
            // 重置计数
            reconnectBy1006Count = 0;
            // 停止websocket重连线程
            executorService.shutdown();
            // 关闭当前websocket通道
            websocketClient.close();
            // 更新状态
            channelStatus = Constants.Channel_Disconnected;
            // 状态变化
            Notify.Companion.updateStatusNotify();
            // 推送到Webview
            sendStatusChangeMsgToWebview(StatusEventType.WSSERVER_ERROR, RtnCode.NO_CHANNEL);
            // 重新注册channel
            ThreadPoolUtil.submit(() -> LoginService.sendAiRegisterChannel());

            // 变化状态栏
            //disconnectedByRemoteClose = true;
            //ApplicationManager.getApplication().invokeLater(Notify.Companion::updateStatusNotify);
        } catch (Exception e) {
            logger.error("[cf] websocket channel disconnect exception: " + e.getMessage());
        }
    }

    public void disconnect() {
        try {
            // 手动停止
            closeManual = true;
            // 设置停止标志位
            isTerminated = true;
            // 重置计数
            reconnectBy1006Count = 0;
            // 停止websocket重连线程
            executorService.shutdown();
            // 关闭当前websocket通道
            websocketClient.close();
            // 更新状态
            channelStatus = Constants.Channel_Disconnected;
            // 状态变化
            Notify.Companion.updateStatusNotify();
            // 推送到Webview
            sendStatusChangeMsgToWebview(StatusEventType.WSSERVER_ERROR, RtnCode.NO_CHANNEL);
        } catch (Exception e) {
            logger.error("[cf] websocket channel disconnect exception: " + e.getMessage());
        }
    }



    public ApiResponse SendMessage(String messageStr) {
        if (channelStatus == Constants.Channel_Disconnected) {
            return new ApiResponse(RtnCode.NO_CHANNEL, "");
        }
        //logger.debug("[cf] SendMessage:{}",messageStr);
        websocketClient.send(messageStr);
        return new ApiResponse(RtnCode.SUCCESS, "");
    }

    public int GetChannelStatus() {
        return channelStatus;
    }

    public boolean GetDisconnectedByRemoteClose() {
        return disconnectedByRemoteClose;
    }

    public static JSONObject msgVerify(String message) {
        JSONObject messageJsonObj = null;
        if (StringUtils.isNotBlank(message)) {
            if (message.startsWith(Constants.WB_CHANNEL_START) && message.endsWith(Constants.WB_CHANNEL_END)) {
                String messageJson = message.replace(Constants.WB_CHANNEL_START, "").replace(Constants.WB_CHANNEL_END, "");
                try {
                    messageJsonObj = JSON.parseObject(messageJson);
                } catch (Exception e) {
                    logger.info("[cf] 请求消息json格式不对：{}", messageJson);
                }
            } else {
                logger.info("[cf] 请求消息缺少帧头或帧尾：{}", message);
            }
        } else {
            logger.info("[cf] 请求消息为空：{}", message);
        }
        return messageJsonObj;
    }
}
