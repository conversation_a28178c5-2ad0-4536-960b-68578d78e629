package com.srdcloud.ideplugin.codenatural

import com.intellij.openapi.editor.Caret
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.markup.GutterIconRenderer
import com.intellij.openapi.editor.markup.RangeHighlighter
import com.intellij.openapi.util.Key
import com.intellij.openapi.util.TextRange

class HighlightState private constructor(
    var highlights: List<RangeHighlighter> = emptyList(),
    var currentRenderer: GutterIconRenderer? = null
) {

    fun getCurrentHighlight(caret: Caret?): RangeHighlighter? {
        for (highlight in highlights) {
            if (highlight.gutterIconRenderer === currentRenderer) return highlight
            if (currentRenderer == null && caret != null &&
                caret.offset >= highlight.startOffset && caret.offset <= highlight.endOffset
            ) return highlight
        }
        return null
    }

    companion object {
        private val NLC_HIGHLIGHT_STATE: Key<HighlightState> = Key.create("com.srdcloud.ideplugin.codenatural.state")

        fun Editor.getHighlightState(): HighlightState? = getUserData(NLC_HIGHLIGHT_STATE)

        fun Editor.getHighlightText(highlighter: RangeHighlighter): String {
            val startOffset = highlighter.startOffset
            val endOffset = highlighter.endOffset

            return if (startOffset >= 0 && endOffset <= document.textLength) {
                document.getText(TextRange(startOffset, endOffset))
            } else ""
        }

        fun Editor.initOrGetHighlightState(): HighlightState =
            getHighlightState() ?: HighlightState().also { putUserData(NLC_HIGHLIGHT_STATE, it) }

        fun Editor.resetHighlightState(): Unit = putUserData(NLC_HIGHLIGHT_STATE, null)
    }
}