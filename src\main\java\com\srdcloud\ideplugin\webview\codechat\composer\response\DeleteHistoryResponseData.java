package com.srdcloud.ideplugin.webview.codechat.composer.response;

import com.srdcloud.ideplugin.webview.codechat.composer.request.ComposerRequest;

public class DeleteHistoryResponseData extends ComposerResponseData {
    private String dialogId;

    public DeleteHistoryResponseData(String dialogId) {
        super(ComposerRequest.REQ_TYPE_DELETE_HISTORY);
        this.dialogId = dialogId;
    }
    
    public String getDialogId() {
        return dialogId;
    }
}
