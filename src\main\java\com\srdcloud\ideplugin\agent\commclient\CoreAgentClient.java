package com.srdcloud.ideplugin.agent.commclient;


import com.google.gson.JsonSyntaxException;
import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.general.constants.AgentNameConstant;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import com.srdcloud.ideplugin.general.utils.JsonUtil;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.function.Consumer;

/**
 * Core Agent 专用客户端
 * 继承自基本的 AgentCommClient
 */
public class CoreAgentClient extends AgentCommClient {

    private final static String AGENT_NAME = AgentNameConstant.CORE_AGENT;
    private Consumer<Object> indexProgresslistener;
    private final static Logger logger = LoggerFactory.getLogger(CoreAgentClient.class);

    // 覆盖基类的 generatorTypes，提供 Core Agent 特定的类型
    protected List<String> generatorTypes = new ArrayList<>(Arrays.asList(
            "llm/streamComplete",
            "llm/streamChat",
            "command/run",
            "streamDiffLines"
    ));

    public CoreAgentClient(Project project, @NotNull Process process, @NotNull AgentMessageReceiver messageReceiver) {
        super(project, process, messageReceiver);
    }

    @Override
    public void request(String messageType, Object data, String messageId, Consumer<Object> onResponse) {
        String id = messageId != null ? messageId : uuid();
        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put("messageId", id);
        messageMap.put("messageType", messageType);
        messageMap.put("data", data);
        String message = JsonUtil.getInstance().toJson(messageMap);
        DebugLogUtil.info("[cf] responseListeners put: " + JsonUtil.getInstance().toJson(data));
        if (onResponse != null) {
            responseListeners.put(id, onResponse);
        }
        write(message);
    }

    @Override
    protected void handleMessage(String json) {
        if (!isValidJson(json)) {
            logger.warn("[cf] message client receive unexpected json: {}", json);
            return;
        }

        // 1、由composerService处理三类消息：chat、diff、ideProtocolClient
        messageReceiver.onAgentMessageHandler(json);

        Map<?, ?> responseMap = JsonUtil.getInstance().fromJson(json, Map.class);
        String messageId = responseMap.get("messageId").toString();
        String messageType = responseMap.get("messageType").toString();
        Object data = responseMap.get("data");

        // 2、由listener处理两类消息：index、contextInput
        Consumer<Object> listener = responseListeners.get(messageId);

        if ("index/forceReIndex".equals(messageType)) {
            // index的消息监听器，继续用于处理索引进度消息
            indexProgresslistener = listener;
        }

        if ("indexProgress".equals(messageType) && indexProgresslistener != null) {
            // 发送索引进度消息
            indexProgresslistener.accept(data);
        }

        if (listener != null) {
            listener.accept(data);
            if (generatorTypes.contains(messageType)) {
                Boolean done = ((Map<?, ?>) data).get("done") instanceof Boolean ? (Boolean) ((Map<?, ?>) data).get("done") : null;
                if (done != null && done) {
                    responseListeners.remove(messageId);
                }
            } else {
                responseListeners.remove(messageId);
            }
        }
    }

    public static String getAgentName() {
        return AGENT_NAME;
    }

    private boolean isValidJson(String json) {
        try {
            JsonUtil.getInstance().fromJson(json, Object.class);
            return true;  // 如果解析成功，说明 JSON 有效
        } catch (JsonSyntaxException e) {
            return false; // 如果抛出异常，说明 JSON 无效
        }
    }
}
