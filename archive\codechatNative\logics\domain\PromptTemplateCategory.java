package com.srdcloud.ideplugin.assistant.codechatNative.logics.domain;

/**
 * <AUTHOR>
 * @date 2024/5/13
 * @desc 模板分类
 */
public class PromptTemplateCategory {

    /**
     * 绑定场景：开发问答、智能问答
     */
    private String app;

    /**
     * ID
     */
    private String id;

    /**
     * 分类名
     */
    private String name;

    /**
     * 分类下模板数量
     */
    private String templateCount;


    public PromptTemplateCategory(String app, String id, String name, String templateCount) {
        this.app = app;
        this.id = id;
        this.name = name;
        this.templateCount = templateCount;
    }

    // 下拉时只显示标题
    @Override
    public String toString() {
        return name;
    }


    public String getApp() {
        return app;
    }

    public void setApp(String app) {
        this.app = app;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTemplateCount() {
        return templateCount;
    }

    public void setTemplateCount(String templateCount) {
        this.templateCount = templateCount;
    }
}
