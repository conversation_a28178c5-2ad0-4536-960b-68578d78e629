package com.srdcloud.ideplugin.webview.codechat;

import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import com.srdcloud.ideplugin.general.utils.IdeUtil;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * <AUTHOR>
 * @date 2025/1/17
 * @desc 重新刷新页面
 */
@Deprecated
public class CodeChatWebviewRefreshTask {
    private static final Logger logger = LoggerFactory.getLogger(CodeChatWebviewRefreshTask.class);

    // 无界阻塞队列
    private LinkedBlockingQueue<Project> taskQueue = new LinkedBlockingQueue<>();

    // 工作线程
    private Thread workThread;

    public static CodeChatWebviewRefreshTask getInstance(@NotNull Project project) {
        return project.getService(CodeChatWebviewRefreshTask.class);
    }

//    public CodeChatWebviewRefreshTask() {
//        // 启动工作线程，监听阻塞队列
//        workThread = new Thread(() -> {
//            try {
//                while (true) {
//                    // 从队列中获取一个元素，如果队列为空，则此方法会阻塞
//                    Project project = taskQueue.take();
//                    if (project.getName().equalsIgnoreCase(Objects.requireNonNull(IdeUtil.findCurrentProject()).getName())) {
//                        DebugLogUtil.println("[cf] CodeChatWebview freshening...");
//                        CodeChatWebview.getInstance(project).refreshUI();
//                    }
//                }
//            } catch (InterruptedException e) {
//                logger.info("[cf] CodeChatWebviewRefreshTask workThread exited.");
//                DebugLogUtil.println("CodeChatWebviewRefreshTask workThread exited.");
//            } catch (Exception e) {
//                logger.warn("[cf] CodeChatWebviewRefreshTask workThread exception:");
//                e.printStackTrace();
//            }
//        });
//        workThread.setDaemon(true);
//        workThread.start();
//    }

    /**
     * 新增webview加载任务
     */
    public void addTaskItem(@NotNull Project project) {
        taskQueue.offer(project);
    }
}
