package com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions.rightmenu;

import com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions.SendAction;
import com.srdcloud.ideplugin.general.enums.ChatMessageType;
import org.jetbrains.annotations.Nls;

/**
 * @author: yegj
 * @date: 2023/03/08 16:08
 * @Desc 生成代码优化建议
 */
public class RightMenuOptimizeAction extends SendAction {
    protected ChatMessageType getChatMessageType() {
        return  ChatMessageType.OPTIMIZE;
    }

    @Override
    public @Nls String toString() {
        return "生成优化建议";
    }

    @Override
    public String getCMD() {
        return "/generate optimization";
    }
}
