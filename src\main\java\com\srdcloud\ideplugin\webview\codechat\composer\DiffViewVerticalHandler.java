package com.srdcloud.ideplugin.webview.codechat.composer;

import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.diff.DiffService;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import com.srdcloud.ideplugin.general.utils.JsonUtil;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCommand;
import com.srdcloud.ideplugin.webview.codechat.composer.request.*;
import com.srdcloud.ideplugin.webview.codechat.composer.response.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class DiffViewVerticalHandler {
    private static final Logger logger = LoggerFactory.getLogger(DiffViewVerticalHandler.class);

    private final Project project;

    private final CodeChatWebview parent;

    public DiffViewVerticalHandler(Project project, CodeChatWebview parent) {
        this.project = project;
        this.parent = parent;
    }

    public void processDiffViewVerticalRequest(String request) {
        DiffRequest diffRequest = JsonUtil.getInstance().fromJson(request, DiffRequest.class);
        String reqType = diffRequest.getData().getReqType();
        DiffRequestData data = diffRequest.getData();
        DiffService diffService;

        switch (reqType) {
            case ComposerRequest.REQ_TYPE_OPEN_DIFF_VIEW_VERTICAL:
                DebugLogUtil.info("[cf] webView to diffService, open: " + data.getPath());
                diffService = DiffService.getInstance(project);
                if (diffService != null) {
                    // 打开文件
                    diffService.openFile(data.getPath());
                    // 返回响应消息给webView
                    ShowDiffViewResponseData showFileDiffResponseData = new ShowDiffViewResponseData(data.getPath(), "");
                    ComposerResponse composerResponse = new ComposerResponse(WebViewRspCommand.DIFF_VIEW_VERTICAL_RESPONSE, showFileDiffResponseData);
                    parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(composerResponse));
                } else {
                    logger.error("[cf] DiffService instance not found for project: " + project.getName());
                }
                break;
            case ComposerRequest.REQ_TYPE_ACCEPT_FILE_DIFF:
                logger.info("[cf] webView to diffService, accept: " + data.getPath());
                diffService = DiffService.getInstance(project);
                if (diffService != null) {
                    // 接受文件
                    diffService.acceptFile(data.getPath());
                    // 返回响应消息给webView
                    AccpetFileDiffResponseData accpetFileDiffResponseData = new AccpetFileDiffResponseData(data.getPath(), "");
                    ComposerResponse composerResponse = new ComposerResponse(WebViewRspCommand.DIFF_VIEW_VERTICAL_RESPONSE, accpetFileDiffResponseData);
                    parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(composerResponse));
                } else {
                    logger.error("[cf] DiffService instance not found for project: " + project.getName());
                }
                break;
            case ComposerRequest.REQ_TYPE_REJECT_FILE_DIFF:
                logger.info("[cf] webView to diffService, reject: " + data.getPath());
                diffService = DiffService.getInstance(project);
                if (diffService != null) {
                    // 拒绝文件
                    diffService.rejectFile(data.getPath());
                    // 返回响应消息给webView
                    RejectFileDiffResponseData rejectFileDiffResponseData = new RejectFileDiffResponseData(data.getPath(), "");
                    ComposerResponse composerResponse = new ComposerResponse(WebViewRspCommand.DIFF_VIEW_VERTICAL_RESPONSE, rejectFileDiffResponseData);
                    parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(composerResponse));
                } else {
                    logger.error("[cf] DiffService instance not found for project: " + project.getName());
                }
                break;
            case ComposerRequest.REQ_TYPE_UNDO_FILE_DIFF:
                logger.info("[cf] webView to diffService, undo: " + data.getPath());
                diffService = DiffService.getInstance(project);
                if (diffService != null) {
                    // 撤销diff操作
                    diffService.undoFile(data.getPath());
                    // 此处不返回响应消息给webView
                } else {
                    logger.error("[cf] DiffService instance not found for project: " + project.getName());
                }
                break;
            default:
                logger.error("[cf] reqType not found: " + reqType + ", please check the request message. ");
                break;
        }
    }
}
