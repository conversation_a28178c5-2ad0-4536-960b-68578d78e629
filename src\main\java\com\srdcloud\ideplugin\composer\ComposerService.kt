package com.srdcloud.ideplugin.composer


import com.google.gson.Gson
import com.google.gson.JsonParser
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.srdcloud.ideplugin.agent.AgentManager
import com.srdcloud.ideplugin.agent.commclient.AgentMessageReceiver
import com.srdcloud.ideplugin.codeindex.CodeIndexService
import com.srdcloud.ideplugin.codeindex.IndexProgressMessage
import com.srdcloud.ideplugin.composer.history.AgentHistoryUtil
import com.srdcloud.ideplugin.composer.history.AgentHistoryUtil.toChatMessages
import com.srdcloud.ideplugin.composer.history.AgentWindowIdUtil
import com.srdcloud.ideplugin.diff.DiffMessage
import com.srdcloud.ideplugin.diff.DiffService
import com.srdcloud.ideplugin.general.config.ConfigWrapper
import com.srdcloud.ideplugin.general.constants.AgentNameConstant
import com.srdcloud.ideplugin.general.utils.DebugLogUtil
import com.srdcloud.ideplugin.general.utils.LocalStorageUtil
import com.srdcloud.ideplugin.remote.domain.WorkItem.WorkItemInfo
import com.srdcloud.ideplugin.statusbar.Notify.Companion.throttledUpdateNotify
import com.srdcloud.ideplugin.statusbar.Notify.Companion.updateStatusNotify
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCommand
import com.srdcloud.ideplugin.webview.codechat.composer.domain.ContextInputItem
import com.srdcloud.ideplugin.webview.codechat.composer.response.*
import kotlinx.coroutines.runBlocking
import java.util.concurrent.ConcurrentHashMap

class ComposerService(private val project: Project) : AgentMessageReceiver {

    private val gson = Gson()
    private val logger = Logger.getInstance(this::class.java)
    private val uiAdapter: ComposerUIAdapter = DefaultComposerUIAdapter(project)
    private val ideProtocolClient: IdeProtocolClient = IdeProtocolClient(project.basePath, project)
    private val conversations = ConcurrentHashMap<String, Conversation>()
    private val chatUrl = "/api/acbackend/codechat/v1/completions"

    data class Conversation(
        val conversationId: String,
        var messageCache: MutableMap<String, StringBuffer> = mutableMapOf(),
        var status: String = "idle" // idle, gettingContextItem, chatting, diffing
    )

    data class Message(
        val messageType: String, val data: MessageData, val messageId: String
    )

    data class MessageData(
        val content: String = "",
        val done: Boolean = false,
        val error: String = "",
    )

    data class ChatMsg(
        val project: ProjectItem,
        val messages: List<ChatMessage>,
        val chatType: String,
        val title: String,
        val fileRanges: List<String>,
        val modelName: String = "d10_cpl",
        val oaiServerUrl: String = "",
        val gitUrls: List<String>,
        val workItems: List<WorkItemInfo>?
    )

    data class DiffFileMsg(
        val project: ProjectItem?
    )

    data class ProjectItem(
        val projectPath: String,
        val projectId: String,
        val windowId: String,
    )

    data class ChatMessage(
        val role: String = "user",
        val content: String,
        val knowledgeBaseIds: List<String> = mutableListOf(),
        val userId: String,
        val project: String = "",
    )

    companion object {
        fun getInstance(project: Project): ComposerService {
            return project.getService(ComposerService::class.java)
        }
    }

    fun startConversation(
        conversationId: String,
        input: String,
        displayContent: String,
        createTime: String,
        contextInputItems: Array<ContextInputItem>,
        chatType: String,
        title: String,
        modelName: String,
        gitUrls: List<String>,
        selectedWorkItems: List<WorkItemInfo>?
    ) {
        val conversation = Conversation(conversationId)
        conversations[conversationId] = conversation

        val composerService = getInstance(project)

        // 服务不可用时
        if (composerService.isEnable()) {

            // 历史记录处理，必须创建新的窗口ID
            AgentWindowIdUtil.createNewWindowIdByConversationId(project, conversationId)

            ApplicationManager.getApplication().executeOnPooledThread {
                // 获取上下文信息
                conversation.status = "gettingContextItem"
                val contextInputService = ContextInputService(project)
                val (_, fileRanges) = contextInputService.getContextCodeItemForInput(input, contextInputItems.toList())

                // 用上下文信息向core发送请求
                conversation.status = "chatting"

                sendToCore(project, conversationId, input, displayContent, createTime, contextInputItems, chatType, fileRanges, title, modelName, gitUrls, selectedWorkItems)
            }
        }else {
            uiAdapter.getAndActivateComponent(project)?.onChatResponseChunk(
                ComposerResponse(
                    WebViewRspCommand.COMPOSER_RESPONSE,
                    ComposerChatResponseData(conversationId,
                        false,
                        "系统加载未完成，请稍后")
                )
            )

            uiAdapter.getAndActivateComponent(project)?.onChatResponseChunk(
                ComposerResponse(
                    WebViewRspCommand.COMPOSER_RESPONSE,
                    ComposerChatResponseData(conversationId,
                        true,
                        "")
                )
            )
        }
    }

    private fun sendToCore(
        project: Project,
        conversationId: String,
        message: String = "",
        displayContent: String = "",
        createTime: String = "",
        contextInputItems: Array<ContextInputItem>,
        chatType: String = "code",
        referenceList: List<String> = emptyList(),
        title: String = "OpenAI",
        modelName: String = "d10_cpl",
        gitUrls: List<String>,
        selectedWorkItems: List<WorkItemInfo>?
    ) {
        // 获取历史对话记录
        val conversation = AgentHistoryUtil.getHistoryByConversationId(project, conversationId)

        // 构建历史消息列表
        val historyMessages =
            conversation?.toChatMessages(project, LocalStorageUtil.getUserId() ?: "") ?: mutableListOf()

        // 添加当前新消息
        val currentMessage = ChatMessage(
            role = "user",
            content = message,
            userId = LocalStorageUtil.getUserId() ?: "",
            project = project.name,
        )

        // 构建请求体数据结构
        val composerRequest = ChatMsg(
            chatType = chatType,
            project = ProjectItem(project.basePath!!, project.name, conversationId),
            messages = historyMessages + currentMessage,
            title = title,
            fileRanges = referenceList,
            modelName = modelName,
            oaiServerUrl = ConfigWrapper.getServerHost() + chatUrl,
            gitUrls = gitUrls,
            workItems = selectedWorkItems
        )

        // 保存新消息到历史记录
        AgentHistoryUtil.addHistory(
            project, listOf(
                mapOf(
                    "role" to "user", "content" to message, "displayContent" to displayContent, "createTime" to createTime, "contextInputItems" to contextInputItems.toList()
                )
            )
        )

        // 向core发送请求
        if (AgentManager.getInstance(project).getAgentCommClient(AgentNameConstant.CORE_AGENT) != null) {
            AgentManager.getInstance(project).getAgentCommClient(AgentNameConstant.CORE_AGENT)
                .request("api/chat", composerRequest, conversationId, null)
        } else {
            logger.warn("[cf] sent to core while core is null")
        }
    }

    override fun onAgentMessageHandler(text: String) {

        // api/Chat接口，返回不是json格式，前面会拼content，提前解析这种情形
        if (text.contains("\"messageType\":\"api/chat\"")) {
            val message = parseMessage(text) ?: return
            val conversation = conversations[message.messageId] ?: return
            handleComposerChatResponse(text, project, conversation)
        }

        // api/edits/getLatestInMemoryApplied接口，返回不是json格式，前面会拼content，提前解析这种情形
        if (text.contains("\"messageType\":\"api/edits/getLatestInMemoryApplied\"")) {
            val message = parseDiffFileMsg(text) ?: return
            val conversation = conversations[message.messageId] ?: return
            handleComposerDiffFileResponse(text, project, conversation)
        }

        if (text.contains("\"messageType\":\"indexProgress\"")) {
            val indexProgressData = gson.fromJson(text, IndexProgressMessage::class.java).data
            val progress = indexProgressData.progress
            val status = indexProgressData.status
            if (status == "indexing") {
                CodeIndexService.setIndexStatus(project, CodeIndexService.IndexStatus.INDEXING)
                CodeIndexService.setIndexProgress(project, (progress * 100).toInt())
                throttledUpdateNotify()
            } else if (status == "done" || status == "failed") {
                CodeIndexService.setIndexStatus(project, CodeIndexService.IndexStatus.IDLE)
                CodeIndexService.setIndexProgress(project, 100)
                updateStatusNotify()
            }
        }

//        if (!text.startsWith("{") || !text.endsWith("}") || !isValidJson(text)) {
        if (!text.startsWith("{") || !text.endsWith("}")) {
            return
        }

        val responseMap = gson.fromJson(text, ComposerResponseMessage::class.java)

        val messageId = responseMap.messageId
        val messageType = responseMap.messageType
        val data = responseMap.data

        // 协议接口处理
        if (ideMessageTypes.contains(messageType)) {
            ideProtocolClient.handleMessage(text) { data ->
                val message = gson.toJson(
                    mapOf(
                        "messageId" to messageId,
                        "messageType" to messageType,
                        "data" to data
                    )
                )
                // 向core发送请求
                AgentManager.getInstance(project).getAgentCommClient(AgentNameConstant.CORE_AGENT).write(message)
            }
        }
    }

    private fun handleComposerChatResponse(data: Any?, project: Project, conversation: Conversation) {
        val message = parseMessage(data as String) ?: return
        val content = message.data.content
        val end = message.data.done
        if (!conversation.messageCache.containsKey(message.messageId)) {
            conversation.messageCache[message.messageId] = StringBuffer()
        }
        val builder = conversation.messageCache[message.messageId]
        val uiComponent = uiAdapter.getAndActivateComponent(project)

//        if (end && uiAdapter.isCurrentWindow(project, conversation.conversationId)) {
        if (end) {
            //渲染最后一条回答的返回值
            ApplicationManager.getApplication().invokeLater {
                if (builder == null || builder.toString().isEmpty()) {
                    runBlocking {
                        uiComponent?.onChatResponseChunk(
                            ComposerResponse(
                                WebViewRspCommand.COMPOSER_RESPONSE,

                                // todo: 后续优化
                                ComposerChatResponseData(message.messageId, true, "")
//                                ComposerChatResponseData(message.messageId, true, "AI服务返回消息失败，请检查配置")
                            )
                        )
                    }
                } else {
                    val aiResponse = builder.toString()
                    runBlocking {
                        uiComponent?.onChatResponseChunk(
                            ComposerResponse(
                                WebViewRspCommand.COMPOSER_RESPONSE,
                                ComposerChatResponseData(message.messageId, true, content)
                            )
                        )
                    }

                    // 保存AI的回复到历史记录
                    AgentHistoryUtil.addHistory(
                        project, listOf(
                            mapOf(
                                "role" to "assistant", "content" to aiResponse, "displayContent" to "", "createTime" to "", "contextInputItems" to emptyList<ContextInputItem>()
                            )
                        )
                    )
                }
                conversation.messageCache.remove(message.messageId)
            }

            //准备获取修改的文件并进行弹窗
            val projectItem = project.basePath?.let { ProjectItem(it, project.name, conversation.conversationId) }

            // 向core发送请求
            AgentManager.getInstance(project).getAgentCommClient(AgentNameConstant.CORE_AGENT).request(
                "api/edits/getLatestInMemoryApplied", DiffFileMsg(projectItem), conversation.conversationId, null
            )
            return
        }
        
//        if (message.data.content.isNotEmpty() && uiAdapter.isCurrentWindow(project, conversation.conversationId)) {
        // 流式回答不需要拼接，但是builder里拼接内容，为end时渲染最后一条回答的返回值。
        builder?.append(content)
        // 渲染回答的返回值
        if (content.isNotBlank()) {
            ApplicationManager.getApplication().invokeLater {
                runBlocking {
                    uiComponent?.onChatResponseChunk(
                        ComposerResponse(
                            WebViewRspCommand.COMPOSER_RESPONSE,
                            ComposerChatResponseData(message.messageId, false, content)
                        )
                    )
                }
            }
        }
//        }
    }

    private fun handleComposerDiffFileResponse(data: Any?, project: Project, conversation: Conversation) {
        val message = parseDiffFileMsg(data.toString()) ?: return
        if (message.data.isEmpty()) {
            DebugLogUtil.warn("[cf] diff消息为空，忽略处理。")
            return
        }

        try {
            // Handle diff files
            val diffService = DiffService(project, message)
            DiffService.setInstance(project, diffService)

            val uiComponent = uiAdapter.getAndActivateComponent(project)
            uiComponent?.sendDiffStatusChangedResponse(
                DiffStatusChangedResponse(
                    WebViewRspCommand.DIFF_STATUS_CHANGED,
                    DiffStatusChangedResponseData(diffService.getFiles())
                )
            )

            // After handling, remove the conversation
            conversations.remove(conversation.conversationId)
        } catch (e: Exception) {
            logger.warn("[cf] 处理diff文件响应错误: ${e.message}")
        }
    }

    fun handleError(project: Project) {
        //渲染返回值
        val uiComponent = uiAdapter.getAndActivateComponent(project)
        ApplicationManager.getApplication().invokeLater {
            runBlocking {
//                uiComponent?.onChatResponseChunk(ComposerChatResponseData(message.messageId,true, "AI尝试修改文件失败，请重试"))
            }
        }
    }

    fun stopConversation(conversationId: String) {

        // 向core发送请求
        AgentManager.getInstance(project).getAgentCommClient(AgentNameConstant.CORE_AGENT).request(
            "api/stopChat", null, conversationId, null
        )

//        conversations.remove(conversationId)
        val uiComponent = uiAdapter.getAndActivateComponent(project)
        uiComponent?.sendStopChatResponse(
            ComposerResponse(
                WebViewRspCommand.COMPOSER_RESPONSE,
                ComposerStopComposerChatResponseData(conversationId)
            )
        )
    }

    fun loadHistory(conversationId: String) {
        val uiComponent = uiAdapter.getAndActivateComponent(project)
        var history = AgentHistoryUtil.getHistoryByConversationId(project, conversationId) ?: return

        uiComponent?.sendLoadHistoryChatResponse(
            ComposerResponse(
                WebViewRspCommand.COMPOSER_RESPONSE,
                LoadHistoryResponseData(conversationId, history.toTypedArray())
            )
        )
    }

    fun deleteHistory(conversationId: String) {
        val uiComponent = uiAdapter.getAndActivateComponent(project)
        AgentHistoryUtil.deleteHistoryByConversationId(project, conversationId) ?: return
        uiComponent?.sendDeleteHistoryChatResponse(
            ComposerResponse(
                WebViewRspCommand.COMPOSER_RESPONSE,
                DeleteHistoryResponseData(conversationId)
            )
        )
    }

    private fun parseMessage(input: String): Message? {
        try {
            // 首先解析完整字符串，获取 content 值
            val jsonString = findJsonPart(input)
            val message = gson.fromJson(jsonString, Message::class.java)
            return message
        } catch (e: Exception) {
            logger.warn("[cf] 解析错误: ${e.message}")
            return null
        }
    }

    private fun findJsonPart(input: String): String {
        // 从左到右查找第一个有效的JSON开始位置
        var jsonStartIndex = input.indexOfFirst { it == '{' }

        while (jsonStartIndex != -1) {
            try {
                val possibleJson = input.substring(jsonStartIndex)
                // 验证是否为有效的JSON
                JsonParser.parseString(possibleJson)
                return possibleJson
            } catch (e: Exception) {
                // 如果解析失败，继续寻找下一个 '{'
                jsonStartIndex = input.indexOf('{', jsonStartIndex + 1)
            }
        }
        throw IllegalArgumentException("未找到有效的JSON部分")
    }

    private fun parseDiffFileMsg(input: String): DiffMessage? {
        try {
            // 首先解析完整字符串，获取 content 值
            val message = gson.fromJson(input, DiffMessage::class.java)
            return message
        } catch (e: Exception) {
            logger.warn("[cf] 解析diff文件错误: ${e.message}")
            return null
        }
    }

    fun isEnable():Boolean {
        val client = AgentManager.getInstance(project).getAgentCommClient(AgentNameConstant.CORE_AGENT)

        DebugLogUtil.info("[cf] composer service enabled: " + (client != null && client.isEnabled))
        return client != null && client.isEnabled
    }
}