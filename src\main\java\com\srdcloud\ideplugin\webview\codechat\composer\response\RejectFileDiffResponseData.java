package com.srdcloud.ideplugin.webview.codechat.composer.response;

import com.srdcloud.ideplugin.webview.codechat.composer.request.ComposerRequest;

public class RejectFileDiffResponseData extends ComposerResponseData {
    private String path;
    private String error;

    public RejectFileDiffResponseData(String path, String error) {
        super(ComposerRequest.REQ_TYPE_REJECT_FILE_DIFF);
        this.path = path;
        this.error = error;
    }

    public String getPath() {
        return path;
    }

    public String getError() {
        return null;
    }
}