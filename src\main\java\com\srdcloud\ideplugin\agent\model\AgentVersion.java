package com.srdcloud.ideplugin.agent.model;

import com.fasterxml.jackson.annotation.JsonProperty;

public class AgentVersion {
    @JsonProperty("type")
    private final String agentName;
    private final String version;
    private final String downloadUrl;
    private final String md5;

    public AgentVersion(
            @JsonProperty("type") String agentName,
            @JsonProperty("version") String version,
            @JsonProperty("downloadUrl") String downloadUrl,
            @JsonProperty("md5") String md5
    ) {
        this.agentName = agentName;
        this.version = version;
        this.downloadUrl = downloadUrl;
        this.md5 = md5;
    }

    public String getAgentName() {
        return agentName;
    }

    public String getVersion() {
        return version;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public String getMd5() {
        return md5;
    }
}