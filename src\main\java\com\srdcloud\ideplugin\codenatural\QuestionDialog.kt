package com.srdcloud.ideplugin.codenatural

import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.AnimatedIcon
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBScrollPane
import com.intellij.ui.components.JBTextArea
import com.intellij.ui.layout.applyToComponent
import com.intellij.ui.layout.panel
import com.srdcloud.ideplugin.codecomplete.domain.CompletionType
import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.prompt.EditorPromptProvider.Companion.getCompletionPrompt
import com.srdcloud.ideplugin.general.constants.Constants
import com.srdcloud.ideplugin.general.constants.RtnCode
import com.srdcloud.ideplugin.general.constants.RtnMessage
import com.srdcloud.ideplugin.general.enums.AnswerMode
import com.srdcloud.ideplugin.general.enums.ChatMessageType
import com.srdcloud.ideplugin.general.utils.ExtractCode
import com.srdcloud.ideplugin.general.utils.GitUtil
import com.srdcloud.ideplugin.service.LoginService
import com.srdcloud.ideplugin.service.QuestionTask
import com.srdcloud.ideplugin.service.domain.apigw.ApigwWebsocketRespPayload
import com.srdcloud.ideplugin.service.domain.codechat.AskQuestionParams
import com.srdcloud.ideplugin.service.interfaces.IQuestionTaskEventHandler
import java.awt.Dimension
import java.awt.event.ActionEvent
import java.util.*
import java.util.concurrent.atomic.AtomicBoolean
import javax.swing.Action
import javax.swing.JComponent
import javax.swing.ScrollPaneConstants.VERTICAL_SCROLLBAR_AS_NEEDED
import javax.swing.Timer

class QuestionDialog(val editor: Editor) : DialogWrapper(true) {
    private val logger = logger<QuestionDialog>()

    private val textArea = JBTextArea()
    private val loadingLabel = JBLabel()
    private val msgText = JBLabel()
    private val scrollPane = JBScrollPane(textArea)

    private lateinit var askAction: Action

    private var questionTask: QuestionTask? = null
    private var reqId = ""
    private var result = RtnCode.SUCCESS
    private var insertEditor: InsertEditor? = null

    private val closeLater = AtomicBoolean(false)

    init {
        init()

        setResizable(false)
        title = "自然语言编程"

        loadingLabel.isVisible = false

        textArea.lineWrap = true
        textArea.wrapStyleWord = true


        val timer = Timer(200) {
            if (closeLater.get()) {
                close(OK_EXIT_CODE)
            }
        }
        timer.isRepeats = true
        timer.start()

        if (LoginService.getLoginStatus() == Constants.LoginStatus_NOK) {
            statusChanged(false, "请先登录！")
            askAction.isEnabled = false
        }

    }

    override fun createCenterPanel(): JComponent {
        return panel {
            row {
                label("请输入问题：")
            }
            row {
                scrollPane().applyToComponent {
                    verticalScrollBarPolicy = VERTICAL_SCROLLBAR_AS_NEEDED
                }
            }
            row {
                cell {
                    loadingLabel().applyToComponent {
                        icon = AnimatedIcon.Default()
                    }
                    msgText()
                }
            }
        }.apply {
            preferredSize = Dimension(600, 200)
        }
    }

    fun getText(): String {
        return textArea.text
    }

    override fun createActions(): Array<Action> {
        askAction = object : OkAction() {
            init {
                putValue(Action.NAME, "提问")
            }

            override fun doAction(e: ActionEvent?) {
                if (textArea.text.isNotBlank()) {
                    questionTask = QuestionTask(object :
                        IQuestionTaskEventHandler {

                        override fun onAnswer(
                            regId: String?,
                            isEnd: Int,
                            accumulateAnswer: String?,
                            segmentAnswer: String?,
                            seqNo: Int,
                            payload: ApigwWebsocketRespPayload?
                        ) {
//                            logger.info("[cf] nlc OnAnswer: $answer, isEnd: $isEnd, regId: $regId, reqId: $reqId") // 本地调试
                            if (reqId == regId) { // ignore other request's response
                                val answerText = ExtractCode().extractFirstCodeBlock(accumulateAnswer)
                                logger.debug("[cf] answerText: $answerText\n")
                                if (answerText.isNotBlank()) {
                                    if (insertEditor == null) {
                                        closeLater.set(true)
                                        insertEditor = InsertEditor(editor)
                                    }
                                    insertEditor?.appendText(answerText)
                                }

                                if (isEnd == 1) {
                                    // 重置资源
                                    finish()
                                }
                            }
                        }

                        override fun onTaskError(regId: String?, eventId: Int, payload: ApigwWebsocketRespPayload?) {
                            if (reqId == regId) { // ignore other request's response
                                logger.warn("[cf] nlc OnTaskError: $eventId, regId: $regId")
                                statusChanged(false, RtnMessage.getMessageByCode(eventId))
                            }
                        }

                    }, AnswerMode.ASYNC.value)

                    val prompt = editor.getCompletionPrompt(CompletionType.MANUAL)
                    reqId = UUID.randomUUID().toString()
                    result = questionTask?.AskQuestion(
                        AskQuestionParams(
                            reqId,
                            textArea.text,
                            null,
                            Constants.QUESTION_TASK_TYPE_NATURAL_LANGUAGE,
                            prompt.fileName,
                            prompt.prefix,
                            prompt.suffix,
                            ChatMessageType.MANUAL_GENERATE.type,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            GitUtil.getGitUrls(editor.project),
                            null,
                            null
                        )
                    ) ?: RtnCode.SUCCESS
                    statusChanged(true, "")
                } else {
                    statusChanged(false, "问题不能为空")
                }
            }
        }

        val cancelAction = object : OkAction() {
            init {
                putValue(Action.NAME, "取消")
            }

            override fun doAction(e: ActionEvent?) {
                reqId = ""
                super.doAction(e)
            }
        }
        return arrayOf(
            cancelAction,
            askAction
        )
    }

    private fun statusChanged(loading: Boolean, errorMsg: String) {
        loadingLabel.isVisible = loading
        askAction.isEnabled = !loading
        if (loading) {
            msgText.text = "正在寻找答案..."
        } else {
            msgText.text = errorMsg
        }
    }

    private fun finish() {
        questionTask = null
        insertEditor?.finish()
        insertEditor = null
        if (!isDisposed) {
            statusChanged(false, "未获取到答案，请重新提问")
        }
    }

}