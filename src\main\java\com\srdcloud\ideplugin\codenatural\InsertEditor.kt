package com.srdcloud.ideplugin.codenatural

import com.intellij.diff.util.DiffGutterRenderer
import com.intellij.openapi.actionSystem.ActionGroup
import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.command.CommandProcessor
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.diff.DiffColors
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.impl.EditorImpl
import com.intellij.openapi.editor.markup.GutterIconRenderer
import com.intellij.openapi.editor.markup.HighlighterLayer
import com.intellij.openapi.editor.markup.HighlighterTargetArea
import com.intellij.openapi.editor.markup.RangeHighlighter
import com.intellij.openapi.ui.popup.JBPopupFactory
import com.intellij.psi.PsiDocumentManager
import com.intellij.psi.codeStyle.CodeStyleManager
import com.intellij.ui.awt.RelativePoint
import com.intellij.util.ObjectUtils
import com.srdcloud.ideplugin.codenatural.HighlightState.Companion.getHighlightText
import com.srdcloud.ideplugin.codenatural.HighlightState.Companion.initOrGetHighlightState
import com.srdcloud.ideplugin.common.icons.MyIcons
import com.srdcloud.ideplugin.general.enums.ActivityType
import com.srdcloud.ideplugin.service.UserActivityReportService
import java.awt.event.MouseEvent
import java.util.*
import javax.swing.Icon
import kotlin.concurrent.schedule

class InsertEditor(private val editor: Editor) {
    private lateinit var highlight: RangeHighlighter
    private var currentOffset: Int = 0
    private var oldOffset: Int = 0
    private val lock = Any()
    private var currentText: StringBuilder = StringBuilder()

    init {
        // 获取当前光标位置
        val caretModel = editor.caretModel
        (editor as? EditorImpl)?.isViewer = true

        // 在当前光标位置插入回车符
        WriteCommandAction.runWriteCommandAction(editor.project) {
            val document = editor.document
            oldOffset = caretModel.offset
            val lineEndOffset = document.getLineEndOffset(document.getLineNumber(oldOffset))

            // 在当前行末尾插入回车符
            document.insertString(lineEndOffset, "\n")

            // 移动光标到新行的起始位置
            currentOffset = lineEndOffset + 1
            caretModel.moveToOffset(currentOffset)
        }
    }

    fun appendText(text: String) {
        synchronized(lock) {
            var remainingText = text

            val commonPrefixLength = getCommonPrefixLength(currentText.toString(), remainingText)

            val newPart = remainingText.substring(commonPrefixLength)
            currentText.append(newPart)
            remainingText = newPart

            for (char in remainingText) {
                val application = ApplicationManager.getApplication()
                application.invokeLater {
                    val project = editor.project
                    CommandProcessor.getInstance().executeCommand(project, {
                        application.runWriteAction {
                            val tmpText = char.toString()
                            editor.document.insertString(currentOffset, tmpText)
                            val end = currentOffset + tmpText.length
                            editor.caretModel.moveToOffset(end)

                            val psiFile = PsiDocumentManager.getInstance(project!!).getPsiFile(editor.document)

                            if (psiFile != null && tmpText.isNotBlank()) {
//                                val codeStyleSettings = CodeStyleSettingsManager.getInstance(project)

                                val codeStyleManager = CodeStyleManager.getInstance(project)
                                codeStyleManager.reformatText(psiFile, oldOffset, end)
                            }
                            currentOffset = editor.caretModel.offset
                            if (this::highlight.isInitialized) {
                                editor.markupModel.removeHighlighter(highlight)
                            }
                            highlight = editor.markupModel.addRangeHighlighter(
                                DiffColors.DIFF_INSERTED,
                                oldOffset,
                                currentOffset,
                                HighlighterLayer.ADDITIONAL_SYNTAX,
                                HighlighterTargetArea.EXACT_RANGE
                            )
                        }
                    }, "Insert Text", null)
                }
                remainingText = remainingText.substring(char.toString().length)
            }
        }
    }

    private fun getCommonPrefixLength(current: String, next: String): Int {
        val minLength = current.length.coerceAtMost(next.length)
        for (i in 0 until minLength) {
            if (current[i] != next[i]) {
                return i
            }
        }
        return minLength
    }

    fun finish() {
        (editor as? EditorImpl)?.isViewer = false
        ApplicationManager.getApplication().invokeLater {
//            highlight.gutterIconRenderer =
//                CheckboxDiffGutterRenderer(MyIcons.NotLoggedIn, "Test\ntest1\ntest2", {
//                    editor.markupModel.removeHighlighter(highlight)
//                }, {
//                    editor.document.deleteString(highlight.startOffset, highlight.endOffset)
//                    editor.markupModel.removeHighlighter(highlight)
//                })
            highlight.gutterIconRenderer = MyGutterIconRenderer(editor)
            editor.initOrGetHighlightState().highlights += highlight
            // 进行埋点上报
            UserActivityReportService.codeActivityReport(
                ActivityType.CODE_CHAT_GEN,
                editor.project,
                editor.getHighlightText(highlight),
                null
            );

        }
    }

    private class MyGutterIconRenderer(val editor: Editor) : GutterIconRenderer() {
        override fun equals(other: Any?) = this === other

        override fun hashCode() = System.identityHashCode(this)
        override fun getIcon(): Icon {
            return MyIcons.NLC
        }

        override fun getClickAction(): AnAction? {
            editor.initOrGetHighlightState().currentRenderer = this
            return MyPopupAction {
                Timer().schedule(400L) {
                    editor.initOrGetHighlightState().currentRenderer = null
                }
            }
        }

        override fun isNavigateAction(): Boolean {
            return true
        }

        override fun getTooltipText(): String? {
            return "Test"
        }
    }

    private class MyPopupAction(val disposeCallback: Runnable) : AnAction() {
        override fun actionPerformed(e: AnActionEvent) {
            val popup = JBPopupFactory.getInstance().createActionGroupPopup(
                null,
                ActionManager.getInstance().getAction("IDEPlugin.NLC") as ActionGroup,
                e.dataContext,
                JBPopupFactory.ActionSelectionAid.MNEMONICS,
                true,
                disposeCallback,
                -1
            )
//            e.inputEvent?.component?.let { popup.showUnderneathOf(it) }
            if (e.inputEvent is MouseEvent) {
                popup.show(RelativePoint(e.inputEvent as MouseEvent))
            }
        }

    }

    private class CheckboxDiffGutterRenderer(
        icon: Icon,
        tooltip: String?,
        val leftClick: Runnable,
        val rightClick: Runnable
    ) : DiffGutterRenderer(icon, tooltip) {
        override fun performAction(e: AnActionEvent) {
            val mouseEvent = ObjectUtils.tryCast(
                e.inputEvent,
                MouseEvent::class.java
            )
            if (mouseEvent == null || mouseEvent.button == MouseEvent.BUTTON1) {
                leftClick.run()
            } else if (mouseEvent.button == MouseEvent.BUTTON3) {
                rightClick.run()
            }
        }

        override fun handleMouseClick() {

        }
    }
}