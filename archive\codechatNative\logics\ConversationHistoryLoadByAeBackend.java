package com.srdcloud.ideplugin.assistant.codechatNative.logics;

import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.Conversation;
import com.srdcloud.ideplugin.general.enums.SubServiceType;
import com.srdcloud.ideplugin.remote.ChatHistoryHandler;
import com.srdcloud.ideplugin.remote.domain.Dialog.ListDialogsResponse;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.DialogBaseInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yangy
 * @create 2024/5/14 10:43
 */
public class ConversationHistoryLoadByAeBackend {
    private static final Logger logger = LoggerFactory.getLogger(ConversationHistoryLoadByAeBackend.class);

    public ConversationHistoryLoadByAeBackend() {
    }

    private static Map<String, Conversation> listDialogsData(int limit, String keyword) {
        Map<String, Conversation> conversationMap = new LinkedHashMap();
        try {
            if (limit == 0) {
                limit = 30;
            }
            ListDialogsResponse listDialogsResponse = ChatHistoryHandler.listDialogs(SubServiceType.getSubServiceTypeAll(), 1, limit, keyword);
            if (listDialogsResponse != null && listDialogsResponse.getDialogs() != null && !listDialogsResponse.getDialogs().isEmpty()) {
                List<DialogBaseInfo> dialogs = listDialogsResponse.getDialogs();
                dialogs.sort(Comparator.comparing(DialogBaseInfo::getSortDateTime));
                for (int i = 0; i < dialogs.size(); i++) {
                    DialogBaseInfo dialog = dialogs.get(i);
                    Conversation conversation = new Conversation(dialog.getDialogId(), dialog.getTitle(), false);
                    conversation.setConversationOrder(i + 1);
                    conversation.setSubServiceType(dialog.getSubService());
                    conversation.setCreateTime(dialog.getCreateTime());
                    conversation.setUpdateTime(dialog.getUpdateTime());
                    conversationMap.put(dialog.getDialogId(), conversation);
                }
            }
        } catch (Exception e) {
            logger.error("[cf] loadLocalConversationList error:{}", e.getMessage());
        }
        return conversationMap;
    }

    /**
     * 查询历史会话列表
     */
    public static Map<String, Conversation> loadConversationHistoryList(Project project, int limit, String keyword) {
        // 从远端查询历史对话数据
        Map<String, Conversation> conversationMap = listDialogsData(limit, keyword);

        // 将历史会话数据，装在到project Service中
        if (project != null) {
            ConversationManagerByAeBackend.getInstance(project).loadHistoryConversations(conversationMap);
        }

        return conversationMap;
    }
}
