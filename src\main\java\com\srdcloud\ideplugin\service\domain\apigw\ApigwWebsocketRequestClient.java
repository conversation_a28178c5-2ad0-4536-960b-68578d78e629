package com.srdcloud.ideplugin.service.domain.apigw;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/25
 */
public class ApigwWebsocketRequestClient implements Serializable {
    /**
     * 客户端类型
     */
    private String type;

    /**
     * 客户端版本号
     */
    private String version;

    /**
     * 客户端插件版本号
     */
    private String pluginVersion;

    /**
     * 项目git地址字段命名
     */
    private String gitUrl;

    /**
     * 项目git地址[多个]
     */
    private List<String> gitUrls;

    /**
     * 项目名字段命名
     */
    private String projectName;

    public ApigwWebsocketRequestClient() {
    }

    public ApigwWebsocketRequestClient(String type, String version, String pluginVersion) {
        this.type = type;
        this.version = version;
        this.pluginVersion = pluginVersion;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getPluginVersion() {
        return pluginVersion;
    }

    public void setPluginVersion(String pluginVersion) {
        this.pluginVersion = pluginVersion;
    }

    public String getGitUrl() {
        return gitUrl;
    }

    public void setGitUrl(String gitUrl) {
        this.gitUrl = gitUrl;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public List<String> getGitUrls() {
        return gitUrls;
    }

    public void setGitUrls(List<String> gitUrls) {
        this.gitUrls = gitUrls;
    }
}
