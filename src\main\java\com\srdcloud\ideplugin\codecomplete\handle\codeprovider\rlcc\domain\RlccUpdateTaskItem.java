package com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc.domain;

import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.VirtualFile;

/**
 * <AUTHOR>
 * @date 2024/11/21
 */
public class RlccUpdateTaskItem {

    private final Project project;


    private final VirtualFile file;

    // 标识是否为初始化关联文件解析任务的结束
    private boolean initTaskLast = false;

    public RlccUpdateTaskItem(Project project, VirtualFile file) {
        this.project = project;
        this.file = file;
    }

    public Project getProject() {
        return project;
    }

    public VirtualFile getFile() {
        return file;
    }

    public boolean isInitTaskLast() {
        return initTaskLast;
    }

    public void setInitTaskLast(boolean initTaskLast) {
        this.initTaskLast = initTaskLast;
    }
}
