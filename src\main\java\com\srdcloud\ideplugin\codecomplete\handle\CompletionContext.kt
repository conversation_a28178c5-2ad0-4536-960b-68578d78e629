package com.srdcloud.ideplugin.codecomplete.handle

import com.intellij.openapi.Disposable
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.ex.EditorEx
import com.intellij.openapi.editor.impl.EditorImpl
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.util.Key
import com.intellij.openapi.util.TextRange
import com.srdcloud.ideplugin.codecomplete.domain.CompletionElement
import com.srdcloud.ideplugin.codecomplete.domain.CompletionState.Companion.resetInlineCompletionState
import com.srdcloud.ideplugin.codecomplete.listener.CompletionFocusListener
import com.srdcloud.ideplugin.codecomplete.listener.CompletionCaretListener
import com.srdcloud.ideplugin.general.enums.ActivityType
import com.srdcloud.ideplugin.service.UserActivityReportService
import java.util.concurrent.atomic.AtomicBoolean

class CompletionContext private constructor(private val editor: Editor) : Disposable {
    private val isSelecting = AtomicBoolean(false)

    //    private val keyListener = InlineCompletionKeyListener(editor)
    private val inlay = ICompletion.forEditor(editor)

    private var selectedIndex = -1
    private var completions = emptyList<CompletionElement>()

    init {
        editor.caretModel.addCaretListener(CompletionCaretListener())
        if (editor is EditorEx) {
            editor.addFocusListener(CompletionFocusListener())
        }
    }

    val isCurrentlyDisplayingInlays: Boolean
        get() = !inlay.isEmpty

    val startOffset: Int?
        get() = inlay.offset

    fun insert() {
        isSelecting.set(true)
        CompletionHandler.mute()
        val offset = inlay.offset ?: return
        if (selectedIndex < 0) return
        val document = editor.document

        val caret = editor.caretModel.currentCaret
        val startOffset = document.getLineStartOffset(caret.logicalPosition.line)
        val endOffset = document.getLineEndOffset(caret.logicalPosition.line)

        val text = completions[selectedIndex].text
        val isAuto = completions[selectedIndex].isAuto
        var currentText: String? = null
        if (startOffset >= 0 && endOffset <= document.textLength) {
            currentText = document.getText(TextRange(startOffset, endOffset))
        }

        UserActivityReportService.codeActivityReport(
            ActivityType.CODE_COMPLETION_ACCEPTED,
            editor.project,
            text,
            currentText,
            isAuto,
            null
        );
        document.insertString(offset, text)
        editor.caretModel.moveToOffset(offset + text.length)

        isSelecting.set(false)
        CompletionHandler.unmute()
        editor.removeInlineCompletionContext()
        Disposer.dispose(this)
    }

    override fun dispose() {
        if (isSelecting.get()) {
            return
        }
        if (!inlay.isEmpty) {
            Disposer.dispose(inlay)
        }

//        editor.contentComponent.removeKeyListener(keyListener)
        editor.resetInlineCompletionState()
    }

    fun update(proposals: List<CompletionElement>, selectedIndex: Int, offset: Int) {
        this.completions = proposals
        this.selectedIndex = selectedIndex
        val proposal = proposals[selectedIndex]
        val text = proposal.text

        if (!inlay.isEmpty) {
            inlay.reset()
        }
        if (text.isNotEmpty() && editor is EditorImpl) {
            inlay.render(proposal, offset)
            if (!inlay.isEmpty) {
                Disposer.register(this, inlay)
//                editor.contentComponent.addKeyListener(keyListener)
            }
        }
    }


    companion object {
        private val INLINE_COMPLETION_CONTEXT_KEY =
            Key.create<CompletionContext>("com.srdcloud.ideplugin.completion.context")

        fun Editor.initOrGetInlineCompletionContext(): CompletionContext {
            return getUserData(INLINE_COMPLETION_CONTEXT_KEY) ?: CompletionContext(this).also {
                putUserData(INLINE_COMPLETION_CONTEXT_KEY, it)
            }
        }

        fun Editor.getInlineCompletionContextOrNull(): CompletionContext? =
            getUserData(INLINE_COMPLETION_CONTEXT_KEY)

        fun Editor.resetInlineCompletionContext(): Unit? = getInlineCompletionContextOrNull()?.let {
            if (it.isCurrentlyDisplayingInlays) {
                removeInlineCompletionContext()
                Disposer.dispose(it)
            }
        }

        private fun Editor.removeInlineCompletionContext(): Unit = putUserData(INLINE_COMPLETION_CONTEXT_KEY, null)
    }
}