// @fileoverview: 对象缓存索引系统
// @author: Jiangh
// @date: 2024-08-08
// @description: 用于存储Java代码对象的信息，包括包路径、文件名称、对象名称和对象内容
package com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc;

import com.alibaba.fastjson.JSON;
import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc.domain.CacheObject;
import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc.domain.CacheObjectType;
import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc.domain.CacheUpdateObject;
import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.rlcc.domain.RelativeCodeObject;
import com.srdcloud.ideplugin.general.enums.LanguageExt;
import com.srdcloud.ideplugin.general.utils.DebugLogUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 对象缓存索引系统
 * 实现存储关联对象的信息，用于全局搜索关联对象
 */
public class CodeObjectIndexSystem {
    private static final Logger logger = LoggerFactory.getLogger(CodeObjectIndexSystem.class);

    private Project myProject;

    /**
     * 缓存表：存储关联内容缓存
     * key：通过 generatePrimaryKey() 生成的12位长度唯一串
     * val：关联对象
     */
    private final ConcurrentHashMap<String, CacheObject> cacheTable;


    // 反向索引：通过各种反向维度，回溯关联对象的key，用于缓存查找

    private final static String KEY_SEP = "|";

    // 以“包路径+缓存对象名”维度建立的缓存key逆向索引
    private final ConcurrentHashMap<String, String> packageObjectKeyIndex;

    // 以“包路径+文件名”维度建立的缓存key逆向索引：逆向得到的索引是一个set，表示该文件下的多种缓存数据
    private final ConcurrentHashMap<String, Set<String>> packageFileKeyIndex;

    // 以“缓存类型”维度建立的缓存key逆向索引：逆向得到的索引是一个set，表示该objectName下的多种缓存数据
    private final ConcurrentHashMap<String, Set<String>> objectNameKeyIndex;

    // go模块路径和go包名称的映射关系，用于Go代码路径的索引查找
    private final ConcurrentHashMap<String, String> goModMap;
    // 预埋：根据后续扩展的语言不同，还可以增加更多维度的逆向拼接key的索引机制


    public CodeObjectIndexSystem() {
        this.cacheTable = new ConcurrentHashMap<>();

        this.packageObjectKeyIndex = new ConcurrentHashMap<>();
        this.packageFileKeyIndex = new ConcurrentHashMap<>();
        this.objectNameKeyIndex = new ConcurrentHashMap<>();
        this.goModMap = new ConcurrentHashMap<>();
    }

    /**
     * 生成缓存表唯一主键，主键为12位长度的随机字符串
     */
    private String generatePrimaryKey() {
        return String.format("%012d", ThreadLocalRandom.current().nextLong(1, 1_000_000_000_000L));
    }

    // ==== 缓存数据增操作 ====

    /**
     * 插入一条缓存数据，并建立各维度反向索引
     */
    public synchronized void insert(String packagePath, String fileName, String objectType, String objectName, String objectBody) {
        // 生成缓存唯一主键
        String primaryKey = generatePrimaryKey();
        // 创建缓存对象
        CacheObject cacheObject = new CacheObject(primaryKey, packagePath, fileName, objectType, objectName, objectBody);
        // 插入缓存表
        cacheTable.put(primaryKey, cacheObject);

        //建立各维度的反向索引
        packageObjectKeyIndex.put(packagePath + KEY_SEP + objectName, primaryKey);
        packageFileKeyIndex.computeIfAbsent(packagePath + KEY_SEP + fileName, k -> new HashSet<>()).add(primaryKey);
        objectNameKeyIndex.computeIfAbsent(objectName, k -> new HashSet<>()).add(primaryKey);
    }


    // ==== 缓存数据删操作 ====

    /**
     * 直接传入主键删除缓存数据
     */
    private synchronized void deleteByPrimaryKey(String primaryKey) {
        // 移除缓存表数据
        CacheObject cacheObject = cacheTable.remove(primaryKey);

        // 移除其他反向索引数据
        if (cacheObject != null) {
            packageObjectKeyIndex.remove(cacheObject.getPackagePath() + KEY_SEP + cacheObject.getObjectName());
            packageFileKeyIndex.getOrDefault(cacheObject.getPackagePath() + KEY_SEP + cacheObject.getFileName(), new HashSet<>()).remove(primaryKey);
            objectNameKeyIndex.getOrDefault(cacheObject.getObjectName(), new HashSet<>()).remove(primaryKey);
        }
    }

    /**
     * 按文件维度进行删除
     *
     * @param packagePath
     * @param key
     */
    private void deleteFileRelative(String packagePath, String key) {
        for (String primaryKey : packageFileKeyIndex.getOrDefault(key, new HashSet<>())) {
            // 移除缓存表数据
            CacheObject cacheObject = cacheTable.remove(primaryKey);

            // 移除其他反向索引数据
            if (cacheObject != null) {
                packageObjectKeyIndex.remove(packagePath + KEY_SEP + cacheObject.getObjectName());
                objectNameKeyIndex.getOrDefault(cacheObject.getObjectName(), new HashSet<>()).remove(primaryKey);
            }
        }

        // 移除包内文件反向索引
        packageFileKeyIndex.remove(key);
    }


    /**
     * 删除某包下某文件的缓存数据
     *
     * @param packagePath
     * @param fileName
     */
    public synchronized void deleteByPkgFile(String packagePath, String fileName) {
        deleteFileRelative(packagePath, packagePath + KEY_SEP + fileName);
    }

    /**
     * 删除某文件的缓存数据
     *
     * @param fileName
     */
    public synchronized void deleteByFileName(String fileName) {
        for (String key : packageFileKeyIndex.keySet()) {
            if (key.endsWith(KEY_SEP + fileName)) {
                String packageName = key.substring(0, key.indexOf(KEY_SEP));
                deleteFileRelative(packageName, key);
            }
        }
    }


    // ==== 缓存数据更新操作 ====

    /**
     * upsert接口：如果缓存存在则更新，否则插入
     */
    public synchronized void insertOrUpdateByFileObject(String packagePath, String fileName, String objectType, String objectName, String objectBody) {
        // 尝试查找是否存在该文件下，对应对象的缓存数据，存在则更新objectBody内容即可
        for (String primaryKey : packageFileKeyIndex.getOrDefault(packagePath + KEY_SEP + fileName, new HashSet<>())) {
            CacheObject cacheObject = cacheTable.get(primaryKey);
            if (cacheObject != null && cacheObject.getObjectName().equals(objectName)) {
                cacheObject.setObjectBody(objectBody);
                cacheTable.put(primaryKey, cacheObject);
                return;
            }
        }
        // 没有，则插入新缓存
        insert(packagePath, fileName, objectType, objectName, objectBody);
    }

    /**
     * 更改文件名反向索引表中的fileName：由文件重命名引起的缓存内容更新
     * 已废弃：重命名操作走先删后插入的fileChange机制
     */
    @Deprecated
    public synchronized void updateFileName(String packagePath, String oldFileName, String newFileName) {
        for (String primaryKey : new HashSet<>(packageFileKeyIndex.getOrDefault(packagePath + KEY_SEP + oldFileName, new HashSet<>()))) {
            CacheObject cacheObject = cacheTable.get(primaryKey);
            if (cacheObject != null && cacheObject.getPackagePath().equals(packagePath)) {
                cacheObject.setFileName(newFileName);
                packageFileKeyIndex.get(packagePath + KEY_SEP + oldFileName).remove(primaryKey);
                packageFileKeyIndex.computeIfAbsent(packagePath + KEY_SEP + newFileName, k -> new HashSet<>()).add(primaryKey);
            }
        }
    }

    /**
     * 更新某文件中提取出来的所有关联内容
     */
    public synchronized void updateByFile(String filePackagePath, String fileName, List<CacheUpdateObject> cacheObjects) {
        if (CollectionUtils.isEmpty(cacheObjects)) {
            return;
        }

        // 遍历该文件所有关联内容，获取package和后缀都相同的对象，重新组合用于后续处理
        // 二级缓存
        Map<String, Map<String, CacheUpdateObject>> objectsSamePackagePath = new HashMap<>();
        for (CacheUpdateObject cacheUpdateObject : cacheObjects) {
            String objectName = cacheUpdateObject.getObjectName();
            String packageSuffix = cacheUpdateObject.getPackageSuffix();

            // 一级缓存key：文件包路径+额外补充的外部类路径
            String key = filePackagePath + packageSuffix;
            // 二级缓存：objectName为key
            objectsSamePackagePath.computeIfAbsent(key, k -> new HashMap<>())
                    .put(objectName, cacheUpdateObject);
        }

        // 更新缓存
        for (Map.Entry<String, Map<String, CacheUpdateObject>> entry : objectsSamePackagePath.entrySet()) {
            // 关联对象的完全路径
            String fullPackagePath = entry.getKey();
            // 同一完全路径下的多个关联对象
            Map<String, CacheUpdateObject> objectsToUpdateMap = entry.getValue();

            // 初始化"包路径+文件"维度反向索引
            packageFileKeyIndex.computeIfAbsent(fullPackagePath + KEY_SEP + fileName, k -> new HashSet<>());

            // 分拣已存在的缓存
            Set<String> existingObjectNames = new HashSet<>();
            for (String primaryKey : new HashSet<>(packageFileKeyIndex.getOrDefault(fullPackagePath + KEY_SEP + fileName, new HashSet<>()))) {
                CacheObject cacheObject = cacheTable.get(primaryKey);
                if (cacheObject != null && cacheObject.getPackagePath().equals(fullPackagePath)) {
                    if (objectsToUpdateMap.containsKey(cacheObject.getObjectName())) {
                        // 覆盖更新缓存内容
                        CacheUpdateObject cacheUpdateObject = objectsToUpdateMap.get(cacheObject.getObjectName());
                        cacheObject.setObjectType(cacheUpdateObject.getObjectType());
                        cacheObject.setObjectName(cacheUpdateObject.getObjectName());
                        cacheObject.setObjectBody(cacheUpdateObject.getSimpleText());
                        cacheTable.put(primaryKey, cacheObject);

                        // 记录已经被更新过的关联对象
                        existingObjectNames.add(cacheObject.getObjectName());
                    } else {
                        // 识别为无需要纳入缓存系统的关联对象，则该"包路径+文件"下无需要继续留存的缓存数据，进行清除
                        deleteByPrimaryKey(primaryKey);
                    }
                }
            }

            // 需要新增的数据，进行插入
            for (Map.Entry<String, CacheUpdateObject> objectEntry : objectsToUpdateMap.entrySet()) {
                if (!existingObjectNames.contains(objectEntry.getKey())) {
                    CacheUpdateObject cacheUpdateObject = objectEntry.getValue();
                    insert(fullPackagePath, fileName, cacheUpdateObject.getObjectType(), cacheUpdateObject.getObjectName(), cacheUpdateObject.getSimpleText());
                }
            }
        }
    }


    // ==== 缓存数据查改操作:多维度、多方式查找 ====

    /**
     * 查询特定文件下，特定缓存类型的数据
     */
    public synchronized String[] queryByFileObject(String packagePath, String fileName, String objectName) {
        for (String primaryKey : packageFileKeyIndex.getOrDefault(packagePath + KEY_SEP + fileName, new HashSet<>())) {
            CacheObject cacheObject = cacheTable.get(primaryKey);
            if (cacheObject != null && cacheObject.getObjectName().equals(objectName)) {
                return new String[]{cacheObject.getFileName(), cacheObject.getObjectBody()};
            }
        }
        return null;
    }

    /**
     * 按包路径和缓存对象名精准查询一个关联对象
     */
    public synchronized RelativeCodeObject queryRelativeCodeObjectByPkgObject(String language, String packagePath, String objectName) {
        // fixme：调试用
        DebugLogUtil.println(String.format("queryRelativeCodeObjectByPkgObject,language:%s,packagePath:%s,objectName:%s", language, packagePath, objectName));

        if (StringUtils.isBlank(packagePath) && StringUtils.isBlank(objectName)) {
            logger.warn("[cf] queryRelativeCodeObjectByPackageAndObjectName skip, packagePath and objectName all blank.");
            return null;
        }

        // 根据不同语言，进行不同维度的查找逻辑
        if (LanguageExt.JAVA.getType().equalsIgnoreCase(language)) {
            return queryCommonRelativeByPathAndObj(packagePath, objectName);
        } else if (LanguageExt.GO.getType().equalsIgnoreCase(language)) {
            return queryGoRelative(packagePath, objectName);
        } else if (LanguageExt.JAVASCRIPT.getType().equalsIgnoreCase(language)) {
            return queryCommonRelativeByPathAndObj(packagePath, objectName);
        } else {
            logger.warn("[cf] queryRelativeCodeObjectByPackageAndObjectName skip, unsupported language:{}", language);
        }
        return null;
    }

    /**
     * 通用查找：通过包路径和关联对象名查找关联对象
     * 支持：Java、Javascript
     */
    private RelativeCodeObject queryCommonRelativeByPathAndObj(String packagePath, String objectName) {
        String relativeObject = "";
        String relativeText = "";

        //-- Java的关联对象：是精确包路径下的类
        // 从反向索引表，反查出缓存key
        String primaryKey = packageObjectKeyIndex.get(packagePath + KEY_SEP + objectName);
        if (StringUtils.isBlank(primaryKey)) {
            logger.warn("[cf] queryJavaRelative skip, primaryKey is blank,packagePath:{}, objectName:{}", packagePath, objectName);
            return null;
        }

        // 从缓存表中获取完整的缓存对象信息
        CacheObject cacheObject = cacheTable.get(primaryKey);
        if (Objects.isNull(cacheObject)) {
            logger.warn("[cf] queryJavaRelative skip, cacheObject is null,packagePath:{}, objectName:{}", packagePath, objectName);
            return null;
        }

        // 拼接关联信息
        relativeObject = cacheObject.getFileName();
        relativeText = cacheObject.getObjectBody();

        return new RelativeCodeObject(relativeObject, relativeText);
    }

    /**
     * 查找Go的关联内容
     */
    private RelativeCodeObject queryGoRelative(String packagePath, String objectName) {
        String relativeObject = "";
        String relativeText = "";

        //-- Go的关联对象：可以是精确包路径下的结构体或接口，也可以是整个包
        String primaryKey;

        // -- 某个具体关联类查找
        if (StringUtils.isNotBlank(objectName)) {
            primaryKey = packageObjectKeyIndex.get(packagePath + KEY_SEP + objectName);
            if (StringUtils.isBlank(primaryKey)) {
                logger.warn("[cf] queryGoRelative skip, primaryKey is blank,packagePath:{}, objectName:{}", packagePath, objectName);
                return null;
            }

            // 从缓存表中获取完整的缓存对象信息
            CacheObject cacheObject = cacheTable.get(primaryKey);
            if (Objects.isNull(cacheObject)) {
                logger.warn("[cf] queryGoRelative skip, cacheObject is null,packagePath:{}, objectName:{}", packagePath, objectName);
                return null;
            }

            // 拼接关联信息
            relativeObject = cacheObject.getFileName();
            relativeText = cacheObject.getObjectBody();
        } else {
            // -- 整个包查找
            relativeObject = packagePath;
            Map<String, Map<String, CacheObject>> cacheTypeObjectMap = new HashMap<>();
            // 分拣缓存数据类型
            for (String key : packageObjectKeyIndex.keySet()) {
                if (!key.startsWith(packagePath)) {
                    continue;
                }
                primaryKey = packageObjectKeyIndex.get(key);
                CacheObject cacheObject = cacheTable.get(primaryKey);
                if (Objects.isNull(cacheObject)) {
                    continue;
                }
                cacheTypeObjectMap.computeIfAbsent(cacheObject.getObjectType(), k -> new HashMap<>()).put(cacheObject.getObjectName(), cacheObject);
            }

            // 拼接关联信息
            StringBuilder relativeTextBuilder = new StringBuilder();
            // 1、优先关联：独立functions
            Map<String, CacheObject> functionMap = cacheTypeObjectMap.get(CacheObjectType.FUNCTION.getName());
            if (MapUtils.isNotEmpty(functionMap)) {
                functionMap.values().forEach(t -> relativeTextBuilder.append(t.getObjectBody()).append("\n"));
            }

            // 2、其次关联：结构体定义
            Map<String, CacheObject> structMap = cacheTypeObjectMap.get(CacheObjectType.STRUCT.getName());
            if (MapUtils.isNotEmpty(structMap)) {
                structMap.values().forEach(t -> {
                    String structBodyWithoutMethods = extractTypeString(t.getObjectBody());
                    relativeTextBuilder.append(structBodyWithoutMethods).append("\n");
                });
            }

            // 汇总关联信息
            relativeText = relativeTextBuilder.toString();
            if (StringUtils.isBlank(relativeText)) {
                logger.warn("[cf] queryGoRelative skip, relativeText is blank,packagePath:{}", packagePath);
                return null;
            }
        }
        return new RelativeCodeObject(relativeObject, relativeText);
    }

    /**
     * 提取仅有结构体定义的部分
     */
    private String extractTypeString(String input) {
        // 找到 "type" 的起始位置
        int startIndex = input.indexOf("type");
        if (startIndex == -1) {
            return ""; // 如果没有找到 "type"，返回空字符串
        }

        // 从 "type" 开始的位置向后查找第一个 "}"
        int endIndex = input.indexOf('}', startIndex);
        if (endIndex == -1) {
            return ""; // 如果没有找到 "}"，返回空字符串
        }

        // 提取从 "type" 到第一个 "}" 之间的字符串
        return input.substring(startIndex, endIndex + 1);
    }

    /**
     * 查询一个文件内的所有类型缓存数据
     */
    public synchronized Map<String, String> queryByPkgFile(String packagePath, String fileName) {
        Map<String, String> result = new HashMap<>();
        for (String primaryKey : packageFileKeyIndex.getOrDefault(packagePath + KEY_SEP + fileName, new HashSet<>())) {
            CacheObject cacheObject = cacheTable.get(primaryKey);
            if (cacheObject != null) {
                // 不同类型的缓存数据
                result.put(cacheObject.getObjectName(), cacheObject.getObjectBody());
            }
        }
        return result;
    }

    /**
     * 查询一个包路径下，所有文件的所有类型缓存数据
     * return：一个map，key为包下文件名，value为一个二级map(缓存类型，缓存数据）
     */
    public synchronized Map<String, Map<String, String>> queryFileCacheByPackage(String packagePath) {
        Map<String, Map<String, String>> result = new HashMap<>();
        for (String primaryKey : packageObjectKeyIndex.values()) {
            CacheObject cacheObject = cacheTable.get(primaryKey);
            if (cacheObject != null && cacheObject.getPackagePath().equals(packagePath)) {
                result.computeIfAbsent(cacheObject.getFileName(), k -> new HashMap<>()).put(cacheObject.getObjectName(), cacheObject.getObjectBody());
            }
        }
        return result;
    }

    /**
     * 查询一个包路径下，所有类型的缓存数据
     * return：不同文件下所有缓存数据打平的list
     */
    public synchronized List<CacheObject> queryObjectCacheByPackage(String packagePath) {
        List<CacheObject> result = new ArrayList<>();
        for (String primaryKey : packageObjectKeyIndex.values()) {
            CacheObject cacheObject = cacheTable.get(primaryKey);
            if (cacheObject != null && cacheObject.getPackagePath().equals(packagePath)) {
                result.add(cacheObject);
            }
        }
        return result;
    }

    /**
     * 检查某个包路径下特定关联对象的缓存是否存在
     * 废弃：改为走Webview侧本地副本进行检索
     */
    @Deprecated
    public synchronized boolean queryObjectInPackage(String packagePath, String objectName) {
        for (String primaryKey : packageObjectKeyIndex.values()) {
            CacheObject cacheObject = cacheTable.get(primaryKey);
            if (cacheObject != null && cacheObject.getPackagePath().equals(packagePath) &&
                    cacheObject.getObjectName().equals(objectName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 建立go mod路径映射关系：哪个dir路径下，对应哪个go module名称
     *
     * @param moduleName    模块名称
     * @param moduleDirPath 模块目录路径
     */
    public synchronized void updateGoModMap(String moduleName, String moduleDirPath) {
        this.goModMap.merge(moduleName, moduleDirPath, (oldValue, v) -> v);
    }


    /**
     * 获取go mod映射关系的JsonString
     *
     * @return
     */
    public String getGoModMap() {
        return JSON.toJSONString(goModMap);
    }

}

