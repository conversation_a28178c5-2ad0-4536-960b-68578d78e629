package com.srdcloud.ideplugin.webview.codechat.common;

/**
 * <AUTHOR>
 * @date 2025/1/10
 */
public class WebViewRspCommand {
    public static final String ANSWER_RECVED = "answer-recved";
    public static final String CONVERSATION_LOADED = "conversation-loaded";
    public static final String CONVERSATION_CHANGED = "conversation-changed";
    public static final String CONVERSATION_REFRESHED = "conversation-refreshed";
    public static final String CONVERSATION_REMOVED = "conversation-removed";
    public static final String CONVERSATION_ADDED = "conversation-added";
    public static final String CODE_SELECTION_ASKED = "code-selection-asked";
    public static final String RETURN_CODE_SELECTION = "return-code-selection";
    public static final String CODE_SELECTION_CHANGED = "code-selection-changed";
    public static final String SRD_CHAT_RESPONSE = "srd-chat-response";
    public static final String CHECK_IF_LOGIN_RESPONSE = "check-if-login-response";
    public static final String PROMPTS_RESPONSE = "prompts-response";
    public static final String SWITCH_CONVERSATION_RESPONSE = "switch-conversation-response";
    public static final String FEEDBACK_CONVERSATION_RESPONSE = "feedback-conversation-response";
    public static final String PUSH_LOGIN_STATUS_RESPONSE = "push-login-status-response";
    public static final String KNOWLEDGE_BASE_RESPONSE = "knowledge-base-response";
    public static final String CODE_SECURITY_SCAN_RESPONSE = "code-security-scan-response";
    public static final String CODE_SECURITY_SCAN_START = "code-security-scan-start";
    public static final String PUSH_THEME_CHANGED = "push-theme-changed";
    public static final String PUSH_NETWORK_STATUS_RESPONSE = "push-network-status-response";
    public static final String COMPOSER_RESPONSE = "composer-response";
    public static final String DIFF_VIEW_VERTICAL_RESPONSE = "diff-view-vertical-response";
    public static final String DIFF_STATUS_CHANGED = "diff-status-changed";
    public static final String INDEXING_RESPONSE = "indexing-response";
    public static final String GET_IDE_UTILS_RESPONSE = "get-ide-utils-response";
    public static final String QA_FOR_RELATED_FILES_RESPONSE = "qa-for-related-files-response";
    public static final String OPEN_TEXT_DOCUMENT_RESPONSE = "open-text-document";
    public static final String VIEW_DIFF = "view-diff";
    public static final String GET_DIRECTORY_STRUCTURE_RESPONSE = "get-directory-structure";
    public static final String FILE_EXCEED_LIMIT = "file-exceed-limit";
    public static final String WORKITEM_RESPONSE = "workitem-response";
}
