package com.srdcloud.ideplugin.general.config;

import com.intellij.DynamicBundle;
import org.jetbrains.annotations.Nls;
import org.jetbrains.annotations.NonNls;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.PropertyKey;

/**
 * 生产环境动态配置，支持流水线动态传入覆盖
 */
public class PipelineBundle extends DynamicBundle {

    @NonNls
    private static final String BUNDLE = "bundles.pipeline";
    private static final PipelineBundle INSTANCE = new PipelineBundle();

    public PipelineBundle() {
        super(BUNDLE);
    }

    /**
     * 获取配置项值
     */
    @NotNull
    public static @Nls String message(@NotNull @PropertyKey(resourceBundle = BUNDLE) String key, Object @NotNull ... params) {
        return INSTANCE.getMessage(key, params);
    }
}
