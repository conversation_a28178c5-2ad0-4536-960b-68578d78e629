package com.srdcloud.ideplugin.service.domain.apigw.codechat;

import com.srdcloud.ideplugin.service.domain.apigw.ApigwWebsocketRequestContext;

import java.io.Serializable;

/**
 * <AUTHOR> yangy
 * @create 2023/7/23 20:21
 */
public class CodeChatRequestMessage implements Serializable {

    /**
     * 消息业务名称
     */
    private String messageName;

    /**
     * 通信上下文
     */
    private ApigwWebsocketRequestContext context;

    /**
     * 消息内容
     */
    private CodeChatRequestPayload payload;

    public String getMessageName() {
        return messageName;
    }

    public void setMessageName(String messageName) {
        this.messageName = messageName;
    }

    public ApigwWebsocketRequestContext getContext() {
        return context;
    }

    public void setContext(ApigwWebsocketRequestContext context) {
        this.context = context;
    }

    public CodeChatRequestPayload getPayload() {
        return payload;
    }

    public void setPayload(CodeChatRequestPayload payload) {
        this.payload = payload;
    }
}
