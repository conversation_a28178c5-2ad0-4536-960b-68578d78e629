package com.srdcloud.ideplugin.general.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/11/25
 * @desc 根据环境不同，决定是否打印调试日志
 */
public class DebugLogUtil {

    private static final Logger logger = LoggerFactory.getLogger(DebugLogUtil.class);

    /**
     * 本地runIde时用：输出到控制台，监听当前调试的逻辑
     *
     * @param msg
     */
    public static void println(String msg) {
        if (EnvUtil.checkDebugAble()) {
            System.out.println(LocalDateTime.now() + ":[cf debug]" + msg);
        }
    }

    /**
     * 调试监听log不再需要print到控制台时，可切换到info日志，在提测后反馈问题时可以查看关键信息
     *
     * @param msg
     */
    public static void info(String msg) {
        if (EnvUtil.checkDebugAble()) {
            logger.info("[cf debug]" + msg);
        }
    }

    public static void warn(String msg) {
        if (EnvUtil.checkDebugAble()) {
            logger.warn("[cf debug]" + msg);
        }
    }

    public static void error(String msg) {
        if (EnvUtil.checkDebugAble()) {
            logger.error("[cf debug]" + msg);
        }
    }

}
