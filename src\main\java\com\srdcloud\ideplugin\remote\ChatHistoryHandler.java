package com.srdcloud.ideplugin.remote;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.general.utils.IdeUtil;
import com.srdcloud.ideplugin.general.utils.JsonUtil;
import com.srdcloud.ideplugin.general.utils.LocalStorageUtil;
import com.srdcloud.ideplugin.general.utils.MessageBalloonNotificationUtil;
import com.srdcloud.ideplugin.remote.client.FastFailHttpClient;
import com.srdcloud.ideplugin.remote.client.HttpClient;
import com.srdcloud.ideplugin.remote.domain.ApiResponse;
import com.srdcloud.ideplugin.remote.domain.Dialog.ChatHistoryCommonResponse;
import com.srdcloud.ideplugin.remote.domain.Dialog.GetDialogResponse;
import com.srdcloud.ideplugin.remote.domain.Dialog.ListDialogsResponse;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.DialogReq;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.StopAnswerReq;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Objects;

/**
 * <AUTHOR> yangy
 * @create 2024/5/13 10:25
 */
public class ChatHistoryHandler {

    private static final Logger logger = LoggerFactory.getLogger(ChatHistoryHandler.class);

    /**
     * 会话记录服务-列举对话
     */
    private static final String LIST_DIALOGS = "api/aebackend/chat-history-admin/v1/list-dialogs";

    /**
     * 会话记录服务-查询对话信息
     */
    private static final String GET_DIALOG = "api/aebackend/chat-history-admin/v1/get-dialog";

    /**
     * 会话记录服务-编辑对话标题
     */
    private static final String EDIT_DIALOG_TITLE = "api/aebackend/chat-history-admin/v1/edit-dialog-title";

    /**
     * 会话记录服务-删除一个对话
     */
    private static final String REMOVE_DIALOG = "api/aebackend/chat-history-admin/v1/remove-dialog";

    /**
     * 会话记录服务-停止回答
     */
    private static final String STOP_ANSWER = "api/acbackend/code-chat/v1/stop-answer";


    /**
     * 问答轮写入（插件端不会直接调用，仅用于测试多分支会话造数据）
     * https://docs.srdcloud.cn/docs/25q5Mj8wO7f9x8qD#anchor-a37b
     */
    @Deprecated
    public static ChatHistoryCommonResponse insertQa(DialogReq dialogReq) {
        ChatHistoryCommonResponse chatHistoryCommonResponse = null;
        try {
            String baseUrl = ConfigWrapper.getServerUrl() + "api/aebackend/chat-history-admin/v1/insert-qa";
            JSONObject params = JSONObject.parseObject(JsonUtil.getInstance().toJson(dialogReq));
            HashMap<String, String> headers = generateAuthHeaders();
            ApiResponse apiResponse = HttpClient.doPost(baseUrl, params.toString(), headers);
            if (apiResponse != null && RtnCode.SUCCESS == apiResponse.getRtnCode()) {
                chatHistoryCommonResponse = JsonUtil.getInstance().fromJson(apiResponse.getMessage(), ChatHistoryCommonResponse.class);
            }
        } catch (Exception e) {
            logger.error("insertQa error:{}", e.getMessage());
        }
        return chatHistoryCommonResponse;
    }


    /**
     * 客户端列举对话
     *
     * @param subServices
     * @param pageNum
     * @param pageDataCount
     * @param title
     * @return
     */
    public static ListDialogsResponse listDialogs(String subServices, Integer pageNum, Integer pageDataCount, String title) {
        ListDialogsResponse response = new ListDialogsResponse();
        response.setOptResult(RtnCode.OFFLINE);
        response.setMsg("网络条件异常，请稍后重试.");
        if (!LocalStorageUtil.checkNetCondition()) {
            logger.warn("[cf] listDialogs skip, net condition fail.");
            return response;
        }

        try {
            String baseUrl = ConfigWrapper.getServerUrl() + LIST_DIALOGS;
            URIBuilder uriBuilder = new URIBuilder(baseUrl);
            uriBuilder.addParameter("userId", LocalStorageUtil.getUserId());
            uriBuilder.addParameter("subServices", subServices);
            uriBuilder.addParameter("pageNum", pageNum != null ? pageNum.toString() : "1");
            uriBuilder.addParameter("pageDataCount", pageDataCount != null ? pageDataCount.toString() : "30");
            if (StringUtils.isNotBlank(title)) {
                uriBuilder.addParameter("title", title);
            }

            HashMap<String, String> headers = generateAuthHeaders();
            ApiResponse apiResponse = FastFailHttpClient.doGet(uriBuilder.build().toString(), headers);

            if (RtnCode.SUCCESS == apiResponse.getRtnCode()) {
                response = JsonUtil.getInstance().fromJson(apiResponse.getMessage(), ListDialogsResponse.class);
            } else {
                response.setOptResult(apiResponse.getRtnCode());
                response.setMsg(apiResponse.getMessage());
            }
        } catch (Exception e) {
            logger.error("[cf] listDialogs error:{}", e.getMessage());
        }
        return response;
    }


    /**
     * 查询一个对话的所有问答轮信息
     *
     * @param subServices
     * @param dialogId
     * @return
     */
    public static GetDialogResponse getDialog(String subServices, String dialogId) {
        GetDialogResponse response = new GetDialogResponse();
        response.setOptResult(RtnCode.OFFLINE);
        response.setMsg("网络条件异常，请稍后重试.");
        if (!LocalStorageUtil.checkNetCondition()) {
            logger.warn("[cf] getDialog skip, net condition fail.");
            return response;
        }

        try {
            String baseUrl = ConfigWrapper.getServerUrl() + GET_DIALOG;
            URIBuilder uriBuilder = new URIBuilder(baseUrl);
            uriBuilder.addParameter("userId", LocalStorageUtil.getUserId());
            uriBuilder.addParameter("subService", subServices);
            uriBuilder.addParameter("dialogId", dialogId);

            HashMap<String, String> headers = generateAuthHeaders();
            ApiResponse apiResponse = FastFailHttpClient.doGet(uriBuilder.build().toString(), headers);

            //if (RtnCode.USER_FORBIDDEN == apiResponse.getRtnCode()) {
            //    ApplicationManager.getApplication().invokeLater(() -> {
            //        MessageDialogUtil.showUserBanDialog();
            //    });
            //}

            if (RtnCode.SUCCESS == apiResponse.getRtnCode()) {
                response = JsonUtil.getInstance().fromJson(apiResponse.getMessage(), GetDialogResponse.class);
            } else {
                response.setOptResult(apiResponse.getRtnCode());
                response.setMsg(apiResponse.getMessage());
            }
        } catch (Exception e) {
            logger.error("[cf] getDialog error:{}", e.getMessage());
            MessageBalloonNotificationUtil.showBalloonNotificationByReason(Objects.requireNonNull(IdeUtil.findCurrentProject()), "查询对话超时，请稍后重试", RtnCode.RECV_TIMEOUT);
        }
        return response;
    }

    /**
     * 删除一个对话
     *
     * @param dialogId
     * @return
     */
    public static ChatHistoryCommonResponse removeDialog(String dialogId) {
        ChatHistoryCommonResponse response = new ChatHistoryCommonResponse();
        response.setOptResult(RtnCode.OFFLINE);
        response.setMsg("网络条件异常，请稍后重试.");
        if (!LocalStorageUtil.checkNetCondition()) {
            logger.warn("[cf] removeDialog skip, net condition fail.");
            return response;
        }

        try {
            String baseUrl = ConfigWrapper.getServerUrl() + REMOVE_DIALOG + "?dialogId=" + dialogId;
            HashMap<String, String> headers = generateAuthHeaders();
            ApiResponse apiResponse = FastFailHttpClient.doDelete(baseUrl, headers);

            //if (RtnCode.USER_FORBIDDEN == apiResponse.getRtnCode()) {
            //    ApplicationManager.getApplication().invokeLater(() -> {
            //        MessageDialogUtil.showUserBanDialog();
            //    });
            //}

            if (RtnCode.SUCCESS == apiResponse.getRtnCode()) {
                response = JsonUtil.getInstance().fromJson(apiResponse.getMessage(), ChatHistoryCommonResponse.class);
            } else {
                response.setOptResult(apiResponse.getRtnCode());
                response.setMsg(apiResponse.getMessage());
            }
        } catch (Exception e) {
            logger.error("removeDialog error:{}", e.getMessage());
        }
        return response;
    }

    /**
     * 编辑一个对话的标题
     *
     * @param dialogId
     * @param title
     * @return
     */
    public static ChatHistoryCommonResponse editDialogTitle(String dialogId, String title) {
        ChatHistoryCommonResponse response = new ChatHistoryCommonResponse();
        response.setOptResult(RtnCode.OFFLINE);
        response.setMsg("网络条件异常，请稍后重试.");
        if (!LocalStorageUtil.checkNetCondition()) {
            logger.warn("[cf] editDialogTitle skip, net condition fail.");
            return response;
        }

        try {
            String baseUrl = ConfigWrapper.getServerUrl() + EDIT_DIALOG_TITLE;
            JSONObject params = new JSONObject();
            params.put("title", title);
            params.put("dialogId", dialogId);
            HashMap<String, String> headers = generateAuthHeaders();
            ApiResponse apiResponse = FastFailHttpClient.doPost(baseUrl, params.toString(), headers);

            //if (RtnCode.USER_FORBIDDEN == apiResponse.getRtnCode()) {
            //    ApplicationManager.getApplication().invokeLater(() -> {
            //        MessageDialogUtil.showUserBanDialog();
            //    });
            //}

            if (RtnCode.SUCCESS == apiResponse.getRtnCode()) {
                response = JsonUtil.getInstance().fromJson(apiResponse.getMessage(), ChatHistoryCommonResponse.class);
            } else {
                response.setOptResult(apiResponse.getRtnCode());
                response.setMsg(apiResponse.getMessage());
            }
        } catch (Exception e) {
            logger.error("editDialogTitle error:{}", e.getMessage());
        }
        return response;
    }


    /**
     * 停止回答
     *
     * @param stopAnswerReq
     * @return
     */
    public static ChatHistoryCommonResponse stopAnswer(StopAnswerReq stopAnswerReq) {
        ChatHistoryCommonResponse response = new ChatHistoryCommonResponse();
        response.setOptResult(RtnCode.OFFLINE);
        response.setMsg("网络条件异常，请稍后重试.");
        if (!LocalStorageUtil.checkNetCondition()) {
            logger.warn("[cf] stopAnswer skip, net condition fail.");
            return response;
        }

        try {
            String baseUrl = ConfigWrapper.getServerUrl() + STOP_ANSWER;
            JSONObject params = JSONObject.parseObject(JsonUtil.getInstance().toJson(stopAnswerReq));
            HashMap<String, String> headers = generateAuthHeaders();

//            ApiResponse apiResponse = HttpClient.doPost(baseUrl, params.toString(), headers);
            // 快速失败原则，不阻塞界面卡死
            ApiResponse apiResponse = FastFailHttpClient.doPost(baseUrl, params.toString(), headers);

            //if (RtnCode.USER_FORBIDDEN == apiResponse.getRtnCode()) {
            //    ApplicationManager.getApplication().invokeLater(() -> {
            //        MessageDialogUtil.showUserBanDialog();
            //    });
            //}

            if (RtnCode.SUCCESS == apiResponse.getRtnCode()) {
                response = JsonUtil.getInstance().fromJson(apiResponse.getMessage(), ChatHistoryCommonResponse.class);
            } else {
                logger.error("stopAnswer error,param:{},response:{}", JsonUtil.getInstance().toJson(stopAnswerReq), JsonUtil.getInstance().toJson(response));
                response.setOptResult(apiResponse.getRtnCode());
                response.setMsg(apiResponse.getMessage());
            }
        } catch (Exception e) {
            logger.error("stopAnswer error:{}", e.getMessage());
        }
        return response;
    }


    /**
     * 生成网关层鉴权头域字段
     */
    private static HashMap<String, String> generateAuthHeaders() {
        HashMap<String, String> headers = Maps.newHashMapWithExpectedSize(4);
        headers.put("apiKey", LocalStorageUtil.getApikey());
        headers.put("invokerId", LocalStorageUtil.getUserId());
        headers.put("userId", LocalStorageUtil.getUserId());
        return headers;
    }
}
