package com.srdcloud.ideplugin.general.utils

import com.intellij.openapi.diagnostic.logger
import java.awt.Desktop
import java.io.IOException
import java.io.UnsupportedEncodingException
import java.net.URI
import java.net.URISyntaxException
import java.net.URLEncoder
import java.util.regex.Pattern

class BrowseUtil {
    companion object {
        private val logger = logger<BrowseUtil>()

        fun browse(url: String) {
            try {
                if (Desktop.isDesktopSupported() && Desktop.getDesktop().isSupported(Desktop.Action.BROWSE)) {
                    // 定义正则表达式，用于匹配非 ASCII 字符（包括中文字符）
                    val nonAsciiPattern = Pattern.compile("[^\\x00-\\x7F]+")

                    // 使用正则表达式查找非 ASCII 字符
                    val matcher = nonAsciiPattern.matcher(url)
                    val encodedUrl = StringBuffer()

                    while (matcher.find()) {
                        val nonAsciiText = matcher.group()
                        val encodedText = URLEncoder.encode(nonAsciiText, "UTF-8")
                        matcher.appendReplacement(encodedUrl, encodedText)
                    }

                    matcher.appendTail(encodedUrl)
                    Desktop.getDesktop().browse(URI(encodedUrl.toString()))
                } else {
                    logger.error("[cf] not support Desktop.Action.BROWSE")
                }
            } catch (ex: IOException) {
                ex.printStackTrace()
            } catch (ex: URISyntaxException) {
                ex.printStackTrace()
            } catch (ex: UnsupportedEncodingException) {
                ex.printStackTrace()
            }
        }
    }
}