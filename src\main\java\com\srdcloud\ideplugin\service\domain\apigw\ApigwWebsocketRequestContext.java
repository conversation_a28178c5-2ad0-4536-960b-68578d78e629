package com.srdcloud.ideplugin.service.domain.apigw;

import java.io.Serializable;

/**
 * @author: yangy
 * @date: 2023/7/23 20:23
 * @Desc
 */
public class ApigwWebsocketRequestContext implements Serializable {

    /**
     * 当客户端缓存有apiKey时携带，此时不能携带sessionId
     */
    private String apiKey;

    /**
     * 用户中心sessionId
     */
    private String sessionId;

    /**
     * 当前用户id
     */
    private String invokerId;

    /**
     * 本次请求id
     */
    private String reqId;


    private String messageName;


    private String appGId;


    private String version;


    private Integer optResult;


    private String msg;

    public String getMessageName() {
        return messageName;
    }

    public void setMessageName(String messageName) {
        this.messageName = messageName;
    }

    public String getReqId() {
        return reqId;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    public String getAppGId() {
        return appGId;
    }

    public void setAppGId(String appGId) {
        this.appGId = appGId;
    }

    public String getInvokerId() {
        return invokerId;
    }

    public void setInvokerId(String invokerId) {
        this.invokerId = invokerId;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Integer getOptResult() {
        return optResult;
    }

    public void setOptResult(Integer optResult) {
        this.optResult = optResult;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

}
