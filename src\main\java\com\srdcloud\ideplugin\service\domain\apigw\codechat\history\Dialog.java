package com.srdcloud.ideplugin.service.domain.apigw.codechat.history;

import com.srdcloud.ideplugin.codechat.domain.ChatMessageSimple;

import java.io.Serializable;

/**
 * @author: yangy
 * @date: 2024/5/13 16:40
 * @Desc
 */
public class Dialog implements Serializable {

    /**
     * 对话标题，用于客户端对话列表显示
     */
    private String title;

    /**
     * 产生本对话的时间，格式为yyyy-MM-dd HH:mm:ss
     */
    private String createTime;

    /**
     * 会话最近更新时间，格式为yyyy-MM-dd HH:mm:ss
     */
    private String updateTime;

    /**
     * 本对话限定条件
     */
    private DialogCondition modelRouteCondition;

    /**
     * 本对话的system prompt对象，其中role=systen
     */
    private ChatMessageSimple systemPrompt;

    /**
     * 用户提问，即本对话用户首轮提问对象，通过这个对象嵌套本对话的所有对话轮的树形结构
     */
    private ChatMessageSimple questions  = new ChatMessageSimple();;

    public DialogCondition getModelRouteCondition() {
        return modelRouteCondition;
    }

    public void setModelRouteCondition(DialogCondition modelRouteCondition) {
        this.modelRouteCondition = modelRouteCondition;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String creatTime) {
        this.createTime = creatTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public ChatMessageSimple getSystemPrompt() {
        return systemPrompt;
    }

    public void setSystemPrompt(ChatMessageSimple systemPrompt) {
        this.systemPrompt = systemPrompt;
    }

    public ChatMessageSimple getQuestions() {
        return questions;
    }

    public void setQuestions(ChatMessageSimple questions) {
        this.questions = questions;
    }
}
