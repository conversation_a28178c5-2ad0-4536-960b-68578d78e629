<!DOCTYPE html>
<html lang="zh">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <link rel="icon" href="data:,">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <title>研发云CodeFree</title>
  <style>
    html,
    body {
      height: 100%;
      margin: 0;
      font-size: 14px;
      font-family: "microsoft yahei", "PingFangSC-Regular";
      color: #30353A;
    }

    .main {
      width: 100%;
      height: 100vh;
    }

    .navbar {
      height: 50px;
      box-sizing: border-box;
      padding: 13px 20px;
      background: #fff;
      box-shadow: 0 5px 10px 0 rgba(230,236,240,.5);
      z-index: 99;
    }

    .navbar > img {
      height: 24px;
      width: auto;
    }

    .container {
      height: calc(100vh - 50px);
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      overflow-y:auto;
      box-sizing: border-box;
      padding-top: 10vh;
      background: linear-gradient(0deg, rgba(48,124,251,0.00) -1%, rgba(48,124,251,0.12) 99%);
      opacity: 0.9;
    }

    .title-container {
      text-align: center;
      position: relative;
    }

    .title-container h1 {
      margin: 4px 0 20px 0;
      font-size: 64px;
      line-height: 1.2;
      font-weight: 400;
    }

    .title-container h2 {
      margin: 0;
      font-size: 36px;
      line-height: 1.4;
      font-weight: 400;
    }

    .title-container h2.sub {
      color: #636C76;
    }
    
    .title-container .ai-150 {
      position:absolute;
      top: 0;
      left: -360px;
      transform: rotate(-30deg);
    }

    .title-container .node-20 {
      position:absolute;
      top: -25px;
      left: 47px;
      transform: rotate(-21.79deg);
    }

    .title-container .code-10 {
      position: absolute;
      top: -21px;
      right: -150px;
      transform: rotate(15deg);
    }

    .title-container .code-0 {
      position: absolute;
      top: 113px;
      left: -120px;
      opacity: 0.4;
    }

    .title-container .ai-30 {
      position:absolute;
      top: 145px;
      right: -266px;
      transform: rotate(30deg);
      opacity: 0.6;
    }

    .card-list {
      position: relative;
      margin-top: 80px;
      display: flex;
      justify-content: center;
    }

    .card-list .card-list-inner {
      width: 1170px;
      display: flex;
      flex-wrap: wrap;
    }

    .card-list .card-item {
      width: 358px;
      min-height: 388px;
      margin: 16px;
      box-sizing: border-box;
      padding: 32px;
      border-radius: 4px;
      border: 1px solid rgba(0,0,0,.1);
      box-shadow: 0px 0px 10px 0px rgba(221, 228, 234, 0.8),0px 4px 10px 0px rgba(221, 228, 234, 0.8);
      background-color: #fff;
    }

    .card-item .icon > img {
      margin-top: 10px;
      width: 50px;
      height: 50px;
    }

    .card-item .title {
      font-size: 20px;
      padding: 8px 0;
      margin: 0;
      font-weight: bold;
    }

    .card-item .desc {
      margin: 0;
      height: 75px;
      border-bottom: 1px solid #E5E9ED;
    }

    .card-item ul {
      margin: 12px 0 0 0;
      padding: 0;
    }

    .card-item ul > li {
      list-style: none;
      padding-bottom: 4px;
    }

    .card-item ul > li > strong {
      display: inline-block;
      color: #307CFB;
      text-align: right;
      margin-right: 12px;
    }

    .card-item ul.center > li > strong {
      width: 120px;
    }

    .card-list .link-0 {
      position:absolute;
      top: -30px;
      left: 12px;
      opacity: 0.4;
    }

    .card-list .node-30 {
      position:absolute;
      top: -52px;
      right: 260px;
      transform: rotate(30deg);
      opacity: 0.4;
    }

    .card-list .node-15 {
      position:absolute;
      bottom: 38px;
      left: -64px;
      transform: rotate(-15deg);
      opacity: 0.6;
    }

    .card-list .ai-40 {
      position:absolute;
      bottom: -50px;
      left: 218px;
      transform: rotate(-41deg);
    }

    .card-list .link-50 {
      position:absolute;
      bottom: -42px;
      right: 400px;
      opacity: 0.4;
      transform: rotate(50.25deg);
    }

    .card-list .code-0 {
      position:absolute;
      bottom: -80px;
      right: 30px;
      opacity: 0.6;
    }
  </style>
</head>

<body>
  <div class="main">
    <div class="navbar">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="85" height="24" viewBox="0 0 85 24">
        <defs>
          <pattern x="0" y="0" width="85" height="24" patternUnits="userSpaceOnUse" id="master_svg0_22_588"><image x="0" y="0" width="85" height="24" transform="scale(1,1)" xlink:href="data:image/png;base64,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"/></pattern>
        </defs>
        <g><rect x="0" y="0" width="85" height="24" rx="0" fill="url(#master_svg0_22_588)" fill-opacity="1"/></g>
      </svg>
    </div>
    <div class="container">
      <div class="title-container">
        <h2></h4>
          <h1></h2>
        <h2 class="sub">登录失败，请重新登录！</h3>
        <div class="ai-150">
          <svg t="1692268245144" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5238" width="16" height="16" xmlns:xlink="http://www.w3.org/1999/xlink">
            <path d="M565.979429 830.171429l-37.961143-103.277715H204.580571l-37.961142 105.472c-14.848 41.179429-27.501714 68.900571-37.961143 83.309715-10.459429 14.409143-27.648 21.577143-51.565715 21.577142-20.260571 0-38.180571-7.68-53.76-23.04-15.579429-15.213714-23.332571-32.621714-23.332571-52.077714 0-11.190857 1.828571-22.820571 5.412571-34.816 3.584-11.995429 9.581714-28.598857 17.92-49.956571l203.483429-534.235429 20.918857-55.149714c8.118857-21.504 16.822857-39.350857 26.038857-53.613714a107.52 107.52 0 0 1 36.352-34.523429c14.994286-8.777143 33.572571-13.165714 55.588572-13.165714 22.454857 0 41.179429 4.388571 56.173714 13.165714 14.994286 8.777143 27.136 20.114286 36.352 33.938286s16.969143 28.745143 23.332571 44.617143c6.363429 15.872 14.409143 37.156571 24.137143 63.707428l207.872 530.870857c16.310857 40.374857 24.429714 69.778286 24.429715 88.064 0 19.090286-7.68 36.571429-23.04 52.443429a74.605714 74.605714 0 0 1-55.588572 23.844571 69.924571 69.924571 0 0 1-32.548571-7.021714 67.730286 67.730286 0 0 1-22.820572-19.090286 180.370286 180.370286 0 0 1-19.821714-37.010285 1833.691429 1833.691429 0 0 1-18.212571-44.032zM246.930286 601.746286h237.714285l-119.954285-339.456-117.76 339.456z m615.936 234.057143V187.684571c0-33.645714 7.387429-58.953143 22.235428-75.776a73.142857 73.142857 0 0 1 57.490286-25.234285c24.210286 0 43.885714 8.338286 58.88 24.941714 14.994286 16.676571 22.528 41.984 22.528 76.068571v648.118858c0 34.011429-7.533714 59.465143-22.528 76.288-14.994286 16.822857-34.669714 25.234286-58.88 25.234285a72.996571 72.996571 0 0 1-57.270857-25.526857c-14.994286-17.042286-22.454857-42.349714-22.454857-75.995428z" p-id="5239" fill="#D6E5FE"></path>
          </svg>
        </div>
        <div class="node-20">
          <svg t="1692268538876" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5452" width="20" height="20" xmlns:xlink="http://www.w3.org/1999/xlink">
            <path d="M950.857143 438.857143h-156.598857A291.84 291.84 0 0 0 512 219.428571a291.84 291.84 0 0 0-282.258286 219.428572H73.142857a73.142857 73.142857 0 0 0 0 146.285714h156.598857A291.913143 291.913143 0 0 0 512 804.571429c136.192 0 249.563429-93.476571 282.258286-219.428572H950.857143a73.142857 73.142857 0 0 0 0-146.285714zM512 658.285714c-80.676571 0-146.285714-65.609143-146.285714-146.285714s65.609143-146.285714 146.285714-146.285714 146.285714 65.609143 146.285714 146.285714-65.609143 146.285714-146.285714 146.285714z" p-id="5453" fill="#d6e5fe"></path>
          </svg>
        </div>
        <div class="code-10">
          <svg t="1692268906830" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5672" width="20" height="20" xmlns:xlink="http://www.w3.org/1999/xlink">
            <path d="M22.742309 296.228571a77.531429 77.531429 0 0 0 0 109.641143l109.641142 109.641143a77.531429 77.531429 0 1 0 109.641143-109.641143l-54.857143-54.857143 54.784-54.784a77.531429 77.531429 0 1 0-109.641142-109.641142L22.742309 296.228571zM1001.393737 722.139429a77.531429 77.531429 0 0 0 0-109.714286l-109.714286-109.568a77.531429 77.531429 0 1 0-109.641142 109.641143l54.857142 54.784-54.857142 54.784a77.531429 77.531429 0 1 0 109.714285 109.641143l109.568-109.568z" p-id="5673" fill="#d6e5fe"></path>
            <path d="M667.13088 232.594286L506.801737 831.634286a77.531429 77.531429 0 1 1-149.796571-40.155429l160.475428-598.893714a77.531429 77.531429 0 1 1 149.796572 40.155428z" p-id="5674" fill="#d6e5fe"></path>
          </svg>
        </div>
        <div class="code-0">
          <svg t="1692268906830" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5672" width="38" height="38" xmlns:xlink="http://www.w3.org/1999/xlink">
            <path d="M22.742309 296.228571a77.531429 77.531429 0 0 0 0 109.641143l109.641142 109.641143a77.531429 77.531429 0 1 0 109.641143-109.641143l-54.857143-54.857143 54.784-54.784a77.531429 77.531429 0 1 0-109.641142-109.641142L22.742309 296.228571zM1001.393737 722.139429a77.531429 77.531429 0 0 0 0-109.714286l-109.714286-109.568a77.531429 77.531429 0 1 0-109.641142 109.641143l54.857142 54.784-54.857142 54.784a77.531429 77.531429 0 1 0 109.714285 109.641143l109.568-109.568z" p-id="5673" fill="#83B0FD"></path>
            <path d="M667.13088 232.594286L506.801737 831.634286a77.531429 77.531429 0 1 1-149.796571-40.155429l160.475428-598.893714a77.531429 77.531429 0 1 1 149.796572 40.155428z" p-id="5674" fill="#83B0FD"></path>
          </svg>
        </div>
        <div class="ai-30">
          <svg t="1692268245144" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5238" width="29" height="29" xmlns:xlink="http://www.w3.org/1999/xlink">
            <path d="M565.979429 830.171429l-37.961143-103.277715H204.580571l-37.961142 105.472c-14.848 41.179429-27.501714 68.900571-37.961143 83.309715-10.459429 14.409143-27.648 21.577143-51.565715 21.577142-20.260571 0-38.180571-7.68-53.76-23.04-15.579429-15.213714-23.332571-32.621714-23.332571-52.077714 0-11.190857 1.828571-22.820571 5.412571-34.816 3.584-11.995429 9.581714-28.598857 17.92-49.956571l203.483429-534.235429 20.918857-55.149714c8.118857-21.504 16.822857-39.350857 26.038857-53.613714a107.52 107.52 0 0 1 36.352-34.523429c14.994286-8.777143 33.572571-13.165714 55.588572-13.165714 22.454857 0 41.179429 4.388571 56.173714 13.165714 14.994286 8.777143 27.136 20.114286 36.352 33.938286s16.969143 28.745143 23.332571 44.617143c6.363429 15.872 14.409143 37.156571 24.137143 63.707428l207.872 530.870857c16.310857 40.374857 24.429714 69.778286 24.429715 88.064 0 19.090286-7.68 36.571429-23.04 52.443429a74.605714 74.605714 0 0 1-55.588572 23.844571 69.924571 69.924571 0 0 1-32.548571-7.021714 67.730286 67.730286 0 0 1-22.820572-19.090286 180.370286 180.370286 0 0 1-19.821714-37.010285 1833.691429 1833.691429 0 0 1-18.212571-44.032zM246.930286 601.746286h237.714285l-119.954285-339.456-117.76 339.456z m615.936 234.057143V187.684571c0-33.645714 7.387429-58.953143 22.235428-75.776a73.142857 73.142857 0 0 1 57.490286-25.234285c24.210286 0 43.885714 8.338286 58.88 24.941714 14.994286 16.676571 22.528 41.984 22.528 76.068571v648.118858c0 34.011429-7.533714 59.465143-22.528 76.288-14.994286 16.822857-34.669714 25.234286-58.88 25.234285a72.996571 72.996571 0 0 1-57.270857-25.526857c-14.994286-17.042286-22.454857-42.349714-22.454857-75.995428z" p-id="5239" fill="#ACCBFD"></path>
          </svg>
        </div>
      </div>
    </div>
  </div>
  <script>
    window.onload = function() {
      var osType = getOsType();

      if(osType === 'mac') {
        document.getElementById('shortcut1').innerText = '⌘+Return';
        document.getElementById('shortcut2').innerText = '⌥+]';
        document.getElementById('shortcut3').innerText = '⌥+[';
        document.getElementById('shortcut4').innerText = '⌥+⇧+⌘+O';
        document.getElementById('shortcut5').innerText = '⌥+⇧+K';
        document.getElementById('shortcut6').innerText = '⌥+⇧+A';
      }
    }

    function getOsType() {
      var userAgent = navigator.userAgent.toLowerCase();

      if (/macintosh|mac os x/i.test(navigator.userAgent)) {
        return "mac";
      } else if(/windows/i.test(navigator.userAgent)){
        return "win"
      }

      return "unknown";
    }
  </script>
</body>

</html>