package com.srdcloud.ideplugin.service.domain.apigw.codechat;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2024/8/5
 * @desc 知识库引用元数据
 */
public class QuoteMetadata {
    /**
     * 所用知识库名
     */
    @SerializedName("knowledge_base")
    private String knowledgeBase;


    /**
     * 文档名
     */
    private String fileName;


    /**
     * 源引用的具体url
     */
    private String source;


    /**
     * 完整引用内容（知识库召回全文）
     */
    @SerializedName("full_text")
    private String fullText;

    public String getKnowledgeBase() {
        return knowledgeBase;
    }

    public void setKnowledgeBase(String knowledgeBase) {
        this.knowledgeBase = knowledgeBase;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getFullText() {
        return fullText;
    }

    public void setFullText(String fullText) {
        this.fullText = fullText;
    }
}
