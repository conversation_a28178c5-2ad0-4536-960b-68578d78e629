package com.srdcloud.ideplugin.general.utils;

import org.apache.commons.lang3.StringUtils;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class StringUtil {
    private static final char[] DIGIT = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    private static final String CHARSET = "UTF-8";

    public static String getUrlEncodeStr(String str) {
        try {
            return URLEncoder.encode(str, CHARSET);
        } catch (Exception e) {
            return str;
        }
    }

    public static String base64Encode(String str) {
        byte[] data = str.getBytes(StandardCharsets.UTF_8);
        String base64Str = Base64.getEncoder().encodeToString(data);

        return base64Str;
    }

    public static boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    public static boolean isNotEmpty(String str) {
        return  !isEmpty(str);
    }

    public static String toHex(String data, String encode) {
        try {
            return toHex(data.getBytes(encode));
        } catch (Exception e) {
            return "";
        }
    }

    private static String toHex(byte[] byteData) {
        int len = byteData.length;
        char[] buf = new char[len * 2];
        int k = 0;
        for (byte b : byteData) {
            buf[k++] = DIGIT[(b & 255) >> 4];
            buf[k++] = DIGIT[b & 15];
        }
        return new String(buf);
    }

    public static boolean isNumber(String s) {
        if (StringUtils.isBlank(s)) {
            return false;
        }
        for (int i = 0; i < s.length(); ++i) {
            if (!Character.isDigit(s.charAt(i))) {
                return false;
            }
        }
        return true;
    }
}
