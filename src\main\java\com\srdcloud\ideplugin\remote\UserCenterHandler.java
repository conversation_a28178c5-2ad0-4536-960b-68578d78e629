package com.srdcloud.ideplugin.remote;

import com.google.common.collect.Maps;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.general.utils.JsonUtil;
import com.srdcloud.ideplugin.general.utils.LocalStorageUtil;
import com.srdcloud.ideplugin.remote.client.HttpClient;
import com.srdcloud.ideplugin.remote.domain.ApiResponse;
import com.srdcloud.ideplugin.remote.domain.User.UserInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2024/12/25
 * @desc 用户中心后端对接
 */
public class UserCenterHandler {
    private static final Logger logger = LoggerFactory.getLogger(UserCenterHandler.class);

    /**
     * 用户中心-查询用户信息
     */
    private static final String GET_USERINFO = "api/usercenterbackend/users/v1/get-user-front/";

    public static UserInfo getUserInfo(String userId) {
        try {
            String url = ConfigWrapper.getServerUrl() + GET_USERINFO + userId;
            HashMap<String, String> headers = generateUserCenterAuthHeaders();
            ApiResponse apiResponse = HttpClient.doGet(url, headers);

            if (apiResponse != null && RtnCode.SUCCESS == apiResponse.getRtnCode()) {
                return JsonUtil.getInstance().fromJson(apiResponse.getMessage(), UserInfo.class);
            }
        } catch (Exception e) {
            logger.error("[cf] getUserInfo:{},error:{}", userId, e.getMessage());
        }
        return null;
    }

    /**
     * 生成网关层鉴权头域字段
     */
    protected static HashMap<String, String> generateUserCenterAuthHeaders() {
        HashMap<String, String> headers = Maps.newHashMapWithExpectedSize(4);
        headers.put("contentType", "application/json");
        headers.put("projectId", "0");
        headers.put("apiKey", LocalStorageUtil.getApikey());
        headers.put("sessionId", LocalStorageUtil.getSessionId());
        headers.put("invokerId", LocalStorageUtil.getUserId());
        headers.put("userId", LocalStorageUtil.getUserId());
        return headers;
    }
}
