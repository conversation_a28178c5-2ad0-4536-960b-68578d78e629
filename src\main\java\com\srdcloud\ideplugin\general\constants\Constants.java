package com.srdcloud.ideplugin.general.constants;

import com.srdcloud.ideplugin.general.utils.EnvUtil;

public class Constants {

    public static final String MESSAGE_APP_GID = "aicode";

    public static final String NEW_CONVERSATION_NAME = "新的会话";

    public static final String QUESTION_TASK_TYPE_CODEGEN = "codegen";

    public static final String QUESTION_TASK_TYPE_CHAT = "CodeChat";

    public static final String QUESTION_TASK_TYPE_COMMIT = "CommitChat";

    public static final String QUESTION_TASK_TYPE_CODEGEN_MANUAL = "codegenmanual";

    public static final String QUESTION_TASK_TYPE_NATURAL_LANGUAGE = "codegennatural";

    public static final String ApiKeyPropKey = "srdApikey";

    public static final String AuthTokenPropKey = "srdAuthToken";

    public static final String SessionPropKey = "srdAuthSid";

    public static final String UserIdPropKey = "srdUid";

    public static final String UserNamePropKey = "srdUserName";

    public static final String UserAccountPropKey = "srdUserAccount";

    public static final String LoginStatusPropKey = "loginStatus";

    public static final String AgentVersionInfoKey = "agentVersionInfo";

    public static final String InputCharacterLimit = "inputCharacterLimit";

    public static final String SnippetsCharacterLimit = "snippetsCharacterLimit";

    public static final String ChatCharacterLimit = "chatCharacterLimit";

    public static final String ChatPrintCacheConfig = "printCacheConfig";

    public static final String RemoteVersion = "remoteVersion";

    public static final String RemoteVersionChange = "remoteVersionChange";

    public static final String CurrentVersion = "currentVersion";

    public static final String CurrentVersionChange = "currentVersionChange";

    public static final String ClientUrlSubPath = "clientUrlSubPath";

    public static final String IndexVersion = "indexVersion";

    public static final String IgnoreList = "ignoreList";

    public static final String CodeCompleteStrategy = "codeCompleteStrategy";

    public static final String ComposerModelName = "d10_cpl";

    public static final int DefaultInputCharacterLimit = 13000;

    public static final int DefaultSnippetsCharacterLimit = 4000;

    public static final int DefaultChatCharacterLimit = 50000;

    public static final int ChatQuestionCharacterLimit = 10000;

    public static final int ChatRelatedFileCharacterLimit = 50000;

    public static final String WB_CHANNEL_START = "<WBChannel>";

    public static final String WB_CHANNEL_END = "</WBChannel>";

    public static final int ChannelStyle_Websocket = 0;
    public static final int ChannelStyle_Http = 1;

    public static final int Channel_Connected = 0;
    public static final int Channel_Disconnected = 1;
    public static final int Channel_Connecting = 2;

    public static final int Channel_Reconn_Interval = 10;

    public static final int WS_1006_LIMIT = 3;

    public static final int Webview_msg_timeout = 200;

    public static final int Webview_msg_interval = 10;

    public static final long Code_completion_debounce = 300;

    public static final long Code_completion_alarm_delay = 1000;

    public static final long Stop_answer_alarm_delay = 10;

    public static final int LoginStatus_NOK = 0;
    public static final int LoginStatus_OK = 1;

    public static final int UserStatus_NOK = 0;
    public static final int UserStatus_OK = 1;


    public static final String LoginMsg_OK = EnvUtil.isSec("海云智码登录成功","研发云CodeFree登录成功");

    public static final String LoginMsg_NOK = EnvUtil.isSec("海云智码登录失败","研发云CodeFree登录失败");

    public static final String PLUGIN_ID = "com.secIdea.secIdea-intellij-plugin-c10";


    public static final int IS_ANSWER_END_FALSE = 0;
    public static final int IS_ANSWER_END_TRUE = 1;

    public static final int MAX_NEW_TOKENS_AI_AUTO = 256;

    public static final int MAX_NEW_TOKENS_AI_MANUAL = 512;

    public static final int MAX_NEW_TOKENS_CHAT = 4096;

    public static final String NOTIFY_CANCEL_TEXT = "取消";

    public static final String NOTIFY_UPDATE_TITLE = "发现新版本{version}，请立即更新以享受更佳体验！";

    public static final String NOTIFY_UPDATE_TEXT = "更新";

    public static final String NOTIFY_OPEN_TEXT = "提问";

    public static final String NOTIFY_DETAILS_TEXT = "详情";

    public static final String NOTIFY_CONFIRM_TEXT = "确认";

    public static final String NOTIFY_RESTART_TEXT = "重启";

    public static final String NOTIFY_REDOWNLOAD_TEXT = "重新下载";

    public static final String NOTIFY_ONLINE_TEXT = "在线更新";

    public static final String NOTIFY_LOCAL_TEXT = "本地更新";

    public static final String PROFILE_DEV = "dev";

    public static final String PROFILE_TEST = "test";

    public static final String PROFILE_PROD = "prod";


    public static final String PROFILE_TRUE = "true";

    public static final String UNKNOWN = "Unknown";

    // 系统限定prompt，限定AI助手相关属性(0731版本：改为后端拼接，客户端保留原格式但不再对模型层起效）
    public static final String systemRoleText = "我的名字是研发云智能助手CodeFree，是由中国电信研发云团队基于软件工程和大模型技术打造的。我使用大模型的能力为用户提供一站式智能化服务，提供包括智能问答、编程助手、知识库、测试助手、文档助手、安全助手等能力。\n" +
            "我的核心目标是以友好、简单、清晰的方式帮助用户解决编程问题。我拥有深厚的编程知识,涵盖各种流行的编程语言和框架。我也掌握广泛的计算机科学知识，如数据结构、算法、操作系统、网络等。\n" +
            "对于用户提出的任何编程相关的问题,我都能给出最佳的解决方案。我会解析问题的本质,运用丰富的知识库推导出正确的代码实现。如果需要,我还会给出多种可选方案的对比分析。\n" +
            "研发云是中国电信自主研发的一站式数智化研发协作与管理平台，该平台具备完整的研发运营一体化（DevOps）工具链，提供从项目研发、集成测试到生产部署的端到端服务能力，并打造了研发大模型CodeFree，实现研发过程的全域智能化，显著提升研发效率、研发质量和研发数字化管理水平。\n" +
            "最后,我会恪守对用户隐私的尊重,所有对话内容仅用于提升我自身的能力,不会泄露或记录任何用户个人信息。请尽管提出你的编程问题,我会提供最专业和有价值的帮助。\n" +
            "我会用中文来回答你的问题。";
}
