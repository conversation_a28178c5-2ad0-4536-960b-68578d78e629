import java.util.Timer
import java.util.TimerTask
import java.util.concurrent.TimeUnit
import javax.swing.SwingUtilities

/**
 * 立即执行第一次：符合条件时立即执行任务
 * 完全忽略中间调用：在节流间隔内的所有调用都会被完全忽略，不保留任何任务
 */
object StrictThrottler {
    private val lastExecutionMap = mutableMapOf<String, Long>()
    private val timerMap = mutableMapOf<String, Timer>()

    /**
     * 严格节流执行函数
     * @param key 唯一标识符
     * @param delay 节流间隔时间(默认500毫秒)
     * @param unit 时间单位(默认毫秒)
     * @param task 要执行的任务
     */
    fun throttle(
        key: String,
        delay: Long = 500,
        unit: TimeUnit = TimeUnit.MILLISECONDS,
        runOnEDT: Boolean = false,
        task: () -> Unit
    ) {
        val currentTime = System.currentTimeMillis()
        val delayInMillis = unit.toMillis(delay)
        val lastExecutionTime = lastExecutionMap[key] ?: 0L

        // 如果是第一次调用或超过节流间隔时间
        if (lastExecutionTime == 0L || currentTime - lastExecutionTime >= delayInMillis) {
            if (runOnEDT) {
                SwingUtilities.invokeLater {
                    task()
                }
            } else {
                task()
            }
            lastExecutionMap[key] = currentTime
            timerMap[key]?.cancel()
            timerMap[key] = Timer().apply {
                schedule(object : TimerTask() {
                    override fun run() {
                        lastExecutionMap.remove(key)
                        timerMap.remove(key)
                    }
                }, delayInMillis)
            }
        }
    }

    fun cancel(key: String) {
        timerMap[key]?.cancel()
        timerMap.remove(key)
        lastExecutionMap.remove(key)
    }
}