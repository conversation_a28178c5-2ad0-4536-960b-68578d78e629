package com.srdcloud.ideplugin.remote.domain;

import com.srdcloud.ideplugin.remote.domain.FeedBack.FeedbackAnswerData;

/**
 * <AUTHOR>
 * @date 2024/5/13
 * @description 回答反馈接口返回格式
 */
public class FeedbackAnswerResponse {

    /**
     * 返回码
     */
    private int code;

    /**
     * 异常描述
     */
    private String msg;

    /**
     * 响应数据，需要按需反序列化解析
     */
    private FeedbackAnswerData data;

    public FeedbackAnswerResponse() {
    }

    public FeedbackAnswerResponse(int code, String msg, FeedbackAnswerData data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public FeedbackAnswerData getData() {
        return data;
    }

    public void setData(FeedbackAnswerData data) {
        this.data = data;
    }
}
