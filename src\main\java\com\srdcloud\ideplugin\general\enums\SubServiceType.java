package com.srdcloud.ideplugin.general.enums;

/**
 * <AUTHOR>
 * @date 2024/4/9
 * @description CodeMessage消息类型枚举，后端用来辅助模型路由
 */
public enum SubServiceType {
    CODE_CHAT("codechat","自然语言编程"),

    CODE_EXPLAIN("codeexplain","代码解释"),

    CODE_OPTIMIZE("codeoptimize","生成优化建议"),

    CODE_COMMENT("codecomment","生成代码注释"),

    CODE_UNITTEST("codeunittest","生成单元测试"),

    FIX_EXCEPTION("fixexception","修复异常报错"),

    ASSISTANT("assistant","编程助手"),

    KB_ASSISTANT("kbassistant","知识库问答");

    private String name;
    private String description;

    private SubServiceType(String name, String description) {
        this.name = name;
        this.description = description;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 获取所有的子服务名称并用“,”拼接
     * @return
     */
    public static String getSubServiceTypeAll(){
        StringBuilder sb = new StringBuilder();
        for (SubServiceType subServiceType : values()) {
            sb.append(subServiceType.getName()).append(",");
        }
        sb.deleteCharAt(sb.length()-1);
        return sb.toString();
    }
}
