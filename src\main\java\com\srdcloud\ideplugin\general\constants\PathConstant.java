package com.srdcloud.ideplugin.general.constants;


import java.io.File;

/**
 * <AUTHOR> yangy
 * @create 2023/6/15 10:28
 */
public class PathConstant {

    public static final String BASE_PATH;
    public static final String TEMP_PATH;

    public static final String CHAT_STORAGE_PATH;
    public static final String CODEBASE_SQLITE_PATH;
    public static final String CHAT_EXPORT_PATH;
    public static final String CONFIG_PATH;


    static {
        // 获取操作系统用户空间根目录路径
        String var10000 = System.getProperty("user.home");

        // 定义插件运行数据存储路径
        BASE_PATH = var10000 + File.separator + "srd-copilot";
        CHAT_STORAGE_PATH = BASE_PATH + File.separator + "storage" + File.separator + "srd-copilot";
        CODEBASE_SQLITE_PATH = CHAT_STORAGE_PATH + File.separator + "srd-copilot.db";
        CHAT_EXPORT_PATH = BASE_PATH + File.separator + "export" + File.separator + "srd-copilot";
        CONFIG_PATH = BASE_PATH + File.separator + "config";
        TEMP_PATH = BASE_PATH + File.separator + "temp";
    }
}
