package com.srdcloud.ideplugin.remote.domain.WorkItem;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/13
 */
public class WorkItemListResponse {
    /**
     * 返回码
     */
    public int code;

    /**
     * 异常描述
     */
    public String msg;

    /**
     * 数据
     */
    public List<WorkItemInfo> data;

    public WorkItemListResponse(int code, String msg, List<WorkItemInfo> data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<WorkItemInfo> getData() {
        return data;
    }

    public void setData(List<WorkItemInfo> data) {
        this.data = data;
    }
}
