package com.srdcloud.ideplugin.webview.codechat.composer.response;

import com.srdcloud.ideplugin.composer.history.AgentHistoryUtil.ChatHistory;
import com.srdcloud.ideplugin.webview.codechat.composer.request.ComposerRequest;

public class LoadHistoryResponseData extends ComposerResponseData {
    private String dialogId;
    private ChatHistory[] historyList;

    public LoadHistoryResponseData(String dialogId, ChatHistory[] historyList) {
        super(ComposerRequest.REQ_TYPE_LOAD_HISTORY);
        this.dialogId = dialogId;
        this.historyList = historyList;
    }
    
    public String getDialogId() {
        return dialogId;
    }

    public ChatHistory[] getHistoryList() {
        return historyList;
    }
}
