package com.srdcloud.ideplugin.webview.codechat.diff.request;

public class DiffCodeRequestData {
    // 模型生成内容
    private String generatedContent;

    // diff源文件的绝对路径
    private String filePath;

    // diff源文件的提问代码块内容
    private String originalContent;

    // diff源文件的提问代码块的起始行号与终止行号，非必传
    // 仅用于回显在界面上展示行号，diff的范围通过 originalContent字符串匹配 的方式来在源文件中定位具体位置
    private Integer startLine;
    private Integer endLine;

    public DiffCodeRequestData(String generatedContent, String filePath, String originalContent) {
        this.generatedContent = generatedContent;
        this.filePath = filePath;
        this.originalContent = originalContent;
    }

    public DiffCodeRequestData(String generatedContent, String filePath, String originalContent, Integer startLine, Integer endLine) {
        this.generatedContent = generatedContent;
        this.filePath = filePath;
        this.originalContent = originalContent;
        this.startLine = startLine;
        this.endLine = endLine;
    }

    public String getGeneratedContent() {
        return generatedContent;
    }

    public void setGeneratedContent(String generatedContent) {
        this.generatedContent = generatedContent;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getOriginalContent() {
        return originalContent;
    }

    public void setOriginalContent(String originalContent) {
        this.originalContent = originalContent;
    }

    public Integer getStartLine() {
        return startLine;
    }

    public void setStartLine(Integer startLine) {
        this.startLine = startLine;
    }

    public Integer getEndLine() {
        return endLine;
    }

    public void setEndLine(Integer endLine) {
        this.endLine = endLine;
    }
}
