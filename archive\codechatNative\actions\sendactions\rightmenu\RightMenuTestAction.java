package com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions.rightmenu;

import com.srdcloud.ideplugin.assistant.codechatNative.actions.sendactions.SendAction;
import com.srdcloud.ideplugin.general.enums.ChatMessageType;
import org.jetbrains.annotations.Nls;

/**
 * @author: yangy
 * @date: 2023/12/15 10:56
 * @Desc
 */
public class RightMenuTestAction extends SendAction {
    protected ChatMessageType getChatMessageType() {
        return  ChatMessageType.UNITTEST;
    }

    @Override
    public @Nls String toString() {
        return "生成单元测试";
    }

    @Override
    public String getCMD() {
        return "/generate unit test";
    }
}
