package com.srdcloud.ideplugin.assistant.codechatNative.logics;

import com.intellij.execution.filters.Filter;
import com.intellij.execution.filters.JvmExceptionOccurrenceFilter;
import com.intellij.execution.impl.InlayProvider;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.EditorCustomElementRenderer;
import com.intellij.openapi.project.Project;
import com.intellij.psi.PsiClass;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * @author: yangy
 * @date: 2024/4/25 10:07
 * @Desc 异常解析功能
 */
public class ExceptionFilter implements JvmExceptionOccurrenceFilter {
    @Nullable
    public Filter.ResultItem applyFilter(@NotNull String exceptionClassName, @NotNull List<PsiClass> classes, int exceptionStartOffset) {
        if (exceptionClassName == null) {
            return null;
        }
        if (classes == null) {
            return null;
        }
        return new CreateExceptionBreakpointResult(exceptionStartOffset, exceptionStartOffset + exceptionClassName.length(), exceptionClassName, ((PsiClass)classes
                .get(0)).getProject());
    }

    private static class CreateExceptionBreakpointResult extends Filter.ResultItem implements InlayProvider {
        private final String myExceptionClassName;

        private final Project project;

        private final int startOffset;

        CreateExceptionBreakpointResult(int highlightStartOffset, int highlightEndOffset, String exceptionClassName, Project project) {
            super(highlightStartOffset, highlightEndOffset, null);
            this.myExceptionClassName = exceptionClassName;
            this.project = project;
            this.startOffset = highlightStartOffset;
        }

        public EditorCustomElementRenderer createInlayRenderer(Editor editor) {
            return (EditorCustomElementRenderer)new ExceptionRenderer(editor, this.project, this.startOffset);
        }
    }
}
