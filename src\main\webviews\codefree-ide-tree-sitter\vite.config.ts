import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'

// Vue构建配置
// https://vite.dev/config/
export default defineConfig({
  // 使用插件
  plugins: [
    vue(),
    vueJsx(),
  ],

  // 文件路径别名映射
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },

  // 项目构建选项
  build: {
    // 输出路径
    outDir: '../../resources/web-views/tree-sitter',

    emptyOutDir: true,
    rollupOptions: {
      output: {
        entryFileNames: `[name].js`,
        chunkFileNames: `[name].js`,
        assetFileNames: `[name].[ext]`,
      },
    },
  },

  // 本地运行模式下，挂载到具体端口
  server: {
    port: 5001,
  },
})
