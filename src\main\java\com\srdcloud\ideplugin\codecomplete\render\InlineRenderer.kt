package com.srdcloud.ideplugin.codecomplete.render

import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.EditorCustomElementRenderer
import com.intellij.openapi.editor.Inlay
import com.intellij.openapi.editor.markup.TextAttributes
import java.awt.Graphics
import java.awt.Rectangle

/**
 * 单行补全渲染器
 */
class InlineRenderer(private val editor: Editor, private val suffix: String) : EditorCustomElementRenderer {
    private val width = editor.contentComponent.getFontMetrics(RenderFontUtils.font(editor)).stringWidth(suffix)
    private val height = editor.contentComponent.getFontMetrics(RenderFontUtils.font(editor)).height

    override fun calcWidthInPixels(inlay: Inlay<*>) = width

    override fun calcHeightInPixels(inlay: Inlay<*>) = height

    override fun paint(inlay: Inlay<*>, g: Graphics, targetRegion: Rectangle, textAttributes: TextAttributes) {
        // 补全字体颜色统一为灰色
        g.color = RenderFontUtils.color

        // 补全字体样式使用编辑器内置样式
        g.font = RenderFontUtils.font(editor)

        // 绘制补全行
        g.drawString(suffix, targetRegion.x, targetRegion.y + editor.ascent)
    }
}