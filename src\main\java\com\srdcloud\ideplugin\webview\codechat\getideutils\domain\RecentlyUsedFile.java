package com.srdcloud.ideplugin.webview.codechat.getideutils.domain;

public class RecentlyUsedFile {
    private String name;
    private String path;
    private String relativePath;
    private long size;

    public RecentlyUsedFile(String name, String path, long size) {
        this.name = name;
        this.path = path;
        this.size = size;
    }


    public RecentlyUsedFile(String name, String path, String relativePath, long size) {
        this.name = name;
        this.path = path;
        this.relativePath = relativePath;
        this.size = size;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getRelativePath() {
        return relativePath;
    }

    public void setRelativePath(String relativePath) {
        this.relativePath = relativePath;
    }

    public long getSize() {
        return size;
    }

    public void setSize(long size) {
        this.size = size;
    }
}
