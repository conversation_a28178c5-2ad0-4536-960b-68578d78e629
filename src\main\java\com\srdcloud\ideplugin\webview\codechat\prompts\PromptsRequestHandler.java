package com.srdcloud.ideplugin.webview.codechat.prompts;

import com.intellij.openapi.project.Project;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.general.utils.GitUtil;
import com.srdcloud.ideplugin.general.utils.IdeUtil;
import com.srdcloud.ideplugin.general.utils.JsonUtil;
import com.srdcloud.ideplugin.remote.PromptManageCommHandler;
import com.srdcloud.ideplugin.remote.domain.ApiResponse;
import com.srdcloud.ideplugin.remote.domain.PromptManage.PromptTemplateListResponse;
import com.srdcloud.ideplugin.service.LoginService;
import com.srdcloud.ideplugin.service.domain.template.PromptTemplateCategory;
import com.srdcloud.ideplugin.webview.codechat.CodeChatWebview;
import com.srdcloud.ideplugin.webview.codechat.common.PromptsEventType;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCode;
import com.srdcloud.ideplugin.webview.codechat.common.WebViewRspCommand;
import com.srdcloud.ideplugin.webview.base.domain.ErrorResponse;
import com.srdcloud.ideplugin.webview.base.domain.WebViewReqTypeRequest;
import com.srdcloud.ideplugin.webview.codechat.prompts.request.GetTemplatesRequest;
import com.srdcloud.ideplugin.webview.codechat.prompts.response.*;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Objects;

/**
 * 代码聊天窗口中与Prompt相关的请求处理类
 */
public class PromptsRequestHandler {

    // 当前的项目
    private final Project project;

    // 代码聊天的WebView父对象
    private final CodeChatWebview parent;

    /**
     * 构造函数，初始化PromptsRequestHandler实例
     *
     * @param project 当前项目
     * @param parent  代码聊天的WebView父对象
     */
    public PromptsRequestHandler(Project project, CodeChatWebview parent) {
        this.project = project;
        this.parent = parent;
    }

    /**
     * 处理来自WebView的Prompt请求
     *
     * @param request 请求字符串
     */
    public void processPromptsRequest(String request) {
        // 检查登录状态，如果未登录，则发送未登录错误响应到WebView
        if (LoginService.getLoginStatus() != Constants.LoginStatus_OK) {
            parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(ErrorResponse.getNoLoginResponse(WebViewRspCommand.PROMPTS_RESPONSE)));
            return;
        }

        // 解析webView请求
        WebViewReqTypeRequest webViewReqTypeRequestPrompts = JsonUtil.getInstance().fromJson(request, WebViewReqTypeRequest.class);

        // 不同业务请求：查分类、查模板列表、收藏/取消模板
        String reqTypePrompts = webViewReqTypeRequestPrompts.getData().getReqType();

        // 根据不同的请求类型处理相应的逻辑
        switch (reqTypePrompts) {
            case PromptsEventType.GET_CATEGORIES:
                // 获取模板分类列表
                ArrayList<PromptTemplateCategory> categories = PromptManageCommHandler.listCategories();
                GetCategoriesResponseCode getCategoriesResponseCode = new GetCategoriesResponseCode(WebViewRspCode.SUCCESS, categories);
                GetCategoriesResponseType getCategoriesResponseType = new GetCategoriesResponseType(PromptsEventType.GET_CATEGORIES, getCategoriesResponseCode);
                GetCategoriesResponse getCategoriesResponse = new GetCategoriesResponse(WebViewRspCommand.PROMPTS_RESPONSE, getCategoriesResponseType);
                parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(getCategoriesResponse));
                break;
            case PromptsEventType.GET_TEMPLATES:
                // 获取模板列表
                GetTemplatesRequest getTemplatesRequest = JsonUtil.getInstance().fromJson(request, GetTemplatesRequest.class);
                // 网络请求，获取模板列表
                PromptTemplateListResponse listResponse = PromptManageCommHandler.listPrompts(getTemplatesRequest.getData().getLastUsed(), getTemplatesRequest.getData().getType(), getTemplatesRequest.getData().getName(), getTemplatesRequest.getData().getCategoryId(), getTemplatesRequest.getData().getPageNum(), getTemplatesRequest.getData().getPageDataCount());

                // webView响应，模板列表
                GetTemplatesResponseCode getTemplatesResponseCode;
                if (Objects.isNull(listResponse) || CollectionUtils.isEmpty(listResponse.getData()) || listResponse.getOptResult() != RtnCode.SUCCESS) {
                    getTemplatesResponseCode = new GetTemplatesResponseCode(listResponse.getOptResult(), null);
                } else {
                    getTemplatesResponseCode = new GetTemplatesResponseCode(WebViewRspCode.SUCCESS, listResponse);
                }
                GetTemplatesResponseType getTemplatesResponseType = new GetTemplatesResponseType(PromptsEventType.GET_TEMPLATES, getTemplatesResponseCode);
                GetTemplatesResponse getTemplatesResponse = new GetTemplatesResponse(WebViewRspCommand.PROMPTS_RESPONSE, getTemplatesResponseType);
                parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(getTemplatesResponse));
                break;
            case PromptsEventType.OPERATE_TEMPLATE:
                // 操作模板（如创建、编辑、删除等）
                GetTemplatesRequest operateRequest = JsonUtil.getInstance().fromJson(request, GetTemplatesRequest.class);
                String operationType = operateRequest.getData().getOperationType();
                // 网络请求，操作模板
                ApiResponse apiResponse = PromptManageCommHandler.promptOps(operateRequest.getData().getTemplateId(), operationType, IdeUtil.getProjectName(project), GitUtil.getGitRepositoryUrl(project));

                // webView响应回传
                OperateTemplateResponseDataDataData operateTemplateResponseDataDataData = new OperateTemplateResponseDataDataData(operateRequest.getData().getTemplateId(), operationType);
                OperateTemplateResponseDataData operateTemplateResponseDataData;
                if (apiResponse.getRtnCode() != RtnCode.SUCCESS) {
                    operateTemplateResponseDataData = new OperateTemplateResponseDataData(apiResponse.getRtnCode(), null);
                } else {
                    operateTemplateResponseDataData = new OperateTemplateResponseDataData(WebViewRspCode.SUCCESS, operateTemplateResponseDataDataData);
                }
                OperateTemplateResponseData operateTemplateResponseData = new OperateTemplateResponseData(PromptsEventType.OPERATE_TEMPLATE, operateTemplateResponseDataData);
                OperateTemplateResponse operateTemplateResponse = new OperateTemplateResponse(WebViewRspCommand.PROMPTS_RESPONSE, operateTemplateResponseData);
                parent.sentMessageToWebviewWithLoadCheck(JsonUtil.getInstance().toJson(operateTemplateResponse));
                break;
            default:
                // 对于未知的请求类型，不做任何处理
                break;
        }
    }
}
