package com.srdcloud.ideplugin.service.domain.codechat;

import com.srdcloud.ideplugin.general.enums.QuestionType;
import com.srdcloud.ideplugin.remote.domain.WorkItem.WorkItemInfo;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.CodeAIRequestPromptChat;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.CodeMessage;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.QuoteItem;
import com.srdcloud.ideplugin.service.domain.apigw.codechat.history.DialogCondition;
import com.srdcloud.ideplugin.webview.codechat.relatedfile.RelatedFile;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/27
 */
public class AskQuestionParams {
    private String reqId;
    private String question;
    private Integer kbId;
    private String questionTaskType;
    private String fileName;
    private String prefix;
    private String suffix;
    private Integer manualType;
    private List<String> stopWords;
    private QuestionType questionType;
    private String dialogId;
    private String parentReqId;
    private DialogCondition currModelRouteCondition;
    private List<CodeAIRequestPromptChat> prompts;
    private String promptTemplateId;
    private QuoteItem quote;
    private List<CodeMessage.ImportSnippets> importSnippets;
    private List<RelatedFile> relatedFiles;
    private List<String> gitUrls;
    private List<String> diffList;
    private List<WorkItemInfo> workItemList;

    public AskQuestionParams() {
    }

    public AskQuestionParams(String reqId, String question, Integer kbId, String questionTaskType, String fileName, String prefix, String suffix, Integer manualType, List<String> stopWords, QuestionType questionType, String dialogId, String parentReqId, DialogCondition currModelRouteCondition, List<CodeAIRequestPromptChat> prompts, String promptTemplateId, QuoteItem quote, List<CodeMessage.ImportSnippets> importSnippets, List<RelatedFile> relatedFiles, List<String> gitUrls, List<String> diffList,List<WorkItemInfo> workItemList) {
        this.reqId = reqId;
        this.question = question;
        this.kbId = kbId;
        this.questionTaskType = questionTaskType;
        this.fileName = fileName;
        this.prefix = prefix;
        this.suffix = suffix;
        this.manualType = manualType;
        this.stopWords = stopWords;
        this.questionType = questionType;
        this.dialogId = dialogId;
        this.parentReqId = parentReqId;
        this.currModelRouteCondition = currModelRouteCondition;
        this.prompts = prompts;
        this.promptTemplateId = promptTemplateId;
        this.quote = quote;
        this.importSnippets = importSnippets;
        this.relatedFiles = relatedFiles;
        this.gitUrls = gitUrls;
        this.diffList = diffList;
        this.workItemList = workItemList;
    }

    public String getReqId() {
        return reqId;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    public Integer getKbId() {
        return kbId;
    }

    public void setKbId(Integer kbId) {
        this.kbId = kbId;
    }

    public String getQuestionTaskType() {
        return questionTaskType;
    }

    public void setQuestionTaskType(String questionTaskType) {
        this.questionTaskType = questionTaskType;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public String getSuffix() {
        return suffix;
    }

    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }

    public Integer getManualType() {
        return manualType;
    }

    public void setManualType(Integer manualType) {
        this.manualType = manualType;
    }

    public List<String> getStopWords() {
        return stopWords;
    }

    public void setStopWords(List<String> stopWords) {
        this.stopWords = stopWords;
    }

    public QuestionType getQuestionType() {
        return questionType;
    }

    public void setQuestionType(QuestionType questionType) {
        this.questionType = questionType;
    }

    public String getDialogId() {
        return dialogId;
    }

    public void setDialogId(String dialogId) {
        this.dialogId = dialogId;
    }

    public String getParentReqId() {
        return parentReqId;
    }

    public void setParentReqId(String parentReqId) {
        this.parentReqId = parentReqId;
    }

    public DialogCondition getCurrModelRouteCondition() {
        return currModelRouteCondition;
    }

    public void setCurrModelRouteCondition(DialogCondition currModelRouteCondition) {
        this.currModelRouteCondition = currModelRouteCondition;
    }

    public List<CodeAIRequestPromptChat> getPrompts() {
        return prompts;
    }

    public void setPrompts(List<CodeAIRequestPromptChat> prompts) {
        this.prompts = prompts;
    }

    public String getPromptTemplateId() {
        return promptTemplateId;
    }

    public void setPromptTemplateId(String promptTemplateId) {
        this.promptTemplateId = promptTemplateId;
    }

    public QuoteItem getQuote() {
        return quote;
    }

    public void setQuote(QuoteItem quote) {
        this.quote = quote;
    }

    public List<CodeMessage.ImportSnippets> getImportSnippets() {
        return importSnippets;
    }

    public void setImportSnippets(List<CodeMessage.ImportSnippets> importSnippets) {
        this.importSnippets = importSnippets;
    }

    public List<RelatedFile> getRelatedFiles() {
        return relatedFiles;
    }

    public void setRelatedFiles(List<RelatedFile> relatedFiles) {
        this.relatedFiles = relatedFiles;
    }

    public List<String> getGitUrls() {
        return gitUrls;
    }

    public void setGitUrls(List<String> gitUrls) {
        this.gitUrls = gitUrls;
    }

    public List<String> getDiffList() {
        return diffList;
    }

    public void setDiffList(List<String> diffList) {
        this.diffList = diffList;
    }

    public List<WorkItemInfo> getWorkItemList() {
        return workItemList;
    }

    public void setWorkItemList(List<WorkItemInfo> workItemList) {
        this.workItemList = workItemList;
    }
}
