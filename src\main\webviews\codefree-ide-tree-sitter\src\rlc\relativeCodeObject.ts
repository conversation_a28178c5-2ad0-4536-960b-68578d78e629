export default class RelativeCodeObject {
  // todo go 语言需要  pkgpath  filename  objecttext  三种 java中对应地方也要改动，改动数据接口，改动返回？
  //  RelativeCodeObject rco = rlccHandler.FindRelativeObject(codeFilePath, codeFileContent, line, column, languageExt);

  private readonly jsonExt: string;
  
  private readonly languageExt: string;

  private readonly codeFilepath: string;

  private readonly objectText: string;

  public constructor(jsonExt: string,languageExt: string,codeFilePath: string, objectText: string) {
    this.jsonExt = jsonExt;
    this.languageExt = languageExt;
    this.codeFilepath = codeFilePath;
    this.objectText = objectText;
  }

  public getJsonExt(): string {
    return this.jsonExt;
  }

  public getLanguageExt(): string {
    return this.languageExt;
  }


  public getCodeFilePath(): string {
    return this.codeFilepath;
  }

  public getObjectText(): string {
    return this.objectText;
  }
}
