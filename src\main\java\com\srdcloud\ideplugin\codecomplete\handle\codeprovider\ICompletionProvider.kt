package com.srdcloud.ideplugin.codecomplete.handle.codeprovider

import com.intellij.openapi.editor.event.DocumentEvent
import com.srdcloud.ideplugin.codecomplete.domain.CompletionElement
import com.srdcloud.ideplugin.codecomplete.domain.CompletionRequest

interface ICompletionProvider {
    suspend fun getProposals(request: CompletionRequest): List<CompletionElement>

    fun isEnabled(event: DocumentEvent): Boolean

    companion object {
        fun extensions(): List<ICompletionProvider> = emptyList()
    }
}