package com.srdcloud.ideplugin.service;

import com.alibaba.fastjson.JSONObject;
import com.srdcloud.ideplugin.general.constants.Constants;
import com.srdcloud.ideplugin.general.constants.MessageNameConstant;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.remote.client.MyWebSocketClient;
import com.srdcloud.ideplugin.service.domain.apigw.ApigwWebsocketRespMessage;
import com.srdcloud.ideplugin.service.interfaces.IAnswerHandler;
import com.srdcloud.ideplugin.service.interfaces.IQuestionTaskEventHandler;
import com.srdcloud.ideplugin.service.interfaces.IRegisterResultHandler;
import com.srdcloud.ideplugin.statusbar.Notify;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * <AUTHOR> yangy
 * @create 2023/9/6 17:18
 */
public class CodeChatResponseReceiver {

    private static Logger logger = LoggerFactory.getLogger(CodeChatResponseReceiver.class);

    private static List<IAnswerHandler> codeAIAnswerHandlers = new ArrayList<IAnswerHandler>();

    private static IQuestionTaskEventHandler codeChatCompleteEngin;
    private static ReentrantReadWriteLock lock = new ReentrantReadWriteLock();
    private static ReentrantReadWriteLock.WriteLock writeLock = lock.writeLock();
    private static ReentrantReadWriteLock.ReadLock readLock = lock.readLock();

    public static void SetChatQuestionTaskEvent(IQuestionTaskEventHandler iQstTskEvent) {
        codeChatCompleteEngin = iQstTskEvent;
    }

    public static void AddMessageHandler(IAnswerHandler codeAIMsgHandler) {
        writeLock.lock();
        codeAIAnswerHandlers.add(codeAIMsgHandler);
        writeLock.unlock();
    }

    public static void DeleteMessageHandler(String redId) {
        writeLock.lock();
        for (IAnswerHandler ch : codeAIAnswerHandlers) {
            if (ch.GetReqId().equals(redId)) {
                codeAIAnswerHandlers.remove(ch);
                break;
            }
        }
        writeLock.unlock();
    }

    public static IAnswerHandler GetMessageHandler(String reqId) {
        IAnswerHandler ch = null;
        readLock.lock();
        for (IAnswerHandler c : codeAIAnswerHandlers) {
            if (c.GetReqId().equals(reqId)) {
                ch = c;
                break;
            }
        }
        readLock.unlock();
        return ch;
    }

    /**
     * 通道下行消息处理
     */
    public static void OnReceiveMessageFromCodeAI(MyWebSocketClient wsChannel, String msgStr) {
        // 去除websocket通信头尾标识，提取下行消息内容：<WBChannel>xxxx</WBChannel>
        String msgStrSub = msgStr.substring(11, msgStr.length() - 12);

        // todo:调试反序列化
//        DebugLogUtil.println(String.format("CodeChatResponseReceiver OnReceiveMessageFromCodeAI:%s", msgStrSub));

        // 反序列化
        ApigwWebsocketRespMessage caiResp = null;
        try {
            caiResp = JSONObject.parseObject(msgStrSub, ApigwWebsocketRespMessage.class);
        } catch (Exception e) {
            logger.warn("[cf] CodeAIRequestReceiver OnReceiveMessageFromCodeAI decode to ApigwWebsocketRespMessage error:{}", e.getMessage());
            e.printStackTrace();
            return;
        }
        if (caiResp == null) {
            logger.warn("[cf] CodeAIRequestReceiver OnReceiveMessageFromCodeAI Receive a message from CodeAI which decode to ApigwWebsocketRespMessage failed,msgStr:{}", msgStrSub);
            return;
        }

        // 流式应答结果处理：开发问答、代码补全、commit信息
        if (caiResp.getMessageName().equals(MessageNameConstant.MessageName_CodeGenResponse)
                || caiResp.getMessageName().equals(MessageNameConstant.MessageName_CodeChatResponse)
                || caiResp.getMessageName().equals(MessageNameConstant.MessageName_CodeGenResponse_APIGW)
                || caiResp.getMessageName().equals(MessageNameConstant.MessageName_CodeChatResponse_APIGW)
                || caiResp.getMessageName().equals(MessageNameConstant.MessageName_CommitChatResponse)
                || caiResp.getMessageName().equals(MessageNameConstant.MessageName_CommitChatResponse_APIGW)) {
            // 根据reqId获取对应的handler，调用handler的onAnswer方法处理应答结果
            IAnswerHandler ch = GetMessageHandler(caiResp.getContext().getReqId());
            if (ch != null) {
                ch.OnAnswer(caiResp.getContext().getReqId(), caiResp.getPayload());
            }
        }
        // 通道注册结果处理
        else if (caiResp.getMessageName().equals(MessageNameConstant.MessageName_RegisterChannelResult) || caiResp.getMessageName().equals(MessageNameConstant.MessageName_RegisterChannelResult_APIGW)) {
            Integer optResult = caiResp.getContext().getOptResult();
            if (optResult != RtnCode.SUCCESS) {
                logger.warn("[cf] CodeChatResponseReceiver OnReceiveMessageFromCodeAI,RegisterChannel optResult:<{}>,msg:<{}>", optResult, caiResp.getContext().getMsg());
            }
            LoginService.onRegisterResult(optResult);
        }
        // 服务端心跳请求处理
        else if (caiResp.getMessageName().equals(MessageNameConstant.MessageName_ServerHeartbeat)) {
            String responseMessage = Constants.WB_CHANNEL_START + "{\"messageName\":\"" + MessageNameConstant.MessageName_ServerHeartbeatResponse + "\"}" + Constants.WB_CHANNEL_END;
            wsChannel.SendMessage(responseMessage);
        }
        // apiKey查询结果处理
        else if (caiResp.getMessageName().equals(MessageNameConstant.MessageName_GetUserApiKeyResp)) {
            String apiKey = "";
            int rtnCode = RtnCode.SUCCESS;
            if (caiResp.getContext().getOptResult() == RtnCode.SUCCESS) {
                if (caiResp.getPayload() != null) {
                    rtnCode = caiResp.getPayload().getRetCode();
                    apiKey = caiResp.getPayload().getApiKey();
                }
            } else {
                if (caiResp.getPayload() != null) {
                    rtnCode = caiResp.getPayload().getRetCode();
                } else {
                    rtnCode = caiResp.getContext().getOptResult();
                }
            }

            LoginService.onUserApiKeyResult(rtnCode, apiKey, caiResp.getPayload());

            Notify.Companion.updateStatusNotify();

        }
    }

    /**
     * 对话关闭或异常处理
     */
    public static void onTaskErrorOrClose(String reqId, boolean cancel) {
        if (cancel) {
            // 手动停止对话
            codeChatCompleteEngin.onTaskError(reqId, RtnCode.STOP_ANSWER, null);

            // 调试用，mock其他异常码
            //codeChatCompleteEngin.onTaskError(reqId, RtnCode.USER_FORBIDDEN,null);
        } else {
            // 对话异常
            if (codeChatCompleteEngin != null) {
                codeChatCompleteEngin.onTaskError(reqId, RtnCode.SEND_ERROR, null);
            }
        }
    }


}
