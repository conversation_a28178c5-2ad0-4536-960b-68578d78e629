<!DOCTYPE html>
<html lang="zh">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <link rel="icon" href="data:,">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <title>研发云CodeFree</title>
  <style>
    html,
    body {
      height: 100%;
      margin: 0;
      font-size: 14px;
      font-family: "microsoft yahei", "PingFangSC-Regular";
      color: #30353A;
    }

    .main {
      width: 100%;
      height: 100vh;
    }

    .navbar {
      height: 50px;
      box-sizing: border-box;
      padding: 13px 20px;
      background: #fff;
      box-shadow: 0 5px 10px 0 rgba(230,236,240,.5);
      z-index: 99;
    }

    .navbar > img {
      height: 24px;
      width: auto;
    }

    .container {
      height: calc(100vh - 50px);
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      overflow-y:auto;
      box-sizing: border-box;
      padding-top: 10vh;
      background: linear-gradient(0deg, rgba(48,124,251,0.00) -1%, rgba(48,124,251,0.12) 99%);
      opacity: 0.9;
    }

    .title-container {
      text-align: center;
      position: relative;
    }

    .title-container h1 {
      margin: 4px 0 20px 0;
      font-size: 64px;
      line-height: 1.2;
      font-weight: 400;
    }

    .title-container h2 {
      margin: 0;
      font-size: 36px;
      line-height: 1.4;
      font-weight: 400;
    }

    .title-container h2.sub {
      color: #636C76;
    }
    
    .title-container .ai-150 {
      position:absolute;
      top: 0;
      left: -360px;
      transform: rotate(-30deg);
    }

    .title-container .node-20 {
      position:absolute;
      top: -25px;
      left: 47px;
      transform: rotate(-21.79deg);
    }

    .title-container .code-10 {
      position: absolute;
      top: -21px;
      right: -150px;
      transform: rotate(15deg);
    }

    .title-container .code-0 {
      position: absolute;
      top: 113px;
      left: -120px;
      opacity: 0.4;
    }

    .title-container .ai-30 {
      position:absolute;
      top: 145px;
      right: -266px;
      transform: rotate(30deg);
      opacity: 0.6;
    }

    .card-list {
      position: relative;
      margin-top: 80px;
      display: flex;
      justify-content: center;
    }

    .card-list .card-list-inner {
      width: 1170px;
      display: flex;
      flex-wrap: wrap;
    }

    .card-list .card-item {
      width: 358px;
      min-height: 388px;
      margin: 16px;
      box-sizing: border-box;
      padding: 32px;
      border-radius: 4px;
      border: 1px solid rgba(0,0,0,.1);
      box-shadow: 0px 0px 10px 0px rgba(221, 228, 234, 0.8),0px 4px 10px 0px rgba(221, 228, 234, 0.8);
      background-color: #fff;
    }

    .card-item .icon > img {
      margin-top: 10px;
      width: 50px;
      height: 50px;
    }

    .card-item .title {
      font-size: 20px;
      padding: 8px 0;
      margin: 0;
      font-weight: bold;
    }

    .card-item .desc {
      margin: 0;
      height: 75px;
      border-bottom: 1px solid #E5E9ED;
    }

    .card-item ul {
      margin: 12px 0 0 0;
      padding: 0;
    }

    .card-item ul > li {
      list-style: none;
      padding-bottom: 4px;
    }

    .card-item ul > li > strong {
      display: inline-block;
      color: #307CFB;
      text-align: right;
      margin-right: 12px;
    }

    .card-item ul.center > li > strong {
      width: 120px;
    }

    .card-list .link-0 {
      position:absolute;
      top: -30px;
      left: 12px;
      opacity: 0.4;
    }

    .card-list .node-30 {
      position:absolute;
      top: -52px;
      right: 260px;
      transform: rotate(30deg);
      opacity: 0.4;
    }

    .card-list .node-15 {
      position:absolute;
      bottom: 38px;
      left: -64px;
      transform: rotate(-15deg);
      opacity: 0.6;
    }

    .card-list .ai-40 {
      position:absolute;
      bottom: -50px;
      left: 218px;
      transform: rotate(-41deg);
    }

    .card-list .link-50 {
      position:absolute;
      bottom: -42px;
      right: 400px;
      opacity: 0.4;
      transform: rotate(50.25deg);
    }

    .card-list .code-0 {
      position:absolute;
      bottom: -80px;
      right: 30px;
      opacity: 0.6;
    }
  </style>
</head>

<body>
  <div class="main">
    <div class="navbar">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="85" height="24" viewBox="0 0 85 24">
        <defs>
          <pattern x="0" y="0" width="85" height="24" patternUnits="userSpaceOnUse" id="master_svg0_22_588"><image x="0" y="0" width="85" height="24" transform="scale(1,1)" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFUAAAAYCAYAAACLM7HoAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAvTSURBVFiF1Zl/dJTllcc/93lnJolAAUXcRaWIsErEZMJkEmYSacDq4h5/rrL2qGfR7laOovXXIu52lagHa9GiiIIeSw+1x64KRbu6XWu1ppXMkEmGTCLELT+U34JFFiQhzI/3ufvHZMIEkpC0nNPt95z3zPve5/567jzPe+9zX+H/MQKBgNc6RcWqVnI0q2RamyIby8rCY9XDyF4F075tiUTdwdxjTU1N4aGO1O3NjZGlgJYFQw+pyuWJpsgMgNLy0O04sq2lIfLeqfDbcyqUnIA31DlnFz6AQg92y92kENHBqrHG+w1V++t8mhFsScW0CaqZtcCYXgW96ReAu3KPB4+kbwQeDQQCy+PxeFoxFYge7tZpjKtW3yoJVE9vja9tyFdVUjHtPEczP1Jw+vdW1Vrnodb42oZTFtRxtTrC57N3inKLbHInAYhmr+LH+UpqM+8I9pkNtb6mgepsblz3PiClwarHBC5MNNb/Q25swoQrxg89/dAaUT7WtO9JALyp/7Ui01pj9fU5vpqaGs/B9tR8RH4Uj8fTWaqWKPpqt51Y/YrSYHiSOHYu0COoXtccdo2sB/X266xgvdj92dtTgIlPZK4UZAUwOhvI7KLMBfW4+5face7dVitHB6rfHwz9UpAPmhsjP+xBrwi/BSQSsUhtli+sQFmiMZLI8ZQFQw8p8gTofSCHRKRQVZer6osi0iOAXY52JGLR1cCgd1YOf/JKnfikOwflxUG4MGeYZkoD83Vm/Ady6GTMxcXFPkQusVa/90c5KNIB7AC5D0BVhwMqRq4ArjiBX6U9FAq9E41GO/8oe/QR1HMW68ViuAbXnifIGQAGEJsdN+jOrfOcuycsSl+ulmU9lrvwBfCmWvnECCpWL1Th74Gz8rimpn2Z1bNm6cxVq8Ttz8HCIcNnWMUF8AfDfteq+3E8+vFAJ9gciywFluae/eXhNzGMSsQil5xMtqws/PU+k2FfcOzBHvE4d4lOVmWJqM6APrcvoryd8siNBWn7P6KM7RqzqizsLDLf33W/9PiXi2vVZ8TOQ/UxwIhqTt8DLY97F/fnoz9YtQb0aqAdwUFxHeub4DrJalT+VeAzFX0NAJU3VeV+I3Z9c2P0t8fruri6eqSTtHsFGlT4TY9B1XbHFqyMx+v2H7MdPgCDDCoc6V6pY5bq1dbyukDhyaSsdV8oTJvbFMbmeXXr5u95ftobf1utpICFk2sznwCryC58gEcueFBf/v0iOdybnD8YDoJeC/wu0Rip8QfDfqAu4ySnCvIsMFrhApCpORkRnafCDmDq8frMUb0VwQeMQKnJGxKQaRmTHAYsyBF9kpmYMmZYN5crN4A8JSJ3qHHf7c1nSXo7PABnP68htawG+s9wWbSfdtTzYbLIfaQ7zynLN/1b7wHNx4Zaz5qLa9PPAfd2kYYXFqRvBl7sjV+Fu0XZezy9JRZ9B3inNBh+X9B3E7Ho09CdqP4uEYsmjpe5oKpqmKR0PmgHIo2JWOSfcmNl5eHrVQh5rFmRLxOLxb4EvoRszeyagtsV1iVi9S/RlcimVFYHJNO5JR6Pd+cHwxvqqLKCvIAqbBeR2RkrZ25/wIiTlhGqMl6MfFONXHYEDEhlF3vaek3tyQKaQxLPQiB1jCJX9sVbQOY+7UowvUFguCq9rvLjUZi0ixRRY5xvWOWmKcHQTICLQqHTEZ4BlsXj9Tv6knel8F5gIqr3kFcZWOs+7UrBynxeM2YfN6JMyqO1qlcCO+6VV/b8i+wH+PQhObR9nnz26QPywbYHZJ0WcR7dxbB8tHWefDGQiQFsqpX9QF0eqawv3lgs9qWITfaj7m/UyOaT2fSXh+aKyBwHe9v6hrVxA/Mt8pq/vLrGm5E1ih62yaF9VheBQOhCRB8FlrU0RWP5YyJmHsLVpcHwbTmaUZiVx6Mi3Lz7LvmyPydFKOgWQAdczHfLI5/mPf71AERG+StC16rK9BxhSmV1ABhaSKalHzlTFgw9jMhSgbnrG6PvAjQ3Rp5D5ReI/RCY7FhzRWvrex29KQiFQkXWyKsIu1MdvvnHjzfH6ptAFgssKamsPAfAI1CZx/O7nffIhgFM8pjXaH+T6h2iHXl17UAq3Akgz4pRRNkB4Lp2thHqut57vcIfDC9WuAP4dnNjZCVk340Z8d2G6FXAJmCCKzweCFQ9fPz2DwQC3k5XXgGmCPq8d2hqrr8idAYqo1BGq3CGwBmgZwHDjDrPALM8Cmfl6ioRBlz/5SA4Gwcro8rEvFqu/11hTVKFjxKx+stytLLKS4rVunNU5br+ZI2R10B+ur5hbTwYDP5VGt98F71FwAgsHD7Et+TA4UzAGLvUFT7zB8MfoPqLEUMLXqqrq8t0dnaKb0hBTdZnuUvgkCKfC+xUw16jbEDYjeVzhDNVWVZSEZ7hATKQbX6oZn8HA9fh88HwlzylQ7Q9U5NHWt8ff3NT/a8CgZrGfJq6OhqR5xJN9b88jj3hWDnQrbihfl3uPklB0OD6ReX+4UN9q+rq6nLH5AagoqSi6hKx9iYR7jx0KP0msKetrS3lD4ausSpfeTX5aTweP9Kfr6XBqrEO2iljluoWlPMBRPlk9z1S3J8gwLhF6nfUNgPu1geNdzAdqIsWuA8a7A9yhwmFea2Pe58eqPxfAowK7+c9Tzr7Gb1hEPLtgwlo8aPpSyWbRXNIeh3PykHY+4uAx8BPFObkCCKsOHex7tt5v3zUl9BpR9iSPI2HsbpvoIYueixzk1iWk3diE/hhPFti9YmyiqpyVIOq5pNE09q6gdobLIqLa4b6vnZ0FEDqq8L9bW117bmxUChUdDTNtRizOZvtswgEqsa6Rk9XJJ3u8G7PyQjAmOf0P4GrcltSVBXR18U1/yWwW2zPDO0hk30T96B1IZ9uKTDGTEZ1liiVx3oHiigbvcZTEa+VPt9T/oqq2VidhbAamIayPdEUeTQQCF3oCjMTTdFn+5ItCVZfBdDauPbtvnjyUVoRulKUrl0qJcDyRGPkZQB/MPyyonsEuUzF3NoSW7spSw+tFGSvKoLIFBX975ZYZLEHIO1wu9clBpzbZUNQ+Rai30JBTc+GilWDmFyAspfNBczk9VMNoCe+HUR0O+Kd2V9AAdRqAJEftzTWrwFeKS0PfackHB7tZngSZVxpZbitpSHyXmlF6Dqj3GKRXZL2LbCe1DkGezdASXn4s9amyIayitA/qpXrQN9PNEVfOMEn+Jo19t9bGxp2dR1JG4CXu0bbQREoyKjp0SzStO/Jrk834i8Pvz05OG2VAfjDXNnrwHSBtv4meYrwYQbv1NZa2XUyRtdxFxrszf5g6MPS8tA/jxxa8JPWSGQ/2cnWFeJms7s149LG3ilIDG/ywdOH+rYgRBAiQ7y6dUpl1VSUa7ySvgMxk8rKw9efYEylwlhnFMD48eOtZOtnAVBlqyDzVXjdq5mq0vJQb6dAVcNHBuvPdYvY+V3ZWihMQZgPgyuTBgKFqAo3bFjgXNpWKyc0SXqDPVzU0dwYvd565UbBDD94JPVzwKLsRjjY0NDwFSBGbIHXOj8T5TDIaXV1dUdRtqFsi0ajnVb1Uot5Pa3en4FOQfD3ZbMkUF25efvuOtC3APWXV11mxE7PGPfrKNciOgsxE/uYpNeouj2a1Fu+K0lgEapPnf80Z1ovI1UpUouogHZV7F6kR0tEOTaGgC/d9SnIQSXtbc8Use/387vaewsYMHxDUq+WVlTPa4ms3VRZWflS0jqzTwhCRXi6KhdNHHf25Vu27/lOb+czVdlisOObm6IzyoJTv6mYPpOj63M3mwyrgUuzc7PngWna0BDdFwiEvu0aaVPRF7J6iY4cwVHIJrPOjMxMe+2y3j+niOhW+ILs9WeDiHkE1R+XBcNFSYsjSi2Al/TetHpm+curYmnHrjMZOX/ztj2/Bb4QtKvhoZtAni8pDzeNHOL9+cEjqf/wB8MRhT+kOnw3n2hNjygmvTEaOQAs8QdDfwsYyRS8gS+10h8MN1ooAO4yVmYDvxGR5MGO5ItlwdCwoxk5E+WJjdHogf8DeIss4feGXZ8AAAAASUVORK5CYII="/></pattern>
        </defs>
        <g><rect x="0" y="0" width="85" height="24" rx="0" fill="url(#master_svg0_22_588)" fill-opacity="1"/></g>
      </svg>
    </div>
    <div class="container">
      <div class="title-container">
        <h2>欢迎使用</h4>
        <h1>研发云CodeFree</h2>
        <h2 class="sub">尽享高效编程乐趣</h3>
        <div class="ai-150">
          <svg t="1692268245144" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5238" width="16" height="16" xmlns:xlink="http://www.w3.org/1999/xlink">
            <path d="M565.979429 830.171429l-37.961143-103.277715H204.580571l-37.961142 105.472c-14.848 41.179429-27.501714 68.900571-37.961143 83.309715-10.459429 14.409143-27.648 21.577143-51.565715 21.577142-20.260571 0-38.180571-7.68-53.76-23.04-15.579429-15.213714-23.332571-32.621714-23.332571-52.077714 0-11.190857 1.828571-22.820571 5.412571-34.816 3.584-11.995429 9.581714-28.598857 17.92-49.956571l203.483429-534.235429 20.918857-55.149714c8.118857-21.504 16.822857-39.350857 26.038857-53.613714a107.52 107.52 0 0 1 36.352-34.523429c14.994286-8.777143 33.572571-13.165714 55.588572-13.165714 22.454857 0 41.179429 4.388571 56.173714 13.165714 14.994286 8.777143 27.136 20.114286 36.352 33.938286s16.969143 28.745143 23.332571 44.617143c6.363429 15.872 14.409143 37.156571 24.137143 63.707428l207.872 530.870857c16.310857 40.374857 24.429714 69.778286 24.429715 88.064 0 19.090286-7.68 36.571429-23.04 52.443429a74.605714 74.605714 0 0 1-55.588572 23.844571 69.924571 69.924571 0 0 1-32.548571-7.021714 67.730286 67.730286 0 0 1-22.820572-19.090286 180.370286 180.370286 0 0 1-19.821714-37.010285 1833.691429 1833.691429 0 0 1-18.212571-44.032zM246.930286 601.746286h237.714285l-119.954285-339.456-117.76 339.456z m615.936 234.057143V187.684571c0-33.645714 7.387429-58.953143 22.235428-75.776a73.142857 73.142857 0 0 1 57.490286-25.234285c24.210286 0 43.885714 8.338286 58.88 24.941714 14.994286 16.676571 22.528 41.984 22.528 76.068571v648.118858c0 34.011429-7.533714 59.465143-22.528 76.288-14.994286 16.822857-34.669714 25.234286-58.88 25.234285a72.996571 72.996571 0 0 1-57.270857-25.526857c-14.994286-17.042286-22.454857-42.349714-22.454857-75.995428z" p-id="5239" fill="#D6E5FE"></path>
          </svg>
        </div>
        <div class="node-20">
          <svg t="1692268538876" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5452" width="20" height="20" xmlns:xlink="http://www.w3.org/1999/xlink">
            <path d="M950.857143 438.857143h-156.598857A291.84 291.84 0 0 0 512 219.428571a291.84 291.84 0 0 0-282.258286 219.428572H73.142857a73.142857 73.142857 0 0 0 0 146.285714h156.598857A291.913143 291.913143 0 0 0 512 804.571429c136.192 0 249.563429-93.476571 282.258286-219.428572H950.857143a73.142857 73.142857 0 0 0 0-146.285714zM512 658.285714c-80.676571 0-146.285714-65.609143-146.285714-146.285714s65.609143-146.285714 146.285714-146.285714 146.285714 65.609143 146.285714 146.285714-65.609143 146.285714-146.285714 146.285714z" p-id="5453" fill="#d6e5fe"></path>
          </svg>
        </div>
        <div class="code-10">
          <svg t="1692268906830" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5672" width="20" height="20" xmlns:xlink="http://www.w3.org/1999/xlink">
            <path d="M22.742309 296.228571a77.531429 77.531429 0 0 0 0 109.641143l109.641142 109.641143a77.531429 77.531429 0 1 0 109.641143-109.641143l-54.857143-54.857143 54.784-54.784a77.531429 77.531429 0 1 0-109.641142-109.641142L22.742309 296.228571zM1001.393737 722.139429a77.531429 77.531429 0 0 0 0-109.714286l-109.714286-109.568a77.531429 77.531429 0 1 0-109.641142 109.641143l54.857142 54.784-54.857142 54.784a77.531429 77.531429 0 1 0 109.714285 109.641143l109.568-109.568z" p-id="5673" fill="#d6e5fe"></path>
            <path d="M667.13088 232.594286L506.801737 831.634286a77.531429 77.531429 0 1 1-149.796571-40.155429l160.475428-598.893714a77.531429 77.531429 0 1 1 149.796572 40.155428z" p-id="5674" fill="#d6e5fe"></path>
          </svg>
        </div>
        <div class="code-0">
          <svg t="1692268906830" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5672" width="38" height="38" xmlns:xlink="http://www.w3.org/1999/xlink">
            <path d="M22.742309 296.228571a77.531429 77.531429 0 0 0 0 109.641143l109.641142 109.641143a77.531429 77.531429 0 1 0 109.641143-109.641143l-54.857143-54.857143 54.784-54.784a77.531429 77.531429 0 1 0-109.641142-109.641142L22.742309 296.228571zM1001.393737 722.139429a77.531429 77.531429 0 0 0 0-109.714286l-109.714286-109.568a77.531429 77.531429 0 1 0-109.641142 109.641143l54.857142 54.784-54.857142 54.784a77.531429 77.531429 0 1 0 109.714285 109.641143l109.568-109.568z" p-id="5673" fill="#83B0FD"></path>
            <path d="M667.13088 232.594286L506.801737 831.634286a77.531429 77.531429 0 1 1-149.796571-40.155429l160.475428-598.893714a77.531429 77.531429 0 1 1 149.796572 40.155428z" p-id="5674" fill="#83B0FD"></path>
          </svg>
        </div>
        <div class="ai-30">
          <svg t="1692268245144" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5238" width="29" height="29" xmlns:xlink="http://www.w3.org/1999/xlink">
            <path d="M565.979429 830.171429l-37.961143-103.277715H204.580571l-37.961142 105.472c-14.848 41.179429-27.501714 68.900571-37.961143 83.309715-10.459429 14.409143-27.648 21.577143-51.565715 21.577142-20.260571 0-38.180571-7.68-53.76-23.04-15.579429-15.213714-23.332571-32.621714-23.332571-52.077714 0-11.190857 1.828571-22.820571 5.412571-34.816 3.584-11.995429 9.581714-28.598857 17.92-49.956571l203.483429-534.235429 20.918857-55.149714c8.118857-21.504 16.822857-39.350857 26.038857-53.613714a107.52 107.52 0 0 1 36.352-34.523429c14.994286-8.777143 33.572571-13.165714 55.588572-13.165714 22.454857 0 41.179429 4.388571 56.173714 13.165714 14.994286 8.777143 27.136 20.114286 36.352 33.938286s16.969143 28.745143 23.332571 44.617143c6.363429 15.872 14.409143 37.156571 24.137143 63.707428l207.872 530.870857c16.310857 40.374857 24.429714 69.778286 24.429715 88.064 0 19.090286-7.68 36.571429-23.04 52.443429a74.605714 74.605714 0 0 1-55.588572 23.844571 69.924571 69.924571 0 0 1-32.548571-7.021714 67.730286 67.730286 0 0 1-22.820572-19.090286 180.370286 180.370286 0 0 1-19.821714-37.010285 1833.691429 1833.691429 0 0 1-18.212571-44.032zM246.930286 601.746286h237.714285l-119.954285-339.456-117.76 339.456z m615.936 234.057143V187.684571c0-33.645714 7.387429-58.953143 22.235428-75.776a73.142857 73.142857 0 0 1 57.490286-25.234285c24.210286 0 43.885714 8.338286 58.88 24.941714 14.994286 16.676571 22.528 41.984 22.528 76.068571v648.118858c0 34.011429-7.533714 59.465143-22.528 76.288-14.994286 16.822857-34.669714 25.234286-58.88 25.234285a72.996571 72.996571 0 0 1-57.270857-25.526857c-14.994286-17.042286-22.454857-42.349714-22.454857-75.995428z" p-id="5239" fill="#ACCBFD"></path>
          </svg>
        </div>
      </div>
      <div class="card-list">
        <div class="card-list-inner">
          <div class="card-item">
            <div class="icon">
              <svg t="1692267396613" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6374" width="50" height="50" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M225.28 96.256h716.8c45.275429 0 81.92 36.644571 81.92 81.92v536.576a81.92 81.92 0 0 1-81.92 81.92h-716.8a81.92 81.92 0 0 1-81.92-81.92V178.176c0-45.275429 36.644571-81.92 81.92-81.92z" fill="#307CFB" opacity=".4" p-id="6375"></path>
                <path d="M81.92 219.136h721.92c45.275429 0 81.92 36.644571 81.92 81.92v544.768a81.92 81.92 0 0 1-81.92 81.92H81.92A81.92 81.92 0 0 1 0 845.824V301.056c0-45.275429 36.644571-81.92 81.92-81.92z" fill="#307CFB" p-id="6376"></path>
                <path d="M484.425143 404.553143c18.285714 4.900571 29.037714 23.625143 24.137143 41.837714l-74.605715 278.528a34.157714 34.157714 0 1 1-65.974857-17.700571l74.605715-278.528a34.157714 34.157714 0 0 1 41.837714-24.137143zM139.849143 587.776l120.685714 86.601143c5.12 3.876571 12.068571 4.388571 17.773714 1.316571a16.457143 16.457143 0 0 0 8.411429-15.652571V486.838857a16.310857 16.310857 0 0 0-25.819429-13.312L139.849143 560.128a16.603429 16.603429 0 0 0 0 27.648zM739.108571 560.128L618.496 473.526857a16.310857 16.310857 0 0 0-25.819429 13.312v173.275429a16.310857 16.310857 0 0 0 25.819429 13.238857l120.612571-85.577143a16.164571 16.164571 0 0 0 0-27.648z" fill="#FFFFFF" p-id="6377"></path>
              </svg>
            </div>
            <p class="title">代码补全</p>
            <p class="desc">根据已有的代码自动生成后续代码，补全当前行或生成后续若干行，帮助你提高编程效率</p>
            <ul class="center">
              <li>
                <strong id="shortcut1">Ctrl+Enter</strong>
                <span>手动发起补全请求</span>
              </li>
              <li>
                <strong id="shortcut2">Alt+]</strong>
                <span>查看下一条补全建议</span>
              </li>
              <li>
                <strong id="shortcut3">Alt+[</strong>
                <span>查看上一条补全建议</span>
              </li>
              <li>
                <strong>Tab</strong>
                <span>选择当前补全建议</span>
              </li>
              <li>
                <strong id="shortcut4">Ctrl+Alt+Shift+O</strong>
                <span>启用/禁用代码补全</span>
              </li>
            </ul>
          </div>
          <div class="card-item">
            <div class="icon">
              <svg t="1692267274288" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6017" width="50" height="50" xmlns:xlink="http://www.w3.org/1999/xlink">
                <path d="M406.513482 465.501089h71.092318a406.586622 406.586622 0 0 1 406.659762 406.513482v61.437806A90.255062 90.255062 0 0 1 794.0105 1024H81.917074A81.917074 81.917074 0 0 1 0 942.082926v-70.068355a406.513482 406.513482 0 0 1 406.513482-406.513482zM442.132781 0.03657a209.473662 209.473662 0 1 0 0 418.874183 209.473662 209.473662 0 0 0 0-418.874183z m0 325.839792A69.922074 69.922074 0 0 1 372.283847 256.027428H511.981715a69.848934 69.848934 0 0 1-69.848934 69.848934z" fill="#307CFB" p-id="6018"></path>
                <path d="M651.679583 558.535481h279.103175c51.490732 0 93.180672 41.68994 93.180672 93.180672v279.103175C1023.96343 982.31006 982.27349 1024 930.782758 1024H651.679583c-51.490732 0-93.180672-41.68994-93.180672-93.180672V651.716153c0-51.490732 41.68994-93.180672 93.180672-93.180672z" fill="#FFFFFF" p-id="6019"></path>
                <path d="M640.415985 558.535481h301.63037c45.273812 0 81.917074 36.643263 81.917075 81.917074v301.630371A81.917074 81.917074 0 0 1 942.046355 1024H640.415985a81.917074 81.917074 0 0 1-81.917074-81.917074V640.452555c0-45.273812 36.643263-81.917074 81.917074-81.917074z" fill="#307CFB" opacity=".4" p-id="6020"></path>
                <path d="M692.199279 685.287525a28.817257 28.817257 0 0 0-9.947074-6.655762 27.647013 27.647013 0 0 0-11.775579-2.340488 32.69369 32.69369 0 0 0-11.77558 2.340488 31.304025 31.304025 0 0 0-16.602835 16.602836c-0.731402 1.828506-1.316524 3.803293-1.755366 5.778079s-0.585122 3.949573-0.585122 5.9975a32.69369 32.69369 0 0 0 2.340488 11.77558 31.304025 31.304025 0 0 0 6.655762 9.947073l62.60805 62.60805-62.60805 62.388629a33.863933 33.863933 0 0 0-8.411128 15.798293 32.69369 32.69369 0 0 0-0.585122 5.9975v1.535945a30.060641 30.060641 0 0 0 13.604086 24.06314 33.863933 33.863933 0 0 0 11.117317 4.607836 32.69369 32.69369 0 0 0 5.9975 0.585122l1.535945-0.07314a30.865183 30.865183 0 0 0 20.113567-8.92311l73.213386-72.993965c9.06939-9.142531 13.604086-20.113567 13.604085-32.91311s-4.534695-23.84372-13.677226-32.986251l-73.067104-73.140245z" fill="#FFFFFF" p-id="6021"></path>
                <path d="M911.912575 915.021035h-92.961252a30.718903 30.718903 0 1 1 0-61.437806h92.961252a30.718903 30.718903 0 1 1 0 61.437806z" fill="#FFFFFF" p-id="6022"></path>
              </svg>
            </div>
            <p class="title">开发问答</p>
            <p class="desc">开发中遇到的技术问题，可直接向AI提问。无需离开IDE环境，去搜索引擎寻找答案，让开发者更专注地沉浸于开发环境</p>
            <ul>
              <li>
                <strong id="shortcut5">Alt+Shift+K</strong>
                <span>打开开发问答控件页面</span>
              </li>
            </ul>
          </div>
		  
		  <div class="card-item">
            <div class="icon">
              <svg t="1692267274288" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6017" width="50" height="50" xmlns:xlink="http://www.w3.org/1999/xlink">
                <path d="M406.513482 465.501089h71.092318a406.586622 406.586622 0 0 1 406.659762 406.513482v61.437806A90.255062 90.255062 0 0 1 794.0105 1024H81.917074A81.917074 81.917074 0 0 1 0 942.082926v-70.068355a406.513482 406.513482 0 0 1 406.513482-406.513482zM442.132781 0.03657a209.473662 209.473662 0 1 0 0 418.874183 209.473662 209.473662 0 0 0 0-418.874183z m0 325.839792A69.922074 69.922074 0 0 1 372.283847 256.027428H511.981715a69.848934 69.848934 0 0 1-69.848934 69.848934z" fill="#307CFB" p-id="6018"></path>
                <path d="M651.679583 558.535481h279.103175c51.490732 0 93.180672 41.68994 93.180672 93.180672v279.103175C1023.96343 982.31006 982.27349 1024 930.782758 1024H651.679583c-51.490732 0-93.180672-41.68994-93.180672-93.180672V651.716153c0-51.490732 41.68994-93.180672 93.180672-93.180672z" fill="#FFFFFF" p-id="6019"></path>
                <path d="M640.415985 558.535481h301.63037c45.273812 0 81.917074 36.643263 81.917075 81.917074v301.630371A81.917074 81.917074 0 0 1 942.046355 1024H640.415985a81.917074 81.917074 0 0 1-81.917074-81.917074V640.452555c0-45.273812 36.643263-81.917074 81.917074-81.917074z" fill="#307CFB" opacity=".4" p-id="6020"></path>
                <path d="M692.199279 685.287525a28.817257 28.817257 0 0 0-9.947074-6.655762 27.647013 27.647013 0 0 0-11.775579-2.340488 32.69369 32.69369 0 0 0-11.77558 2.340488 31.304025 31.304025 0 0 0-16.602835 16.602836c-0.731402 1.828506-1.316524 3.803293-1.755366 5.778079s-0.585122 3.949573-0.585122 5.9975a32.69369 32.69369 0 0 0 2.340488 11.77558 31.304025 31.304025 0 0 0 6.655762 9.947073l62.60805 62.60805-62.60805 62.388629a33.863933 33.863933 0 0 0-8.411128 15.798293 32.69369 32.69369 0 0 0-0.585122 5.9975v1.535945a30.060641 30.060641 0 0 0 13.604086 24.06314 33.863933 33.863933 0 0 0 11.117317 4.607836 32.69369 32.69369 0 0 0 5.9975 0.585122l1.535945-0.07314a30.865183 30.865183 0 0 0 20.113567-8.92311l73.213386-72.993965c9.06939-9.142531 13.604086-20.113567 13.604085-32.91311s-4.534695-23.84372-13.677226-32.986251l-73.067104-73.140245z" fill="#FFFFFF" p-id="6021"></path>
                <path d="M911.912575 915.021035h-92.961252a30.718903 30.718903 0 1 1 0-61.437806h92.961252a30.718903 30.718903 0 1 1 0 61.437806z" fill="#FFFFFF" p-id="6022"></path>
              </svg>
            </div>
            <p class="title">智能编程</p>
            <p class="desc">在智能编程模式下发送需求后查看项目多文件的修改</p>
            <ul>
              <li>
                <span>选择界面上方的智能编程，输入需求进行提问，在工作区进行文件的变更操作</span>
              </li>
            </ul>
          </div>
		  
          <div class="card-item">
            <div class="icon">
              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="50" height="50" viewBox="0 0 50 50">
                <g>
                  <g>
                    <g>
                      <g><rect x="16" y="2" width="32" height="32" rx="4.550000190734863" fill="#FFFFFF" fill-opacity="1"/></g>
                      <g style="opacity:0.****************;"><rect x="16" y="2" width="32" height="32" rx="4" fill="#307CFB" fill-opacity="1"/></g>
                      <g transform="matrix(0,1,-1,0,52.3055419921875,-29.0345458984375)"><rect x="40.6700439453125" y="11.635498046875" width="3" height="17.34000015258789" rx="1.5" fill="#FFFFFF" fill-opacity="1"/></g>
                      <g transform="matrix(0,1,-1,0,62.0345458984375,-19.3055419921875)"><rect x="40.6700439453125" y="21.364501953125" width="3" height="17.34000015258789" rx="1.5" fill="#FFFFFF" fill-opacity="1"/></g></g><g><rect x="0" y="25" width="25" height="25" rx="4.550000190734863" fill="#FFFFFF" fill-opacity="1"/></g><g><rect x="0" y="18" width="32" height="32" rx="4" fill="#307CFB" fill-opacity="1"/></g>
                      <g><path d="M13.49860357421875,33.2470955859375L9.57207357421875,29.3205655859375Q9.46760757421875,29.2160955859375,9.34476857421875,29.1340255859375Q9.22192957421875,29.0519455859375,9.08543857421875,28.9954055859375Q8.94894757421875,28.9388655859375,8.80404857421875,28.9100455859375Q8.65915057421875,28.8812255859375,8.51141353930315,28.8812255859375Q8.36367657421875,28.8812255859375,8.21877757421875,28.9100455859375Q8.07387957421875,28.9388655859375,7.93738857421875,28.9954055859375Q7.80089757421875,29.0519455859375,7.67805857421875,29.1340255859375Q7.55521957421875,29.2160955859375,7.45075357421875,29.3205655859375Q7.3462835742187504,29.4250315859375,7.26421357421875,29.5478705859375Q7.18213357421875,29.6707095859375,7.12559357421875,29.8072005859375Q7.06905357421875,29.9436915859375,7.04023357421875,30.0885905859375Q7.01141357421875,30.2334885859375,7.01141357421875,30.3812255859375Q7.01141357421875,30.5289625859375,7.04023357421875,30.6738615859375Q7.06905357421875,30.8187595859375,7.12559357421875,30.9552505859375Q7.18213357421875,31.0917415859375,7.26421357421875,31.2145805859375Q7.3462835742187504,31.3374195859375,7.45075357421875,31.4418855859375L10.92091357421875,34.9120455859375L7.45224357421875,38.3709955859375Q7.34763357421875,38.4753155859375,7.26538357421875,38.5980355859375Q7.18312357421875,38.720755585937496,7.12640357421875,38.8571755859375Q7.06967357421875,38.9935855859375,7.04064357421875,39.138445585937504Q7.01162357421875,39.2832955859375,7.01141357421875,39.4310355859375L7.01141357421875,39.4331355859375Q7.01141357421875,39.4681355859375,7.0130435742187505,39.5030955859375Q7.02608357421875,39.7824655859375,7.1389335742187505,40.0383655859375Q7.25178357421875,40.2942655859375,7.44926357421875,40.4923255859375Q7.55377357421875,40.5971255859375,7.67674657421875,40.6794255859375Q7.79971957421875,40.7618255859375,7.93641657421875,40.8185255859375Q8.07311357421875,40.8753255859375,8.21826257421875,40.904225585937496Q8.36341057421875,40.9331255859375,8.51141357421875,40.9331255859375Q8.54919367421875,40.9331255859375,8.58692577421875,40.9312255859375Q8.86481257421875,40.9172255859375,9.11918857421875,40.8045255859375Q9.37356457421875,40.6917255859375,9.57058357421875,40.4953255859375L9.57123357421875,40.4946255859375L13.50156357421875,36.575295585937496Q14.187523574218751,35.8854955859375,14.187523574218751,34.9126855859375Q14.187523574218751,33.9398655859375,13.49860357421875,33.2470955859375Z" fill-rule="evenodd" fill="#FFFFFF" fill-opacity="1"/></g><g><line x1="18.4951171875" y1="40.055908203125" x2="23.488518238067627" y2="40.055908203125" fill-opacity="0" stroke-opacity="1" stroke="#FFFFFF" fill="none" stroke-width="3" stroke-linecap="ROUND"/>
                    </g>
                  </g>
                </g>
              </svg>
            </div>
            <p class="title">代码解释</p>
            <p class="desc">对选中的代码段进行解释，让开发者更方便地了解代码的内容</p>
            <ul>
              <li>
                <span>右键菜单或在开发问答中触发功能</span>
              </li>
            </ul>
          </div>
          <div class="card-item">
            <div class="icon">
              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="50" height="47.001708984375" viewBox="0 0 50 47.001708984375">
                <g>
                  <g>
                    <g>
                      <g><path d="M39.25,6.199999809265137L4,6.199999809265137C1.79086,6.1999988555911365,0,7.990859809265137,0,10.199999809265137L0,36.79999980926514C0,39.00909980926514,1.79086,40.79999980926514,4,40.79999980926514L21.61,40.79999980926514L26.07,46.60999980926513C26.58,47.13219980926514,27.42,47.13219980926514,27.93,46.60999980926513L32.39,40.79999980926514L39.25,40.79999980926514C41.4591,40.79999980926514,43.25,39.00909980926514,43.25,36.79999980926514L43.25,10.199999809265137C43.25,7.990859809265137,41.4591,6.199999809265137,39.25,6.199999809265137Z" fill="#307CFB" fill-opacity="1"/></g>
                      <g><g style="opacity:0.****************;"><rect x="7" y="0" width="43" height="34.20000076293945" rx="4" fill="#307CFB" fill-opacity="1"/></g>
                    </g>
                  </g>
                  <g transform="matrix(0.9781476259231567,0.20791170001029968,-0.20791170001029968,0.9781476259231567,3.3194111077500565,-1.9710048925626324)">
                    <rect x="11.0361328125" y="14.8055419921875" width="3" height="17" rx="1.5" fill="#FFFFFF" fill-opacity="1"/>
                  </g>
                  <g transform="matrix(0.9781476259231567,0.20791170001029968,-0.20791170001029968,0.9781476259231567,3.795308439879591,-6.498870972718578)">
                    <rect x="32.81396484375" y="14.8055419921875" width="3.000000238418579" height="17" rx="1.5" fill="#FFFFFF" fill-opacity="1"/>
                  </g>
                  <g><path d="M23.06495,19.0918723828125L23.06495,21.9058723828125L25.741500000000002,21.0362223828125L25.741500000000002,21.0362223828125C25.8856,20.9893923828125,26.035600000000002,20.9656423828125,26.1866,20.9656423828125C26.392,20.9656423828125,26.595100000000002,21.0096523828125,26.7821,21.0947023828125C26.8015,21.1035223828125,26.8206,21.112762382812498,26.8396,21.122422382812502C27.1736,21.2925823828125,27.4279,21.5863823828125,27.5485,21.9412523828125C27.5485,21.9412523828125,27.5546,21.9595823828125,27.5546,21.9595823828125C27.6012,22.1031323828125,27.625,22.2531323828125,27.625,22.404062382812498C27.625,22.9941023828125,27.264699999999998,23.5243123828125,26.7161,23.7415023828125C26.688000000000002,23.7525823828125,26.6597,23.7627923828125,26.6311,23.7720923828125L26.6311,23.7720923828125L23.95186,24.6426023828125L25.60925,26.923752382812502C25.773600000000002,27.149942382812497,25.8684,27.4191223828125,25.882199999999997,27.6983423828125C25.8834,27.7219423828125,25.884,27.7456423828125,25.884,27.7692423828125C25.884,28.2297423828125,25.6635,28.6623423828125,25.29105,28.9329423828125C25.06013,29.1007423828125,24.78452,29.1960423828125,24.499290000000002,29.2066423828125C24.48138,29.2073423828125,24.46347,29.2076423828125,24.44555,29.2076423828125C24.3702,29.2076423828125,24.29497,29.201742382812498,24.22054,29.1899423828125C23.84375,29.1303423828125,23.50609,28.9233423828125,23.28185,28.6147423828125L23.28185,28.6147423828125L21.62581,26.3354623828125L19.96856,28.6164423828125L19.96856,28.6164423828125C19.74391,28.925642382812498,19.40625,29.132542382812503,19.02946,29.192242382812502C18.9606,29.2031423828125,18.89105,29.208942382812502,18.82134,29.2098423828125L18.80445,29.2099423828125C18.50068,29.2099423828125,18.20471,29.1137423828125,17.95895,28.9351423828125C17.60837,28.6804423828125,17.39131,28.2812423828125,17.36809,27.8485423828125C17.36671,27.8228423828125,17.36603,27.797142382812503,17.36603,27.771442382812502C17.36603,27.4677023828125,17.46219,27.1717323828125,17.64075,26.9259823828125L17.64075,26.9259823828125L19.29945,24.6430323828125L16.619817,23.772382382812502L16.619817,23.772382382812502C16.258936,23.6551223828125,15.959551,23.4009723828125,15.785695,23.0647323828125C15.785695,23.0647323828125,15.781783,23.057102382812502,15.781783,23.057102382812502C15.6787253,22.8548423828125,15.625000114316,22.6310723828125,15.625,22.404062382812498C15.625,22.2584623828125,15.6471072,22.113702382812498,15.690566,21.9747323828125C15.690566,21.9747323828125,15.695399,21.9595723828125,15.695399,21.9595723828125C15.887965,21.3669123828125,16.440259,20.9656423828125,17.06343,20.9656423828125C17.20903,20.9656423828125,17.35379,20.9877523828125,17.49276,21.0312023828125C17.49276,21.0312023828125,17.50792,21.0360423828125,17.50792,21.0360423828125L17.50792,21.0360423828125L20.1881,21.9068623828125L20.1881,19.0918723828125C20.1881,18.2974483828125,20.8321,17.6534423828125,21.62652,17.6534423828125C22.42094,17.6534423828125,23.06495,18.2974483828125,23.06495,19.0918723828125Z" fill-rule="evenodd" fill="#FFFFFF" fill-opacity="1"/></g>
                  </g>
                </g>
              </svg>
            </div>
            <p class="title">代码注释</p>
            <p class="desc">为选中的代码段生成注释，支持一键插入代码注释</p>
            <ul>
              <li>
                <span>右键菜单或在开发问答中触发功能</span>
              </li>
            </ul>
          </div>
          <div class="card-item">
            <div class="icon">
              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="50" height="50" viewBox="0 0 50 50">
                <g>
                  <g>
                    <g><rect x="3.57916259765625" y="5" width="34.20000076293945" height="43" rx="4" fill="#307CFB" fill-opacity="1"/></g><g><rect x="13.17913818359375" y="2" width="15" height="6" rx="3" fill="#ACCBFD" fill-opacity="1"/></g>
                    <g transform="matrix(0,1,-1,0,43.69244384765625,-15.66583251953125)"><rect x="29.67913818359375" y="14.0133056640625" width="3" height="18" rx="1.5" fill="#FFFFFF" fill-opacity="1"/></g><g transform="matrix(0,1,-1,0,53.17913818359375,-6.17913818359375)">
                      <rect x="29.67913818359375" y="23.5" width="3" height="18" rx="1.5" fill="#FFFFFF" fill-opacity="1"/></g><g><g><ellipse cx="36.25" cy="37.82916259765625" rx="10.17083740234375" ry="10.17083740234375" fill="#ACCBFD" fill-opacity="1"/></g>
                      <g><path d="M35.46710337890625,42.745667890625C35.06255337890625,42.762527890625,34.65247337890625,42.616517890625,34.34358337890625,42.307637890625L30.80805337890625,38.772107890625C30.22226637890625,38.186317890625,30.22226637890625,37.236567890625,30.80805337890625,36.650787890625C31.39384337890625,36.064997890625,32.34358337890625,36.064997890625,32.92937337890625,36.650777890625L35.46667337890625,39.188087890625L40.12533337890625,34.529427890625C40.71111337890625,33.943640890625,41.66091337890625,33.943641890625,42.24661337890625,34.529427890625C42.83241337890625,35.115217890625,42.83241337890625,36.064957890625,42.24661337890625,36.650747890625L36.58979337890625,42.307597890625004C36.28112337890625,42.616277890625,35.87139337890625,42.762297890625,35.46710337890625,42.745667890625Z" fill-rule="evenodd" fill="#FFFFFF" fill-opacity="1"/></g>
                    </g>
                  </g>
                </g>
              </svg>
            </div>
            <p class="title">生成单元测试</p>
            <p class="desc">为选中的代码段生成单元测试代码，支持一键新建文件并嵌入生成的代码</p>
            <ul>
              <li>
                <span>右键菜单或在开发问答中触发功能</span>
              </li>
            </ul>
          </div>

          <div class="card-item">
            <div class="icon">
              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="50" height="50" viewBox="0 0 50 50">
                <defs><clipPath id="master_svg0_7219_085525"><rect x="0" y="0" width="50" height="50" rx="0"/></clipPath></defs>
                <g clip-path="url(#master_svg0_7219_085525)"><rect x="0" y="0" width="50" height="50" rx="0" fill="#FFFFFF" fill-opacity="1"/><g><g><rect x="0" y="7.2664794921875" width="43" height="34.599998474121094" rx="4" fill="#307CFB" fill-opacity="1"/></g>
                <g transform="matrix(0.9700000286102295,0.25999999046325684,-0.25999999046325684,0.9700000286102295,4.792723216800368,-5.254741455326439)"><rect x="22.04400634765625" y="15.8900146484375" width="3.334080219268799" height="17.**************" rx="1.659999966621399" fill="#FFFFFF" fill-opacity="1"/></g>
                <g><path d="M6.8300294375,25.266505625L12.7200334375,29.496505624999998C12.9711034375,29.686315625,13.3101334375,29.711215625,13.586273437500001,29.560215624999998C13.862413437499999,29.409185625,14.0243434375,29.110295625,14.0000334375,28.796505625L14.0000334375,20.336503625C13.9983534375,20.038745625,13.8314334375,19.766559625,13.566813437499999,19.630046825C13.3021834375,19.493534125,12.9836434375,19.515285525,12.7400334375,19.686504625L6.8300294375,23.916505625C6.3470214375,24.236895625,6.3470214375,24.946115625,6.8300294375,25.266505625Z" fill="#FFFFFF" fill-opacity="1"/></g>
                <g><path d="M36.09000244140625,23.916505625L30.20000244140625,19.686504625C29.95639244140625,19.515285525,29.63784644140625,19.493534125,29.37322044140625,19.630046825C29.10859344140625,19.766559625,28.94168091140625,20.038745625,28.94000244140625,20.336503625L28.94000244140625,28.796505625C28.94168281140625,29.094255625000002,29.10859444140625,29.366445625,29.37322044140625,29.502965625C29.63784444140625,29.639515625,29.95639244140625,29.617715625000002,30.20000244140625,29.446505625L36.09000244140625,25.266505625C36.59605244140625,24.958775625,36.59605244140625,24.224235625,36.09000244140625,23.916505625Z" fill="#FFFFFF" fill-opacity="1"/></g></g><g><g><ellipse cx="39.82916259765625" cy="39.82916259765625" rx="10.17083740234375" ry="10.17083740234375" fill="#ACCBFD" fill-opacity="1"/></g><g><path d="M39.0462659765625,44.745667890625C38.6417159765625,44.762527890625,38.2316359765625,44.616517890625,37.9227459765625,44.307637890625L34.3872159765625,40.772107890625C33.8014289765625,40.186317890625,33.8014289765625,39.236567890625,34.3872159765625,38.650787890625C34.9730059765625,38.064997890625,35.9227459765625,38.064997890625,36.5085359765625,38.650777890625L39.0458359765625,41.188087890625L43.7044959765625,36.529427890625C44.2902759765625,35.943640890625,45.2400759765625,35.943641890625,45.8257759765625,36.529427890625C46.4115759765625,37.115217890625,46.4115759765625,38.064957890625,45.8257759765625,38.650747890625L40.1689559765625,44.307597890625004C39.8602859765625,44.616277890625,39.*************,44.762297890625,39.0462659765625,44.745667890625Z" fill-rule="evenodd" fill="#FFFFFF" fill-opacity="1"/></g></g></g></svg>
            </div>
            <p class="title">代码优化</p>
            <p class="desc">为选中的代码提供优化建议</p>
            <ul>
              <li>
                <span>右键菜单或在开发问答中触发功能</span>
              </li>
            </ul>
          </div>

          <div class="card-item">
            <div class="icon">
              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="50" height="50" viewBox="0 0 50 50"><g><g><g><rect x="3.57916259765625" y="5" width="34.20000076293945" height="43" rx="4" fill="#307CFB" fill-opacity="1"/></g><g><rect x="13.17913818359375" y="2" width="15" height="6" rx="3" fill="#ACCBFD" fill-opacity="1"/></g><g><path d="M48.162739733886724,43.5833193359375L40.28633973388672,29.4889693359375C39.785339733886715,28.5724043359375,38.82413973388672,28.0023193359375,37.77963973388672,28.0023193359375C36.73504973388672,28.0023194276304,35.77379973388672,28.5724043359375,35.272849733886716,29.4889693359375L27.396482733886717,43.5833193359375C26.295022733886718,45.5509193359375,27.685692733886718,47.9999193359375,29.90245973388672,47.9999193359375L45.65513973388672,47.9999193359375C47.87193973388672,47.9999193359375,49.262639733886715,45.5509193359375,48.162739733886724,43.5833193359375Z" fill="#ACCBFD" fill-opacity="1"/></g><g transform="matrix(0,1,-1,0,44.474510192871094,-16.474510192871094)"><rect x="30.474510192871094" y="14" width="3" height="18" rx="1.5" fill="#FFFFFF" fill-opacity="1"/></g><g transform="matrix(0,1,-1,0,47.961204528808594,-0.9878158569335938)"><rect x="24.474510192871094" y="23.4866943359375" width="3" height="12" rx="1.5" fill="#FFFFFF" fill-opacity="1"/></g></g><g transform="matrix(-1,5.400848479553133e-8,-5.400848479553133e-8,-1,78.55832128994226,81.39999940447102)"><rect x="39.27915954589844" y="40.70000076293945" width="3" height="7.199999809265137" rx="1.5" fill="#FFFFFF" fill-opacity="1"/></g><g transform="matrix(-1,5.400848479553133e-8,-5.400848479553133e-8,-1,78.55832153298043,90.39999940447102)"><rect x="39.27915954589844" y="45.20000076293945" width="3" height="3" rx="1.5" fill="#FFFFFF" fill-opacity="1"/></g></g></svg>
            </div>
            <p class="title">异常报错解释</p>
            <p class="desc">为代码的异常报错提供分析和修复建议</p>
            <ul>
              <li>
                <span>运行代码后在console的报错位置触发功能</span>
              </li>
            </ul>
          </div>
		  
		  <div class="card-item">
            <div class="icon">
              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="50" height="50" viewBox="0 0 50 50"><defs><clipPath id="master_svg0_7219_085517"><rect x="0" y="0" width="50" height="50" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_7219_085517)"><rect x="0" y="0" width="50" height="50" rx="0" fill="#FFFFFF" fill-opacity="1"/><g><g><path d="M47.5743712890625,25.25775146484375L26.1986812890625,25.25775146484375C24.8590512890625,25.25775088653275,23.7730712890625,26.34373146484375,23.7730712890625,27.68336146484375L23.7730712890625,43.81365146484375C23.7730712890625,45.15335146484375,24.8590512890625,46.23925146484375,26.1986812890625,46.23925146484375L36.8774712890625,46.23925146484375L39.5819712890625,49.76245146484375C39.8912712890625,50.07915146484375,40.*************,50.07915146484375,40.709871289062505,49.76245146484375L43.***************,46.23925146484375L47.5743712890625,46.23925146484375C48.9139712890625,46.23925146484375,49.9999712890625,45.15335146484375,49.9999712890625,43.81365146484375L49.9999712890625,27.68336146484375C49.9999712890625,26.34373146484375,48.9139712890625,25.25775146484375,47.5743712890625,25.25775146484375Z" fill="#ACCBFD" fill-opacity="1"/></g><g transform="matrix(-1,0,0,1,86.5,0)"><path d="M82.5,4.85693359375L47.25,4.85693359375C45.04086,4.856932640076,43.25,6.64779359375,43.25,8.85693359375L43.25,35.45693359375C43.25,37.66603359375,45.04086,39.45693359375,47.25,39.45693359375L64.86,39.45693359375L69.32,45.26693359375C69.83,45.78913359375,70.67,45.78913359375,71.18,45.26693359375L75.64,39.45693359375L82.5,39.45693359375C84.7091,39.45693359375,86.5,37.66603359375,86.5,35.45693359375L86.5,8.85693359375C86.5,6.64779359375,84.7091,4.85693359375,82.5,4.85693359375Z" fill="#307CFB" fill-opacity="1"/></g></g><g><path d="M31.196466162109374,15.142203437500001L31.018566162109376,15.0287134375C30.865466162109374,14.9314434375,30.710566162109377,14.8485764375,30.552166162109376,14.7513014375L30.320766162109376,14.6306084375C30.242466162109373,14.5909774375,30.165966162109374,14.5549494375,30.087666162109375,14.5189214375L30.027166162109374,14.4918994375C29.881166162109373,14.4270494375,29.731666162109374,14.3676034375,29.582166162109374,14.3117604375L29.519866162109373,14.2883424375C29.437966162109376,14.2595194375,29.357866162109374,14.2306974375,29.276066162109377,14.2054784375C29.194166162109376,14.1802584375,29.098066162109376,14.1478334375,29.001966162109376,14.1226144375L28.823966162109375,14.0739774375L28.645966162109374,14.0307444375L28.430566162109375,13.9875104375C28.313066162109376,13.9658936375,28.192066162109374,13.9460782375,28.074566162109374,13.9298662375L27.880566162109375,13.9064486375L27.745366162109374,13.8920371375L27.489066162109374,13.8740234375L27.337766162109375,13.8740234375L27.092066162109376,13.8740234375L26.850066162109375,13.8740234375C22.806566162109377,13.8779774575,19.527766162109373,17.1908134375,19.516966162109377,21.2831634375L19.516966162109377,25.664123437500002C19.517866162109375,25.8739234375,19.685166162109375,26.044123437499998,19.892466162109375,26.0460234375L20.782466162109372,26.0460234375C20.986066162109374,26.0461234375,21.153066162109376,25.882723437499997,21.159766162109374,25.6767234375L21.159766162109374,25.5903234375C21.159766162109374,25.3273234375,21.170466162109374,25.0661234375,21.182866162109377,24.8049234375C21.240766162109374,23.491173437500002,21.421366162109376,22.1859434375,21.722166162109374,20.9066734375Q21.732866162109374,20.8472234375,21.739966162109376,20.8184034375C21.741966162109374,20.8032034375,21.744966162109375,20.7881534375,21.748866162109373,20.7733634375C21.763166162109375,20.6977134375,21.780966162109372,20.6220534375,21.798766162109374,20.5481934375L21.820066162109377,20.4689334375C21.834366162109376,20.414893437499998,21.848566162109375,20.3626534375,21.866366162109372,20.3104034375C21.879366162109378,20.2610134375,21.894866162109373,20.2123134375,21.912666162109375,20.1644934375C21.961966162109377,20.0046434375,22.021366162109373,19.8481934375,22.090666162109375,19.696133437500002C22.749966162109374,18.2271234375,24.039366162109374,17.1459534375,25.588066162109374,16.7634634375C26.435866162109374,16.555003437499998,27.321866162109377,16.5674034375,28.163566162109376,16.7994834375L28.341566162109373,16.8535334375C29.961066162109375,17.3651434375,31.229466162109375,18.6482434375,31.735866162109374,20.2869934375C31.776766162109375,20.4220934375,31.814166162109377,20.5608034375,31.844366162109374,20.701313437499998C32.106166162109375,21.8871834375,31.952666162109374,23.128283437500002,31.410066162109374,24.2122234375C31.394066162109375,24.2428234375,31.378066162109373,24.2753234375,31.360266162109376,24.3059234375C31.339566162109374,24.3498234375,31.316366162109375,24.3925234375,31.290866162109374,24.4338234375L31.280166162109374,24.4536234375C31.196466162109374,24.600423437499998,31.105566162109376,24.7429234375,31.007866162109377,24.880523437500003L30.981166162109375,24.9166234375C30.947366162109375,24.963423437499998,30.911766162109377,25.012023437499998,30.876166162109374,25.0571234375L30.780066162109375,25.177823437500003C30.553866162109376,25.455923437499997,30.299066162109376,25.708923437499998,30.019966162109377,25.932523437500002C29.955966162109377,25.9866234375,29.888266162109375,26.0370234375,29.820666162109376,26.0857234375C29.562066162109375,26.275723437499998,29.286066162109375,26.4404234375,28.996566162109374,26.577423437500002C28.943166162109375,26.6027234375,28.891566162109374,26.6279234375,28.838166162109374,26.6495234375L28.717166162109375,26.6999234375C28.594366162109374,26.7504234375,28.469766162109376,26.7954234375,28.345166162109376,26.8332234375L28.330866162109373,26.8332234375C28.183666162109375,26.880723437500002,28.033966162109376,26.9198234375,27.882366162109374,26.9503234375C27.647766162109374,27.0019234375,27.409866162109374,27.0362234375,27.170466162109374,27.053023437500002L14.971116162109375,27.053023437500002C12.947576162109375,26.9960234375,11.328646162109376,25.333723437499998,11.301426162109376,23.2851434375C11.274196162109375,21.2365334375,12.848366162109375,19.5308134375,14.869666162109375,19.4187234375L15.067226162109375,19.4187234375C16.096596162109375,19.419203437500002,17.123946162109377,19.5108234375,18.137526162109374,19.6925334375C18.167466162109378,19.6982034375,18.197836162109375,19.7012234375,18.228296162109373,19.7015434375C18.451196162109376,19.7034834375,18.648866162109375,19.5569434375,18.714166162109375,19.3412634375C18.718166162109377,19.3283134375,18.721166162109377,19.315053437499998,18.723066162109376,19.3016334375C18.891766162109377,18.5526334375,19.158766162109373,17.8298534375,19.516966162109377,17.1525634375C19.544366162109377,17.0998234375,19.558366162109373,17.0410234375,19.557866162109377,16.9814334375C19.558866162109375,16.769813437499998,19.389666162109375,16.5977334375,19.180566162109375,16.5977334375L15.067226162109375,16.5977334375C11.446116162109375,16.5995234375,8.511566162109375,19.5709934375,8.511566162109375,23.2358734375C8.511566162109375,26.900723437499998,11.446116162109375,29.8722234375,15.067226162109375,29.8740234375L15.266576162109375,29.8740234375L27.412466162109375,29.8740234375C27.679866162109374,29.853123437500003,27.946066162109375,29.818223437500002,28.209866162109375,29.7695234375L28.355866162109375,29.7407234375L28.533766162109377,29.7011234375C28.738466162109376,29.6542234375,28.941366162109375,29.6002234375,29.138966162109377,29.5371234375L29.308066162109377,29.483123437499998C29.732466162109375,29.3387234375,30.143866162109376,29.1579234375,30.537966162109374,28.9427234375L30.691066162109376,28.8580234375L30.765766162109376,28.8148234375C30.870766162109376,28.7535234375,30.972266162109374,28.690523437499998,31.073666162109376,28.6238234375L31.251666162109377,28.5067234375L31.429666162109374,28.3806234375L31.593466162109376,28.2563234375L31.760766162109373,28.124823437499998C31.778566162109374,28.1122234375,31.796366162109376,28.0960234375,31.814166162109377,28.0816234375C31.944066162109376,27.9735234375,32.06866616210937,27.8618234375,32.19146616210938,27.7466234375C32.20026616210937,27.7386234375,32.20856616210938,27.7302234375,32.216366162109374,27.7213234375C32.330266162109375,27.6150234375,32.43886616210938,27.5034234375,32.54566616210937,27.389923437500002L32.583066162109375,27.3502234375C32.68446616210937,27.2404234375,32.782366162109376,27.1287234375,32.878466162109376,27.013423437500002L32.91766616210937,26.9647234375C33.01376616210938,26.8477234375,33.106366162109374,26.7270234375,33.19706616210938,26.6045234375C33.20346616210938,26.5970234375,33.209366162109376,26.5892234375,33.214866162109374,26.5810234375C33.69746616210938,25.9097234375,34.07506616210938,25.167323437500002,34.33446616210938,24.379723437499997C35.460166162109374,20.936753437500002,34.17606616210938,17.1567634375,31.196466162109374,15.142203437500001ZM31.988566162109375,26.5883234375C31.988566162109375,26.5883234375,31.988566162109375,26.5847234375,31.988566162109375,26.5847234375L31.988566162109375,26.5883234375Z" fill="#FFFFFF" fill-opacity="1"/></g></g></svg>
            </div>
            <p class="title">开发知识库</p>
            <p class="desc">支持对API知识库进行提问，提供备选接口列表，选择后直接生成调用代码</p>
            <ul>
              <li>
                <span>在输入框上方选择知识库，输入问题进行提问</span>
              </li>
            </ul>
          </div>

        </div>
        <div class="link-0">
          <svg t="1692322631756" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5730" width="26" height="26" xmlns:xlink="http://www.w3.org/1999/xlink">
            <path d="M965.747115 695.68231a191.455917 191.455917 0 0 0-270.90063 0c-6.717751 7.009828-12.92437 14.603808-18.473817 22.635902L382.690099 630.111214a190.871765 190.871765 0 0 0-25.191568-85.505293l148.447703-173.201157a192.405164 192.405164 0 1 0-96.531168-83.53378L260.748306 461.437237a191.601955 191.601955 0 1 0 66.593362 313.908843c6.717751-7.009828 12.92437-14.603808 18.473817-22.635901l293.682569 88.206997a191.601955 191.601955 0 1 0 326.030004-144.94279l0.219057-0.292076z" p-id="5731" fill="#ACCBFD"></path>
          </svg>
        </div>
        <div class="node-30">
          <svg t="1692268538876" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5452" width="26" height="26" xmlns:xlink="http://www.w3.org/1999/xlink">
            <path d="M950.857143 438.857143h-156.598857A291.84 291.84 0 0 0 512 219.428571a291.84 291.84 0 0 0-282.258286 219.428572H73.142857a73.142857 73.142857 0 0 0 0 146.285714h156.598857A291.913143 291.913143 0 0 0 512 804.571429c136.192 0 249.563429-93.476571 282.258286-219.428572H950.857143a73.142857 73.142857 0 0 0 0-146.285714zM512 658.285714c-80.676571 0-146.285714-65.609143-146.285714-146.285714s65.609143-146.285714 146.285714-146.285714 146.285714 65.609143 146.285714 146.285714-65.609143 146.285714-146.285714 146.285714z" p-id="5453" fill="#ACCBFD"></path>
          </svg>
        </div>
         <div class="node-15">
          <svg t="1692268538876" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5452" width="30" height="30" xmlns:xlink="http://www.w3.org/1999/xlink">
            <path d="M950.857143 438.857143h-156.598857A291.84 291.84 0 0 0 512 219.428571a291.84 291.84 0 0 0-282.258286 219.428572H73.142857a73.142857 73.142857 0 0 0 0 146.285714h156.598857A291.913143 291.913143 0 0 0 512 804.571429c136.192 0 249.563429-93.476571 282.258286-219.428572H950.857143a73.142857 73.142857 0 0 0 0-146.285714zM512 658.285714c-80.676571 0-146.285714-65.609143-146.285714-146.285714s65.609143-146.285714 146.285714-146.285714 146.285714 65.609143 146.285714 146.285714-65.609143 146.285714-146.285714 146.285714z" p-id="5453" fill="#E6EFFE"></path>
          </svg>
        </div>
        <div class="ai-40">
          <svg t="1692268245144" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5238" width="20" height="20" xmlns:xlink="http://www.w3.org/1999/xlink">
            <path d="M565.979429 830.171429l-37.961143-103.277715H204.580571l-37.961142 105.472c-14.848 41.179429-27.501714 68.900571-37.961143 83.309715-10.459429 14.409143-27.648 21.577143-51.565715 21.577142-20.260571 0-38.180571-7.68-53.76-23.04-15.579429-15.213714-23.332571-32.621714-23.332571-52.077714 0-11.190857 1.828571-22.820571 5.412571-34.816 3.584-11.995429 9.581714-28.598857 17.92-49.956571l203.483429-534.235429 20.918857-55.149714c8.118857-21.504 16.822857-39.350857 26.038857-53.613714a107.52 107.52 0 0 1 36.352-34.523429c14.994286-8.777143 33.572571-13.165714 55.588572-13.165714 22.454857 0 41.179429 4.388571 56.173714 13.165714 14.994286 8.777143 27.136 20.114286 36.352 33.938286s16.969143 28.745143 23.332571 44.617143c6.363429 15.872 14.409143 37.156571 24.137143 63.707428l207.872 530.870857c16.310857 40.374857 24.429714 69.778286 24.429715 88.064 0 19.090286-7.68 36.571429-23.04 52.443429a74.605714 74.605714 0 0 1-55.588572 23.844571 69.924571 69.924571 0 0 1-32.548571-7.021714 67.730286 67.730286 0 0 1-22.820572-19.090286 180.370286 180.370286 0 0 1-19.821714-37.010285 1833.691429 1833.691429 0 0 1-18.212571-44.032zM246.930286 601.746286h237.714285l-119.954285-339.456-117.76 339.456z m615.936 234.057143V187.684571c0-33.645714 7.387429-58.953143 22.235428-75.776a73.142857 73.142857 0 0 1 57.490286-25.234285c24.210286 0 43.885714 8.338286 58.88 24.941714 14.994286 16.676571 22.528 41.984 22.528 76.068571v648.118858c0 34.011429-7.533714 59.465143-22.528 76.288-14.994286 16.822857-34.669714 25.234286-58.88 25.234285a72.996571 72.996571 0 0 1-57.270857-25.526857c-14.994286-17.042286-22.454857-42.349714-22.454857-75.995428z" p-id="5239" fill="#D6E5FE"></path>
          </svg>
        </div>
        <div class="link-50">
          <svg t="1692322631756" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5730" width="26" height="26" xmlns:xlink="http://www.w3.org/1999/xlink">
            <path d="M965.747115 695.68231a191.455917 191.455917 0 0 0-270.90063 0c-6.717751 7.009828-12.92437 14.603808-18.473817 22.635902L382.690099 630.111214a190.871765 190.871765 0 0 0-25.191568-85.505293l148.447703-173.201157a192.405164 192.405164 0 1 0-96.531168-83.53378L260.748306 461.437237a191.601955 191.601955 0 1 0 66.593362 313.908843c6.717751-7.009828 12.92437-14.603808 18.473817-22.635901l293.682569 88.206997a191.601955 191.601955 0 1 0 326.030004-144.94279l0.219057-0.292076z" p-id="5731" fill="#D6E5FE"></path>
          </svg>
        </div>
        <div class="code-0">
          <svg t="1692268906830" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5672" width="30" height="30" xmlns:xlink="http://www.w3.org/1999/xlink">
            <path d="M22.742309 296.228571a77.531429 77.531429 0 0 0 0 109.641143l109.641142 109.641143a77.531429 77.531429 0 1 0 109.641143-109.641143l-54.857143-54.857143 54.784-54.784a77.531429 77.531429 0 1 0-109.641142-109.641142L22.742309 296.228571zM1001.393737 722.139429a77.531429 77.531429 0 0 0 0-109.714286l-109.714286-109.568a77.531429 77.531429 0 1 0-109.641142 109.641143l54.857142 54.784-54.857142 54.784a77.531429 77.531429 0 1 0 109.714285 109.641143l109.568-109.568z" p-id="5673" fill="#ACCBFD"></path>
            <path d="M667.13088 232.594286L506.801737 831.634286a77.531429 77.531429 0 1 1-149.796571-40.155429l160.475428-598.893714a77.531429 77.531429 0 1 1 149.796572 40.155428z" p-id="5674" fill="#ACCBFD"></path>
          </svg>
        </div>
      </div>
    </div>
  </div>
  <script>
    window.onload = function() {
      var osType = getOsType();

      if(osType === 'mac') {
        document.getElementById('shortcut1').innerText = '⌘+Return';
        document.getElementById('shortcut2').innerText = '⌥+]';
        document.getElementById('shortcut3').innerText = '⌥+[';
        document.getElementById('shortcut4').innerText = '⌥+⇧+⌘+O';
        document.getElementById('shortcut5').innerText = '⌥+⇧+K';
        document.getElementById('shortcut6').innerText = '⌥+⇧+A';
      }
    }

    function getOsType() {
      var userAgent = navigator.userAgent.toLowerCase();

      if (/macintosh|mac os x/i.test(navigator.userAgent)) {
        return "mac";
      } else if(/windows/i.test(navigator.userAgent)){
        return "win"
      }

      return "unknown";
    }
  </script>
</body>

</html>