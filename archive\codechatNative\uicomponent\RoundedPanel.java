package com.srdcloud.ideplugin.assistant.codechatNative.uicomponent;

import com.intellij.ui.ColorUtil;
import com.intellij.ui.JBColor;
import com.srdcloud.ideplugin.general.utils.UIUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.geom.RoundRectangle2D;


/**
 * <AUTHOR> 蔡一新
 * @since  : 2024/5/27
 *
 * 圆角背景基类，可做卡片组件
 */
public class RoundedPanel extends JPanel {

    // 设计角弧度
    private int radius;

    // 卡片背景底色
    private Color backgroundColor;

    // 背景颜色深度
    private double backgroundColorDepth;

    // 是否有边框
    private final boolean hasBorder;

    // 是否是虚线
    private boolean isDashed;

    // 背景边框距离
    private int verOffset;
    private int horOffset;

    // 边框颜色
    private Color borderColor;

    // 边框距离
    private float borderWidth;


    public static RoundedPanel createDefaultPanel() {
        return new RoundedPanel(10, 0, 0, UIUtil.getBackground(), 1,  UIUtil.getCardBackgroundColor(), 1);
    }

    /**
     * 构造一个带有圆角的面板。
     * 这个构造函数初始化了一个具有指定圆角半径、边际距离、背景颜色的面板。
     * 背景颜色深度默认为1.0
     *
     * @param radius 圆角的半径。
     * @param horOffset 面板的水平边际距离。
     * @param verOffset 面板的垂直边际距离。
     * @param backgroundColor 面板的背景颜色。
     */
    public RoundedPanel(int radius, int horOffset, int verOffset, Color backgroundColor) {
        this(radius, horOffset, verOffset, backgroundColor, 1.0);
    }

    /**
     * 构造一个带有圆角的面板。
     * 这个构造函数初始化了一个具有指定圆角半径、边际距离、背景颜色和背景颜色深度的面板。
     *
     * @param radius 圆角的半径。
     * @param horOffset 面板的水平边际距离。
     * @param verOffset 面板的垂直边际距离。
     * @param backgroundColor 背景颜色，使用JBColor定义颜色。
     * @param backgroundColorDepth 背景颜色的深度，用于控制颜色的透明度。
     */
    public RoundedPanel(int radius, int horOffset, int verOffset, Color backgroundColor, double backgroundColorDepth) {
        super();
        hasBorder = false;
        setRadius(radius);
        setHorOffset(horOffset);
        setVerOffset(verOffset);
        setBackgroundColor(backgroundColor);
        setBackgroundColorDepth(backgroundColorDepth);

        isDashed = false;
    }

    /**
     * 构造一个带有圆角面板，并为该面板设定边框。
     * 这个构造函数初始化了一个具有指定圆角半径、边际距离、背景颜色和背景颜色深度的面板。
     *
     * @param radius 圆角的半径。
     * @param horOffset 面板的水平边际距离。
     * @param verOffset 面板的垂直边际距离。
     * @param backgroundColor 背景颜色。
     * @param backgroundColorDepth 背景颜色的深度。
     * @param borderColor 边框颜色。
     * @param borderWidth 边框宽度。
     */
    public RoundedPanel(int radius, int horOffset, int  verOffset, Color backgroundColor,double backgroundColorDepth,
                        Color borderColor, int borderWidth) {
        super();
        hasBorder = true;
        setRadius(radius);
        setHorOffset(horOffset);
        setVerOffset(verOffset);
        setBackgroundColor(backgroundColor);
        setBackgroundColorDepth(backgroundColorDepth);
        setBorderColor(borderColor);
        setBorderWidth(borderWidth);

        isDashed = false;
    }

    /**
     * 重写paintComponent方法来绘制具有圆角和可选边框的组件背景。
     * @param g Graphics对象（内置，无需操作），用于绘制组件。
     */
    @Override
    protected void paintComponent(Graphics g) {

        // 构建不覆盖背景图层
        super.paintComponent(g);
        Graphics2D g2d = (Graphics2D) g;
        setOpaque(false);

        // 构建圆角图层，添加颜色
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        g2d.setColor(new JBColor(ColorUtil.withAlpha(backgroundColor, backgroundColorDepth),
                ColorUtil.withAlpha(backgroundColor, backgroundColorDepth)));// todo:需要解决只能用Color而不能用JB
        g2d.fill(new RoundRectangle2D.Float(horOffset, verOffset,
                getWidth() - 2*horOffset, getHeight() - 2*verOffset, radius, radius));

        // 如果需要边框，绘制圆角边框
        if(hasBorder) {
            g2d.setColor(borderColor);

            if (isDashed) {
                g2d.setStroke(new BasicStroke(borderWidth, BasicStroke.CAP_BUTT, BasicStroke.JOIN_BEVEL,
                        0, new float[]{2}, 1));
            }else {
                g2d.setStroke(new BasicStroke(borderWidth));
            }

            g2d.draw(new RoundRectangle2D.Float(horOffset + borderWidth, verOffset,
                    getWidth() - 2*horOffset - 2*borderWidth, getHeight() - 2*verOffset, radius, radius));

        }

    }


    public void setRadius(int radius) {
        this.radius = radius;
    }

    public void setBackgroundColor(Color backgroundColor) {
        this.backgroundColor = backgroundColor;
    }

    public void setBorderColor(Color borderColor) {
        this.borderColor = borderColor;
    }

    public void setBorderWidth(float borderWidth) {
        this.borderWidth = borderWidth;
    }

    public void setBackgroundColorDepth(double backgroundColorDepth) {
        this.backgroundColorDepth = backgroundColorDepth;
    }


    public void setHorOffset(int horOffset) {
        this.horOffset = horOffset;
    }

    public void setVerOffset(int verOffset) {
        this.verOffset = verOffset;
    }

    public void setDashed(boolean dashed) {
        isDashed = dashed;
    }
}
