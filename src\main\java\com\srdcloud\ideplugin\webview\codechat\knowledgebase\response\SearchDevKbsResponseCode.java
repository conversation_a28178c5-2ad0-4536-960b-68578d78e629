package com.srdcloud.ideplugin.webview.codechat.knowledgebase.response;

import com.srdcloud.ideplugin.remote.domain.KnowledgeBase.KnowledgeBaseInfoV2;
import com.srdcloud.ideplugin.webview.base.domain.WebViewCode;

import java.util.List;

public class SearchDevKbsResponseCode extends WebViewCode {

    private List<KnowledgeBaseInfoV2> data;

    public SearchDevKbsResponseCode(int code, List<KnowledgeBaseInfoV2> data) {
        super(code);
        this.data = data;
    }

    public List<KnowledgeBaseInfoV2> getData() {
        return data;
    }

    public void setData(List<KnowledgeBaseInfoV2> data) {
        this.data = data;
    }
}
