package com.srdcloud.ideplugin.general.utils;

import com.intellij.openapi.application.ApplicationInfo;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.project.ProjectManager;
import com.intellij.openapi.util.Key;
import com.intellij.openapi.wm.IdeFocusManager;
import com.intellij.openapi.wm.IdeFrame;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import git4idea.GitUtil;
import git4idea.repo.GitRemote;
import git4idea.repo.GitRepository;
import git4idea.repo.GitRepositoryManager;
import org.jetbrains.annotations.Nullable;

import javax.swing.*;
import java.awt.*;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;


/**
 * IDE工具类
 */
public class IdeUtil {

    public static final String TOOL_WINDOW_KEY_PREFIX = "ActiveToolWindow";

    public static final String CODE_CHAT_TAB_KEY_PREFIX = "ActiveCodeChat";

    public static final String SRD_CHAT_TAB_KEY_PREFIX = "ActiveSRDChat";


    public IdeUtil() {
    }

    public static Key buildToolWindowKey(final String projectLocationHash) {
        return Key.create(TOOL_WINDOW_KEY_PREFIX + projectLocationHash);
    }

    public static Key buildCodeChatTabKey(final String projectLocationHash) {
        return Key.create(CODE_CHAT_TAB_KEY_PREFIX + projectLocationHash);
    }

    public static Key buildSRDChatTabKey(final String projectLocationHash) {
        return Key.create(SRD_CHAT_TAB_KEY_PREFIX + projectLocationHash);
    }

    public static Font getIDELabelFont() {
        UIDefaults defaults = UIManager.getDefaults();
        return defaults.getFont("Label.font");
    }

    /**
     * 获取当前操作中的项目对象
     *
     * @return
     */
    public static Project findCurrentProject() {
        IdeFrame frame = IdeFocusManager.getGlobalInstance().getLastFocusedFrame();
        Project project = frame != null ? frame.getProject() : null;
        if (isValidProject(project)) {
            return project;
        } else {
            Project[] var2 = ProjectManager.getInstance().getOpenProjects();
            for (Project p : var2) {
                if (isValidProject(p)) {
                    return p;
                }
            }

            // 兜底返回默认项目实例
            return ProjectManager.getInstance().getDefaultProject();
        }
    }

    /**
     * 获取工程名
     */
    public static String getProjectName(Project project) {
        return project == null ? "" : project.getName();
    }

    /**
     * 判断是否为有效项目
     */
    private static boolean isValidProject(@Nullable Project project) {
        return project != null && !project.isDisposed() && !project.isDefault();
    }


    /**
     * 获取IDE类型
     *
     * @return
     */
    public static String getIDEType() {
        return ConfigWrapper.ClientType;
    }

    /**
     * 获取IDE版本
     *
     * @return
     */
    public static String getIDEVersion() {
        return ApplicationInfo.getInstance().getFullVersion();
    }


    /**
     * 获取插件版本
     * - 需要剥离环境后缀
     *
     * @return
     */
    public static String getPluginVersion() {
        String version = PluginUtil.Companion.getVersion();
        if (version.contains("_beta")) {
            version = version.replace("_beta", "");
        }
        if (version.contains("_test")) {
            version = version.replace("_test", "");
        }
        return version;
    }

    /**
     * 获取插件变更描述
     *
     * @return
     */
    public static String getPluginChangeNotes() {
        return PluginUtil.Companion.getChangeNotes();
    }


    /**
     * 获取当前构建号
     */
    public static int getBuildNumber() {
        ApplicationInfo appInfo = ApplicationInfo.getInstance();
        if (appInfo != null) {
            return appInfo.getBuild().getBaselineVersion();
        }

        // 默认为最低支持版本
        return 223;
    }
}
