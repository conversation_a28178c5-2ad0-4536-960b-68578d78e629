package com.srdcloud.ideplugin.general.utils

import com.intellij.openapi.diff.DiffColors
import com.intellij.openapi.editor.colors.EditorColorsManager
import com.intellij.openapi.vcs.FileStatus
import com.intellij.ui.Gray
import com.intellij.ui.JBColor
import com.intellij.util.ui.UIUtil
import java.awt.Color

/**
 * Chat面板颜色工具类
 */
object ChatColorUtil {

    /**
     * 欢迎页面按键背景色、文件背景色
     */
    val CONTEXT_BACKGROUND_COLOR = JBColor(Color(230, 240, 250), Gray._75)

    /**
     * 聊天用户消息背景色、添加上下文按钮背景色
     */
    val CHAT_USER_COLOR = JBColor(0xF2F2F2, 0x2B2B2B)


    /**
     * 绘制边框颜色
     */
    val BORDER_LINE_COLOR = JBColor(0xD8D8D8, 0x646464)

    /**
     * 内容被选中时的边框颜色
     */
    val FOCUS_BORDER_LINE_COLOR = UIUtil.getFocusedBorderColor()

    /**
     * diff时增添文本的颜色
     */
    val ADD_COLOR = EditorColorsManager.getInstance().globalScheme
        .getAttributes(DiffColors.DIFF_INSERTED)?.backgroundColor

    /**
     * diff时删除文本的颜色
     */
    val REMOVED_COLOR = EditorColorsManager.getInstance().globalScheme
        .getAttributes(DiffColors.DIFF_DELETED)?.backgroundColor

    fun getIdeaEditorBackground(): Color {
        val scheme = EditorColorsManager.getInstance().globalScheme
        return scheme.defaultBackground
    }

    /**
     * 接受修改按钮颜色、设置框提示消息成功颜色
     */
    val ACCEPTED_COLOR = FileStatus.ADDED.color

    /**
     * 撤回修改按钮颜色
     */
    val REVERTED_COLOR = FileStatus.DELETED.color

    /**
     * 设置框提示消息成功颜色
     */
    val SUCCESS_COLOR = Color(28, 141, 31)

}