package com.srdcloud.ideplugin.assistant.codechatNative.ui.message;

import com.srdcloud.ideplugin.assistant.codechatNative.logics.domain.ConversationMessageQuoteLink;
import com.srdcloud.ideplugin.assistant.codechatNative.uicomponent.ListViewer;
import com.srdcloud.ideplugin.assistant.codechatNative.uicomponent.RoundedPanel;
import com.srdcloud.ideplugin.common.icons.MyIcons;
import com.srdcloud.ideplugin.general.utils.BrowseUtil;
import com.srdcloud.ideplugin.general.utils.UIUtil;
import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.List;
import static javax.swing.SwingUtilities.invokeLater;

// 定义一个面板类，用于展示代码聊天中的消息引用链接
public class CodeChatMessageQuoteLinkPanel extends ListViewer<ConversationMessageQuoteLink> {
    // 构造函数，初始化面板
    public CodeChatMessageQuoteLinkPanel(ArrayList<ConversationMessageQuoteLink> list) {
        super(list);
        setOpaque(false);
    }

    // 重写创建单元格视图的方法，用于定制每个链接的显示方式
    @Override
    public JComponent createCellView(List<ConversationMessageQuoteLink> list, int index) {
        // 获取当前索引的引用链接对象
        ConversationMessageQuoteLink item = list.get(index);

        // 创建主面板
        RoundedPanel main = RoundedPanel.createDefaultPanel();

        // 判断当前UI主题是深色还是浅色
        boolean isDark = UIUtil.judgeBackgroudDarkTheme();

        // 根据主题设置面板背景色和边框色
        if (isDark) {
            main.setBackgroundColor(Color.decode("#2E3339"));
            main.setBorderColor(Color.decode("#4D4D4D"));
        } else {
            main.setBackgroundColor(Color.decode("#FFFFFF"));
            main.setBorderColor(Color.decode("#DDE2E7"));
        }

        // 设置边框宽度和布局
        main.setBorderWidth(1);
        main.setLayout(new BorderLayout());
        main.setVerOffset(1);

        // 创建并添加标题标签到面板
        JLabel text = new JLabel((index + 1) + "、" + item.getFullTittle());
        main.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));
        main.add(text, BorderLayout.CENTER);

        // 鼠标事件处理
        main.addMouseListener(new MouseAdapter() {
            // 鼠标移入时改变边框色
            @Override
            public void mouseEntered(MouseEvent e) {
                main.setBorderColor(UIUtil.getTextHoverColor());
                revalidate();
                repaint();
            }

            // 鼠标移出时恢复边框色
            @Override
            public void mouseExited(MouseEvent e) {
                main.setBorderColor(Color.decode(isDark ? "#4D4D4D" : "#DDE2E7"));
                revalidate();
                repaint();
            }

            // 鼠标点击时打开链接
            @Override
            public void mouseClicked(MouseEvent e) {
                BrowseUtil.Companion.browse(item.getLink());
            }
        });

        // 添加空白标签以调整布局
        main.add(new JLabel("  "), BorderLayout.WEST);
        main.add(new JLabel(" "), BorderLayout.NORTH);
        main.add(new JLabel(" "), BorderLayout.SOUTH);

        // 返回定制的面板组件
        return main;
    }

    // 重写改变选择的方法，留空以特殊处理或未来扩展
    @Override
    public void changeSelection() {

    }

    // 重写创建视图的方法，包括头部的创建和主题判断
    @Override
    public void createView() {

        // 创建头
        boolean isDark = UIUtil.judgeBackgroudDarkTheme();

        JPanel main = new JPanel(new BorderLayout());
        JPanel header = new JPanel();
        main.setOpaque(false);
        header.setOpaque(false);

        JLabel headText = new JLabel("相关链接(" + getList().size() + ")");
        JLabel headIcon = new JLabel(MyIcons.Downer);
        headText.setForeground(UIUtil.getLinkColor());
        headText.setForeground(UIUtil.textPlaceHolderColor);
        header.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));

        header.addMouseListener(new MouseAdapter() {
            private boolean isPressed = false;

            // 头部鼠标点击事件处理
            @Override
            public void mouseClicked(MouseEvent e) {
                invokeLater(() -> {
                    if (isPressed) {
                        headText.setForeground(UIUtil.getLinkColor());
                        headIcon.setIcon(isDark? MyIcons.DownerDark :MyIcons.Downer);
                        refresh();
                        isPressed = false;
                    } else {
                        headText.setForeground(UIUtil.getTextHoverColor());
                        headIcon.setIcon(MyIcons.Upper);
                        creatSuperView();
                        isPressed = true;
                    }
                });
            }
        });

        // 添加组件到头部并将其添加到主面板
        header.add(headText);
        header.add(headIcon);
        main.add(header, BorderLayout.WEST);
        add(main);
        revalidate();
        repaint();
    }

    // 创建超级视图的方法，调用父类的createView方法
    public void creatSuperView() {
        super.createView();
    }

    // 重写空视图方法，返回null以特殊处理空视图
    @Override
    public JComponent emptyView() {
        return null;
    }
}
