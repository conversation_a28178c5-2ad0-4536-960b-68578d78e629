package com.srdcloud.ideplugin.remote.domain.KnowledgeBase;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/5
 */
public class KnowledgeBaseListResponseData {
    /**
     * 总记录条数
     */
    public Long total;

    /**
     * 页大小
     */
    public Long size;

    /**
     * 总页数
     */
    public Long pages;

    /**
     * 当前页
     */
    public Long current;

    public List<KnowledgeBaseInfoV2> records;

    public KnowledgeBaseListResponseData(Long total, Long size, Long pages, Long current, List<KnowledgeBaseInfoV2> records) {
        this.total = total;
        this.size = size;
        this.pages = pages;
        this.current = current;
        this.records = records;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public Long getPages() {
        return pages;
    }

    public void setPages(Long pages) {
        this.pages = pages;
    }

    public Long getCurrent() {
        return current;
    }

    public void setCurrent(Long current) {
        this.current = current;
    }

    public List<KnowledgeBaseInfoV2> getRecords() {
        return records;
    }

    public void setRecords(List<KnowledgeBaseInfoV2> records) {
        this.records = records;
    }
}
