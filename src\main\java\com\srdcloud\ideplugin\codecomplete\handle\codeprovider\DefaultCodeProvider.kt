package com.srdcloud.ideplugin.codecomplete.handle.codeprovider

import com.intellij.openapi.editor.event.DocumentEvent
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.srdcloud.ideplugin.codecomplete.domain.CompletionType
import com.srdcloud.ideplugin.codecomplete.domain.CompletionElement
import com.srdcloud.ideplugin.codecomplete.handle.CompletionHandler
import com.srdcloud.ideplugin.codecomplete.domain.CompletionRequest
import com.srdcloud.ideplugin.codecomplete.domain.CompletionState.Companion.initOrGetInlineCompletionState
import com.srdcloud.ideplugin.codecomplete.domain.CompletionState.Companion.resetInlineCompletionState
import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.strategy.AgentCodeCompletionStrategy
import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.strategy.CodeCompletionStrategy
import com.srdcloud.ideplugin.codecomplete.handle.codeprovider.strategy.DefaultCodeCompletionStrategy
import com.srdcloud.ideplugin.general.constants.CodeCompletionType
import com.srdcloud.ideplugin.general.constants.RtnCode
import com.srdcloud.ideplugin.general.utils.FileUtil
import com.srdcloud.ideplugin.general.utils.pluginSettings
import kotlin.coroutines.Continuation
import kotlin.coroutines.suspendCoroutine

class DefaultCodeProvider private constructor() : ICompletionProvider {
    // 策略模式：本地模式 和 agent模式
    // 本地模式：只有代码文档上下文
    // agent模式：走tabby-agent进行补全
    private val strategies: MutableMap<String, CodeCompletionStrategy> = HashMap()
    init {
        strategies[CodeCompletionType.DEFAULT_CODE_COMPLETION] = DefaultCodeCompletionStrategy()
        strategies[CodeCompletionType.AGENT_CODE_COMPLETION] = AgentCodeCompletionStrategy()
    }

    companion object {
        val instance: DefaultCodeProvider by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            DefaultCodeProvider()
        }
    }

    /**
     * 获取代码补全推理提示
     */
    override suspend fun getProposals(request: CompletionRequest): List<CompletionElement> {
        val fileType = FileUtil.getExtension(FileDocumentManager.getInstance().getFile(request.document)?.name)
        val disabledLangs = pluginSettings().getDisabledLanguages()
        if (fileType != null && disabledLangs.contains(fileType)) {
            return emptyList()
        }

        val editor = request.editor
        val state = editor.initOrGetInlineCompletionState()

        val ret = listOf<CompletionElement>()

        // 校验时间戳：当前行内补全state上下文的时间戳，与文件最新修改时间戳一致,则获取补全建议
        // todo：代码补全逻辑修复
        if (state.lastModificationStamp == request.document.modificationStamp && request.type == CompletionType.AUTO) {
            // 查看是否已存在有效建议
            val suggestion = state.suggestions.getOrNull(state.suggestionIndex)
            if (suggestion != null) {
                // 检查 state 是否需要新的建议
                if (state.needGetSuggestion()) {
                    // 发起补全提问
                    askCodeCompletion(request)
                }
                return ret
            }
        } else {
            // 文档的修改时间戳不匹配，意味着文档可能已经被修改，则重置当前编辑器的内联补全状态。
            editor.resetInlineCompletionState()
        }

        // 初始化行内建议state
        editor.initOrGetInlineCompletionState()

        // 进行提问：挂起当前协程，等待askQuestion返回
        suspendCoroutine { continuation ->
            askCodeCompletion(request, continuation)
        }

        return ret
    }


    private fun askCodeCompletion(request: CompletionRequest, continuation: Continuation<Unit>? = null) {
        // 如果tabby-agent就绪，则优先走tabby补全
//        if (strategies[CodeCompletionType.AGENT_CODE_COMPLETION]?.isEnabled == true) {
            strategies[CodeCompletionType.AGENT_CODE_COMPLETION]?.getCodeCompletion(request, continuation)
//        }else {
//            // 否则，走本地补全
//            strategies[CodeCompletionType.DEFAULT_CODE_COMPLETION]?.getCodeCompletion(request, continuation)
//        }
    }

    override fun isEnabled(event: DocumentEvent): Boolean {
        return true
    }

    private fun finished(errCode: Int = RtnCode.SUCCESS) {
        CompletionHandler.stopWorking(errCode)
    }
}