package com.srdcloud.ideplugin.remote;

import com.google.common.collect.Maps;
import com.srdcloud.ideplugin.general.config.ConfigWrapper;
import com.srdcloud.ideplugin.general.constants.RtnCode;
import com.srdcloud.ideplugin.general.utils.*;
import com.srdcloud.ideplugin.remote.client.FastFailHttpClient;
import com.srdcloud.ideplugin.remote.domain.ApiResponse;
import com.srdcloud.ideplugin.remote.domain.KnowledgeBase.KnowledgeBaseDetailResponse;
import com.srdcloud.ideplugin.remote.domain.KnowledgeBase.KnowledgeBaseListResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Optional;

import static com.srdcloud.ideplugin.general.utils.PluginSettingsForSecUtilKt.pluginSettings;

/**
 * <AUTHOR>
 * @description 知识库后端接口通信对接
 */
public class KnowledgeBaseCommHandler {

    private static final Logger logger = LoggerFactory.getLogger(KnowledgeBaseCommHandler.class);


    // 查询开发问答可用的知识库列表（旧接口，知识库会保留该接口）
    private static final String kbListPath = "api/smartassistbackend/aiknowledge-base/v1/ide/search-dev-kbs";

//    // 全面接入知识库，列表接口V2
//    private static final String kbListPathV2 = "api/smartassistbackend/aiknowledge-base/v2/ide/search-dev-kbs";

    // 查询知识库详情（可以返回删除或禁用状态）
    private static final String kbDetailPath = "api/smartassistbackend/aiknowledge-base/v1/ide/get-kb-info";

    /**
     * 获取知识库列表
     */
    public static KnowledgeBaseListResponse getKnowledgeBaseList() {
        KnowledgeBaseListResponse response = new KnowledgeBaseListResponse(RtnCode.OFFLINE, "网络条件异常，请稍后重试.", null);
        if (!LocalStorageUtil.checkNetCondition()) {
            logger.warn("[cf] getKnowledgeBaseList skip, net condition fail.");
            return response;
        }

        final String baseUrl = ConfigWrapper.getServerUrl() + kbListPath;
        final HashMap<String, String> params = Maps.newHashMapWithExpectedSize(2);
        params.put("currentPage", String.valueOf(1));
        if (EnvUtil.isSec()) {
            params.put("kbServer",ConfigWrapper.getServerUrl());
            if(pluginSettings().getSecretKey() == null){
                params.put("userSecret", "");
            } else {
                params.put("userSecret", pluginSettings().getSecretKey());
            }
        }
        //params.put("pageSize", String.valueOf(5));

        final String queryUrl = UrlUtil.buildUrlWithParams(baseUrl, params);
        final HashMap<String, String> headers = generateAuthHeaders();
        final ApiResponse apiResponse = FastFailHttpClient.doGet(queryUrl, headers);

        if (apiResponse == null) {
            logger.warn("[cf] KnowledgeBaseCommHandler kbList, KnowledgeBaseListResponse is null}");
            return new KnowledgeBaseListResponse(RtnCode.OFFLINE, "网络异常", null);
        }

        if (RtnCode.SUCCESS != apiResponse.getRtnCode()) {
            logger.warn("[cf] KnowledgeBaseCommHandler kbList,rtnCode:{},msg:{}", apiResponse.getRtnCode(), apiResponse.getMessage());
            return new KnowledgeBaseListResponse(apiResponse.getRtnCode(), apiResponse.getMessage(), null);
        }

        return JsonUtil.getInstance().fromJson(apiResponse.getMessage(), KnowledgeBaseListResponse.class);
    }

    /**
     * 获取知识库详情
     */
    public static KnowledgeBaseDetailResponse getKnowledgeBaseDetail(final Integer kbId) {
        if (!LocalStorageUtil.checkNetCondition()) {
            logger.warn("[cf] getKnowledgeBaseDetail skip, net condition fail.");
            return new KnowledgeBaseDetailResponse(RtnCode.OFFLINE, "网络条件异常，请稍后重试.", null);
        }

        final String baseUrl = ConfigWrapper.getServerUrl() + kbDetailPath;
        final HashMap<String, String> params = Maps.newHashMapWithExpectedSize(1);
        params.put("kbId", String.valueOf(kbId));

        final String queryUrl = UrlUtil.buildUrlWithParams(baseUrl, params);
        final HashMap<String, String> headers = generateAuthHeaders();
        final ApiResponse apiResponse = FastFailHttpClient.doGet(queryUrl, headers);

        if (apiResponse == null) {
            logger.warn("[cf] KnowledgeBaseCommHandler kbList, KnowledgeBaseDetailResponse is null}");
            return new KnowledgeBaseDetailResponse(RtnCode.OFFLINE, "网络异常", null);
        }

        if (RtnCode.SUCCESS != apiResponse.getRtnCode()) {
            logger.warn("[cf] KnowledgeBaseCommHandler getKnowledgeBaseDetail,rtnCode:{},msg:{}", apiResponse.getRtnCode(), apiResponse.getMessage());
            return new KnowledgeBaseDetailResponse(apiResponse.getRtnCode(), apiResponse.getMessage(), null);
        }

        return JsonUtil.getInstance().fromJson(apiResponse.getMessage(), KnowledgeBaseDetailResponse.class);
    }


    /**
     * 生成网关层鉴权头域字段
     */
    private static HashMap<String, String> generateAuthHeaders() {
        HashMap<String, String> headers = Maps.newHashMapWithExpectedSize(4);
        headers.put("apiKey", LocalStorageUtil.getApikey());
        headers.put("userid", LocalStorageUtil.getUserId());
        headers.put("invokerId", LocalStorageUtil.getUserId());
        headers.put("x-dup-id", String.valueOf(TimeUtil.getNowTimeSecTimestamp() + "-" + MyRandomUtil.generateRandomString(8)));
        return headers;
    }
}
